#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لواجهة تسجيل الدخول
"""

import sys
import os
sys.path.append('src')

def test_simple_login():
    """اختبار بسيط لواجهة تسجيل الدخول"""
    
    print("🧪 اختبار بسيط لواجهة تسجيل الدخول...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # إنشاء واجهة تسجيل الدخول (بدون config_manager)
        print("🔄 إنشاء واجهة تسجيل الدخول...")
        login_window = LoginWindow(db)
        
        print("✅ تم إنشاء واجهة تسجيل الدخول بنجاح")
        
        # محاكاة إدخال البيانات
        login_window.username_edit.setText('admin')
        login_window.password_edit.setText('123')
        
        print("📝 تم إدخال البيانات:")
        print(f"  • اسم المستخدم: '{login_window.username_edit.text()}'")
        print(f"  • كلمة المرور: '{login_window.password_edit.text()}'")
        
        # محاكاة عملية تسجيل الدخول
        print("🔄 محاكاة عملية تسجيل الدخول...")
        
        # استدعاء دالة تسجيل الدخول مباشرة
        login_window.handle_login()
        
        # فحص النتيجة
        if hasattr(login_window, 'current_user') and login_window.current_user:
            print("✅ تسجيل الدخول نجح!")
            print(f"المستخدم الحالي: {login_window.current_user}")
            return True
        else:
            print("❌ تسجيل الدخول فشل!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_actual_login_window():
    """تشغيل واجهة تسجيل الدخول الفعلية"""
    
    print("\n🖥️ تشغيل واجهة تسجيل الدخول الفعلية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # إنشاء واجهة تسجيل الدخول
        print("🔄 إنشاء واجهة تسجيل الدخول...")
        login_window = LoginWindow(db)
        
        print("✅ تم إنشاء واجهة تسجيل الدخول")
        print("📋 المستخدمين المتاحين:")
        print("  • admin / 123")
        print("  • user1 / 123")
        print("  • cashier / 123")
        print("  • manager / 123")
        
        # عرض الواجهة
        print("🖥️ عرض واجهة تسجيل الدخول...")
        login_window.show()
        
        # تشغيل التطبيق
        result = app.exec_()
        
        # فحص النتيجة
        if hasattr(login_window, 'current_user') and login_window.current_user:
            print("✅ تم تسجيل الدخول بنجاح!")
            print(f"المستخدم: {login_window.current_user}")
            return True
        else:
            print("❌ لم يتم تسجيل الدخول")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل واجهة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_login_window_code():
    """فحص كود واجهة تسجيل الدخول"""
    
    print("\n🔍 فحص كود واجهة تسجيل الدخول...")
    
    try:
        # قراءة ملف واجهة تسجيل الدخول
        with open('src/ui/login_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة handle_login
        if 'def handle_login(self):' in content:
            print("✅ دالة handle_login موجودة")
        else:
            print("❌ دالة handle_login مفقودة")
        
        # البحث عن استعلام قاعدة البيانات
        if 'SELECT * FROM users WHERE username = ?' in content:
            print("✅ استعلام البحث عن المستخدم موجود")
        else:
            print("❌ استعلام البحث عن المستخدم مفقود")
        
        # البحث عن تشفير كلمة المرور
        if 'hashlib.sha256' in content:
            print("✅ تشفير كلمة المرور موجود")
        else:
            print("❌ تشفير كلمة المرور مفقود")
        
        # البحث عن رسالة "المستخدم غير موجود"
        if 'المستخدم غير موجود' in content:
            print("✅ رسالة 'المستخدم غير موجود' موجودة")
        else:
            print("❌ رسالة 'المستخدم غير موجود' مفقودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص كود واجهة تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار بسيط لواجهة تسجيل الدخول")
    print("=" * 50)
    
    # فحص كود واجهة تسجيل الدخول
    code_check = check_login_window_code()
    
    # اختبار بسيط لواجهة تسجيل الدخول
    simple_test = test_simple_login()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"  • فحص كود واجهة تسجيل الدخول: {'✅ نجح' if code_check else '❌ فشل'}")
    print(f"  • اختبار بسيط لواجهة تسجيل الدخول: {'✅ نجح' if simple_test else '❌ فشل'}")
    
    if simple_test:
        print("\n🎉 واجهة تسجيل الدخول تعمل بشكل صحيح!")
        
        # تشغيل الواجهة الفعلية
        print("\n🚀 هل تريد تشغيل واجهة تسجيل الدخول الفعلية؟ (y/n)")
        choice = input().strip().lower()
        
        if choice == 'y':
            run_actual_login_window()
        
    else:
        print("\n❌ هناك مشكلة في واجهة تسجيل الدخول!")
        
        print("\n💡 الحلول المقترحة:")
        print("  1. تأكد من وجود المستخدمين في قاعدة البيانات")
        print("  2. تأكد من صحة كلمات المرور")
        print("  3. تأكد من أن المستخدمين نشطين (is_active = 1)")
        print("  4. راجع كود واجهة تسجيل الدخول")

if __name__ == "__main__":
    main()

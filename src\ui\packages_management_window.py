# -*- coding: utf-8 -*-
"""
نافذة إدارة الباقات
Packages Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QDoubleSpinBox, QGroupBox, 
                            QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency


class PackagesManagementWindow(QDialog):
    """نافذة إدارة الباقات"""
    
    package_updated = pyqtSignal()
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الباقات")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة باقات الإنترنت")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #e8f5e8;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # نموذج إضافة/تعديل باقة
        package_form = self.create_package_form()
        
        # جدول الباقات
        self.packages_table = self.create_packages_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addWidget(package_form)
        layout.addWidget(self.packages_table)
        layout.addLayout(buttons_layout)
        
    def create_package_form(self):
        """إنشاء نموذج إضافة/تعديل باقة"""
        group = QGroupBox("إضافة/تعديل باقة")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # اسم الباقة
        name_label = QLabel("اسم الباقة:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)
        self.name_edit.setPlaceholderText("مثال: باقة 50 ميجا")
        
        # السعر
        price_label = QLabel("السعر:")
        apply_arabic_style(price_label, 10)
        self.price_spin = QDoubleSpinBox()
        apply_arabic_style(self.price_spin, 10)
        self.price_spin.setRange(0, 999999999)
        self.price_spin.setSuffix(" ل.س")
        
        # السرعة
        speed_label = QLabel("السرعة:")
        apply_arabic_style(speed_label, 10)
        self.speed_edit = QLineEdit()
        apply_arabic_style(self.speed_edit, 10)
        self.speed_edit.setPlaceholderText("مثال: 50 Mbps")
        
        # الوصف
        description_label = QLabel("الوصف:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QTextEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف تفصيلي للباقة...")
        
        # الحالة
        self.is_active_check = QCheckBox("باقة نشطة")
        apply_arabic_style(self.is_active_check, 10)
        self.is_active_check.setChecked(True)
        
        # أزرار النموذج
        form_buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة باقة")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        update_button = QPushButton("تحديث باقة")
        apply_arabic_style(update_button, 10)
        update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        clear_button = QPushButton("مسح النموذج")
        apply_arabic_style(clear_button, 10)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        form_buttons_layout.addWidget(add_button)
        form_buttons_layout.addWidget(update_button)
        form_buttons_layout.addWidget(clear_button)
        form_buttons_layout.addStretch()
        
        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.name_edit, 0, 1)
        layout.addWidget(price_label, 0, 2)
        layout.addWidget(self.price_spin, 0, 3)
        layout.addWidget(speed_label, 1, 0)
        layout.addWidget(self.speed_edit, 1, 1)
        layout.addWidget(self.is_active_check, 1, 2, 1, 2)
        layout.addWidget(description_label, 2, 0)
        layout.addWidget(self.description_edit, 2, 1, 1, 3)
        layout.addLayout(form_buttons_layout, 3, 0, 1, 4)
        
        # ربط الأحداث
        add_button.clicked.connect(self.add_package)
        update_button.clicked.connect(self.update_package)
        clear_button.clicked.connect(self.clear_form)
        
        # حفظ مراجع الأزرار
        self.add_button = add_button
        self.update_button = update_button
        self.selected_package_id = None
        
        return group
        
    def create_packages_table(self):
        """إنشاء جدول الباقات"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "الرقم", "اسم الباقة", "السعر", "السرعة", "الحالة", "الوصف"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, header.Stretch)  # عمود اسم الباقة
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر تحديد للتعديل
        select_button = QPushButton("تحديد للتعديل")
        apply_arabic_style(select_button, 10)
        select_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر حذف باقة
        delete_button = QPushButton("حذف باقة")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(select_button)
        layout.addWidget(delete_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        select_button.clicked.connect(self.select_package_for_edit)
        delete_button.clicked.connect(self.delete_package)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل بيانات الباقات"""
        try:
            packages = self.db_manager.fetch_all("""
                SELECT id, name, price, speed, description, is_active, created_at
                FROM packages 
                ORDER BY name
            """)
            
            self.packages_table.setRowCount(len(packages))
            
            for row, package in enumerate(packages):
                # الرقم
                self.packages_table.setItem(row, 0, QTableWidgetItem(str(package['id'])))
                
                # اسم الباقة
                self.packages_table.setItem(row, 1, QTableWidgetItem(package['name']))
                
                # السعر
                price_text = format_currency(package['price'])
                self.packages_table.setItem(row, 2, QTableWidgetItem(price_text))
                
                # السرعة
                self.packages_table.setItem(row, 3, QTableWidgetItem(package['speed'] or ''))
                
                # الحالة
                status = "نشطة" if package['is_active'] else "غير نشطة"
                status_item = QTableWidgetItem(status)
                if package['is_active']:
                    status_item.setBackground(QColor("#d5f4e6"))
                    status_item.setForeground(QColor("#27ae60"))
                else:
                    status_item.setBackground(QColor("#fadbd8"))
                    status_item.setForeground(QColor("#e74c3c"))
                self.packages_table.setItem(row, 4, status_item)
                
                # الوصف
                self.packages_table.setItem(row, 5, QTableWidgetItem(package['description'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الباقات: {e}")
            
    def add_package(self):
        """إضافة باقة جديدة"""
        if not self.validate_form():
            return
            
        try:
            package_data = self.get_form_data()
            
            self.db_manager.execute_query("""
                INSERT INTO packages (name, price, speed, description, is_active)
                VALUES (?, ?, ?, ?, ?)
            """, (
                package_data['name'], package_data['price'], package_data['speed'],
                package_data['description'], package_data['is_active']
            ))
            
            QMessageBox.information(self, "تم", "تم إضافة الباقة بنجاح")
            self.clear_form()
            self.load_data()
            self.package_updated.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الباقة: {e}")
            
    def update_package(self):
        """تحديث باقة موجودة"""
        if not self.selected_package_id:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد باقة للتعديل أولاً")
            return
            
        if not self.validate_form():
            return
            
        try:
            package_data = self.get_form_data()
            
            self.db_manager.execute_query("""
                UPDATE packages SET name = ?, price = ?, speed = ?, 
                                  description = ?, is_active = ?
                WHERE id = ?
            """, (
                package_data['name'], package_data['price'], package_data['speed'],
                package_data['description'], package_data['is_active'], 
                self.selected_package_id
            ))
            
            QMessageBox.information(self, "تم", "تم تحديث الباقة بنجاح")
            self.clear_form()
            self.load_data()
            self.package_updated.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الباقة: {e}")
            
    def select_package_for_edit(self):
        """تحديد باقة للتعديل"""
        current_row = self.packages_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار باقة من الجدول")
            return
            
        try:
            package_id = int(self.packages_table.item(current_row, 0).text())
            
            # جلب بيانات الباقة
            package = self.db_manager.fetch_one("""
                SELECT * FROM packages WHERE id = ?
            """, (package_id,))
            
            if package:
                # ملء النموذج
                self.name_edit.setText(package['name'])
                self.price_spin.setValue(package['price'])
                self.speed_edit.setText(package['speed'] or '')
                self.description_edit.setPlainText(package['description'] or '')
                self.is_active_check.setChecked(bool(package['is_active']))
                
                # حفظ معرف الباقة المحددة
                self.selected_package_id = package_id
                
                QMessageBox.information(self, "تم", f"تم تحديد الباقة '{package['name']}' للتعديل")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديد الباقة: {e}")
            
    def delete_package(self):
        """حذف باقة"""
        current_row = self.packages_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار باقة للحذف")
            return
            
        package_id = self.packages_table.item(current_row, 0).text()
        package_name = self.packages_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف الباقة '{package_name}'؟\n\nتحذير: قد يؤثر هذا على الاشتراكات الموجودة",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM packages WHERE id = ?", (package_id,))
                QMessageBox.information(self, "تم", "تم حذف الباقة بنجاح")
                self.load_data()
                self.package_updated.emit()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الباقة: {e}")
                
    def clear_form(self):
        """مسح النموذج"""
        self.name_edit.clear()
        self.price_spin.setValue(0)
        self.speed_edit.clear()
        self.description_edit.clear()
        self.is_active_check.setChecked(True)
        self.selected_package_id = None
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الباقة")
            self.name_edit.setFocus()
            return False
            
        if self.price_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح للباقة")
            self.price_spin.setFocus()
            return False
            
        return True
        
    def get_form_data(self):
        """الحصول على بيانات النموذج"""
        return {
            'name': self.name_edit.text().strip(),
            'price': self.price_spin.value(),
            'speed': self.speed_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'is_active': self.is_active_check.isChecked()
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح الأخطاء
"""

import sys
import os
sys.path.append('src')

def test_cash_box_closure():
    """اختبار إغلاق الصندوق"""
    
    print("🧪 اختبار إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.cash_box_closure_window import CashBoxClosureWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء واجهة إغلاق الصندوق
        window = CashBoxClosureWindow(db, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة إغلاق الصندوق بدون أخطاء")
        
        # اختبار وجود الحقول المطلوبة
        if hasattr(window, 'actual_cash_spin') and window.actual_cash_spin is not None:
            print("✅ حقل المبلغ الفعلي موجود")
        else:
            print("❌ حقل المبلغ الفعلي غير موجود")
            return False
        
        # اختبار وجود دالة الإغلاق
        if hasattr(window, 'close_cash_box') and callable(window.close_cash_box):
            print("✅ دالة إغلاق الصندوق موجودة")
        else:
            print("❌ دالة إغلاق الصندوق غير موجودة")
            return False
        
        print("✅ إغلاق الصندوق يعمل بدون خطأ closing_amount")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_products_management():
    """اختبار إدارة المنتجات"""
    
    print("\n🧪 اختبار إدارة المنتجات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.config_manager import ConfigManager
        from ui.products_management_window import ProductsManagementWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        config_manager = ConfigManager()
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء واجهة إدارة المنتجات
        window = ProductsManagementWindow(db, inventory_manager, config_manager, current_user)
        
        print("✅ تم إنشاء واجهة إدارة المنتجات بدون أخطاء")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بدون خطأ sqlite3.Row")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المنتجات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_units_management():
    """اختبار إدارة الوحدات"""
    
    print("\n🧪 اختبار إدارة الوحدات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from ui.units_management_window import UnitsManagementWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء واجهة إدارة الوحدات
        window = UnitsManagementWindow(db, inventory_manager, current_user)
        
        print("✅ تم إنشاء واجهة إدارة الوحدات بدون أخطاء")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بدون خطأ sqlite3.Row")
        
        # اختبار اختيار منتج
        if window.product_combo.count() > 1:
            window.product_combo.setCurrentIndex(1)
            print("✅ تم اختيار منتج بدون خطأ sqlite3.Row")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_manager():
    """اختبار مدير المخزون الموحد"""
    
    print("\n🧪 اختبار مدير المخزون الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        print("✅ تم إنشاء مدير المخزون الموحد")
        
        # اختبار تحميل جميع المنتجات
        products = inventory_manager.get_all_products()
        print(f"✅ تم تحميل {len(products)} منتج بدون خطأ sqlite3.Row")
        
        # اختبار تحميل منتجات بفئة معينة
        routers = inventory_manager.get_products_by_category('راوتر')
        print(f"✅ تم تحميل {len(routers)} راوتر بدون خطأ sqlite3.Row")
        
        # اختبار أن المنتجات تحتوي على الحقول المطلوبة
        if len(products) > 0:
            product = products[0]
            required_fields = ['id', 'name', 'category', 'sale_price', 'current_stock']
            
            for field in required_fields:
                if field in product:
                    print(f"  ✅ حقل {field} موجود")
                else:
                    print(f"  ❌ حقل {field} مفقود")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير المخزون: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح الأخطاء")
    print("=" * 50)
    
    # اختبار إغلاق الصندوق
    cash_box_test = test_cash_box_closure()
    
    # اختبار إدارة المنتجات
    products_test = test_products_management()
    
    # اختبار إدارة الوحدات
    units_test = test_units_management()
    
    # اختبار مدير المخزون
    inventory_test = test_inventory_manager()
    
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • إغلاق الصندوق: {'✅ مُصلح' if cash_box_test else '❌ يحتاج إصلاح'}")
    print(f"  • إدارة المنتجات: {'✅ مُصلح' if products_test else '❌ يحتاج إصلاح'}")
    print(f"  • إدارة الوحدات: {'✅ مُصلح' if units_test else '❌ يحتاج إصلاح'}")
    print(f"  • مدير المخزون: {'✅ مُصلح' if inventory_test else '❌ يحتاج إصلاح'}")
    
    if all([cash_box_test, products_test, units_test, inventory_test]):
        print("\n🎉 تم إصلاح جميع الأخطاء بنجاح!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح خطأ 'closing_amount' is not defined")
        print("    • استبدال closing_amount بـ actual_cash")
        print("    • إصلاح رسالة نجاح إغلاق الصندوق")
        
        print("  ✅ إصلاح خطأ 'sqlite3.Row' object has no attribute 'get'")
        print("    • معالجة آمنة للحقول في إدارة المنتجات")
        print("    • معالجة آمنة للحقول في إدارة الوحدات")
        print("    • تحويل sqlite3.Row إلى قاموس في مدير المخزون")
        
        print("\n🔧 النظام الآن:")
        print("  • إغلاق الصندوق يعمل بدون أخطاء")
        print("  • تحميل بيانات المنتجات يعمل بدون أخطاء")
        print("  • إدارة الوحدات تعمل بدون أخطاء")
        print("  • جميع الواجهات مستقرة")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. جرب إغلاق الصندوق - لن يظهر خطأ closing_amount")
        print("  3. جرب إدارة المنتجات - لن يظهر خطأ sqlite3.Row")
        print("  4. جرب إدارة الوحدات - لن يظهر خطأ sqlite3.Row")
        
    else:
        print("\n❌ هناك أخطاء تحتاج إصلاح إضافي!")
        
        if not cash_box_test:
            print("  • راجع كود إغلاق الصندوق")
        if not products_test:
            print("  • راجع كود إدارة المنتجات")
        if not units_test:
            print("  • راجع كود إدارة الوحدات")
        if not inventory_test:
            print("  • راجع كود مدير المخزون")

if __name__ == "__main__":
    main()

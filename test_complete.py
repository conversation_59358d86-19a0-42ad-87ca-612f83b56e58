#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الكامل
Complete System Test
"""

import sys
import os
from pathlib import Path

def test_all_imports():
    """اختبار جميع الاستيرادات"""
    print("🔍 اختبار جميع الاستيرادات...")
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        # الوحدات الأساسية
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager")
        
        from src.utils.config_manager import ConfigManager
        print("✅ ConfigManager")
        
        from src.utils.arabic_support import setup_arabic_support, simple_arabic_text
        print("✅ Arabic Support")
        
        # الواجهات الأساسية
        from src.ui.login_window import LoginWindow
        print("✅ LoginWindow")
        
        from src.ui.main_window import MainWindow
        print("✅ MainWindow")
        
        # الواجهات الوظيفية
        from src.ui.new_subscription_window import NewSubscriptionWindow
        print("✅ NewSubscriptionWindow")
        
        from src.ui.router_delivery_window import RouterDeliveryWindow
        print("✅ RouterDeliveryWindow")
        
        from src.ui.package_renewal_window import PackageRenewalWindow
        print("✅ PackageRenewalWindow")
        
        from src.ui.cash_close_window import CashCloseWindow
        print("✅ CashCloseWindow")
        
        # الواجهات الإدارية
        from src.ui.subscribers_management_window import SubscribersManagementWindow
        print("✅ SubscribersManagementWindow")
        
        from src.ui.products_management_window import ProductsManagementWindow
        print("✅ ProductsManagementWindow")
        
        from src.ui.settings_window import SettingsWindow
        print("✅ SettingsWindow")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n📊 اختبار قاعدة البيانات...")
    
    try:
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from src.database.database_manager import DatabaseManager
        
        # إنشاء مجلد اختبار
        test_dir = project_root / "test_data"
        test_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager(str(test_dir))
        
        if db_manager.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات")
            
            # اختبار البيانات
            users = db_manager.fetch_all("SELECT * FROM users")
            print(f"✅ المستخدمون: {len(users)}")
            
            products = db_manager.fetch_all("SELECT * FROM products")
            print(f"✅ المنتجات: {len(products)}")
            
            packages = db_manager.fetch_all("SELECT * FROM packages")
            print(f"✅ الباقات: {len(packages)}")
            
            return True
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_arabic_text():
    """اختبار النصوص العربية"""
    print("\n🔤 اختبار النصوص العربية...")
    
    try:
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from src.utils.arabic_support import simple_arabic_text, format_currency
        
        # اختبار النصوص
        test_texts = [
            "نظام إدارة شركة الإنترنت",
            "اشتراك جديد",
            "تسليم راوتر",
            "إدارة المشتركين",
            "الإعدادات"
        ]
        
        print("النصوص العربية:")
        for text in test_texts:
            processed = simple_arabic_text(text)
            print(f"  • {processed}")
            
        # اختبار تنسيق العملة
        amounts = [50000, 150000, 1000000]
        print("\nتنسيق العملة:")
        for amount in amounts:
            formatted = format_currency(amount)
            print(f"  • {formatted}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النصوص العربية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام إدارة شركة الإنترنت")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # اختبار الاستيرادات
    if test_all_imports():
        success_count += 1
        
    # اختبار قاعدة البيانات
    if test_database():
        success_count += 1
        
    # اختبار النصوص العربية
    if test_arabic_text():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{total_tests} اختبار نجح")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للتشغيل الكامل")
        
        print("\n🚀 الواجهات المتاحة:")
        print("   ✅ اشتراك جديد")
        print("   ✅ تسليم راوتر") 
        print("   ✅ تجديد باقة")
        print("   ✅ إغلاق الصندوق")
        print("   ✅ إدارة المشتركين")
        print("   ✅ إدارة المنتجات")
        print("   ✅ الإعدادات")
        
        print("\n🔧 لتشغيل النظام:")
        print("python run_complete_system.py")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

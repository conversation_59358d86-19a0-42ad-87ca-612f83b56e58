#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مباشر لقاعدة البيانات
"""

import sqlite3
import hashlib
import os
from pathlib import Path

def create_users_table(db_path):
    """إنشاء جدول المستخدمين"""
    
    print(f"📊 إنشاء جدول المستخدمين في: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'employee',
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جدول المستخدمين")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول المستخدمين: {e}")
        return False

def add_users_to_database(db_path):
    """إضافة المستخدمين إلى قاعدة البيانات"""
    
    print(f"👤 إضافة المستخدمين إلى: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # المستخدمين الافتراضيين
        users = [
            ('admin', '123', 'المدير العام', '<EMAIL>', 'admin'),
            ('manager', '123', 'المدير', '<EMAIL>', 'manager'),
            ('employee', '123', 'الموظف', '<EMAIL>', 'employee'),
            ('accountant', '123', 'المحاسب', '<EMAIL>', 'accountant'),
            ('user1', '123', 'مستخدم 1', '<EMAIL>', 'user'),
            ('cashier', '123', 'أمين الصندوق', '<EMAIL>', 'cashier')
        ]
        
        created_count = 0
        
        for username, password, full_name, email, role in users:
            # تشفير كلمة المرور
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            existing = cursor.fetchone()
            
            if existing:
                print(f"  ⚠️ المستخدم '{username}' موجود بالفعل")
                continue
            
            # إنشاء المستخدم
            cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
            """, (username, password_hash, full_name, email, role))
            
            print(f"  ✅ تم إنشاء المستخدم: {username} / {password} ({role})")
            created_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"📊 تم إنشاء {created_count} مستخدم جديد")
        return created_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدمين: {e}")
        return False

def test_login_direct(db_path):
    """اختبار تسجيل الدخول مباشرة"""
    
    print(f"🧪 اختبار تسجيل الدخول في: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # بيانات تسجيل الدخول
        username = 'admin'
        password = '123'
        
        print(f"📝 اختبار تسجيل الدخول:")
        print(f"  • اسم المستخدم: {username}")
        print(f"  • كلمة المرور: {password}")
        
        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"  • كلمة المرور المشفرة: {hashed_password}")
        
        # البحث عن المستخدم
        cursor.execute("""
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, hashed_password))
        
        user = cursor.fetchone()
        
        if user:
            print("✅ تسجيل الدخول نجح!")
            print(f"المستخدم: {user['full_name']} ({user['role']})")
            conn.close()
            return True
        else:
            print("❌ تسجيل الدخول فشل!")
            
            # تشخيص السبب
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            user_check = cursor.fetchone()
            
            if user_check:
                print(f"المستخدم موجود لكن:")
                if user_check['password_hash'] != hashed_password:
                    print(f"  • كلمة المرور خاطئة")
                    print(f"    المتوقع: {user_check['password_hash']}")
                    print(f"    المدخل: {hashed_password}")
                if user_check['is_active'] != 1:
                    print(f"  • المستخدم غير نشط: {user_check['is_active']}")
            else:
                print("المستخدم غير موجود")
            
            conn.close()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def list_users_in_database(db_path):
    """عرض المستخدمين في قاعدة البيانات"""
    
    print(f"👥 المستخدمين في: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, full_name, role, is_active FROM users")
        users = cursor.fetchall()
        
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        if users:
            for user in users:
                status = "نشط" if user['is_active'] == 1 else "غير نشط"
                print(f"  • ID: {user['id']}, اسم المستخدم: '{user['username']}', الاسم: '{user['full_name']}', الدور: '{user['role']}', الحالة: {status}")
        else:
            print("  ❌ لا يوجد مستخدمين!")
        
        conn.close()
        return len(users)
        
    except Exception as e:
        print(f"❌ خطأ في عرض المستخدمين: {e}")
        return 0

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مباشر لقاعدة البيانات")
    print("=" * 50)
    
    # مسارات قواعد البيانات المحتملة
    db_paths = [
        'data/company_system.db',
        './data/company_system.db'
    ]
    
    # التأكد من وجود مجلد data
    os.makedirs('data', exist_ok=True)
    
    success_count = 0
    
    for db_path in db_paths:
        print(f"\n🔧 معالجة قاعدة البيانات: {db_path}")
        
        # إنشاء جدول المستخدمين
        table_created = create_users_table(db_path)
        
        if table_created:
            # إضافة المستخدمين
            users_added = add_users_to_database(db_path)
            
            # عرض المستخدمين
            user_count = list_users_in_database(db_path)
            
            # اختبار تسجيل الدخول
            login_test = test_login_direct(db_path)
            
            if login_test:
                success_count += 1
                print(f"✅ قاعدة البيانات {db_path} جاهزة!")
            else:
                print(f"❌ مشكلة في قاعدة البيانات {db_path}")
        else:
            print(f"❌ فشل في إنشاء جدول المستخدمين في {db_path}")
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"  • قواعد البيانات المعالجة: {len(db_paths)}")
    print(f"  • قواعد البيانات الناجحة: {success_count}")
    
    if success_count > 0:
        print("\n🎉 تم إصلاح قاعدة البيانات!")
        
        print("\n📋 المستخدمين المتاحين:")
        print("  • admin / 123 (المدير العام)")
        print("  • manager / 123 (المدير)")
        print("  • employee / 123 (الموظف)")
        print("  • accountant / 123 (المحاسب)")
        print("  • user1 / 123 (مستخدم)")
        print("  • cashier / 123 (أمين الصندوق)")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. استخدم: admin / 123")
        print("  3. تسجيل الدخول يجب أن يعمل الآن!")
        
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات!")

if __name__ == "__main__":
    main()

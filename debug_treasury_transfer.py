#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة نقل الخزينة الفعلية
"""

import sys
import os
sys.path.append('src')

def check_actual_balances():
    """فحص الأرصدة الفعلية في قاعدة البيانات"""
    
    print("🔍 فحص الأرصدة الفعلية في قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        print("\n📊 فحص جدول unified_treasury مباشرة:")
        records = db.fetch_all("""
            SELECT user_id, session_date, currency_type, daily_balance, main_balance, 
                   is_session_active, last_updated
            FROM unified_treasury 
            WHERE currency_type = 'SYP'
            ORDER BY user_id, session_date DESC, last_updated DESC
        """)
        
        print(f"إجمالي السجلات: {len(records)}")
        
        total_daily = 0
        total_main = 0
        
        for record in records:
            status = "نشط" if record['is_session_active'] else "مغلق"
            print(f"  • مستخدم {record['user_id']} - {record['session_date']}: يومي {record['daily_balance']:,} ل.س - رئيسي {record['main_balance']:,} ل.س - {status}")
            
            if record['is_session_active']:
                total_daily += record['daily_balance']
            total_main = max(total_main, record['main_balance'])
        
        print(f"\n💰 الإجماليات المحسوبة يدوياً:")
        print(f"  • إجمالي الخزينة اليومية: {total_daily:,} ل.س")
        print(f"  • أعلى رصيد رئيسي: {total_main:,} ل.س")
        
        return total_daily, total_main, records
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأرصدة: {e}")
        return 0, 0, []

def test_transfer_step_by_step():
    """اختبار النقل خطوة بخطوة"""
    
    print("\n🔍 اختبار النقل خطوة بخطوة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # 1. فحص الأرصدة قبل النقل
        print("\n1️⃣ الأرصدة قبل النقل:")
        daily_before, main_before, records_before = check_actual_balances()
        
        if daily_before <= 0:
            print("❌ لا يوجد رصيد يومي للنقل")
            return False
        
        # 2. تنفيذ نقل صغير
        transfer_amount = min(10000, daily_before)
        print(f"\n2️⃣ تنفيذ نقل {transfer_amount:,} ل.س...")
        
        # فحص المستخدمين الذين لديهم رصيد
        users_with_balance = db.fetch_all("""
            SELECT user_id, daily_balance FROM unified_treasury
            WHERE currency_type = 'SYP' AND is_session_active = 1 AND daily_balance > 0
            ORDER BY daily_balance DESC
        """)
        
        print(f"المستخدمون الذين لديهم رصيد: {len(users_with_balance)}")
        for user in users_with_balance:
            print(f"  • مستخدم {user['user_id']}: {user['daily_balance']:,} ل.س")
        
        if not users_with_balance:
            print("❌ لا يوجد مستخدمون لديهم رصيد يومي")
            return False
        
        # 3. تنفيذ النقل يدوياً
        print(f"\n3️⃣ تنفيذ النقل يدوياً...")
        first_user = users_with_balance[0]
        user_id = first_user['user_id']
        user_balance = first_user['daily_balance']
        
        actual_transfer = min(transfer_amount, user_balance)
        print(f"نقل {actual_transfer:,} ل.س من المستخدم {user_id}")
        
        # تنفيذ الاستعلام مباشرة
        db.execute_query("""
            UPDATE unified_treasury
            SET daily_balance = daily_balance - ?,
                main_balance = main_balance + ?,
                last_updated = CURRENT_TIMESTAMP
            WHERE user_id = ? AND currency_type = 'SYP' AND is_session_active = 1
        """, (actual_transfer, actual_transfer, user_id))
        
        print("✅ تم تنفيذ الاستعلام")
        
        # 4. فحص النتائج
        print(f"\n4️⃣ فحص النتائج بعد النقل:")
        daily_after, main_after, records_after = check_actual_balances()
        
        # 5. مقارنة النتائج
        print(f"\n5️⃣ مقارنة النتائج:")
        daily_change = daily_before - daily_after
        main_change = main_after - main_before
        
        print(f"  • تغيير الخزينة اليومية: {daily_change:,} ل.س")
        print(f"  • تغيير الخزينة الرئيسية: {main_change:,} ل.س")
        print(f"  • المبلغ المنقول: {actual_transfer:,} ل.س")
        
        if abs(daily_change - actual_transfer) < 1 and abs(main_change - actual_transfer) < 1:
            print("✅ النقل تم بنجاح!")
            return True
        else:
            print("❌ النقل لم يتم بشكل صحيح")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النقل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_manager_functions():
    """اختبار دوال مدير الخزينة"""
    
    print("\n🔍 اختبار دوال مدير الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # اختبار get_total_daily_balance
        print("\n📊 اختبار get_total_daily_balance:")
        total_daily = treasury_manager.get_total_daily_balance('SYP')
        print(f"النتيجة: {total_daily:,} ل.س")
        
        # اختبار get_main_balance
        print("\n📊 اختبار get_main_balance:")
        main_balance = treasury_manager.get_main_balance('SYP')
        print(f"النتيجة: {main_balance:,} ل.س")
        
        # مقارنة مع الحساب اليدوي
        manual_daily, manual_main, _ = check_actual_balances()
        
        print(f"\n📊 مقارنة النتائج:")
        print(f"  • get_total_daily_balance: {total_daily:,} ل.س")
        print(f"  • الحساب اليدوي للخزينة اليومية: {manual_daily:,} ل.س")
        print(f"  • get_main_balance: {main_balance:,} ل.س")
        print(f"  • الحساب اليدوي للخزينة الرئيسية: {manual_main:,} ل.س")
        
        daily_match = abs(total_daily - manual_daily) < 1
        main_match = abs(main_balance - manual_main) < 1
        
        print(f"  • تطابق الخزينة اليومية: {'✅' if daily_match else '❌'}")
        print(f"  • تطابق الخزينة الرئيسية: {'✅' if main_match else '❌'}")
        
        return daily_match and main_match
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال مدير الخزينة: {e}")
        return False

def test_interface_transfer():
    """اختبار النقل من الواجهة"""
    
    print("\n🔍 اختبار النقل من الواجهة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة قبل النقل
        daily_before = treasury_manager.get_total_daily_balance('SYP')
        main_before = treasury_manager.get_main_balance('SYP')
        
        print(f"الأرصدة قبل النقل:")
        print(f"  • يومي: {daily_before:,} ل.س")
        print(f"  • رئيسي: {main_before:,} ل.س")
        
        if daily_before <= 0:
            print("❌ لا يوجد رصيد يومي للنقل")
            return False
        
        # تنفيذ النقل باستخدام الدالة الجديدة
        transfer_amount = min(5000, daily_before)
        print(f"\nتنفيذ نقل {transfer_amount:,} ل.س باستخدام transfer_from_total_daily_to_main...")
        
        success = treasury_manager.transfer_from_total_daily_to_main(
            currency_type='SYP',
            amount=transfer_amount
        )
        
        if success:
            # فحص الأرصدة بعد النقل
            daily_after = treasury_manager.get_total_daily_balance('SYP')
            main_after = treasury_manager.get_main_balance('SYP')
            
            print(f"\nالأرصدة بعد النقل:")
            print(f"  • يومي: {daily_after:,} ل.س")
            print(f"  • رئيسي: {main_after:,} ل.س")
            
            daily_change = daily_before - daily_after
            main_change = main_after - main_before
            
            print(f"\nالتغييرات:")
            print(f"  • خصم من اليومي: {daily_change:,} ل.س")
            print(f"  • إضافة للرئيسي: {main_change:,} ل.س")
            
            if abs(daily_change - transfer_amount) < 1 and abs(main_change - transfer_amount) < 1:
                print("✅ النقل من الواجهة يعمل بشكل صحيح")
                return True
            else:
                print("❌ النقل من الواجهة لا يعمل بشكل صحيح")
                return False
        else:
            print("❌ فشل النقل من الواجهة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النقل من الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص مشكلة نقل الخزينة الفعلية")
    print("=" * 60)
    
    # 1. فحص الأرصدة الحالية
    print("1️⃣ فحص الأرصدة الحالية:")
    daily, main, records = check_actual_balances()
    
    # 2. اختبار دوال مدير الخزينة
    print("\n2️⃣ اختبار دوال مدير الخزينة:")
    functions_work = test_treasury_manager_functions()
    
    # 3. اختبار النقل خطوة بخطوة
    print("\n3️⃣ اختبار النقل خطوة بخطوة:")
    step_by_step_works = test_transfer_step_by_step()
    
    # 4. اختبار النقل من الواجهة
    print("\n4️⃣ اختبار النقل من الواجهة:")
    interface_works = test_interface_transfer()
    
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص:")
    print(f"  • دوال مدير الخزينة: {'✅ تعمل' if functions_work else '❌ لا تعمل'}")
    print(f"  • النقل خطوة بخطوة: {'✅ يعمل' if step_by_step_works else '❌ لا يعمل'}")
    print(f"  • النقل من الواجهة: {'✅ يعمل' if interface_works else '❌ لا يعمل'}")
    
    if all([functions_work, step_by_step_works, interface_works]):
        print("\n✅ جميع الاختبارات تعمل - المشكلة قد تكون في مكان آخر")
    else:
        print("\n❌ هناك مشاكل في النظام:")
        if not functions_work:
            print("  • دوال مدير الخزينة لا تحسب بشكل صحيح")
        if not step_by_step_works:
            print("  • النقل المباشر في قاعدة البيانات لا يعمل")
        if not interface_works:
            print("  • دالة النقل من الواجهة لا تعمل")

if __name__ == "__main__":
    main()

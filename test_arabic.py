#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النصوص العربية
Arabic Text Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_arabic_display():
    """اختبار عرض النصوص العربية"""
    print("🔤 اختبار النصوص العربية...")
    
    # نصوص للاختبار
    test_texts = [
        "مرحباً بك في نظام إدارة شركة الإنترنت",
        "اسم المستخدم",
        "كلمة المرور", 
        "تسجيل الدخول",
        "اشتراك جديد",
        "تسليم راوتر",
        "تجديد باقة",
        "إغلاق الصندوق",
        "150,000 ل.س",
        "admin",
        "نظام إدارة شركة الإنترنت - المدير العام"
    ]
    
    print("\n📝 النصوص الأصلية:")
    for i, text in enumerate(test_texts, 1):
        print(f"{i:2d}. {text}")
    
    # اختبار مع المعالجة
    try:
        from src.utils.arabic_support import reshape_arabic_text, get_display_text, simple_arabic_text
        
        print("\n🔄 مع reshape_arabic_text:")
        for i, text in enumerate(test_texts, 1):
            processed = reshape_arabic_text(text)
            print(f"{i:2d}. {processed}")
            
        print("\n✨ مع get_display_text:")
        for i, text in enumerate(test_texts, 1):
            processed = get_display_text(text)
            print(f"{i:2d}. {processed}")
            
        print("\n🎯 مع simple_arabic_text:")
        for i, text in enumerate(test_texts, 1):
            processed = simple_arabic_text(text)
            print(f"{i:2d}. {processed}")
            
    except Exception as e:
        print(f"❌ خطأ في المعالجة: {e}")
        
def test_pyqt_arabic():
    """اختبار PyQt5 مع النصوص العربية"""
    print("\n🖥️ اختبار PyQt5 مع النصوص العربية...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # إعداد الخط العربي
        font = QFont("Tahoma", 12)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة اختبار
        window = QWidget()
        window.setWindowTitle("اختبار النصوص العربية")
        window.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout()
        
        # إضافة نصوص للاختبار
        test_texts = [
            "مرحباً بك في نظام إدارة شركة الإنترنت",
            "اسم المستخدم: admin",
            "كلمة المرور: ********",
            "تسجيل الدخول",
            "اشتراك جديد - تسليم راوتر - تجديد باقة",
            "المبلغ: 150,000 ل.س"
        ]
        
        for text in test_texts:
            label = QLabel(text)
            label.setFont(font)
            label.setAlignment(Qt.AlignRight)
            layout.addWidget(label)
            
        window.setLayout(layout)
        window.show()
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("🔍 تحقق من النافذة لرؤية النصوص العربية")
        print("⏹️ أغلق النافذة للمتابعة...")
        
        # تشغيل لمدة قصيرة
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في PyQt5: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل للنصوص العربية")
    print("=" * 60)
    
    # اختبار النصوص
    test_arabic_display()
    
    # اختبار PyQt5
    test_pyqt_arabic()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def check_tables():
    print("فحص الجداول...")
    
    db = DatabaseManager('data/company_system.db')
    
    try:
        # عرض الجداول
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        print(f"الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  • {table[0]}")
        
        # فحص جدول currency_exchange
        if any(table[0] == 'currency_exchange' for table in tables):
            print(f"\nبنية جدول currency_exchange:")
            columns = db.fetch_all("PRAGMA table_info(currency_exchange)")
            for col in columns:
                print(f"  • {col[1]} - {col[2]}")
            
            # عرض البيانات
            data = db.fetch_all("SELECT * FROM currency_exchange LIMIT 3")
            print(f"\nعينة من البيانات ({len(data)}):")
            for row in data:
                print(f"  • {row}")
        else:
            print("\nجدول currency_exchange غير موجود")
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_tables()

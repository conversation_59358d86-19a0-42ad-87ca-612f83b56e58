#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لواجهة تسجيل الدخول
"""

import sys
import os
import hashlib
sys.path.append('src')

def test_login_window_directly():
    """اختبار واجهة تسجيل الدخول مباشرة"""
    
    print("🧪 اختبار واجهة تسجيل الدخول مباشرة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # محاكاة عملية تسجيل الدخول كما تحدث في الواجهة
        username = 'admin'
        password = '123'
        
        print(f"📝 بيانات تسجيل الدخول:")
        print(f"  • اسم المستخدم: '{username}'")
        print(f"  • كلمة المرور: '{password}'")
        print(f"  • طول اسم المستخدم: {len(username)}")
        print(f"  • طول كلمة المرور: {len(password)}")
        
        # تشفير كلمة المرور (نفس الطريقة في login_window.py)
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"  • كلمة المرور المشفرة: {hashed_password}")
        
        # فحص تفصيلي - البحث عن المستخدم بالاسم فقط أولاً (نفس الكود في login_window.py)
        print(f"\n🔍 البحث عن المستخدم بالاسم فقط...")
        user_check = db.fetch_one(
            "SELECT * FROM users WHERE username = ?",
            (username,)
        )
        
        if user_check:
            print(f"✅ المستخدم موجود: {user_check['username']}")
            if user_check['password_hash']:
                print(f"كلمة المرور في قاعدة البيانات: {user_check['password_hash'][:20]}...")
            else:
                print("⚠️ كلمة المرور غير موجودة في قاعدة البيانات")
            print(f"المستخدم نشط: {user_check['is_active']}")

            if user_check['password_hash'] and user_check['password_hash'] == hashed_password:
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور خاطئة")
                if user_check['password_hash']:
                    print(f"المتوقع: {user_check['password_hash']}")
                else:
                    print("المتوقع: None (كلمة المرور غير موجودة)")
                print(f"المدخل: {hashed_password}")
        else:
            print("❌ المستخدم غير موجود")

        # البحث عن المستخدم في قاعدة البيانات (نفس الكود في login_window.py)
        print(f"\n🔍 البحث الكامل...")
        user = db.fetch_one(
            "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
            (username, hashed_password)
        )

        if user:
            print("✅ تسجيل الدخول نجح!")
            print(f"مرحباً {user['full_name']} ({user['role']})")
            return True
        else:
            print("❌ تسجيل الدخول فشل!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار اتصال قاعدة البيانات"""
    
    print("\n🔗 اختبار اتصال قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # اختبار استعلام بسيط
        result = db.fetch_one("SELECT COUNT(*) as count FROM users")
        
        if result:
            print(f"✅ الاتصال بقاعدة البيانات نجح")
            print(f"📊 عدد المستخدمين: {result['count']}")
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اتصال قاعدة البيانات: {e}")
        return False

def test_actual_login_window():
    """اختبار واجهة تسجيل الدخول الفعلية"""
    
    print("\n🖥️ اختبار واجهة تسجيل الدخول الفعلية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        from config.config_manager import ConfigManager
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # إنشاء مدير الإعدادات
        config = ConfigManager()
        
        # إنشاء واجهة تسجيل الدخول
        print("🔄 إنشاء واجهة تسجيل الدخول...")
        login_window = LoginWindow(db, config)
        
        # محاكاة إدخال البيانات
        login_window.username_edit.setText('admin')
        login_window.password_edit.setText('123')
        
        print("📝 تم إدخال البيانات في الواجهة")
        print(f"  • اسم المستخدم: '{login_window.username_edit.text()}'")
        print(f"  • كلمة المرور: '{login_window.password_edit.text()}'")
        
        # محاكاة الضغط على زر تسجيل الدخول
        print("🔄 محاكاة عملية تسجيل الدخول...")
        
        # استدعاء دالة تسجيل الدخول مباشرة
        login_window.handle_login()
        
        # فحص النتيجة
        if hasattr(login_window, 'current_user') and login_window.current_user:
            print("✅ تسجيل الدخول نجح في الواجهة!")
            print(f"المستخدم الحالي: {login_window.current_user}")
            return True
        else:
            print("❌ تسجيل الدخول فشل في الواجهة!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_database_manager():
    """تشخيص مدير قاعدة البيانات"""
    
    print("\n🔧 تشخيص مدير قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        print(f"📁 مسار قاعدة البيانات: {db.db_path if hasattr(db, 'db_path') else 'غير محدد'}")
        
        # اختبار fetch_one
        print("🔍 اختبار fetch_one...")
        
        # اختبار 1: استعلام بسيط
        result1 = db.fetch_one("SELECT 1 as test")
        print(f"  • اختبار بسيط: {'✅ نجح' if result1 and result1['test'] == 1 else '❌ فشل'}")
        
        # اختبار 2: عدد المستخدمين
        result2 = db.fetch_one("SELECT COUNT(*) as count FROM users")
        print(f"  • عدد المستخدمين: {result2['count'] if result2 else 'فشل'}")
        
        # اختبار 3: البحث عن admin
        result3 = db.fetch_one("SELECT * FROM users WHERE username = ?", ('admin',))
        print(f"  • البحث عن admin: {'✅ موجود' if result3 else '❌ غير موجود'}")
        
        if result3:
            print(f"    - ID: {result3['id']}")
            print(f"    - اسم المستخدم: {result3['username']}")
            print(f"    - كلمة المرور: {result3['password_hash'][:20]}...")
            print(f"    - نشط: {result3['is_active']}")
        
        return result3 is not None
        
    except Exception as e:
        print(f"❌ خطأ في تشخيص مدير قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار مباشر لواجهة تسجيل الدخول")
    print("=" * 60)
    
    # اختبار اتصال قاعدة البيانات
    db_test = test_database_connection()
    
    # تشخيص مدير قاعدة البيانات
    db_manager_test = debug_database_manager()
    
    # اختبار تسجيل الدخول مباشرة
    direct_test = test_login_window_directly()
    
    # اختبار واجهة تسجيل الدخول الفعلية
    window_test = test_actual_login_window()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"  • اتصال قاعدة البيانات: {'✅ نجح' if db_test else '❌ فشل'}")
    print(f"  • تشخيص مدير قاعدة البيانات: {'✅ نجح' if db_manager_test else '❌ فشل'}")
    print(f"  • اختبار تسجيل الدخول المباشر: {'✅ نجح' if direct_test else '❌ فشل'}")
    print(f"  • اختبار واجهة تسجيل الدخول: {'✅ نجح' if window_test else '❌ فشل'}")
    
    if all([db_test, db_manager_test, direct_test]):
        if window_test:
            print("\n🎉 جميع الاختبارات نجحت! واجهة تسجيل الدخول تعمل بشكل صحيح")
        else:
            print("\n⚠️ الاختبارات الأساسية نجحت لكن هناك مشكلة في واجهة تسجيل الدخول")
        
        print("\n📋 للاستخدام:")
        print("  • اسم المستخدم: admin")
        print("  • كلمة المرور: 123")
        print("  • يجب أن يعمل تسجيل الدخول الآن!")
        
    else:
        print("\n❌ هناك مشاكل أساسية!")
        
        if not db_test:
            print("  🔧 مراجعة اتصال قاعدة البيانات")
        if not db_manager_test:
            print("  🔧 مراجعة مدير قاعدة البيانات")
        if not direct_test:
            print("  🔧 مراجعة عملية تسجيل الدخول")

if __name__ == "__main__":
    main()

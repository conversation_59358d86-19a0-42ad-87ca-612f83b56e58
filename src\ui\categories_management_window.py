#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة التصنيفات
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QMessageBox, QHeaderView,
                            QTextEdit, QFrame, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class CategoriesManagementWindow(QDialog):
    """واجهة إدارة التصنيفات"""
    
    category_updated = pyqtSignal()
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        self.selected_category_id = None
        
        self.setWindowTitle(reshape_arabic_text("إدارة التصنيفات"))
        self.setModal(True)
        self.resize(900, 600)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.create_categories_table()
        self.load_categories()
        self.setup_connections()
        
        print("✅ تم إنشاء واجهة إدارة التصنيفات")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("📂 إدارة تصنيفات المنتجات"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # تقسيم الواجهة
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # الجانب الأيسر - نموذج إضافة/تعديل
        form_frame = self.create_form_section()
        splitter.addWidget(form_frame)
        
        # الجانب الأيمن - جدول التصنيفات
        table_frame = self.create_table_section()
        splitter.addWidget(table_frame)
        
        # تحديد نسب التقسيم
        splitter.setSizes([300, 600])
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_form_section(self):
        """إنشاء قسم النموذج"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(15)
        
        # عنوان القسم
        section_title = QLabel(reshape_arabic_text("➕ إضافة/تعديل تصنيف"))
        section_title.setFont(create_arabic_font(13, bold=True))
        section_title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(section_title)
        
        # نموذج البيانات
        form_layout = QGridLayout()
        form_layout.setSpacing(10)
        
        # اسم التصنيف
        form_layout.addWidget(QLabel(reshape_arabic_text("اسم التصنيف:")), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setFont(create_arabic_font(11))
        self.name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        form_layout.addWidget(self.name_edit, 0, 1)
        
        # الوصف
        form_layout.addWidget(QLabel(reshape_arabic_text("الوصف:")), 1, 0)
        self.description_edit = QTextEdit()
        self.description_edit.setFont(create_arabic_font(10))
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #007bff;
            }
        """)
        form_layout.addWidget(self.description_edit, 1, 1)
        
        layout.addLayout(form_layout)
        
        # أزرار النموذج
        form_buttons = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton(reshape_arabic_text("💾 حفظ"))
        self.save_btn.setFont(create_arabic_font(11, bold=True))
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        form_buttons.addWidget(self.save_btn)
        
        # زر التعديل
        self.edit_btn = QPushButton(reshape_arabic_text("✏️ تعديل"))
        self.edit_btn.setFont(create_arabic_font(11))
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.edit_btn.setEnabled(False)
        form_buttons.addWidget(self.edit_btn)
        
        # زر الحذف
        self.delete_btn = QPushButton(reshape_arabic_text("🗑️ حذف"))
        self.delete_btn.setFont(create_arabic_font(11))
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.delete_btn.setEnabled(False)
        form_buttons.addWidget(self.delete_btn)
        
        # زر مسح النموذج
        clear_btn = QPushButton(reshape_arabic_text("🔄 مسح"))
        clear_btn.setFont(create_arabic_font(11))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        form_buttons.addWidget(clear_btn)
        
        layout.addLayout(form_buttons)
        layout.addStretch()
        
        return frame

    def create_table_section(self):
        """إنشاء قسم الجدول"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(10)
        
        # عنوان القسم
        section_title = QLabel(reshape_arabic_text("📋 قائمة التصنيفات"))
        section_title.setFont(create_arabic_font(13, bold=True))
        section_title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(section_title)
        
        # جدول التصنيفات
        self.categories_table = QTableWidget()
        self.categories_table.setFont(create_arabic_font(10))
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.categories_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.categories_table)
        
        return frame

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر تحديث
        refresh_btn = QPushButton(reshape_arabic_text("🔄 تحديث"))
        refresh_btn.setFont(create_arabic_font(11))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_categories)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton(reshape_arabic_text("❌ إغلاق"))
        close_btn.setFont(create_arabic_font(11))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        return layout

    def create_categories_table(self):
        """إنشاء جدول التصنيفات في قاعدة البيانات"""
        try:
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            """)

            # إضافة التصنيفات الأساسية إذا لم تكن موجودة
            basic_categories = [
                ('راوتر', 'أجهزة راوتر وأجهزة الشبكة'),
                ('كبل', 'كبلات الشبكة والكهرباء'),
                ('إكسسوارات', 'إكسسوارات وقطع غيار'),
                ('أدوات', 'أدوات التركيب والصيانة'),
                ('مواد استهلاكية', 'مواد قابلة للاستهلاك')
            ]

            for name, description in basic_categories:
                try:
                    self.db_manager.execute_query("""
                        INSERT OR IGNORE INTO categories (name, description, created_by)
                        VALUES (?, ?, ?)
                    """, (name, description, self.current_user['username']))
                except:
                    pass

            print("✅ تم إنشاء جدول التصنيفات")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول التصنيفات: {e}")

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        self.save_btn.clicked.connect(self.save_category)
        self.edit_btn.clicked.connect(self.edit_category)
        self.delete_btn.clicked.connect(self.delete_category)
        self.categories_table.itemSelectionChanged.connect(self.on_category_selected)

    def load_categories(self):
        """تحميل التصنيفات"""
        try:
            categories = self.db_manager.fetch_all("""
                SELECT c.id, c.name, c.description, c.created_at, c.created_by,
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN products p ON c.name = p.category
                WHERE c.is_active = 1
                GROUP BY c.id, c.name, c.description, c.created_at, c.created_by
                ORDER BY c.name
            """)

            # إعداد الجدول
            self.categories_table.setRowCount(len(categories))
            self.categories_table.setColumnCount(5)

            headers = ["اسم التصنيف", "الوصف", "عدد المنتجات", "تاريخ الإنشاء", "المنشئ"]
            self.categories_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])

            # تعبئة البيانات
            for row, category in enumerate(categories):
                # اسم التصنيف
                name_item = QTableWidgetItem(str(category['name']))
                name_item.setData(Qt.UserRole, category['id'])
                self.categories_table.setItem(row, 0, name_item)

                # الوصف
                description = str(category['description']) if category['description'] else ''
                self.categories_table.setItem(row, 1, QTableWidgetItem(description))

                # عدد المنتجات
                products_count = str(category['products_count'])
                count_item = QTableWidgetItem(products_count)
                count_item.setTextAlignment(Qt.AlignCenter)
                self.categories_table.setItem(row, 2, count_item)

                # تاريخ الإنشاء
                created_at = str(category['created_at'])[:10] if category['created_at'] else ''
                self.categories_table.setItem(row, 3, QTableWidgetItem(created_at))

                # المنشئ
                created_by = str(category['created_by']) if category['created_by'] else ''
                self.categories_table.setItem(row, 4, QTableWidgetItem(created_by))

            # تنسيق الجدول
            header = self.categories_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم التصنيف
            header.setSectionResizeMode(1, QHeaderView.Stretch)           # الوصف
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # عدد المنتجات
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الإنشاء
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المنشئ

            print(f"✅ تم تحميل {len(categories)} تصنيف")

        except Exception as e:
            print(f"❌ خطأ في تحميل التصنيفات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل التصنيفات: {e}"))

    def on_category_selected(self):
        """عند اختيار تصنيف من الجدول"""
        try:
            current_row = self.categories_table.currentRow()
            if current_row >= 0:
                # الحصول على معرف التصنيف
                name_item = self.categories_table.item(current_row, 0)
                if name_item:
                    self.selected_category_id = name_item.data(Qt.UserRole)

                    # تعبئة النموذج
                    self.name_edit.setText(name_item.text())

                    description_item = self.categories_table.item(current_row, 1)
                    description = description_item.text() if description_item else ''
                    self.description_edit.setPlainText(description)

                    # تفعيل أزرار التعديل والحذف
                    self.edit_btn.setEnabled(True)
                    self.delete_btn.setEnabled(True)

                    print(f"تم اختيار التصنيف: {name_item.text()}")
            else:
                self.clear_form()

        except Exception as e:
            print(f"❌ خطأ في اختيار التصنيف: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.name_edit.clear()
        self.description_edit.clear()
        self.selected_category_id = None
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.categories_table.clearSelection()

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text("يرجى إدخال اسم التصنيف"))
            return False
        return True

    def save_category(self):
        """حفظ تصنيف جديد"""
        if not self.validate_form():
            return

        try:
            name = self.name_edit.text().strip()
            description = self.description_edit.toPlainText().strip()

            # التحقق من عدم وجود تصنيف بنفس الاسم
            existing = self.db_manager.fetch_one("""
                SELECT id FROM categories WHERE name = ? AND is_active = 1
            """, (name,))

            if existing:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يوجد تصنيف بهذا الاسم بالفعل"))
                return

            # حفظ التصنيف الجديد
            self.db_manager.execute_query("""
                INSERT INTO categories (name, description, created_by)
                VALUES (?, ?, ?)
            """, (name, description, self.current_user['username']))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم حفظ التصنيف بنجاح"))

            self.clear_form()
            self.load_categories()
            self.category_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في حفظ التصنيف: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في حفظ التصنيف: {e}"))

    def edit_category(self):
        """تعديل التصنيف المختار"""
        if not self.selected_category_id or not self.validate_form():
            return

        try:
            name = self.name_edit.text().strip()
            description = self.description_edit.toPlainText().strip()

            # التحقق من عدم وجود تصنيف آخر بنفس الاسم
            existing = self.db_manager.fetch_one("""
                SELECT id FROM categories
                WHERE name = ? AND id != ? AND is_active = 1
            """, (name, self.selected_category_id))

            if existing:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يوجد تصنيف آخر بهذا الاسم"))
                return

            # تحديث التصنيف
            self.db_manager.execute_query("""
                UPDATE categories
                SET name = ?, description = ?
                WHERE id = ?
            """, (name, description, self.selected_category_id))

            # تحديث تصنيف المنتجات المرتبطة
            old_name = self.categories_table.item(self.categories_table.currentRow(), 0).text()
            if old_name != name:
                self.db_manager.execute_query("""
                    UPDATE products SET category = ? WHERE category = ?
                """, (name, old_name))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم تحديث التصنيف بنجاح"))

            self.clear_form()
            self.load_categories()
            self.category_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في تعديل التصنيف: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تعديل التصنيف: {e}"))

    def delete_category(self):
        """حذف التصنيف المختار"""
        if not self.selected_category_id:
            return

        try:
            # التحقق من وجود منتجات مرتبطة
            category_name = self.name_edit.text().strip()
            products_count = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count FROM products WHERE category = ?
            """, (category_name,))

            if products_count and products_count['count'] > 0:
                reply = QMessageBox.question(
                    self,
                    reshape_arabic_text("تأكيد الحذف"),
                    reshape_arabic_text(f"يوجد {products_count['count']} منتج مرتبط بهذا التصنيف.\nهل تريد المتابعة؟ (سيتم إزالة التصنيف من المنتجات)"),
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

                # إزالة التصنيف من المنتجات
                self.db_manager.execute_query("""
                    UPDATE products SET category = NULL WHERE category = ?
                """, (category_name,))

            # حذف التصنيف (حذف منطقي)
            self.db_manager.execute_query("""
                UPDATE categories SET is_active = 0 WHERE id = ?
            """, (self.selected_category_id,))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم حذف التصنيف بنجاح"))

            self.clear_form()
            self.load_categories()
            self.category_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في حذف التصنيف: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في حذف التصنيف: {e}"))

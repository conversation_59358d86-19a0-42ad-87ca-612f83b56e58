# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول - النسخة المتكاملة
Login Window - Integrated Version

نافذة تسجيل دخول محسنة مع أمان أفضل
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QCheckBox, QProgressBar, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QIcon, QPalette, QFont
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.arabic_support import (apply_arabic_style, create_arabic_font, 
                                 apply_dialog_style, simple_arabic_text)

class LoginThread(QThread):
    """خيط منفصل لعملية تسجيل الدخول"""
    login_result = pyqtSignal(bool, dict)
    
    def __init__(self, db_manager, username, password):
        super().__init__()
        self.db_manager = db_manager
        self.username = username
        self.password = password
    
    def run(self):
        """تنفيذ عملية تسجيل الدخول"""
        try:
            # البحث عن المستخدم
            user = self.db_manager.fetch_one("""
                SELECT id, username, password_hash, full_name, role, is_active
                FROM users 
                WHERE username = ? AND is_active = 1
            """, (self.username,))
            
            if user and self.db_manager.verify_password(self.password, user['password_hash']):
                # تحديث آخر تسجيل دخول
                self.db_manager.execute_query("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                """, (user['id'],))
                
                self.login_result.emit(True, dict(user))
            else:
                self.login_result.emit(False, {})
                
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            self.login_result.emit(False, {})

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, config_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = None
        self.login_attempts = 0
        self.max_attempts = config_manager.get('security.max_login_attempts', 5)
        
        self.setup_ui()
        self.setup_connections()
        
        # تطبيق النمط العربي
        apply_dialog_style(self)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة شركة الإنترنت")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # تعيين أيقونة النافذة
        self.setWindowIcon(QIcon("assets/icon.png"))
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # شعار الشركة
        self.create_logo_section(main_layout)
        
        # عنوان النظام
        self.create_title_section(main_layout)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_layout)
        
        # أزرار التحكم
        self.create_buttons_section(main_layout)
        
        # شريط التقدم
        self.create_progress_section(main_layout)
        
        # معلومات النظام
        self.create_info_section(main_layout)
    
    def create_logo_section(self, layout):
        """إنشاء قسم الشعار"""
        logo_frame = QFrame()
        logo_layout = QHBoxLayout(logo_frame)
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # شعار الشركة (إذا كان متوفراً)
        logo_label = QLabel()
        logo_label.setFixedSize(80, 80)
        logo_label.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 40px;
                background-color: #ecf0f1;
                font-size: 24px;
                font-weight: bold;
                color: #3498db;
            }
        """)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setText("🌐")
        
        logo_layout.addWidget(logo_label)
        layout.addWidget(logo_frame)
    
    def create_title_section(self, layout):
        """إنشاء قسم العنوان"""
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)
        
        # عنوان النظام
        title_label = QLabel("نظام إدارة شركة الإنترنت")
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        
        # العنوان الفرعي
        subtitle_label = QLabel("النسخة المتكاملة 2.0")
        subtitle_label.setFont(create_arabic_font(12))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #7f8c8d;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        layout.addWidget(title_frame)
    
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        apply_arabic_style(username_label, 11, bold=True)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        apply_arabic_style(self.username_input, 11)
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        apply_arabic_style(password_label, 11, bold=True)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        apply_arabic_style(self.password_input, 11)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكر اسم المستخدم")
        apply_arabic_style(self.remember_checkbox, 10)
        
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(self.remember_checkbox)
        
        layout.addWidget(form_frame)
    
    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(create_arabic_font(11, bold=True))
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(create_arabic_font(11))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        layout.addWidget(buttons_frame)
    
    def create_progress_section(self, layout):
        """إنشاء قسم شريط التقدم"""
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
    
    def create_info_section(self, layout):
        """إنشاء قسم المعلومات"""
        info_label = QLabel("للحصول على المساعدة، اتصل بمدير النظام")
        info_label.setFont(create_arabic_font(9))
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; margin-top: 10px;")
        layout.addWidget(info_label)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.login_button.clicked.connect(self.login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.login)
        
        # تحميل اسم المستخدم المحفوظ
        self.load_saved_username()
    
    def load_saved_username(self):
        """تحميل اسم المستخدم المحفوظ"""
        saved_username = self.config_manager.get('login.saved_username', '')
        if saved_username:
            self.username_input.setText(saved_username)
            self.remember_checkbox.setChecked(True)
            self.password_input.setFocus()
        else:
            self.username_input.setFocus()
    
    def save_username_if_needed(self):
        """حفظ اسم المستخدم إذا لزم الأمر"""
        if self.remember_checkbox.isChecked():
            self.config_manager.set('login.saved_username', self.username_input.text())
        else:
            self.config_manager.set('login.saved_username', '')
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من صحة الإدخال
        if not username:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # التحقق من عدد المحاولات
        if self.login_attempts >= self.max_attempts:
            QMessageBox.critical(self, "خطأ", f"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({self.max_attempts})")
            return
        
        # تعطيل الواجهة أثناء المعالجة
        self.set_ui_enabled(False)
        self.show_progress("جاري التحقق من بيانات الدخول...")
        
        # بدء عملية تسجيل الدخول في خيط منفصل
        self.login_thread = LoginThread(self.db_manager, username, password)
        self.login_thread.login_result.connect(self.on_login_result)
        self.login_thread.start()
    
    def on_login_result(self, success, user_data):
        """معالجة نتيجة تسجيل الدخول"""
        self.hide_progress()
        self.set_ui_enabled(True)
        
        if success:
            self.current_user = user_data
            self.save_username_if_needed()
            QMessageBox.information(self, "نجح", f"مرحباً {user_data['full_name']}")
            self.accept()
        else:
            self.login_attempts += 1
            remaining = self.max_attempts - self.login_attempts
            
            if remaining > 0:
                QMessageBox.warning(self, "خطأ", 
                    f"اسم المستخدم أو كلمة المرور غير صحيحة\n"
                    f"المحاولات المتبقية: {remaining}")
            else:
                QMessageBox.critical(self, "خطأ", 
                    "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول")
            
            self.password_input.clear()
            self.password_input.setFocus()
    
    def set_ui_enabled(self, enabled):
        """تفعيل/تعطيل واجهة المستخدم"""
        self.username_input.setEnabled(enabled)
        self.password_input.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
    
    def show_progress(self, message):
        """إظهار شريط التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
    
    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)
    
    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        return self.current_user
    
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

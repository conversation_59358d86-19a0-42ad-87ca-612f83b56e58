# -*- coding: utf-8 -*-
"""
نافذة إدارة الموزعين
Distributors Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    # في حالة عدم وجود الملف، استخدم دوال بديلة
    def create_arabic_font(size, bold=False):
        from PyQt5.QtGui import QFont
        font = QFont("Arial", size)
        font.setBold(bold)
        return font
    def apply_arabic_style(widget, size, bold=False):
        widget.setFont(create_arabic_font(size, bold))
    def format_currency(amount):
        return f"{amount:,.0f} ل.س"

class DistributorsManagementWindow(QDialog):
    """نافذة إدارة الموزعين"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الموزعين")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الموزعين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # جدول الموزعين
        self.create_distributors_table()
        layout.addWidget(self.distributors_table)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
        
    def create_distributors_table(self):
        """إنشاء جدول الموزعين"""
        self.distributors_table = QTableWidget()
        apply_arabic_style(self.distributors_table, 10)
        
        # إعداد الأعمدة
        headers = ["الرقم", "الاسم", "الهاتف", "العنوان", "الرصيد", "تاريخ الإنشاء"]
        self.distributors_table.setColumnCount(len(headers))
        self.distributors_table.setHorizontalHeaderLabels(headers)
        
        # إعداد خصائص الجدول
        self.distributors_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.distributors_table.setAlternatingRowColors(True)
        self.distributors_table.horizontalHeader().setStretchLastSection(True)
        self.distributors_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة موزع")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        edit_button = QPushButton("تعديل موزع")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        delete_button = QPushButton("حذف موزع")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        # ربط الأزرار
        add_button.clicked.connect(self.add_distributor)
        edit_button.clicked.connect(lambda: self.edit_distributor_safe())
        delete_button.clicked.connect(lambda: self.delete_distributor_safe())
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل بيانات الموزعين"""
        try:
            distributors = self.db_manager.fetch_all("""
                SELECT id, name, phone, address, balance, created_at 
                FROM distributors 
                ORDER BY created_at DESC
            """)
            
            self.distributors_table.setRowCount(len(distributors))
            
            for row, distributor in enumerate(distributors):
                # الرقم
                self.distributors_table.setItem(row, 0, QTableWidgetItem(str(distributor['id'])))
                
                # الاسم
                self.distributors_table.setItem(row, 1, QTableWidgetItem(distributor['name'] or ""))
                
                # الهاتف
                self.distributors_table.setItem(row, 2, QTableWidgetItem(distributor['phone'] or ""))
                
                # العنوان
                address = distributor['address'] or ""
                if len(address) > 30:
                    address = address[:30] + "..."
                self.distributors_table.setItem(row, 3, QTableWidgetItem(address))
                
                # الرصيد
                balance = format_currency(distributor['balance'] or 0)
                self.distributors_table.setItem(row, 4, QTableWidgetItem(balance))
                
                # تاريخ الإنشاء
                created_at = distributor['created_at'] or ""
                if len(created_at) > 10:
                    created_at = created_at[:10]  # عرض التاريخ فقط
                self.distributors_table.setItem(row, 5, QTableWidgetItem(created_at))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الموزعين: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص
            # في حالة الخطأ، أظهر جدول فارغ
            self.distributors_table.setRowCount(0)
            
    def add_distributor(self):
        """إضافة موزع جديد"""
        try:
            dialog = AddDistributorDialog(self.db_manager, self.current_user, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # تحديث القائمة
                QMessageBox.information(self, "نجح", "تم إضافة الموزع بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الموزع: {e}")

    def edit_distributor_safe(self):
        """تعديل موزع - نسخة آمنة"""
        try:
            current_row = self.distributors_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للتعديل")
                return

            QMessageBox.information(self, "قريباً", "ميزة تعديل الموزع قيد التطوير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تعديل الموزع: {e}")

    def delete_distributor_safe(self):
        """حذف موزع - نسخة آمنة"""
        try:
            current_row = self.distributors_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للحذف")
                return

            QMessageBox.information(self, "قريباً", "ميزة حذف الموزع قيد التطوير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الموزع: {e}")

    def edit_distributor(self):
        """تعديل موزع"""
        current_row = self.distributors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للتعديل")
            return

        QMessageBox.information(self, "قريباً", "ميزة تعديل الموزع قيد التطوير")

    def delete_distributor(self):
        """حذف موزع"""
        current_row = self.distributors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للحذف")
            return

        QMessageBox.information(self, "قريباً", "ميزة حذف الموزع قيد التطوير")


class AddDistributorDialog(QDialog):
    """نافذة إضافة موزع جديد"""

    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة موزع جديد")
        self.setGeometry(200, 200, 400, 300)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # عنوان النافذة
        title_label = QLabel("إضافة موزع جديد")
        apply_arabic_style(title_label, 14, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج البيانات
        form_layout = QGridLayout()

        # اسم الموزع
        name_label = QLabel("اسم الموزع:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)

        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        apply_arabic_style(phone_label, 10)
        self.phone_edit = QLineEdit()
        apply_arabic_style(self.phone_edit, 10)

        # العنوان
        address_label = QLabel("العنوان:")
        apply_arabic_style(address_label, 10)
        self.address_edit = QLineEdit()
        apply_arabic_style(self.address_edit, 10)

        # الرصيد الابتدائي
        balance_label = QLabel("الرصيد الابتدائي:")
        apply_arabic_style(balance_label, 10)
        self.balance_spin = QDoubleSpinBox()
        apply_arabic_style(self.balance_spin, 10)
        self.balance_spin.setRange(0, 999999999)
        self.balance_spin.setSuffix(" ل.س")

        form_layout.addWidget(name_label, 0, 0)
        form_layout.addWidget(self.name_edit, 0, 1)
        form_layout.addWidget(phone_label, 1, 0)
        form_layout.addWidget(self.phone_edit, 1, 1)
        form_layout.addWidget(address_label, 2, 0)
        form_layout.addWidget(self.address_edit, 2, 1)
        form_layout.addWidget(balance_label, 3, 0)
        form_layout.addWidget(self.balance_spin, 3, 1)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

        # ربط الأزرار
        save_button.clicked.connect(self.save_distributor)
        cancel_button.clicked.connect(self.reject)

    def save_distributor(self):
        """حفظ الموزع الجديد"""
        # التحقق من البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموزع")
            return

        try:
            # حفظ في قاعدة البيانات
            self.db_manager.execute_query("""
                INSERT INTO distributors (name, phone, address, balance)
                VALUES (?, ?, ?, ?)
            """, (
                self.name_edit.text().strip(),
                self.phone_edit.text().strip(),
                self.address_edit.text().strip(),
                self.balance_spin.value()
            ))

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الموزع: {e}")
        


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل نظام إدارة شركة الإنترنت
Internet Company Management System Launcher
"""

import sys
import os
from pathlib import Path

def setup_paths():
    """إعداد المسارات"""
    current_dir = Path(__file__).parent
    src_dir = current_dir / "src"
    
    # إضافة المسارات
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    # تجاهل تحذيرات Qt
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    return current_dir

def test_imports():
    """اختبار الاستيرادات"""
    try:
        # اختبار PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5")
        
        # اختبار الوحدات الأساسية
        import database.database_manager
        print("✅ Database Manager")
        
        import utils.config_manager
        print("✅ Config Manager")
        
        import utils.arabic_support
        print("✅ Arabic Support")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت")
    print("=" * 50)
    
    # إعداد المسارات
    project_dir = setup_paths()
    
    # اختبار الاستيرادات
    if not test_imports():
        print("❌ فشل في تحميل الوحدات المطلوبة")
        return 1
    
    try:
        # استيراد المكتبات
        from PyQt5.QtWidgets import QApplication, QMessageBox, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # استيراد وحدات النظام
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from utils.arabic_support import setup_arabic_support
        
        print("✅ تم تحميل جميع المكتبات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        
        # إنشاء مجلد البيانات
        data_dir = project_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة بسيطة للاختبار
        window = QWidget()
        window.setWindowTitle("نظام إدارة شركة الإنترنت - جاهز للتشغيل")
        window.setGeometry(300, 300, 500, 300)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("🎉 النظام جاهز للتشغيل!")
        title.setFont(QFont("Tahoma", 16))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #27ae60; font-weight: bold; padding: 20px;")
        
        # معلومات
        info = QLabel("""
النظام يعمل بشكل صحيح!

📋 بيانات الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

🔧 الواجهات المتاحة:
• اشتراك جديد
• تسليم راوتر
• تجديد باقة
• إغلاق الصندوق
• إدارة المشتركين
• إدارة المنتجات
• الإعدادات
        """)
        info.setFont(QFont("Tahoma", 10))
        info.setAlignment(Qt.AlignRight)
        info.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px;")
        
        # زر تشغيل النظام الكامل
        launch_button = QPushButton("🚀 تشغيل النظام الكامل")
        launch_button.setFont(QFont("Tahoma", 12))
        launch_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        def launch_full_system():
            """تشغيل النظام الكامل"""
            try:
                window.hide()
                
                # استيراد الواجهات
                import ui.login_window
                import ui.main_window
                
                # إعادة تحميل الوحدات
                import importlib
                importlib.reload(ui.login_window)
                importlib.reload(ui.main_window)
                
                from ui.login_window import LoginWindow
                from ui.main_window import MainWindow
                
                # نافذة تسجيل الدخول
                login_window = LoginWindow(db_manager)
                
                if login_window.exec_() == login_window.Accepted:
                    current_user = login_window.get_current_user()
                    
                    # النافذة الرئيسية
                    main_window = MainWindow(db_manager, config_manager, current_user)
                    main_window.show()
                    
                    print(f"✅ مرحباً {current_user['full_name']}")
                else:
                    window.show()
                    
            except Exception as e:
                print(f"❌ خطأ في تشغيل النظام الكامل: {e}")
                QMessageBox.critical(None, "خطأ", f"خطأ في تشغيل النظام: {str(e)}")
                window.show()
        
        launch_button.clicked.connect(launch_full_system)
        
        layout.addWidget(title)
        layout.addWidget(info)
        layout.addWidget(launch_button)
        
        window.setLayout(layout)
        window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

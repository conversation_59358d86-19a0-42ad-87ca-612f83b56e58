#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الجرد الشاملة
Comprehensive Inventory Audit Window
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QMessageBox, QHeaderView,
                            QTextEdit, QFrame, QGroupBox, QDoubleSpinBox,
                            QComboBox, QDateEdit, QCheckBox, QTabWidget,
                            QSplitter, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class InventoryAuditWindow(QDialog):
    """واجهة الجرد الشاملة"""
    
    audit_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        
        self.setWindowTitle(reshape_arabic_text("الجرد الشامل للمخزون"))
        self.setModal(True)
        self.resize(1200, 800)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_data()

        # تحميل جميع المنتجات في المخزن الرئيسي تلقائياً
        self.load_main_stock()

        print("✅ تم إنشاء واجهة الجرد الشاملة مع تحميل المنتجات تلقائياً")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("📦 الجرد الشامل للمخزون"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # ملخص الجرد
        summary_group = self.create_summary_section()
        layout.addWidget(summary_group)
        
        # التبويبات الرئيسية
        tabs_widget = self.create_tabs_widget()
        layout.addWidget(tabs_widget)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_summary_section(self):
        """إنشاء قسم ملخص الجرد"""
        group = QGroupBox(reshape_arabic_text("📊 ملخص الجرد"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # إجمالي المنتجات
        layout.addWidget(QLabel(reshape_arabic_text("إجمالي المنتجات:")), 0, 0)
        self.total_products_label = QLabel("0")
        self.total_products_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(self.total_products_label, 0, 1)
        
        # في المخزن الرئيسي
        layout.addWidget(QLabel(reshape_arabic_text("في المخزن الرئيسي:")), 0, 2)
        self.main_stock_label = QLabel("0")
        self.main_stock_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 14px;")
        layout.addWidget(self.main_stock_label, 0, 3)
        
        # مع العمال
        layout.addWidget(QLabel(reshape_arabic_text("مع العمال:")), 1, 0)
        self.workers_stock_label = QLabel("0")
        self.workers_stock_label.setStyleSheet("font-weight: bold; color: #3498db; font-size: 14px;")
        layout.addWidget(self.workers_stock_label, 1, 1)
        
        # تم تسليمه للعملاء
        layout.addWidget(QLabel(reshape_arabic_text("تم تسليمه للعملاء:")), 1, 2)
        self.delivered_label = QLabel("0")
        self.delivered_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
        layout.addWidget(self.delivered_label, 1, 3)
        
        # عدد العمال النشطين
        layout.addWidget(QLabel(reshape_arabic_text("عدد العمال النشطين:")), 2, 0)
        self.active_workers_label = QLabel("0")
        self.active_workers_label.setStyleSheet("font-weight: bold; color: #9b59b6; font-size: 14px;")
        layout.addWidget(self.active_workers_label, 2, 1)
        
        # آخر تحديث
        layout.addWidget(QLabel(reshape_arabic_text("آخر تحديث:")), 2, 2)
        self.last_update_label = QLabel(datetime.now().strftime('%Y-%m-%d %H:%M'))
        self.last_update_label.setStyleSheet("font-weight: bold; color: #f39c12; font-size: 14px;")
        layout.addWidget(self.last_update_label, 2, 3)
        
        return group

    def create_tabs_widget(self):
        """إنشاء التبويبات الرئيسية"""
        tabs = QTabWidget()
        tabs.setFont(create_arabic_font(11))
        
        # تبويب المخزن الرئيسي
        main_stock_tab = self.create_main_stock_tab()
        tabs.addTab(main_stock_tab, reshape_arabic_text("🏪 المخزن الرئيسي"))
        
        # تبويب مخزون العمال
        workers_stock_tab = self.create_workers_stock_tab()
        tabs.addTab(workers_stock_tab, reshape_arabic_text("👷 مخزون العمال"))
        
        # تبويب المنتجات المسلمة
        delivered_tab = self.create_delivered_tab()
        tabs.addTab(delivered_tab, reshape_arabic_text("📤 المنتجات المسلمة"))
        
        # تبويب تقرير مفصل
        detailed_report_tab = self.create_detailed_report_tab()
        tabs.addTab(detailed_report_tab, reshape_arabic_text("📋 تقرير مفصل"))
        
        return tabs

    def create_main_stock_tab(self):
        """إنشاء تبويب المخزن الرئيسي"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # فلاتر البحث
        filters_group = QGroupBox(reshape_arabic_text("🔍 فلاتر البحث"))
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر التصنيف
        filters_layout.addWidget(QLabel(reshape_arabic_text("التصنيف:")))
        self.main_category_combo = QComboBox()
        self.main_category_combo.addItem(reshape_arabic_text("جميع التصنيفات"), "")
        filters_layout.addWidget(self.main_category_combo)
        
        # فلتر البحث
        filters_layout.addWidget(QLabel(reshape_arabic_text("البحث:")))
        self.main_search_edit = QLineEdit()
        self.main_search_edit.setPlaceholderText(reshape_arabic_text("ابحث عن منتج..."))
        filters_layout.addWidget(self.main_search_edit)
        
        # زر البحث
        search_btn = QPushButton(reshape_arabic_text("🔍 بحث"))
        search_btn.clicked.connect(self.filter_main_stock)
        filters_layout.addWidget(search_btn)
        
        layout.addWidget(filters_group)
        
        # جدول المخزن الرئيسي
        self.main_stock_table = QTableWidget()
        self.main_stock_table.setFont(create_arabic_font(10))
        self.main_stock_table.setAlternatingRowColors(True)
        self.main_stock_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.main_stock_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # تحديد الأعمدة
        headers = ["ID", "اسم المنتج", "التصنيف", "الوحدة", "الكمية المتوفرة", "الحد الأدنى", "الحالة", "آخر تحديث"]
        self.main_stock_table.setColumnCount(len(headers))
        self.main_stock_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        
        layout.addWidget(self.main_stock_table)
        
        return widget

    def create_workers_stock_tab(self):
        """إنشاء تبويب مخزون العمال"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # فلاتر البحث
        filters_group = QGroupBox(reshape_arabic_text("🔍 فلاتر البحث"))
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر العامل
        filters_layout.addWidget(QLabel(reshape_arabic_text("العامل:")))
        self.worker_combo = QComboBox()
        self.worker_combo.addItem(reshape_arabic_text("جميع العمال"), "")
        filters_layout.addWidget(self.worker_combo)
        
        # فلتر التصنيف
        filters_layout.addWidget(QLabel(reshape_arabic_text("التصنيف:")))
        self.worker_category_combo = QComboBox()
        self.worker_category_combo.addItem(reshape_arabic_text("جميع التصنيفات"), "")
        filters_layout.addWidget(self.worker_category_combo)
        
        # زر البحث
        search_btn = QPushButton(reshape_arabic_text("🔍 بحث"))
        search_btn.clicked.connect(self.filter_workers_stock)
        filters_layout.addWidget(search_btn)
        
        layout.addWidget(filters_group)
        
        # جدول مخزون العمال
        self.workers_stock_table = QTableWidget()
        self.workers_stock_table.setFont(create_arabic_font(10))
        self.workers_stock_table.setAlternatingRowColors(True)
        self.workers_stock_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.workers_stock_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # تحديد الأعمدة
        headers = ["العامل", "اسم المنتج", "التصنيف", "الكمية", "تاريخ التسليم", "الحالة"]
        self.workers_stock_table.setColumnCount(len(headers))
        self.workers_stock_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        
        layout.addWidget(self.workers_stock_table)
        
        return widget

    def create_delivered_tab(self):
        """إنشاء تبويب المنتجات المسلمة"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # فلاتر البحث
        filters_group = QGroupBox(reshape_arabic_text("🔍 فلاتر البحث"))
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر التاريخ من
        filters_layout.addWidget(QLabel(reshape_arabic_text("من تاريخ:")))
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.setCalendarPopup(True)
        filters_layout.addWidget(self.date_from_edit)
        
        # فلتر التاريخ إلى
        filters_layout.addWidget(QLabel(reshape_arabic_text("إلى تاريخ:")))
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.setCalendarPopup(True)
        filters_layout.addWidget(self.date_to_edit)
        
        # فلتر نوع العملية
        filters_layout.addWidget(QLabel(reshape_arabic_text("نوع العملية:")))
        self.operation_type_combo = QComboBox()
        self.operation_type_combo.addItems([
            reshape_arabic_text("جميع العمليات"),
            reshape_arabic_text("تسليم راوتر"),
            reshape_arabic_text("أمر صيانة")
        ])
        filters_layout.addWidget(self.operation_type_combo)
        
        # زر البحث
        search_btn = QPushButton(reshape_arabic_text("🔍 بحث"))
        search_btn.clicked.connect(self.filter_delivered_items)
        filters_layout.addWidget(search_btn)
        
        layout.addWidget(filters_group)
        
        # جدول المنتجات المسلمة
        self.delivered_table = QTableWidget()
        self.delivered_table.setFont(create_arabic_font(10))
        self.delivered_table.setAlternatingRowColors(True)
        self.delivered_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.delivered_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # تحديد الأعمدة
        headers = ["التاريخ", "نوع العملية", "المشترك/العميل", "المنتج", "الكمية", "العامل المسؤول", "الملاحظات"]
        self.delivered_table.setColumnCount(len(headers))
        self.delivered_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        
        layout.addWidget(self.delivered_table)
        
        return widget

    def create_detailed_report_tab(self):
        """إنشاء تبويب التقرير المفصل"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # شجرة التقرير المفصل
        self.report_tree = QTreeWidget()
        self.report_tree.setFont(create_arabic_font(10))
        self.report_tree.setHeaderLabels([
            reshape_arabic_text("المنتج/الموقع"),
            reshape_arabic_text("الكمية"),
            reshape_arabic_text("الحالة"),
            reshape_arabic_text("التفاصيل")
        ])
        self.report_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTreeWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.report_tree)
        
        return widget

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر تحديث
        refresh_btn = QPushButton(reshape_arabic_text("🔄 تحديث"))
        refresh_btn.setFont(create_arabic_font(11))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_data)
        layout.addWidget(refresh_btn)
        
        # زر طباعة التقرير
        print_btn = QPushButton(reshape_arabic_text("🖨️ طباعة التقرير"))
        print_btn.setFont(create_arabic_font(11))
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        layout.addWidget(print_btn)
        
        layout.addStretch()
        
        # زر إغلاق
        close_btn = QPushButton(reshape_arabic_text("❌ إغلاق"))
        close_btn.setFont(create_arabic_font(11))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        return layout

    def load_data(self):
        """تحميل جميع البيانات"""
        try:
            self.load_summary_data()
            self.load_categories()
            self.load_workers()
            self.load_main_stock()
            self.load_workers_stock()
            self.load_delivered_items()
            self.load_detailed_report()

            # تحديث وقت آخر تحديث
            self.last_update_label.setText(datetime.now().strftime('%Y-%m-%d %H:%M'))

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل البيانات: {e}"))

    def load_summary_data(self):
        """تحميل بيانات الملخص"""
        try:
            # إجمالي المنتجات
            total_products = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM products")
            self.total_products_label.setText(str(total_products['count'] if total_products else 0))

            # المخزن الرئيسي
            main_stock = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(stock_quantity), 0) as total
                FROM products
                WHERE stock_quantity > 0
            """)
            self.main_stock_label.setText(str(int(main_stock['total'] if main_stock else 0)))

            # مخزون العمال
            workers_stock = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) as total
                FROM worker_inventory
                WHERE quantity > 0
            """)
            self.workers_stock_label.setText(str(int(workers_stock['total'] if workers_stock else 0)))

            # المنتجات المسلمة (آخر 30 يوم)
            from datetime import datetime, timedelta
            thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            delivered = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count
                FROM router_deliveries
                WHERE delivery_date >= ?
            """, (thirty_days_ago,))
            self.delivered_label.setText(str(delivered['count'] if delivered else 0))

            # عدد العمال النشطين
            active_workers = self.db_manager.fetch_one("""
                SELECT COUNT(DISTINCT worker_id) as count
                FROM worker_inventory
                WHERE quantity > 0
            """)
            self.active_workers_label.setText(str(active_workers['count'] if active_workers else 0))

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الملخص: {e}")

    def load_categories(self):
        """تحميل التصنيفات"""
        try:
            categories = self.db_manager.fetch_all("SELECT DISTINCT category FROM products WHERE category IS NOT NULL")

            # تحديث قوائم التصنيفات
            for combo in [self.main_category_combo, self.worker_category_combo]:
                combo.clear()
                combo.addItem(reshape_arabic_text("جميع التصنيفات"), "")
                for cat in categories:
                    combo.addItem(cat['category'], cat['category'])

        except Exception as e:
            print(f"❌ خطأ في تحميل التصنيفات: {e}")

    def load_workers(self):
        """تحميل العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT DISTINCT w.id, w.name
                FROM workers w
                INNER JOIN worker_inventory wi ON w.id = wi.worker_id
                WHERE wi.quantity > 0
                ORDER BY w.name
            """)

            self.worker_combo.clear()
            self.worker_combo.addItem(reshape_arabic_text("جميع العمال"), "")
            for worker in workers:
                self.worker_combo.addItem(worker['name'], worker['id'])

        except Exception as e:
            print(f"❌ خطأ في تحميل العمال: {e}")

    def load_main_stock(self):
        """تحميل بيانات المخزن الرئيسي"""
        try:
            products = self.db_manager.fetch_all("""
                SELECT p.id, p.name, p.category, p.unit_type, p.stock_quantity,
                       p.min_stock_level, p.updated_at
                FROM products p
                ORDER BY p.category, p.name
            """)

            self.main_stock_table.setRowCount(len(products))

            for row, product in enumerate(products):
                # ID
                id_item = QTableWidgetItem(str(product['id']))
                id_item.setFont(create_arabic_font(10))
                id_item.setTextAlignment(Qt.AlignCenter)
                self.main_stock_table.setItem(row, 0, id_item)

                # اسم المنتج
                name_item = QTableWidgetItem(str(product['name']))
                name_item.setFont(create_arabic_font(10))
                self.main_stock_table.setItem(row, 1, name_item)

                # التصنيف
                category_item = QTableWidgetItem(str(product['category'] or ''))
                category_item.setFont(create_arabic_font(10))
                category_item.setTextAlignment(Qt.AlignCenter)
                self.main_stock_table.setItem(row, 2, category_item)

                # الوحدة
                unit_item = QTableWidgetItem(str(product['unit_type'] or ''))
                unit_item.setFont(create_arabic_font(10))
                unit_item.setTextAlignment(Qt.AlignCenter)
                self.main_stock_table.setItem(row, 3, unit_item)

                # الكمية
                quantity = product['stock_quantity'] or 0
                min_level = product['min_stock_level'] or 0
                quantity_item = QTableWidgetItem(str(int(quantity)))
                quantity_item.setFont(create_arabic_font(10))
                quantity_item.setTextAlignment(Qt.AlignCenter)

                # تلوين حسب المستوى
                if quantity <= 0:
                    quantity_item.setBackground(Qt.red)
                    quantity_item.setForeground(Qt.black)
                elif quantity <= min_level:
                    quantity_item.setBackground(Qt.yellow)
                    quantity_item.setForeground(Qt.black)
                else:
                    quantity_item.setBackground(Qt.green)
                    quantity_item.setForeground(Qt.black)

                self.main_stock_table.setItem(row, 4, quantity_item)

                # الحد الأدنى
                min_item = QTableWidgetItem(str(int(min_level)))
                min_item.setFont(create_arabic_font(10))
                min_item.setTextAlignment(Qt.AlignCenter)
                self.main_stock_table.setItem(row, 5, min_item)

                # الحالة
                if quantity <= 0:
                    status = "نفد المخزون"
                    color = "#e74c3c"
                elif quantity <= min_level:
                    status = "مخزون منخفض"
                    color = "#f39c12"
                else:
                    status = "متوفر"
                    color = "#27ae60"

                status_item = QTableWidgetItem(reshape_arabic_text(status))
                status_item.setFont(create_arabic_font(10))
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setForeground(Qt.white)
                status_item.setStyleSheet(f"background-color: {color}; color: white;")
                self.main_stock_table.setItem(row, 6, status_item)

                # آخر تحديث
                updated_at = product['updated_at'] or ''
                if updated_at:
                    try:
                        updated_date = datetime.strptime(updated_at, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                    except:
                        updated_date = updated_at[:10] if len(updated_at) >= 10 else updated_at
                else:
                    updated_date = '--'

                date_item = QTableWidgetItem(updated_date)
                date_item.setFont(create_arabic_font(10))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.main_stock_table.setItem(row, 7, date_item)

            # تنسيق الجدول
            header = self.main_stock_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
            header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المنتج
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # التصنيف
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوحدة
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الكمية
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحد الأدنى
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # آخر تحديث

        except Exception as e:
            print(f"❌ خطأ في تحميل المخزن الرئيسي: {e}")

    def load_workers_stock(self):
        """تحميل مخزون العمال"""
        try:
            workers_inventory = self.db_manager.fetch_all("""
                SELECT w.name as worker_name, p.name as product_name, p.category,
                       wi.quantity, wi.delivery_date
                FROM worker_inventory wi
                INNER JOIN workers w ON wi.worker_id = w.id
                INNER JOIN products p ON wi.product_id = p.id
                WHERE wi.quantity > 0
                ORDER BY w.name, p.category, p.name
            """)

            self.workers_stock_table.setRowCount(len(workers_inventory))

            for row, item in enumerate(workers_inventory):
                # العامل
                worker_item = QTableWidgetItem(str(item['worker_name']))
                worker_item.setFont(create_arabic_font(10))
                self.workers_stock_table.setItem(row, 0, worker_item)

                # اسم المنتج
                product_item = QTableWidgetItem(str(item['product_name']))
                product_item.setFont(create_arabic_font(10))
                self.workers_stock_table.setItem(row, 1, product_item)

                # التصنيف
                category_item = QTableWidgetItem(str(item['category'] or ''))
                category_item.setFont(create_arabic_font(10))
                category_item.setTextAlignment(Qt.AlignCenter)
                self.workers_stock_table.setItem(row, 2, category_item)

                # الكمية
                quantity_item = QTableWidgetItem(str(int(item['quantity'])))
                quantity_item.setFont(create_arabic_font(10))
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.workers_stock_table.setItem(row, 3, quantity_item)

                # تاريخ التسليم
                delivery_date = item['delivery_date'] or ''
                if delivery_date:
                    try:
                        date_formatted = datetime.strptime(delivery_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                    except:
                        date_formatted = delivery_date[:10] if len(delivery_date) >= 10 else delivery_date
                else:
                    date_formatted = '--'

                date_item = QTableWidgetItem(date_formatted)
                date_item.setFont(create_arabic_font(10))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.workers_stock_table.setItem(row, 4, date_item)

                # الحالة
                status_item = QTableWidgetItem(reshape_arabic_text("مع العامل"))
                status_item.setFont(create_arabic_font(10))
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setBackground(Qt.blue)
                status_item.setForeground(Qt.white)
                self.workers_stock_table.setItem(row, 5, status_item)

            # تنسيق الجدول
            header = self.workers_stock_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.Stretch)           # العامل
            header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المنتج
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # التصنيف
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الكمية
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # تاريخ التسليم
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة

        except Exception as e:
            print(f"❌ خطأ في تحميل مخزون العمال: {e}")

    def filter_main_stock(self):
        """فلترة المخزن الرئيسي"""
        try:
            category = self.main_category_combo.currentData()
            search_text = self.main_search_edit.text().strip()

            query = """
                SELECT p.id, p.name, p.category, p.unit_type, p.stock_quantity,
                       p.min_stock_level, p.updated_at
                FROM products p
                WHERE 1=1
            """
            params = []

            if category:
                query += " AND p.category = ?"
                params.append(category)

            if search_text:
                query += " AND p.name LIKE ?"
                params.append(f"%{search_text}%")

            query += " ORDER BY p.category, p.name"

            products = self.db_manager.fetch_all(query, params)

            # تحديث الجدول بالنتائج المفلترة
            self.update_main_stock_table(products)

        except Exception as e:
            print(f"❌ خطأ في فلترة المخزن الرئيسي: {e}")

    def filter_workers_stock(self):
        """فلترة مخزون العمال"""
        try:
            worker_id = self.worker_combo.currentData()
            category = self.worker_category_combo.currentData()

            query = """
                SELECT w.name as worker_name, p.name as product_name, p.category,
                       wi.quantity, wi.delivery_date
                FROM worker_inventory wi
                INNER JOIN workers w ON wi.worker_id = w.id
                INNER JOIN products p ON wi.product_id = p.id
                WHERE wi.quantity > 0
            """
            params = []

            if worker_id:
                query += " AND wi.worker_id = ?"
                params.append(worker_id)

            if category:
                query += " AND p.category = ?"
                params.append(category)

            query += " ORDER BY w.name, p.category, p.name"

            workers_inventory = self.db_manager.fetch_all(query, params)

            # تحديث الجدول بالنتائج المفلترة
            self.update_workers_stock_table(workers_inventory)

        except Exception as e:
            print(f"❌ خطأ في فلترة مخزون العمال: {e}")

    def filter_delivered_items(self):
        """فلترة المنتجات المسلمة"""
        # سيتم تنفيذها لاحقاً
        pass

    def load_delivered_items(self):
        """تحميل المنتجات المسلمة"""
        # سيتم تنفيذها لاحقاً
        pass

    def load_detailed_report(self):
        """تحميل التقرير المفصل"""
        # سيتم تنفيذها لاحقاً
        pass

    def update_main_stock_table(self, products):
        """تحديث جدول المخزن الرئيسي"""
        # نفس منطق load_main_stock لكن مع البيانات المفلترة
        pass

    def update_workers_stock_table(self, workers_inventory):
        """تحديث جدول مخزون العمال"""
        # نفس منطق load_workers_stock لكن مع البيانات المفلترة
        pass

    def print_report(self):
        """طباعة التقرير"""
        try:
            QMessageBox.information(self, reshape_arabic_text("طباعة"),
                                  reshape_arabic_text("سيتم تنفيذ ميزة الطباعة قريباً"))
        except Exception as e:
            print(f"❌ خطأ في الطباعة: {e}")

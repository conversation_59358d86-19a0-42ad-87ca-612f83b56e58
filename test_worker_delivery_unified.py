#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة تسليم العمال مع النظام الموحد
"""

import sys
import os
sys.path.append('src')

def test_worker_delivery_integration():
    """اختبار تكامل واجهة تسليم العمال مع النظام الموحد"""
    
    print("🧪 اختبار تكامل واجهة تسليم العمال مع النظام الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        print("✅ تم إنشاء مدير المخزون الموحد")
        
        # إعداد البيانات التجريبية
        print("\n=== إعداد البيانات التجريبية ===")
        
        # إضافة منتجات تجريبية
        products_data = [
            ("كبل شبكة Cat6 للعمال", "كبل", "متر", 500, 750, 100, 10),
            ("راوتر TP-Link للعمال", "راوتر", "قطعة", 50000, 75000, 20, 5),
            ("موزع 8 منافذ", "موزع", "قطعة", 15000, 25000, 15, 3)
        ]
        
        product_ids = []
        for name, category, unit_type, purchase_price, sale_price, stock, min_stock in products_data:
            result = db.execute_query("""
                INSERT OR IGNORE INTO unified_products 
                (name, category, unit_type, purchase_price, sale_price, current_stock, min_stock, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (name, category, unit_type, purchase_price, sale_price, stock, min_stock, 1))
            
            if result:
                product_id = db.fetch_one("SELECT id FROM unified_products WHERE name = ?", (name,))['id']
                product_ids.append(product_id)
                print(f"✅ تم إضافة منتج: {name} - ID: {product_id}")
        
        # إضافة عامل تجريبي
        worker_result = db.execute_query("""
            INSERT OR IGNORE INTO workers (name, phone, is_active)
            VALUES (?, ?, ?)
        """, ("عامل التسليم التجريبي", "987654321", 1))
        
        if worker_result:
            worker_id = db.fetch_one("SELECT id FROM workers WHERE name = 'عامل التسليم التجريبي'")['id']
            print(f"✅ تم إضافة عامل: عامل التسليم التجريبي - ID: {worker_id}")
        else:
            worker_id = db.fetch_one("SELECT id FROM workers WHERE name = 'عامل التسليم التجريبي'")['id']
            print(f"ℹ️ العامل موجود مسبقاً - ID: {worker_id}")
        
        if len(product_ids) >= 3:
            cable_id, router_id, switch_id = product_ids[:3]
            
            # اختبار 1: تحميل المنتجات من النظام الموحد
            print("\n=== 1. تحميل المنتجات من النظام الموحد ===")
            
            products = inventory_manager.get_all_products()
            available_products = [p for p in products if p.get('current_stock', 0) > 0]
            
            print(f"📊 عدد المنتجات المتوفرة: {len(available_products)}")
            
            test_products = []
            for product in available_products:
                if product['id'] in product_ids:
                    test_products.append(product)
                    print(f"✅ منتج متوفر: {product['name']} - مخزون: {product['current_stock']}")
            
            if len(test_products) < 3:
                print("❌ لم يتم العثور على المنتجات التجريبية")
                return False
            
            # اختبار 2: محاكاة تسليم مواد للعامل
            print("\n=== 2. محاكاة تسليم مواد للعامل ===")
            
            deliveries = [
                (cable_id, "كبل شبكة Cat6 للعمال", 50, "متر"),
                (router_id, "راوتر TP-Link للعمال", 5, "قطعة"),
                (switch_id, "موزع 8 منافذ", 3, "قطعة")
            ]
            
            delivery_results = []
            
            for product_id, product_name, quantity, unit in deliveries:
                print(f"\n🚚 تسليم {quantity} {unit} من {product_name}...")
                
                # التحقق من المخزون المتوفر
                current_stock = inventory_manager.get_product_stock(product_id)
                print(f"📦 المخزون الحالي: {current_stock}")
                
                if current_stock >= quantity:
                    # تنفيذ التسليم
                    success = inventory_manager.transfer_to_worker(
                        product_id=product_id,
                        worker_id=worker_id,
                        quantity=quantity,
                        operation_type="delivery",
                        notes=f"تسليم تجريبي - {product_name}",
                        user_id=1
                    )
                    
                    if success:
                        new_main_stock = inventory_manager.get_product_stock(product_id)
                        worker_stock = inventory_manager.get_worker_stock(worker_id, product_id)
                        
                        print(f"✅ تم التسليم بنجاح:")
                        print(f"  • المخزون الرئيسي: {current_stock} → {new_main_stock}")
                        print(f"  • مخزون العامل: {worker_stock}")
                        
                        delivery_results.append({
                            'product_id': product_id,
                            'product_name': product_name,
                            'quantity': quantity,
                            'success': True,
                            'worker_stock': worker_stock
                        })
                    else:
                        print(f"❌ فشل في التسليم")
                        delivery_results.append({
                            'product_id': product_id,
                            'product_name': product_name,
                            'quantity': quantity,
                            'success': False
                        })
                else:
                    print(f"❌ المخزون غير كافي: متوفر {current_stock}, مطلوب {quantity}")
                    delivery_results.append({
                        'product_id': product_id,
                        'product_name': product_name,
                        'quantity': quantity,
                        'success': False
                    })
            
            # اختبار 3: التحقق من مخزون العامل
            print("\n=== 3. التحقق من مخزون العامل ===")
            
            worker_inventory = inventory_manager.get_worker_inventory(worker_id)
            print(f"📊 عدد أنواع المنتجات عند العامل: {len(worker_inventory)}")
            
            for item in worker_inventory:
                print(f"  • {item['name']} ({item['category']}): {item['quantity']} {item['unit_type']}")
            
            # اختبار 4: التحقق من حركات المخزون
            print("\n=== 4. التحقق من حركات المخزون ===")
            
            movements = inventory_manager.get_inventory_movements(limit=20)
            delivery_movements = [m for m in movements if m['operation_type'] == 'delivery']
            
            print(f"📊 عدد حركات التسليم: {len(delivery_movements)}")
            
            for movement in delivery_movements[-3:]:  # آخر 3 حركات
                print(f"  • {movement['created_at'][:16]} - {movement['product_name']} - {movement['quantity']} - {movement['to_location']}")
            
            # اختبار 5: التحقق من صحة الحسابات
            print("\n=== 5. التحقق من صحة الحسابات ===")
            
            all_successful = all(result['success'] for result in delivery_results)
            
            if all_successful:
                print("✅ جميع التسليمات نجحت!")
                
                # التحقق من الأرصدة
                total_worker_items = sum(item['quantity'] for item in worker_inventory)
                expected_total = sum(result['quantity'] for result in delivery_results if result['success'])
                
                print(f"📊 إجمالي المواد عند العامل: {total_worker_items}")
                print(f"📊 إجمالي المواد المُسلمة: {expected_total}")
                
                # تنظيف البيانات التجريبية
                print("\n=== تنظيف البيانات التجريبية ===")
                
                # حذف الحركات
                for product_id in product_ids:
                    db.execute_query("DELETE FROM unified_inventory_movements WHERE product_id = ?", (product_id,))
                
                # حذف مخزون العامل
                db.execute_query("DELETE FROM worker_inventory WHERE worker_id = ?", (worker_id,))
                
                # حذف المنتجات
                for product_id in product_ids:
                    db.execute_query("DELETE FROM unified_products WHERE id = ?", (product_id,))
                
                # حذف العامل
                db.execute_query("DELETE FROM workers WHERE id = ?", (worker_id,))
                
                print("🗑️ تم تنظيف البيانات التجريبية")
                
                return True
            else:
                failed_deliveries = [r for r in delivery_results if not r['success']]
                print(f"❌ فشل في {len(failed_deliveries)} تسليم")
                for failed in failed_deliveries:
                    print(f"  • {failed['product_name']}: {failed['quantity']}")
                return False
        else:
            print("❌ فشل في إعداد المنتجات التجريبية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار واجهة تسليم العمال مع النظام الموحد")
    print("=" * 60)
    
    # اختبار التكامل
    integration_test = test_worker_delivery_integration()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • تكامل تسليم العمال: {'✅ نجح' if integration_test else '❌ فشل'}")
    
    if integration_test:
        print("\n🎉 واجهة تسليم العمال تعمل مع النظام الموحد بنجاح!")
        
        print("\n📋 المميزات المحققة:")
        print("  ✅ تحميل المنتجات من النظام الموحد")
        print("  ✅ عرض المخزون المتوفر لكل منتج")
        print("  ✅ نقل المواد من المخزون الرئيسي لمخزون العامل")
        print("  ✅ تسجيل دقيق لحركات المخزون")
        print("  ✅ تتبع مخزون كل عامل منفصل")
        print("  ✅ حسابات صحيحة للأرصدة")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'تسليم للعمال' من القائمة الرئيسية")
        print("  3. اختر عامل من القائمة")
        print("  4. اختر منتج من النظام الموحد")
        print("  5. حدد الكمية المطلوبة")
        print("  6. أضف للقائمة")
        print("  7. احفظ التسليم:")
        print("     • المواد تُخصم من المخزون الرئيسي")
        print("     • المواد تُضاف لمخزون العامل")
        print("     • تسجيل حركات المخزون")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

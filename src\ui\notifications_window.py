# -*- coding: utf-8 -*-
"""
نافذة الإشعارات والتنبيهات
Notifications Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QListWidget,
                            QListWidgetItem, QTabWidget, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDateTime
from PyQt5.QtGui import QFont, QIcon, QColor

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class NotificationsWindow(QDialog):
    """نافذة الإشعارات والتنبيهات"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_notifications()
        self.setup_timer()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الإشعارات والتنبيهات")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("مركز الإشعارات والتنبيهات")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # تبويبات الإشعارات
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)
        
        # تبويب التنبيهات العاجلة
        urgent_tab = self.create_urgent_tab()
        self.tabs.addTab(urgent_tab, "🔴 عاجل")
        
        # تبويب التنبيهات العامة
        general_tab = self.create_general_tab()
        self.tabs.addTab(general_tab, "🔔 عام")
        
        # تبويب إعدادات التنبيهات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_urgent_tab(self):
        """إنشاء تبويب التنبيهات العاجلة"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # قائمة التنبيهات العاجلة
        self.urgent_list = QListWidget()
        apply_arabic_style(self.urgent_list, 9)
        self.urgent_list.setStyleSheet("""
            QListWidget {
                background-color: #fff5f5;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #fadbd8;
                margin: 2px;
                border-radius: 3px;
            }
            QListWidget::item:selected {
                background-color: #e74c3c;
                color: white;
            }
        """)
        
        layout.addWidget(self.urgent_list)
        
        return widget
        
    def create_general_tab(self):
        """إنشاء تبويب التنبيهات العامة"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # قائمة التنبيهات العامة
        self.general_list = QListWidget()
        apply_arabic_style(self.general_list, 9)
        self.general_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                margin: 1px;
                border-radius: 3px;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(self.general_list)
        
        return widget
        
    def create_settings_tab(self):
        """إنشاء تبويب إعدادات التنبيهات"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # مجموعة إعدادات التنبيهات
        settings_group = QGroupBox("إعدادات التنبيهات")
        apply_arabic_style(settings_group, 10, bold=True)
        
        settings_layout = QGridLayout(settings_group)
        settings_layout.setSpacing(10)
        
        # تنبيهات الاشتراكات المنتهية
        self.expired_subscriptions_checkbox = QCheckBox("تنبيه عند انتهاء الاشتراكات")
        apply_arabic_style(self.expired_subscriptions_checkbox, 10)
        self.expired_subscriptions_checkbox.setChecked(True)
        
        # تنبيهات المخزون المنخفض
        self.low_stock_checkbox = QCheckBox("تنبيه عند انخفاض المخزون")
        apply_arabic_style(self.low_stock_checkbox, 10)
        self.low_stock_checkbox.setChecked(True)
        
        # حد المخزون المنخفض
        stock_limit_label = QLabel("حد المخزون المنخفض:")
        apply_arabic_style(stock_limit_label, 10)
        self.stock_limit_spin = QSpinBox()
        apply_arabic_style(self.stock_limit_spin, 10)
        self.stock_limit_spin.setRange(1, 100)
        self.stock_limit_spin.setValue(10)
        
        # تنبيهات المدفوعات المتأخرة
        self.overdue_payments_checkbox = QCheckBox("تنبيه عند تأخر المدفوعات")
        apply_arabic_style(self.overdue_payments_checkbox, 10)
        self.overdue_payments_checkbox.setChecked(True)
        
        # فترة التحديث
        update_interval_label = QLabel("فترة تحديث التنبيهات (دقيقة):")
        apply_arabic_style(update_interval_label, 10)
        self.update_interval_spin = QSpinBox()
        apply_arabic_style(self.update_interval_spin, 10)
        self.update_interval_spin.setRange(1, 60)
        self.update_interval_spin.setValue(5)
        
        settings_layout.addWidget(self.expired_subscriptions_checkbox, 0, 0, 1, 2)
        settings_layout.addWidget(self.low_stock_checkbox, 1, 0, 1, 2)
        settings_layout.addWidget(stock_limit_label, 2, 0)
        settings_layout.addWidget(self.stock_limit_spin, 2, 1)
        settings_layout.addWidget(self.overdue_payments_checkbox, 3, 0, 1, 2)
        settings_layout.addWidget(update_interval_label, 4, 0)
        settings_layout.addWidget(self.update_interval_spin, 4, 1)
        
        # زر حفظ الإعدادات
        save_settings_button = QPushButton("حفظ الإعدادات")
        apply_arabic_style(save_settings_button, 10, bold=True)
        save_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(settings_group)
        layout.addWidget(save_settings_button)
        layout.addStretch()
        
        save_settings_button.clicked.connect(self.save_notification_settings)
        
        return widget
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        mark_read_button = QPushButton("تحديد كمقروء")
        apply_arabic_style(mark_read_button, 10)
        mark_read_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(refresh_button)
        layout.addWidget(mark_read_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        refresh_button.clicked.connect(self.load_notifications)
        mark_read_button.clicked.connect(self.mark_as_read)
        close_button.clicked.connect(self.accept)
        
        return layout

    def load_notifications(self):
        """تحميل الإشعارات"""
        try:
            self.load_urgent_notifications()
            self.load_general_notifications()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإشعارات: {e}")

    def load_urgent_notifications(self):
        """تحميل التنبيهات العاجلة"""
        self.urgent_list.clear()

        try:
            # الاشتراكات المنتهية
            expired_subscriptions = self.db_manager.fetch_all("""
                SELECT name, subscription_end_date
                FROM subscribers
                WHERE subscription_end_date < date('now')
                LIMIT 10
            """)

            for sub in expired_subscriptions:
                item = QListWidgetItem(f"🔴 اشتراك منتهي: {sub['name']} - انتهى في {sub['subscription_end_date']}")
                item.setBackground(QColor("#fadbd8"))
                self.urgent_list.addItem(item)

            # المخزون المنخفض
            low_stock = self.db_manager.fetch_all("""
                SELECT p.name, i.quantity
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                WHERE i.quantity < 10
                LIMIT 5
            """)

            for stock in low_stock:
                item = QListWidgetItem(f"⚠️ مخزون منخفض: {stock['name']} - الكمية: {stock['quantity']}")
                item.setBackground(QColor("#fef9e7"))
                self.urgent_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحميل التنبيهات العاجلة: {e}")

    def load_general_notifications(self):
        """تحميل التنبيهات العامة"""
        self.general_list.clear()

        try:
            # الاشتراكات التي ستنتهي قريباً
            expiring_soon = self.db_manager.fetch_all("""
                SELECT name, subscription_end_date
                FROM subscribers
                WHERE subscription_end_date BETWEEN date('now') AND date('now', '+7 days')
                LIMIT 10
            """)

            for sub in expiring_soon:
                item = QListWidgetItem(f"📅 ينتهي قريباً: {sub['name']} - ينتهي في {sub['subscription_end_date']}")
                self.general_list.addItem(item)

            # المشتركون الجدد اليوم
            new_subscribers = self.db_manager.fetch_all("""
                SELECT name, created_at
                FROM subscribers
                WHERE date(created_at) = date('now')
            """)

            for sub in new_subscribers:
                item = QListWidgetItem(f"🎉 مشترك جديد: {sub['name']}")
                self.general_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحميل التنبيهات العامة: {e}")

    def mark_as_read(self):
        """تحديد الإشعارات كمقروءة"""
        current_tab = self.tabs.currentIndex()

        if current_tab == 0:  # التنبيهات العاجلة
            current_item = self.urgent_list.currentItem()
            if current_item:
                current_item.setBackground(QColor("#d5f4e6"))
        elif current_tab == 1:  # التنبيهات العامة
            current_item = self.general_list.currentItem()
            if current_item:
                current_item.setBackground(QColor("#d5f4e6"))

    def save_notification_settings(self):
        """حفظ إعدادات التنبيهات"""
        try:
            # حفظ الإعدادات في قاعدة البيانات
            settings = [
                ("notifications_expired_subscriptions", str(self.expired_subscriptions_checkbox.isChecked())),
                ("notifications_low_stock", str(self.low_stock_checkbox.isChecked())),
                ("notifications_stock_limit", str(self.stock_limit_spin.value())),
                ("notifications_overdue_payments", str(self.overdue_payments_checkbox.isChecked())),
                ("notifications_update_interval", str(self.update_interval_spin.value())),
            ]

            for key, value in settings:
                self.db_manager.execute_query("""
                    INSERT OR REPLACE INTO settings (key, value, description)
                    VALUES (?, ?, ?)
                """, (key, value, f"إعداد التنبيهات: {key}"))

            QMessageBox.information(self, "تم", "تم حفظ إعدادات التنبيهات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ إعدادات التنبيهات: {e}")
        
    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_notifications)
        self.timer.start(300000)  # 5 دقائق
        
    def load_notifications(self):
        """تحميل الإشعارات"""
        try:
            self.load_urgent_notifications()
            self.load_general_notifications()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإشعارات: {e}")
            
    def load_urgent_notifications(self):
        """تحميل التنبيهات العاجلة"""
        self.urgent_list.clear()
        
        try:
            # الاشتراكات المنتهية
            expired_subscriptions = self.db_manager.fetch_all("""
                SELECT name, subscription_end_date 
                FROM subscribers 
                WHERE subscription_end_date < date('now')
                LIMIT 10
            """)
            
            for sub in expired_subscriptions:
                item = QListWidgetItem(f"🔴 اشتراك منتهي: {sub['name']} - انتهى في {sub['subscription_end_date']}")
                item.setBackground(QColor("#fadbd8"))
                self.urgent_list.addItem(item)
            
            # المخزون المنخفض
            low_stock = self.db_manager.fetch_all("""
                SELECT p.name, i.quantity 
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                WHERE i.quantity < 10
                LIMIT 5
            """)
            
            for stock in low_stock:
                item = QListWidgetItem(f"⚠️ مخزون منخفض: {stock['name']} - الكمية: {stock['quantity']}")
                item.setBackground(QColor("#fef9e7"))
                self.urgent_list.addItem(item)
                
        except Exception as e:
            print(f"خطأ في تحميل التنبيهات العاجلة: {e}")
            
    def load_general_notifications(self):
        """تحميل التنبيهات العامة"""
        self.general_list.clear()
        
        try:
            # الاشتراكات التي ستنتهي قريباً
            expiring_soon = self.db_manager.fetch_all("""
                SELECT name, subscription_end_date 
                FROM subscribers 
                WHERE subscription_end_date BETWEEN date('now') AND date('now', '+7 days')
                LIMIT 10
            """)
            
            for sub in expiring_soon:
                item = QListWidgetItem(f"📅 ينتهي قريباً: {sub['name']} - ينتهي في {sub['subscription_end_date']}")
                self.general_list.addItem(item)
            
            # المشتركون الجدد اليوم
            new_subscribers = self.db_manager.fetch_all("""
                SELECT name, created_at 
                FROM subscribers 
                WHERE date(created_at) = date('now')
            """)
            
            for sub in new_subscribers:
                item = QListWidgetItem(f"🎉 مشترك جديد: {sub['name']}")
                self.general_list.addItem(item)
                
        except Exception as e:
            print(f"خطأ في تحميل التنبيهات العامة: {e}")
            
    def mark_as_read(self):
        """تحديد الإشعارات كمقروءة"""
        current_tab = self.tabs.currentIndex()
        
        if current_tab == 0:  # التنبيهات العاجلة
            current_item = self.urgent_list.currentItem()
            if current_item:
                current_item.setBackground(QColor("#d5f4e6"))
        elif current_tab == 1:  # التنبيهات العامة
            current_item = self.general_list.currentItem()
            if current_item:
                current_item.setBackground(QColor("#d5f4e6"))
                
    def save_notification_settings(self):
        """حفظ إعدادات التنبيهات"""
        try:
            # حفظ الإعدادات في قاعدة البيانات
            settings = [
                ("notifications_expired_subscriptions", str(self.expired_subscriptions_checkbox.isChecked())),
                ("notifications_low_stock", str(self.low_stock_checkbox.isChecked())),
                ("notifications_stock_limit", str(self.stock_limit_spin.value())),
                ("notifications_overdue_payments", str(self.overdue_payments_checkbox.isChecked())),
                ("notifications_update_interval", str(self.update_interval_spin.value())),
            ]
            
            for key, value in settings:
                self.db_manager.execute_query("""
                    INSERT OR REPLACE INTO settings (key, value, description)
                    VALUES (?, ?, ?)
                """, (key, value, f"إعداد التنبيهات: {key}"))
            
            # تحديث المؤقت
            interval_minutes = self.update_interval_spin.value()
            self.timer.stop()
            self.timer.start(interval_minutes * 60000)  # تحويل إلى ميلي ثانية
            
            QMessageBox.information(self, "تم", "تم حفظ إعدادات التنبيهات بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ إعدادات التنبيهات: {e}")

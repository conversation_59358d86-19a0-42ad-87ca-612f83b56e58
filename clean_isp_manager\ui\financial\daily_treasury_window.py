#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الخزينة اليومية المتجاوبة والكاملة
"""

import logging
from datetime import datetime, date
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QGridLayout, QPushButton, QLabel, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QGroupBox, QFrame, QMessageBox, QScrollArea,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QDateEdit, QSplitter, QAbstractItemView,
                             QProgressBar, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager
from utils.window_utils import center_window, apply_modern_style
from config.app_config import AppConfig

class DailyTreasuryWindow(QDialog):
    """واجهة الخزينة اليومية المتجاوبة والكاملة"""
    
    # إشارات مخصصة
    cash_box_opened = pyqtSignal(dict)
    cash_box_closed = pyqtSignal(dict)
    balance_updated = pyqtSignal()
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """تهيئة واجهة الخزينة اليومية"""
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.current_user = current_user
        self.treasury_manager = TreasuryManager(db_manager)
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الحالة
        self.is_session_active = False
        self.current_balances = {}
        self.is_mobile_view = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.setup_responsive_design()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        # بدء المؤقتات
        self.start_timers()
        
        self.logger.info("تم فتح واجهة الخزينة اليومية")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتجاوبة"""
        
        # إعداد النافذة
        self.setWindowTitle("الخزينة اليومية")
        self.setModal(True)
        self.setMinimumSize(900, 700)
        self.resize(1200, 900)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الرئيسي
        main_widget = QFrame()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الأقسام
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        status_section = self.create_status_section()
        main_layout.addWidget(status_section)
        
        balances_section = self.create_balances_section()
        main_layout.addWidget(balances_section)
        
        operations_section = self.create_operations_section()
        main_layout.addWidget(operations_section)
        
        summary_section = self.create_summary_section()
        main_layout.addWidget(summary_section)
        
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        # إعداد التخطيط النهائي
        dialog_layout = QVBoxLayout()
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(scroll_area)
        self.setLayout(dialog_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2ecc71, stop:1 #27ae60);
                color: white;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # أيقونة الخزينة
        icon_label = QLabel("🏦")
        icon_label.setStyleSheet("font-size: 28px; margin-right: 10px;")
        layout.addWidget(icon_label)
        
        # معلومات الهيدر
        info_layout = QVBoxLayout()
        
        title_label = QLabel("الخزينة اليومية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        info_layout.addWidget(title_label)
        
        subtitle_label = QLabel("إدارة الصندوق النقدي اليومي")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #d5f4e6;
                margin: 0;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # معلومات المستخدم والوقت
        user_info_layout = QVBoxLayout()
        
        user_label = QLabel(f"المستخدم: {self.current_user['full_name']}")
        user_label.setStyleSheet("font-size: 12px; color: #d5f4e6;")
        user_info_layout.addWidget(user_label)
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 11px; color: #a9dfbf;")
        user_info_layout.addWidget(self.time_label)
        
        layout.addLayout(user_info_layout)
        
        return frame
    
    def create_status_section(self) -> QGroupBox:
        """إنشاء قسم حالة الجلسة"""
        
        group = QGroupBox("حالة الجلسة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #3498db;
                background-color: white;
            }
        """)
        
        layout = QHBoxLayout(group)
        layout.setSpacing(20)
        
        # حالة الجلسة
        self.session_status_label = QLabel("الجلسة مغلقة")
        self.session_status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #e74c3c;
                background-color: #fadbd8;
                padding: 10px 20px;
                border-radius: 8px;
                border: 2px solid #e74c3c;
            }
        """)
        layout.addWidget(self.session_status_label)
        
        # تاريخ الجلسة
        self.session_date_label = QLabel(f"التاريخ: {date.today().strftime('%Y-%m-%d')}")
        self.session_date_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px 15px;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.session_date_label)
        
        layout.addStretch()
        
        return group
    
    def create_balances_section(self) -> QGroupBox:
        """إنشاء قسم الأرصدة"""
        
        group = QGroupBox("الأرصدة الحالية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #f39c12;
                background-color: white;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رصيد الليرة السورية
        syp_frame = self.create_balance_card("الليرة السورية", "SYP", "#3498db")
        self.syp_balance_label = syp_frame.findChild(QLabel, "balance_label")
        layout.addWidget(syp_frame, 0, 0)
        
        # رصيد الدولار
        usd_frame = self.create_balance_card("الدولار الأمريكي", "USD", "#f39c12")
        self.usd_balance_label = usd_frame.findChild(QLabel, "balance_label")
        layout.addWidget(usd_frame, 0, 1)
        
        # إجمالي المبيعات
        sales_frame = self.create_balance_card("إجمالي المبيعات", "SYP", "#27ae60")
        self.sales_total_label = sales_frame.findChild(QLabel, "balance_label")
        layout.addWidget(sales_frame, 1, 0)
        
        # إجمالي المصروفات
        expenses_frame = self.create_balance_card("إجمالي المصروفات", "SYP", "#e74c3c")
        self.expenses_total_label = expenses_frame.findChild(QLabel, "balance_label")
        layout.addWidget(expenses_frame, 1, 1)
        
        return group
    
    def create_balance_card(self, title: str, currency: str, color: str) -> QFrame:
        """إنشاء بطاقة رصيد"""
        
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                color: white;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 80px;
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # عنوان البطاقة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # قيمة الرصيد
        balance_label = QLabel("0")
        balance_label.setObjectName("balance_label")
        balance_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                margin: 5px 0;
            }
        """)
        layout.addWidget(balance_label)
        
        # رمز العملة
        symbol = "ل.س" if currency == "SYP" else "$"
        symbol_label = QLabel(symbol)
        symbol_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(symbol_label)
        
        return frame
    
    def create_operations_section(self) -> QGroupBox:
        """إنشاء قسم العمليات"""
        
        group = QGroupBox("عمليات الخزينة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # الرصيد الافتتاحي
        self.opening_balance_spin = QDoubleSpinBox()
        self.opening_balance_spin.setRange(0, 999999999)
        self.opening_balance_spin.setDecimals(0)
        self.opening_balance_spin.setSuffix(" ل.س")
        self.opening_balance_spin.setMinimumHeight(40)
        self.opening_balance_spin.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addRow("الرصيد الافتتاحي:", self.opening_balance_spin)
        
        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.addItem("الليرة السورية", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي", "USD")
        self.currency_combo.setMinimumHeight(40)
        layout.addRow("العملة:", self.currency_combo)
        
        # ملاحظات الجلسة
        self.session_notes_edit = QTextEdit()
        self.session_notes_edit.setPlaceholderText("أدخل أي ملاحظات للجلسة...")
        self.session_notes_edit.setMaximumHeight(80)
        layout.addRow("ملاحظات الجلسة:", self.session_notes_edit)
        
        return group

    def create_summary_section(self) -> QGroupBox:
        """إنشاء قسم الملخص"""

        group = QGroupBox("ملخص الجلسة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #34495e;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #34495e;
                background-color: #f8f9fa;
            }
        """)

        layout = QVBoxLayout(group)

        self.summary_label = QLabel("لا توجد جلسة نشطة")
        self.summary_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 13px;
                padding: 15px;
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)
        self.summary_label.setWordWrap(True)
        layout.addWidget(self.summary_label)

        return group

    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار"""

        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)

        # زر فتح الصندوق
        self.open_button = QPushButton("🔓 فتح الصندوق")
        self.open_button.setMinimumHeight(45)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.open_button)

        # زر إغلاق الصندوق
        self.close_button = QPushButton("🔒 إغلاق الصندوق")
        self.close_button.setMinimumHeight(45)
        self.close_button.setEnabled(False)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.close_button)

        # زر تحديث
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setMinimumHeight(45)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_button)

        layout.addStretch()

        # زر الإغلاق
        exit_button = QPushButton("❌ إغلاق")
        exit_button.setMinimumHeight(45)
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        exit_button.clicked.connect(self.close)
        layout.addWidget(exit_button)

        return frame

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""

        # ربط الأزرار
        self.open_button.clicked.connect(self.open_cash_box)
        self.close_button.clicked.connect(self.close_cash_box)

        # ربط تغيير العملة
        self.currency_combo.currentTextChanged.connect(self.on_currency_changed)

        # ربط الإشارات المخصصة
        self.cash_box_opened.connect(self.on_cash_box_opened)
        self.cash_box_closed.connect(self.on_cash_box_closed)
        self.balance_updated.connect(self.refresh_balances)

    def open_cash_box(self):
        """فتح الصندوق النقدي"""
        try:
            opening_balance = self.opening_balance_spin.value()
            currency = self.currency_combo.currentData()

            # التحقق من صحة البيانات
            if opening_balance < 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رصيد افتتاحي صحيح")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد فتح الصندوق",
                f"هل أنت متأكد من فتح الصندوق برصيد افتتاحي {opening_balance:,.0f if currency == 'SYP' else opening_balance:.2f} "
                f"{'ل.س' if currency == 'SYP' else '$'}؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تعطيل الزر أثناء المعالجة
            self.open_button.setEnabled(False)
            self.open_button.setText("جاري الفتح...")

            # فتح الصندوق
            success = self.treasury_manager.open_cash_box(
                user_id=self.current_user['id'],
                opening_balance=opening_balance,
                currency=currency
            )

            if success:
                # إرسال إشارة النجاح
                cash_box_data = {
                    'opening_balance': opening_balance,
                    'currency': currency,
                    'notes': self.session_notes_edit.toPlainText().strip()
                }
                self.cash_box_opened.emit(cash_box_data)

                QMessageBox.information(self, "نجح", "تم فتح الصندوق النقدي بنجاح")

                # تحديث الحالة
                self.check_session_status()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في فتح الصندوق النقدي")

        except Exception as e:
            self.logger.error(f"خطأ في فتح الصندوق: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح الصندوق: {e}")

        finally:
            # إعادة تفعيل الزر
            self.open_button.setEnabled(True)
            self.open_button.setText("🔓 فتح الصندوق")

    def close_cash_box(self):
        """إغلاق الصندوق النقدي"""
        try:
            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد إغلاق الصندوق",
                "هل أنت متأكد من إغلاق الصندوق النقدي؟\n"
                "سيتم حساب الإجماليات وإنهاء الجلسة.",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تعطيل الزر أثناء المعالجة
            self.close_button.setEnabled(False)
            self.close_button.setText("جاري الإغلاق...")

            # إغلاق الصندوق للليرة السورية
            syp_result = self.treasury_manager.close_cash_box(
                user_id=self.current_user['id'],
                currency='SYP'
            )

            # إغلاق الصندوق للدولار
            usd_result = self.treasury_manager.close_cash_box(
                user_id=self.current_user['id'],
                currency='USD'
            )

            if syp_result.get('success') or usd_result.get('success'):
                # إرسال إشارة النجاح
                close_data = {
                    'syp_result': syp_result,
                    'usd_result': usd_result,
                    'notes': self.session_notes_edit.toPlainText().strip()
                }
                self.cash_box_closed.emit(close_data)

                # عرض ملخص الإغلاق
                self.show_closing_summary(syp_result, usd_result)

                # تحديث الحالة
                self.check_session_status()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في إغلاق الصندوق النقدي")

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الصندوق: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في إغلاق الصندوق: {e}")

        finally:
            # إعادة تفعيل الزر
            self.close_button.setEnabled(True)
            self.close_button.setText("🔒 إغلاق الصندوق")

    def show_closing_summary(self, syp_result: dict, usd_result: dict):
        """عرض ملخص الإغلاق"""

        summary_text = "📊 ملخص إغلاق الصندوق النقدي\n\n"

        if syp_result.get('success'):
            summary_text += f"""
💰 الليرة السورية:
   • الرصيد الافتتاحي: {syp_result.get('opening_balance', 0):,.0f} ل.س
   • إجمالي المبيعات: {syp_result.get('sales_total', 0):,.0f} ل.س
   • إجمالي المصروفات: {syp_result.get('expenses_total', 0):,.0f} ل.س
   • الرصيد الختامي: {syp_result.get('closing_balance', 0):,.0f} ل.س
   • صافي الربح: {syp_result.get('net_profit', 0):,.0f} ل.س
            """

        if usd_result.get('success'):
            summary_text += f"""
💵 الدولار الأمريكي:
   • الرصيد الافتتاحي: ${usd_result.get('opening_balance', 0):.2f}
   • إجمالي المبيعات: ${usd_result.get('sales_total', 0):.2f}
   • إجمالي المصروفات: ${usd_result.get('expenses_total', 0):.2f}
   • الرصيد الختامي: ${usd_result.get('closing_balance', 0):.2f}
   • صافي الربح: ${usd_result.get('net_profit', 0):.2f}
            """

        summary_text += f"\n📅 تاريخ الإغلاق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        QMessageBox.information(self, "ملخص الإغلاق", summary_text.strip())

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.check_session_status()
        self.refresh_balances()
        self.update_summary()

    def on_cash_box_opened(self, cash_box_data: dict):
        """معالجة فتح الصندوق"""
        self.logger.info(f"تم فتح الصندوق: {cash_box_data}")
        self.refresh_data()

    def on_cash_box_closed(self, close_data: dict):
        """معالجة إغلاق الصندوق"""
        self.logger.info(f"تم إغلاق الصندوق: {close_data}")
        self.refresh_data()

    def check_session_status(self):
        """التحقق من حالة الجلسة"""
        try:
            # التحقق من وجود جلسة نشطة للليرة السورية
            syp_active = self.treasury_manager.is_session_active(self.current_user['id'], 'SYP')

            # التحقق من وجود جلسة نشطة للدولار
            usd_active = self.treasury_manager.is_session_active(self.current_user['id'], 'USD')

            self.is_session_active = syp_active or usd_active

            if self.is_session_active:
                self.session_status_label.setText("الجلسة نشطة")
                self.session_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #27ae60;
                        background-color: #d5f4e6;
                        padding: 10px 20px;
                        border-radius: 8px;
                        border: 2px solid #27ae60;
                    }
                """)
                self.open_button.setEnabled(False)
                self.close_button.setEnabled(True)
            else:
                self.session_status_label.setText("الجلسة مغلقة")
                self.session_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #e74c3c;
                        background-color: #fadbd8;
                        padding: 10px 20px;
                        border-radius: 8px;
                        border: 2px solid #e74c3c;
                    }
                """)
                self.open_button.setEnabled(True)
                self.close_button.setEnabled(False)

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من حالة الجلسة: {e}")

    def refresh_balances(self):
        """تحديث الأرصدة"""
        try:
            # الحصول على الأرصدة
            syp_balance = self.treasury_manager.get_user_daily_balance(self.current_user['id'], 'SYP')
            usd_balance = self.treasury_manager.get_user_daily_balance(self.current_user['id'], 'USD')

            # الحصول على إجمالي المبيعات والمصروفات
            today = date.today().strftime('%Y-%m-%d')
            sales_total = self.treasury_manager.get_user_sales_total(self.current_user['id'], today, 'SYP')
            expenses_total = self.treasury_manager.get_user_expenses_total(self.current_user['id'], today, 'SYP')

            # تحديث العرض
            self.syp_balance_label.setText(f"{syp_balance:,.0f}")
            self.usd_balance_label.setText(f"{usd_balance:.2f}")
            self.sales_total_label.setText(f"{sales_total:,.0f}")
            self.expenses_total_label.setText(f"{expenses_total:,.0f}")

            # حفظ الأرصدة الحالية
            self.current_balances = {
                'syp': syp_balance,
                'usd': usd_balance,
                'sales': sales_total,
                'expenses': expenses_total
            }

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الأرصدة: {e}")

    def update_summary(self):
        """تحديث ملخص الجلسة"""
        try:
            if self.is_session_active:
                syp_balance = self.current_balances.get('syp', 0)
                usd_balance = self.current_balances.get('usd', 0)
                sales_total = self.current_balances.get('sales', 0)
                expenses_total = self.current_balances.get('expenses', 0)
                net_profit = sales_total - expenses_total

                summary_text = f"""
📊 ملخص الجلسة النشطة:

💰 الأرصدة الحالية:
   • الليرة السورية: {syp_balance:,.0f} ل.س
   • الدولار الأمريكي: ${usd_balance:.2f}

📈 المبيعات اليوم: {sales_total:,.0f} ل.س
📉 المصروفات اليوم: {expenses_total:,.0f} ل.س
💵 صافي الربح: {net_profit:,.0f} ل.س

📅 تاريخ الجلسة: {date.today().strftime('%Y-%m-%d')}
⏰ وقت آخر تحديث: {datetime.now().strftime('%H:%M:%S')}
                """

                self.summary_label.setText(summary_text.strip())
            else:
                self.summary_label.setText("لا توجد جلسة نشطة. يرجى فتح الصندوق لبدء العمل.")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الملخص: {e}")

    def on_currency_changed(self):
        """معالجة تغيير العملة"""
        currency = self.currency_combo.currentData()

        if currency == 'SYP':
            self.opening_balance_spin.setSuffix(" ل.س")
            self.opening_balance_spin.setDecimals(0)
        else:
            self.opening_balance_spin.setSuffix(" $")
            self.opening_balance_spin.setDecimals(2)

    def setup_responsive_design(self):
        """إعداد التصميم المتجاوب"""

        screen_size = self.size()

        if screen_size.width() < 1000 or screen_size.height() < 800:
            self.is_mobile_view = True
            self.apply_mobile_styles()
        else:
            self.is_mobile_view = False
            self.apply_desktop_styles()

    def apply_mobile_styles(self):
        """تطبيق أنماط الجوال"""

        mobile_style = """
            QLabel { font-size: 10px; }
            QPushButton { font-size: 10px; padding: 6px 12px; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                font-size: 10px;
                min-height: 30px;
            }
        """
        self.setStyleSheet(self.styleSheet() + mobile_style)

    def apply_desktop_styles(self):
        """تطبيق أنماط سطح المكتب"""

        desktop_style = """
            QLabel { font-size: 10pt; }
            QPushButton { font-size: 10pt; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                font-size: 10pt;
            }
        """
        self.setStyleSheet(self.styleSheet() + desktop_style)

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للتجاوب"""
        super().resizeEvent(event)
        self.setup_responsive_design()

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""

        # إيقاف المؤقتات
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()
        if hasattr(self, 'balance_timer'):
            self.balance_timer.stop()

        event.accept()

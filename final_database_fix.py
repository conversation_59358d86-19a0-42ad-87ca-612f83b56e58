#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي لقاعدة البيانات
"""

import sys
import os
sys.path.append('src')

def final_database_fix():
    """الإصلاح النهائي لقاعدة البيانات"""
    
    print("🔧 الإصلاح النهائي لقاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        print("✅ اتصال قاعدة البيانات")
        
        # إضافة العمود المفقود router_type_id
        print("1️⃣ إضافة عمود router_type_id...")
        try:
            db.execute_query("ALTER TABLE subscribers ADD COLUMN router_type_id INTEGER")
            print("✅ تم إضافة عمود router_type_id")
        except:
            print("ℹ️ عمود router_type_id موجود بالفعل")
        
        # تحديث المشتركين الموجودين
        print("2️⃣ تحديث المشتركين...")
        db.execute_query("UPDATE subscribers SET router_type_id = 1 WHERE router_type_id IS NULL")
        print("✅ تم تحديث المشتركين")
        
        # إضافة مشترك جديد للاختبار
        print("3️⃣ إضافة مشترك جديد...")
        db.execute_query("""
            INSERT OR IGNORE INTO subscribers 
            (name, phone, address, package_id, delivered, subscription_paid, router_paid, router_type_id)
            VALUES ('مشترك تجريبي', '0999888777', 'عنوان تجريبي', 1, 0, 0, 0, 1)
        """)
        print("✅ تم إضافة مشترك جديد")
        
        print("✅ تم الإصلاح النهائي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح النهائي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_final_router_delivery():
    """اختبار نهائي لتسليم الراوتر"""
    
    print("\n🧪 اختبار نهائي لتسليم الراوتر...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء الواجهة
        print("🔄 إنشاء واجهة تسليم الراوتر...")
        window = RouterDeliveryWindow(db, current_user)
        print("✅ تم إنشاء الواجهة")
        
        # فحص البيانات المحملة
        subscriber_count = window.subscriber_combo.count() if hasattr(window, 'subscriber_combo') else 0
        router_count = window.router_combo.count() if hasattr(window, 'router_combo') else 0
        package_count = window.package_combo.count() if hasattr(window, 'package_combo') else 0
        worker_count = window.worker_combo.count() if hasattr(window, 'worker_combo') else 0
        
        print(f"📊 البيانات المحملة:")
        print(f"  • المشتركين: {subscriber_count}")
        print(f"  • الراوترات: {router_count}")
        print(f"  • الباقات: {package_count}")
        print(f"  • العمال: {worker_count}")
        
        # محاكاة ملء البيانات
        if subscriber_count > 0 and router_count > 0 and package_count > 0 and worker_count > 0:
            print("🔄 محاكاة ملء البيانات...")
            
            # اختيار مشترك
            window.subscriber_combo.setCurrentIndex(0)
            print("✅ تم اختيار مشترك")
            
            # اختيار راوتر
            window.router_combo.setCurrentIndex(0)
            print("✅ تم اختيار راوتر")
            
            # اختيار باقة
            window.package_combo.setCurrentIndex(0)
            print("✅ تم اختيار باقة")
            
            # اختيار عامل
            window.worker_combo.setCurrentIndex(0)
            print("✅ تم اختيار عامل")
            
            # إدخال أمتار الكبل
            if hasattr(window, 'cable_meters_input'):
                window.cable_meters_input.setText("50")
                print("✅ تم إدخال أمتار الكبل")
            
            # محاكاة الضغط على زر الحفظ
            print("🚨 محاكاة الضغط على زر الحفظ...")
            try:
                # استدعاء دالة الحفظ مباشرة
                window.save_delivery()
                print("✅ تم تنفيذ دالة الحفظ بنجاح!")
                return True
            except Exception as save_error:
                print(f"❌ خطأ في دالة الحفظ: {save_error}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("❌ البيانات غير كاملة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار النهائي: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test_app():
    """إنشاء تطبيق اختبار بسيط"""
    
    print("\n🚀 إنشاء تطبيق اختبار بسيط...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
        from PyQt5.QtCore import QTimer
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        class TestApp(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("اختبار تسليم الراوتر")
                self.setGeometry(100, 100, 400, 300)
                
                # إعداد قاعدة البيانات
                self.db = DatabaseManager('data/company_system.db')
                self.current_user = {
                    'id': 1,
                    'username': 'admin',
                    'full_name': 'المدير',
                    'role': 'admin'
                }
                
                # إعداد الواجهة
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                layout = QVBoxLayout()
                central_widget.setLayout(layout)
                
                # تسمية
                label = QLabel("اختبار تسليم الراوتر")
                layout.addWidget(label)
                
                # زر فتح تسليم الراوتر
                self.open_btn = QPushButton("فتح تسليم الراوتر")
                self.open_btn.clicked.connect(self.open_router_delivery)
                layout.addWidget(self.open_btn)
                
                # زر الإغلاق
                close_btn = QPushButton("إغلاق")
                close_btn.clicked.connect(self.close)
                layout.addWidget(close_btn)
                
                # حالة
                self.status_label = QLabel("جاهز للاختبار")
                layout.addWidget(self.status_label)
            
            def open_router_delivery(self):
                """فتح واجهة تسليم الراوتر"""
                try:
                    self.status_label.setText("فتح واجهة تسليم الراوتر...")
                    
                    self.router_window = RouterDeliveryWindow(self.db, self.current_user, self)
                    self.router_window.show()
                    
                    self.status_label.setText("تم فتح واجهة تسليم الراوتر")
                    
                except Exception as e:
                    self.status_label.setText(f"خطأ: {str(e)}")
                    print(f"❌ خطأ في فتح الواجهة: {e}")
                    import traceback
                    traceback.print_exc()
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء النافذة الرئيسية
        main_window = TestApp()
        main_window.show()
        
        print("✅ تم إنشاء تطبيق الاختبار")
        print("📋 تعليمات الاختبار:")
        print("  1. اضغط على 'فتح تسليم الراوتر'")
        print("  2. املأ البيانات في الواجهة")
        print("  3. اضغط على 'حفظ وتسليم'")
        print("  4. راقب ما إذا كان التطبيق يغلق أم لا")
        
        # إغلاق التطبيق بعد 30 ثانية
        QTimer.singleShot(30000, app.quit)
        
        # تشغيل التطبيق
        result = app.exec_()
        print(f"📊 انتهى التطبيق بالكود: {result}")
        
        return result == 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تطبيق الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 الإصلاح النهائي لمشكلة إغلاق التطبيق")
    print("=" * 70)
    
    # الإصلاح النهائي
    final_fix = final_database_fix()
    
    # الاختبار النهائي
    final_test = test_final_router_delivery()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الإصلاح النهائي:")
    print(f"  • الإصلاح النهائي: {'✅ نجح' if final_fix else '❌ فشل'}")
    print(f"  • الاختبار النهائي: {'✅ نجح' if final_test else '❌ فشل'}")
    
    if final_fix and final_test:
        print("\n🎉 تم إصلاح جميع المشاكل!")
        print("\n📋 الآن التطبيق جاهز:")
        print("  ✅ جميع الجداول موجودة")
        print("  ✅ جميع الأعمدة موجودة")
        print("  ✅ البيانات التجريبية متوفرة")
        print("  ✅ دالة الحفظ تعمل")
        print("  ✅ معالجة الأخطاء محسنة")
        
        print("\n🚀 للاختبار النهائي:")
        print("  python system_launcher.py")
        print("  admin / 123")
        print("  تسليم راوتر → ملء البيانات → حفظ وتسليم")
        
        # تشغيل تطبيق الاختبار
        print("\n🧪 تشغيل تطبيق اختبار تفاعلي...")
        create_simple_test_app()
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")

if __name__ == "__main__":
    main()

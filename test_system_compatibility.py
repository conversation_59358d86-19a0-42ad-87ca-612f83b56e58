#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التوافق بين جداول النظام
"""

import sys
import os
sys.path.append('src')

def test_categories_compatibility():
    """اختبار التوافق بين جداول التصنيفات"""
    
    print("🧪 اختبار التوافق بين جداول التصنيفات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.system_compatibility_manager import SystemCompatibilityManager
        
        db = DatabaseManager('.')
        compatibility_manager = SystemCompatibilityManager(db)
        
        print("✅ تم إنشاء مدير التوافق")
        
        # ضمان التوافق
        print("\n🔧 ضمان التوافق بين الجداول...")
        compatibility_success = compatibility_manager.ensure_system_compatibility()
        
        if compatibility_success:
            print("✅ تم ضمان التوافق بين الجداول")
        else:
            print("❌ فشل في ضمان التوافق")
            return False
        
        # مزامنة التصنيفات
        print("\n🔄 مزامنة التصنيفات عبر النظام...")
        sync_success = compatibility_manager.sync_categories_across_system()
        
        if sync_success:
            print("✅ تم مزامنة التصنيفات")
        else:
            print("❌ فشل في مزامنة التصنيفات")
            return False
        
        # إنشاء تقرير التوافق
        print("\n📊 إنشاء تقرير التوافق...")
        compatibility_manager.generate_compatibility_report()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوافق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_unified_system():
    """اختبار النظام الموحد للمخزون"""
    
    print("\n🧪 اختبار النظام الموحد للمخزون...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        print("✅ تم إنشاء مدير المخزون الموحد")
        
        # جلب جميع المنتجات
        print("\n📦 جلب جميع المنتجات من النظام الموحد...")
        products = inventory_manager.get_all_products()
        
        print(f"✅ تم جلب {len(products)} منتج من النظام الموحد")
        
        # عرض عينة من المنتجات
        if products:
            print("\n📋 عينة من المنتجات:")
            for i, product in enumerate(products[:3]):  # أول 3 منتجات
                print(f"  {i+1}. {product.get('name', 'غير محدد')} - {product.get('category', 'غير محدد')} - مخزون: {product.get('current_stock', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الموحد للمخزون: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_unified_system():
    """اختبار النظام الموحد للخزينة"""
    
    print("\n🧪 اختبار النظام الموحد للخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة الموحد")
        
        # فحص الأرصدة
        print("\n💰 فحص الأرصدة من النظام الموحد...")
        
        currencies = ['SYP', 'USD', 'EUR']
        for currency in currencies:
            daily_balance = treasury_manager.get_daily_balance(current_user['id'], currency)
            main_balance = treasury_manager.get_main_balance(currency)
            
            symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")
            print(f"  • {currency}: يومي {daily_balance:,.2f} {symbol} - رئيسي {main_balance:,.2f} {symbol}")
        
        print("✅ النظام الموحد للخزينة يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الموحد للخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_categories_management_integration():
    """اختبار تكامل إدارة التصنيفات"""
    
    print("\n🧪 اختبار تكامل إدارة التصنيفات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص جدول categories
        print("\n📋 فحص جدول التصنيفات...")
        categories = db.fetch_all("SELECT name, description FROM categories WHERE is_active = 1 ORDER BY name")
        
        print(f"✅ تم العثور على {len(categories)} تصنيف في جدول categories:")
        for category in categories[:5]:  # أول 5 تصنيفات
            print(f"  • {category['name']}: {category.get('description', 'بدون وصف')}")
        
        # فحص استخدام التصنيفات في المنتجات
        print("\n🔗 فحص استخدام التصنيفات في المنتجات...")
        
        for category in categories[:3]:  # أول 3 تصنيفات
            category_name = category['name']
            
            # فحص في جدول products
            products_count = db.fetch_one("SELECT COUNT(*) as count FROM products WHERE category = ?", (category_name,))
            
            # فحص في جدول unified_products
            try:
                unified_count = db.fetch_one("SELECT COUNT(*) as count FROM unified_products WHERE category = ?", (category_name,))
                unified_count = unified_count['count'] if unified_count else 0
            except:
                unified_count = 0
            
            print(f"  • {category_name}: {products_count['count']} في products، {unified_count} في unified_products")
        
        print("✅ تكامل إدارة التصنيفات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل إدارة التصنيفات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار التوافق الشامل بين جداول النظام")
    print("=" * 70)
    
    # اختبار التوافق بين التصنيفات
    categories_test = test_categories_compatibility()
    
    # اختبار النظام الموحد للمخزون
    inventory_test = test_inventory_unified_system()
    
    # اختبار النظام الموحد للخزينة
    treasury_test = test_treasury_unified_system()
    
    # اختبار تكامل إدارة التصنيفات
    integration_test = test_categories_management_integration()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • التوافق بين التصنيفات: {'✅ يعمل' if categories_test else '❌ لا يعمل'}")
    print(f"  • النظام الموحد للمخزون: {'✅ يعمل' if inventory_test else '❌ لا يعمل'}")
    print(f"  • النظام الموحد للخزينة: {'✅ يعمل' if treasury_test else '❌ لا يعمل'}")
    print(f"  • تكامل إدارة التصنيفات: {'✅ يعمل' if integration_test else '❌ لا يعمل'}")
    
    if all([categories_test, inventory_test, treasury_test, integration_test]):
        print("\n🎉 تم إصلاح التوافق بين جميع جداول النظام!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح التوافق بين جداول التصنيفات")
        print("    • واجهة إدارة التصنيف تحفظ في جدول categories")
        print("    • واجهة إدارة الجرد تقرأ من جدول categories")
        print("    • مزامنة تلقائية للتصنيفات عبر النظام")
        
        print("  ✅ إصلاح النظام الموحد للمخزون")
        print("    • واجهة إدارة الجرد تستخدم UnifiedInventoryManager")
        print("    • جميع الواجهات تتعامل مع نفس جداول المخزون")
        print("    • توحيد البيانات عبر النظام")
        
        print("  ✅ إصلاح النظام الموحد للخزينة")
        print("    • واجهة نقل الخزينة تقرأ من النظام الموحد")
        print("    • إغلاق الصناديق يحفظ في النظام الموحد")
        print("    • دعم العملات المتعددة موحد")
        
        print("  ✅ إضافة مدير التوافق")
        print("    • فحص تلقائي للتوافق عند بدء التشغيل")
        print("    • مزامنة البيانات بين الجداول")
        print("    • تقارير التوافق والحالة")
        
        print("\n🚀 النظام الآن:")
        print("  • جميع الواجهات تتعامل مع نفس الجداول")
        print("  • التصنيفات موحدة عبر النظام")
        print("  • المخزون موحد ومتكامل")
        print("  • الخزينة موحدة ومتعددة العملات")
        print("  • فحص تلقائي للتوافق")
        
        print("\n🎯 المميزات:")
        print("  • إدارة التصنيف → تحفظ في categories")
        print("  • إدارة الجرد → تقرأ من categories و unified_products")
        print("  • نقل الخزينة → تقرأ من unified_treasury")
        print("  • جميع العمليات متكاملة ومتوافقة")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل نظام إدارة شركة الإنترنت النهائي
Final Internet Company Management System Launcher
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """إعداد البيئة"""
    # إعداد المسارات
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    
    sys.path.insert(0, str(project_root))
    sys.path.insert(0, str(src_path))
    
    # تجاهل تحذيرات Qt
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    # إعداد Qt platform plugin
    try:
        import PyQt5
        qt_plugin_path = Path(PyQt5.__file__).parent / "Qt5" / "plugins"
        if qt_plugin_path.exists():
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qt_plugin_path)
        else:
            qt_plugin_path = Path(PyQt5.__file__).parent / "Qt" / "plugins"
            if qt_plugin_path.exists():
                os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qt_plugin_path)
    except:
        pass
    
    return project_root, src_path

def migrate_closed_cash_boxes():
    """نقل الصناديق المغلقة للنظام الموحد"""
    print("🔄 نقل الصناديق المغلقة للنظام الموحد...")

    try:
        from src.database.database_manager import DatabaseManager
        from src.utils.system_compatibility_manager import SystemCompatibilityManager

        db = DatabaseManager('.')
        compatibility_manager = SystemCompatibilityManager(db)

        # نقل الصناديق المغلقة
        success = compatibility_manager.migrate_closed_cash_boxes()

        if success:
            print("✅ تم نقل الصناديق المغلقة بنجاح")

            # فحص النتائج
            from src.utils.unified_treasury_manager import UnifiedTreasuryManager
            treasury_manager = UnifiedTreasuryManager(db)
            daily_balance = treasury_manager.get_daily_balance(1, 'SYP')

            print(f"💰 الرصيد الحالي في الخزينة اليومية: {daily_balance:,} ل.س")

            if daily_balance > 0:
                print("🎉 الآن يمكن استخدام واجهة شراء الدولار ونقل الخزينة!")
            else:
                print("⚠️ لا يزال الرصيد صفر - قد لا توجد صناديق مغلقة")
        else:
            print("❌ فشل في نقل الصناديق المغلقة")

    except Exception as e:
        print(f"❌ خطأ في نقل الصناديق المغلقة: {e}")
        import traceback
        traceback.print_exc()

def test_system_components():
    """اختبار مكونات النظام"""
    print("🧪 اختبار مكونات النظام...")

    try:
        # اختبار الوحدات الأساسية
        from src.database.database_manager import DatabaseManager
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support, format_currency
        print("✅ الوحدات الأساسية")

        # اختبار جميع الواجهات
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.ui.new_subscription_window import NewSubscriptionWindow
        from src.ui.router_delivery_window import RouterDeliveryWindow
        from src.ui.package_renewal_window import PackageRenewalWindow
        from src.ui.cash_close_window import CashCloseWindow
        from src.ui.subscribers_management_window import SubscribersManagementWindow
        from src.ui.products_management_window import ProductsManagementWindow
        from src.ui.workers_management_window import WorkersManagementWindow
        from src.ui.suppliers_management_window import SuppliersManagementWindow
        from src.ui.purchases_window import PurchasesWindow
        from src.ui.balance_charge_window import BalanceChargeWindow
        from src.ui.reports_window import ReportsWindow
        from src.ui.settings_window import SettingsWindow
        print("✅ جميع الواجهات (14 واجهة)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت - النسخة النهائية")
    print("=" * 70)
    
    # إعداد البيئة
    project_root, src_path = setup_environment()
    
    # اختبار المكونات
    if not test_system_components():
        print("❌ فشل في اختبار المكونات")
        return 1
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("✅ PyQt5 جاهز")
        
        # استيراد وحدات النظام
        from src.database.database_manager import DatabaseManager
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        
        print("✅ تم تحميل جميع الوحدات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        print("✅ تم إعداد الدعم العربي")
        
        # إنشاء مجلد البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)

        # إنشاء قاعدة البيانات (استخدام المسار الحالي)
        db_manager = DatabaseManager('.')
        config_manager = ConfigManager()
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # عرض نافذة تسجيل الدخول
        login_window = LoginWindow(db_manager, config_manager)
        
        if login_window.exec_() != login_window.Accepted:
            print("تم إلغاء تسجيل الدخول")
            return 0
        
        current_user = login_window.get_current_user()
        print(f"✅ مرحباً {current_user['full_name']}")

        # فتح الشيفت تلقائياً (بدون سؤال)
        try:
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            treasury_manager = UnifiedTreasuryManager(db_manager)

            # التحقق من وجود شيفت مفتوح سابق
            has_open_shift = treasury_manager.is_session_active(current_user['id'])

            if has_open_shift:
                print(f"✅ يوجد شيفت مفتوح سابق للمستخدم {current_user['username']} - المتابعة مع الشيفت المفتوح")
            else:
                # فتح شيفت جديد تلقائياً
                shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
                if shift_opened:
                    print(f"✅ تم فتح شيفت جديد للمستخدم {current_user['username']}")
                else:
                    print(f"❌ فشل في فتح شيفت للمستخدم {current_user['username']}")

        except Exception as e:
            print(f"❌ خطأ في التحقق من الشيفت: {e}")
            # لا نوقف النظام، فقط نسجل الخطأ

        # عرض النافذة الرئيسية
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        print("=" * 70)
        print("📋 معلومات النظام:")
        print("   📱 نظام إدارة شركة إنترنت متكامل")
        print("   🇸🇾 يدعم اللغة العربية بالكامل")
        print("   💾 قاعدة بيانات SQLite")
        print("   🖥️ واجهة PyQt5 حديثة")
        print("   👥 إدارة متعددة المستخدمين")
        print("   💰 نظام محاسبي متكامل")
        print("")
        print("🔧 الواجهات المتاحة (14 واجهة):")
        print("   ✅ اشتراك جديد")
        print("   ✅ تسليم راوتر")
        print("   ✅ تجديد باقة")
        print("   ✅ إغلاق الصندوق")
        print("   ✅ المشتريات من الموردين")
        print("   ✅ شحن رصيد الموزعين")
        print("   ✅ إدارة المشتركين")
        print("   ✅ إدارة المنتجات")
        print("   ✅ إدارة العمال")
        print("   ✅ إدارة الموردين")
        print("   ✅ التقارير والإحصائيات")
        print("   ✅ الإعدادات")
        print("   ✅ نافذة تسجيل الدخول")
        print("   ✅ النافذة الرئيسية")
        print("")
        print("📊 قاعدة البيانات:")
        try:
            users = db_manager.fetch_all("SELECT COUNT(*) as count FROM users")[0]['count']
            products = db_manager.fetch_all("SELECT COUNT(*) as count FROM products")[0]['count']
            packages = db_manager.fetch_all("SELECT COUNT(*) as count FROM packages")[0]['count']
            workers = db_manager.fetch_all("SELECT COUNT(*) as count FROM workers")[0]['count']
            suppliers = db_manager.fetch_all("SELECT COUNT(*) as count FROM suppliers")[0]['count']
            subscribers = db_manager.fetch_all("SELECT COUNT(*) as count FROM subscribers")[0]['count']
            
            print(f"   👤 المستخدمون: {users}")
            print(f"   📦 المنتجات: {products}")
            print(f"   📋 الباقات: {packages}")
            print(f"   👷 العمال: {workers}")
            print(f"   🏢 الموردون: {suppliers}")
            print(f"   📱 المشتركون: {subscribers}")
        except:
            print("   📊 قاعدة البيانات جاهزة")
        
        print("")
        print("📋 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 70)
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
    
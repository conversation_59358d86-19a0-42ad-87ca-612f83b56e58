# -*- coding: utf-8 -*-
"""
مدير الإعدادات
Configuration Manager
"""

import json
import os
from pathlib import Path

class ConfigManager:
    """مدير الإعدادات"""
    
    def __init__(self, project_dir=None):
        if project_dir:
            self.project_dir = Path(project_dir)
        else:
            self.project_dir = Path(".")
        self.config_file = self.project_dir / "data" / "config" / "settings.json"
        self.config_data = {}

        # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
        self.config_file.parent.mkdir(parents=True, exist_ok=True)

        # تحميل الإعدادات
        self.load_config()
        
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                # إنشاء إعدادات افتراضية
                self.config_data = self.get_default_config()
                self.save_config()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self.config_data = self.get_default_config()
            
    def save_config(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
            
    def get_default_config(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            "database": {
                "type": "sqlite",
                "path": "data/internet_company.db"
            },
            "ui": {
                "theme": "default",
                "language": "ar",
                "font_family": "Tahoma",
                "font_size": 10,
                "window_size": {
                    "width": 1200,
                    "height": 800
                }
            },
            "printing": {
                "printer_name": "",
                "paper_size": "A4",
                "margins": {
                    "top": 20,
                    "bottom": 20,
                    "left": 20,
                    "right": 20
                }
            },
            "company": {
                "name": "شركة الإنترنت",
                "address": "",
                "phone": "",
                "email": "",
                "logo_path": ""
            },
            "financial": {
                "default_currency": "SYP",
                "exchange_rates": {
                    "USD": 15000,
                    "EUR": 16000
                },
                "subscription_fee": 50000
            },
            "backup": {
                "auto_backup": True,
                "backup_interval": 24,  # ساعات
                "backup_path": "backups",
                "max_backups": 30
            }
        }
        
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key, value):
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config_data
        
        # التنقل إلى المستوى الأخير
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # تعيين القيمة
        config[keys[-1]] = value
        
        # حفظ الإعدادات
        return self.save_config()
        
    def get_database_path(self):
        """الحصول على مسار قاعدة البيانات"""
        db_path = self.get("database.path", "data/internet_company.db")
        return self.project_dir / db_path
        
    def get_backup_path(self):
        """الحصول على مسار النسخ الاحتياطية"""
        backup_path = self.get("backup.backup_path", "backups")
        return self.project_dir / backup_path
        
    def get_company_info(self):
        """الحصول على معلومات الشركة"""
        return self.get("company", {})
        
    def update_company_info(self, info):
        """تحديث معلومات الشركة"""
        current_info = self.get("company", {})
        current_info.update(info)
        return self.set("company", current_info)
        
    def get_ui_settings(self):
        """الحصول على إعدادات الواجهة"""
        return self.get("ui", {})
        
    def get_financial_settings(self):
        """الحصول على الإعدادات المالية"""
        return self.get("financial", {})
        
    def get_printing_settings(self):
        """الحصول على إعدادات الطباعة"""
        return self.get("printing", {})
        
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.config_data = self.get_default_config()
        return self.save_config()

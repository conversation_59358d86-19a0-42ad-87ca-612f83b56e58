# -*- coding: utf-8 -*-
"""
نافذة نقل الخزينة
Treasury Transfer Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class TreasuryTransferWindow(QDialog):
    """نافذة نقل الخزينة"""
    
    transfer_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)

        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نقل الخزينة")
        self.setGeometry(100, 100, 700, 550)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("نقل الأموال من الخزينة اليومية إلى الخزينة الرئيسية")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # معلومات الخزائن
        treasuries_info_group = self.create_treasuries_info_group()
        
        # تفاصيل النقل
        transfer_details_group = self.create_transfer_details_group()
        
        # ملخص العملية
        summary_group = self.create_summary_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(treasuries_info_group)
        main_layout.addWidget(transfer_details_group)
        main_layout.addWidget(summary_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)

    def on_currency_changed(self):
        """عند تغيير نوع العملة"""
        try:
            selected_currency = self.currency_combo.currentData()

            # تحديث وحدة المبلغ
            if selected_currency == "SYP":
                self.amount_spin.setSuffix(" ل.س")
                self.amount_spin.setDecimals(0)
            elif selected_currency == "USD":
                self.amount_spin.setSuffix(" $")
                self.amount_spin.setDecimals(2)
            elif selected_currency == "EUR":
                self.amount_spin.setSuffix(" €")
                self.amount_spin.setDecimals(2)

            # تحديث الحد الأقصى للمبلغ حسب الرصيد المتاح
            self.update_max_amount()

        except Exception as e:
            print(f"❌ خطأ في تغيير العملة: {e}")

    def update_max_amount(self):
        """تحديث الحد الأقصى للمبلغ حسب العملة المختارة"""
        try:
            selected_currency = self.currency_combo.currentData()

            if selected_currency == "SYP":
                # الحصول على رصيد الليرة السورية من الشيفتات
                max_amount = self.get_daily_syp_balance()
            else:
                # الحصول على رصيد العملة من جدول الخزينة
                max_amount = self.get_currency_balance(selected_currency)

            self.amount_spin.setMaximum(max(0, max_amount))
            self.amount_spin.setValue(max(0, max_amount))

        except Exception as e:
            print(f"❌ خطأ في تحديث الحد الأقصى: {e}")

    def create_treasuries_info_group(self):
        """إنشاء مجموعة معلومات الخزائن"""
        group = QGroupBox("أرصدة الخزائن الحالية")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # الخزينة اليومية
        daily_label = QLabel("الخزينة اليومية:")
        apply_arabic_style(daily_label, 10, bold=True)
        
        daily_syp_label = QLabel("بالليرة السورية:")
        apply_arabic_style(daily_syp_label, 9)
        self.daily_syp_balance_label = QLabel("0 ل.س")
        apply_arabic_style(self.daily_syp_balance_label, 10, bold=True)
        self.daily_syp_balance_label.setStyleSheet("color: #e74c3c;")
        
        daily_usd_label = QLabel("بالدولار:")
        apply_arabic_style(daily_usd_label, 9)
        self.daily_usd_balance_label = QLabel("$0")
        apply_arabic_style(self.daily_usd_balance_label, 10, bold=True)
        self.daily_usd_balance_label.setStyleSheet("color: #e74c3c;")
        
        # الخزينة الرئيسية
        main_label = QLabel("الخزينة الرئيسية:")
        apply_arabic_style(main_label, 10, bold=True)
        
        main_syp_label = QLabel("بالليرة السورية:")
        apply_arabic_style(main_syp_label, 9)
        self.main_syp_balance_label = QLabel("0 ل.س")
        apply_arabic_style(self.main_syp_balance_label, 10, bold=True)
        self.main_syp_balance_label.setStyleSheet("color: #27ae60;")
        
        main_usd_label = QLabel("بالدولار:")
        apply_arabic_style(main_usd_label, 9)
        self.main_usd_balance_label = QLabel("$0")
        apply_arabic_style(self.main_usd_balance_label, 10, bold=True)
        self.main_usd_balance_label.setStyleSheet("color: #27ae60;")
        
        layout.addWidget(daily_label, 0, 0, 1, 4)
        layout.addWidget(daily_syp_label, 1, 0)
        layout.addWidget(self.daily_syp_balance_label, 1, 1)
        layout.addWidget(daily_usd_label, 1, 2)
        layout.addWidget(self.daily_usd_balance_label, 1, 3)
        
        layout.addWidget(main_label, 2, 0, 1, 4)
        layout.addWidget(main_syp_label, 3, 0)
        layout.addWidget(self.main_syp_balance_label, 3, 1)
        layout.addWidget(main_usd_label, 3, 2)
        layout.addWidget(self.main_usd_balance_label, 3, 3)
        
        return group
        
    def create_transfer_details_group(self):
        """إنشاء مجموعة تفاصيل النقل"""
        group = QGroupBox("تفاصيل عملية النقل")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # نوع العملة
        currency_label = QLabel("نوع العملة:")
        apply_arabic_style(currency_label, 10)
        self.currency_combo = QComboBox()
        apply_arabic_style(self.currency_combo, 10)
        self.currency_combo.addItem("الليرة السورية (SYP)", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي (USD)", "USD")
        self.currency_combo.addItem("اليورو (EUR)", "EUR")
        self.currency_combo.currentTextChanged.connect(self.on_currency_changed)
        
        # المبلغ المراد نقله
        amount_label = QLabel("المبلغ المراد نقله:")
        apply_arabic_style(amount_label, 10)
        self.amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.amount_spin, 10)
        self.amount_spin.setRange(0, 10000000)
        self.amount_spin.setSuffix(" ل.س")
        
        # نقل الكل
        transfer_all_checkbox = QCheckBox("نقل كامل الرصيد")
        apply_arabic_style(transfer_all_checkbox, 10)
        transfer_all_checkbox.setStyleSheet("color: #e67e22;")
        
        # تاريخ النقل
        date_label = QLabel("تاريخ النقل:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات حول عملية النقل...")
        self.notes_edit.setMaximumHeight(60)
        
        layout.addWidget(currency_label, 0, 0)
        layout.addWidget(self.currency_combo, 0, 1)
        layout.addWidget(amount_label, 1, 0)
        layout.addWidget(self.amount_spin, 1, 1)
        layout.addWidget(transfer_all_checkbox, 1, 2, 1, 2)
        layout.addWidget(date_label, 2, 0)
        layout.addWidget(self.date_edit, 2, 1, 1, 3)
        layout.addWidget(notes_label, 3, 0)
        layout.addWidget(self.notes_edit, 3, 1, 1, 3)
        
        # ربط الأحداث
        transfer_all_checkbox.toggled.connect(self.toggle_transfer_all)
        
        return group
        
    def create_summary_group(self):
        """إنشاء مجموعة الملخص"""
        group = QGroupBox("ملخص العملية")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # الرصيد الجديد للخزينة اليومية
        new_daily_label = QLabel("الرصيد الجديد للخزينة اليومية:")
        apply_arabic_style(new_daily_label, 10)
        self.new_daily_balance_label = QLabel("0")
        apply_arabic_style(self.new_daily_balance_label, 11, bold=True)
        self.new_daily_balance_label.setStyleSheet("color: #f39c12;")
        
        # الرصيد الجديد للخزينة الرئيسية
        new_main_label = QLabel("الرصيد الجديد للخزينة الرئيسية:")
        apply_arabic_style(new_main_label, 10)
        self.new_main_balance_label = QLabel("0")
        apply_arabic_style(self.new_main_balance_label, 11, bold=True)
        self.new_main_balance_label.setStyleSheet("color: #f39c12;")
        
        layout.addWidget(new_daily_label, 0, 0)
        layout.addWidget(self.new_daily_balance_label, 0, 1)
        layout.addWidget(new_main_label, 1, 0)
        layout.addWidget(self.new_main_balance_label, 1, 1)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        transfer_button = QPushButton("تنفيذ عملية النقل")
        apply_arabic_style(transfer_button, 10, bold=True)
        transfer_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(transfer_button)
        
        transfer_button.clicked.connect(self.execute_transfer)
        cancel_button.clicked.connect(self.reject)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات من النظام الموحد"""
        try:
            print("🔄 تحميل بيانات الخزينة من النظام الموحد...")

            # استخدام النظام الموحد للحصول على الأرصدة الإجمالية
            daily_syp_balance = self.treasury_manager.get_total_daily_balance('SYP')
            daily_usd_balance = self.treasury_manager.get_total_daily_balance('USD')

            main_syp_balance = self.treasury_manager.get_main_balance('SYP')
            main_usd_balance = self.treasury_manager.get_main_balance('USD')

            print(f"💰 الخزينة اليومية - ليرة: {daily_syp_balance:,} ل.س، دولار: ${daily_usd_balance:.2f}")
            print(f"💰 الخزينة الرئيسية - ليرة: {main_syp_balance:,} ل.س، دولار: ${main_usd_balance:.2f}")

            # تحديث الواجهة بالأرصدة الصحيحة
            self.update_display_with_unified_data(daily_syp_balance, daily_usd_balance, main_syp_balance, main_usd_balance)

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")

    def update_display_with_unified_data(self, daily_syp_balance, daily_usd_balance, main_syp_balance, main_usd_balance):
        """تحديث الواجهة بالبيانات من النظام الموحد"""
        try:
            # تحديث أرصدة الخزينة اليومية
            self.daily_syp_balance_label.setText(format_currency(daily_syp_balance))
            self.daily_usd_balance_label.setText(f"${daily_usd_balance:,.2f}")

            # تحديث أرصدة الخزينة الرئيسية
            self.main_syp_balance_label.setText(format_currency(main_syp_balance))
            self.main_usd_balance_label.setText(f"${main_usd_balance:,.2f}")

            # تحديث الحد الأقصى للنقل (الليرة السورية فقط)
            self.amount_spin.setMaximum(max(0, daily_syp_balance))
            self.amount_spin.setValue(min(daily_syp_balance, 100000))  # قيمة افتراضية معقولة

            # حساب القيم الجديدة
            self.calculate_new_balances()

            print(f"✅ تم تحديث الواجهة بالأرصدة الصحيحة")

        except Exception as e:
            print(f"❌ خطأ في تحديث الواجهة: {e}")

    def calculate_new_balances(self):
        """حساب الأرصدة الجديدة بعد النقل"""
        try:
            transfer_amount = self.amount_spin.value()

            # الأرصدة الحالية
            current_daily_syp = self.treasury_manager.get_total_daily_balance('SYP')
            current_main_syp = self.treasury_manager.get_main_balance('SYP')

            # الأرصدة الجديدة
            new_daily_syp = current_daily_syp - transfer_amount
            new_main_syp = current_main_syp + transfer_amount

            # تحديث التسميات
            self.new_daily_syp_balance_label.setText(format_currency(new_daily_syp))
            self.new_main_syp_balance_label.setText(format_currency(new_main_syp))

        except Exception as e:
            print(f"❌ خطأ في حساب الأرصدة الجديدة: {e}")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")

    def refresh_treasury_data(self):
        """إعادة تحميل بيانات الخزينة (للاستدعاء من واجهات أخرى)"""
        try:
            self.load_data()
            print("🔄 تم تحديث بيانات الخزينة في واجهة النقل")
        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات الخزينة: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.currency_combo.currentTextChanged.connect(self.update_currency_display)
        self.amount_spin.valueChanged.connect(self.calculate_new_balances)
        
    def update_currency_display(self):
        """تحديث عرض العملة"""
        currency = self.currency_combo.currentData()
        if currency == "USD":
            self.amount_spin.setSuffix(" $")
        else:
            self.amount_spin.setSuffix(" ل.س")
        self.calculate_new_balances()
        
    def toggle_transfer_all(self, checked):
        """تبديل نقل الكل"""
        if checked:
            currency = self.currency_combo.currentData()
            if currency == "SYP":
                current_balance = float(self.daily_syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())
            else:
                current_balance = float(self.daily_usd_balance_label.text().replace("$", "").replace(",", "").strip())
            
            self.amount_spin.setValue(current_balance)
            self.amount_spin.setEnabled(False)
        else:
            self.amount_spin.setEnabled(True)
            
    def calculate_new_balances(self):
        """حساب الأرصدة الجديدة"""
        try:
            currency = self.currency_combo.currentData()
            transfer_amount = self.amount_spin.value()
            
            if currency == "SYP":
                current_daily = float(self.daily_syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())
                current_main = float(self.main_syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())
                
                new_daily = current_daily - transfer_amount
                new_main = current_main + transfer_amount
                
                self.new_daily_balance_label.setText(format_currency(new_daily))
                self.new_main_balance_label.setText(format_currency(new_main))
            else:
                current_daily = float(self.daily_usd_balance_label.text().replace("$", "").replace(",", "").strip())
                current_main = float(self.main_usd_balance_label.text().replace("$", "").replace(",", "").strip())
                
                new_daily = current_daily - transfer_amount
                new_main = current_main + transfer_amount
                
                self.new_daily_balance_label.setText(f"${new_daily:,.2f}")
                self.new_main_balance_label.setText(f"${new_main:,.2f}")
                
        except:
            pass
            
    def execute_transfer(self):
        """تنفيذ عملية النقل"""
        try:
            currency = self.currency_combo.currentData()
            transfer_amount = self.amount_spin.value()
            
            if transfer_amount <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح للنقل")
                return
            
            # التحقق من الرصيد المتاح
            if currency == "SYP":
                current_balance = float(self.daily_syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())
                currency_symbol = "ل.س"
            else:
                current_balance = float(self.daily_usd_balance_label.text().replace("$", "").replace(",", "").strip())
                currency_symbol = "$"
            
            if transfer_amount > current_balance:
                QMessageBox.warning(self, "تحذير", f"المبلغ المطلوب أكبر من الرصيد المتاح ({current_balance:,.2f} {currency_symbol})")
                return
            
            # تأكيد العملية
            reply = QMessageBox.question(
                self,
                "تأكيد عملية النقل",
                f"هل تريد نقل {transfer_amount:,.2f} {currency_symbol} من الخزينة اليومية إلى الخزينة الرئيسية؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # تنفيذ العملية في قاعدة البيانات
                self.db_manager.execute_query("BEGIN TRANSACTION")
                
                try:
                    # استخدام مدير الخزينة الموحد للنقل من الخزينة الإجمالية
                    success = self.treasury_manager.transfer_from_total_daily_to_main(
                        currency_type=currency,
                        amount=transfer_amount
                    )

                    if not success:
                        raise Exception("فشل في نقل الأموال")
                    
                    # تسجيل المعاملة المالية
                    currency_name = "الليرة السورية" if currency == "SYP" else "الدولار الأمريكي"
                    self.db_manager.execute_query("""
                        INSERT INTO transactions (type, amount, description, created_at)
                        VALUES (?, ?, ?, datetime('now'))
                    """, ("treasury_transfer", transfer_amount, f"نقل خزينة: {transfer_amount:,.2f} {currency_symbol} إلى الخزينة الرئيسية"))
                    
                    self.db_manager.execute_query("COMMIT")
                    
                    # إعداد بيانات النتيجة
                    transfer_data = {
                        'currency': currency,
                        'amount': transfer_amount,
                        'date': self.date_edit.date().toString("yyyy-MM-dd"),
                        'notes': self.notes_edit.toPlainText().strip()
                    }
                    
                    # تحديث قوي للواجهة
                    self.force_refresh_interface()

                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم نقل {transfer_amount:,.2f} {currency_symbol} إلى الخزينة الرئيسية بنجاح"
                    )

                    # إرسال إشارة
                    self.transfer_completed.emit(transfer_data)
                    
                    self.accept()
                    
                except Exception as e:
                    self.db_manager.execute_query("ROLLBACK")
                    raise e
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ عملية النقل: {e}")

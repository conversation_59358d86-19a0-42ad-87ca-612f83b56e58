2025-07-22 01:03:54,266 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:03:54,272 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:03:56,416 - __main__ - INFO - تم عرض شاشة البداية
2025-07-22 01:03:56,426 - __main__ - INFO - جاري تهيئة قاعدة البيانات...
2025-07-22 01:03:56,434 - database.database_manager - INFO - تم الاتصال بقاعدة البيانات بنجاح
2025-07-22 01:03:56,562 - database.database_manager - INFO - تم إنشاء جداول قاعدة البيانات بنجاح
2025-07-22 01:03:56,647 - database.database_manager - INFO - تم <PERSON>در<PERSON><PERSON> البيانات الافتراضية بنجاح
2025-07-22 01:03:56,674 - __main__ - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-07-22 01:03:56,691 - __main__ - INFO - عرض نافذة تسجيل الدخول
2025-07-22 01:03:56,760 - ui.login_window - INFO - تم تشغيل نافذة تسجيل الدخول
2025-07-22 01:04:01,386 - ui.login_window - INFO - تسجيل دخول ناجح للمستخدم: admin
2025-07-22 01:04:01,432 - ui.interface_manager - INFO - تم تحميل 5 فئة واجهة
2025-07-22 01:04:02,892 - ui.main_window - INFO - تم تشغيل الواجهة الرئيسية للمستخدم: المدير العام
2025-07-22 01:04:02,938 - __main__ - ERROR - خطأ في عرض نافذة تسجيل الدخول: type object 'QSplashScreen' has no attribute 'Accepted'
2025-07-22 01:04:07,094 - __main__ - INFO - إنهاء التطبيق
2025-07-22 01:04:17,025 - ui.interface_manager - WARNING - لم يتم العثور على وحدة DailyTreasuryWindow: No module named 'ui.financial'
2025-07-22 01:04:17,035 - ui.interface_manager - ERROR - خطأ في تحميل واجهة الخزينة اليومية: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:19,135 - ui.interface_manager - WARNING - لم يتم العثور على وحدة DailyTreasuryWindow: No module named 'ui.financial'
2025-07-22 01:04:19,137 - ui.interface_manager - ERROR - خطأ في تحميل واجهة الخزينة اليومية: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:20,185 - ui.interface_manager - WARNING - لم يتم العثور على وحدة TreasuryTransferWindow: No module named 'ui.financial'
2025-07-22 01:04:20,188 - ui.interface_manager - ERROR - خطأ في تحميل واجهة نقل الخزينة: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:22,281 - ui.interface_manager - WARNING - لم يتم العثور على وحدة CurrencyExchangeWindow: No module named 'ui.financial'
2025-07-22 01:04:22,289 - ui.interface_manager - ERROR - خطأ في تحميل واجهة صرف العملة: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:24,978 - ui.interface_manager - WARNING - لم يتم العثور على وحدة FinancialReportsWindow: No module named 'ui.financial'
2025-07-22 01:04:24,979 - ui.interface_manager - ERROR - خطأ في تحميل واجهة التقارير المالية: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:27,077 - ui.interface_manager - WARNING - لم يتم العثور على وحدة NewSubscriptionWindow: No module named 'ui.subscribers'
2025-07-22 01:04:27,091 - ui.interface_manager - ERROR - خطأ في تحميل واجهة اشتراك جديد: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:33,708 - ui.interface_manager - WARNING - لم يتم العثور على وحدة MainInventoryWindow: No module named 'ui.inventory'
2025-07-22 01:04:33,711 - ui.interface_manager - ERROR - خطأ في تحميل واجهة المخزون الرئيسي: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:34,566 - ui.interface_manager - WARNING - لم يتم العثور على وحدة MainInventoryWindow: No module named 'ui.inventory'
2025-07-22 01:04:34,594 - ui.interface_manager - ERROR - خطأ في تحميل واجهة المخزون الرئيسي: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:35,713 - ui.interface_manager - WARNING - لم يتم العثور على وحدة WorkersInventoryWindow: No module named 'ui.inventory'
2025-07-22 01:04:35,717 - ui.interface_manager - ERROR - خطأ في تحميل واجهة مخزون العمال: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:37,082 - ui.interface_manager - WARNING - لم يتم العثور على وحدة RenewSubscriptionWindow: No module named 'ui.subscribers'
2025-07-22 01:04:37,098 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تجديد الاشتراك: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:38,192 - ui.interface_manager - WARNING - لم يتم العثور على وحدة RouterDeliveryWindow: No module named 'ui.subscribers'
2025-07-22 01:04:38,194 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تسليم الراوتر: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:46,718 - ui.interface_manager - WARNING - لم يتم العثور على وحدة UsersManagementWindow: No module named 'ui.system'
2025-07-22 01:04:46,727 - ui.interface_manager - ERROR - خطأ في تحميل واجهة إدارة المستخدمين: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:48,107 - ui.interface_manager - WARNING - لم يتم العثور على وحدة UsersManagementWindow: No module named 'ui.system'
2025-07-22 01:04:48,116 - ui.interface_manager - ERROR - خطأ في تحميل واجهة إدارة المستخدمين: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:50,744 - ui.interface_manager - WARNING - لم يتم العثور على وحدة ActivityLogWindow: No module named 'ui.system'
2025-07-22 01:04:50,750 - ui.interface_manager - ERROR - خطأ في تحميل واجهة سجل العمليات: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:52,411 - ui.interface_manager - WARNING - لم يتم العثور على وحدة SystemSettingsWindow: No module named 'ui.system'
2025-07-22 01:04:52,419 - ui.interface_manager - ERROR - خطأ في تحميل واجهة إعدادات النظام: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:04:54,080 - ui.interface_manager - WARNING - لم يتم العثور على وحدة BackupWindow: No module named 'ui.system'
2025-07-22 01:04:54,081 - ui.interface_manager - ERROR - خطأ في تحميل واجهة النسخ الاحتياطي: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:05:02,293 - ui.interface_manager - WARNING - لم يتم العثور على وحدة PerformanceAnalysisWindow: No module named 'ui.reports'
2025-07-22 01:05:02,308 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تحليل الأداء: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:05:04,563 - ui.interface_manager - WARNING - لم يتم العثور على وحدة SalesReportsWindow: No module named 'ui.reports'
2025-07-22 01:05:04,564 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تقارير المبيعات: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:05:21,543 - ui.interface_manager - WARNING - لم يتم العثور على وحدة RenewSubscriptionWindow: No module named 'ui.subscribers'
2025-07-22 01:05:21,545 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تجديد الاشتراك: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:05:24,984 - database.database_manager - INFO - تم إنشاء نسخة احتياطية: c:\Users\<USER>\Desktop\التعديل\clean_isp_manager\backups\backup_20250722_010524.db
2025-07-22 01:05:48,981 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:05:49,007 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:05:49,012 - __main__ - INFO - تم تنظيف الموارد
2025-07-22 01:06:14,864 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:06:14,877 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:06:15,675 - __main__ - INFO - تم عرض شاشة البداية
2025-07-22 01:06:15,676 - __main__ - INFO - جاري تهيئة قاعدة البيانات...
2025-07-22 01:06:15,680 - database.database_manager - INFO - تم الاتصال بقاعدة البيانات بنجاح
2025-07-22 01:06:15,684 - database.database_manager - INFO - تم إنشاء جداول قاعدة البيانات بنجاح
2025-07-22 01:06:15,686 - database.database_manager - INFO - تم إدراج البيانات الافتراضية بنجاح
2025-07-22 01:06:15,686 - __main__ - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-07-22 01:06:15,701 - __main__ - INFO - عرض نافذة تسجيل الدخول
2025-07-22 01:06:15,733 - ui.login_window - INFO - تم تشغيل نافذة تسجيل الدخول
2025-07-22 01:09:19,292 - ui.login_window - INFO - تسجيل دخول ناجح للمستخدم: admin
2025-07-22 01:09:19,321 - ui.interface_manager - INFO - تم تحميل 5 فئة واجهة
2025-07-22 01:09:19,816 - ui.main_window - INFO - تم تشغيل الواجهة الرئيسية للمستخدم: المدير العام
2025-07-22 01:09:19,849 - __main__ - ERROR - خطأ في عرض نافذة تسجيل الدخول: type object 'QSplashScreen' has no attribute 'Accepted'
2025-07-22 01:09:25,579 - __main__ - INFO - إنهاء التطبيق
2025-07-22 01:09:27,900 - ui.interface_manager - WARNING - لم يتم العثور على وحدة TreasuryTransferWindow: No module named 'ui.financial'
2025-07-22 01:09:27,901 - ui.interface_manager - ERROR - خطأ في تحميل واجهة نقل الخزينة: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:09:41,498 - ui.interface_manager - WARNING - لم يتم العثور على وحدة WorkersInventoryWindow: No module named 'ui.inventory'
2025-07-22 01:09:41,499 - ui.interface_manager - ERROR - خطأ في تحميل واجهة مخزون العمال: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:09:43,607 - ui.interface_manager - WARNING - لم يتم العثور على وحدة MainInventoryWindow: No module named 'ui.inventory'
2025-07-22 01:09:43,608 - ui.interface_manager - ERROR - خطأ في تحميل واجهة المخزون الرئيسي: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:09:45,087 - ui.interface_manager - WARNING - لم يتم العثور على وحدة InventoryReportsWindow: No module named 'ui.inventory'
2025-07-22 01:09:45,090 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تقارير المخزون: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:09:52,016 - ui.interface_manager - WARNING - لم يتم العثور على وحدة SystemSettingsWindow: No module named 'ui.system'
2025-07-22 01:09:52,017 - ui.interface_manager - ERROR - خطأ في تحميل واجهة إعدادات النظام: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:09:58,465 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:09:58,497 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:09:58,512 - __main__ - INFO - تم تنظيف الموارد
2025-07-22 01:17:01,663 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:17:01,671 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:17:03,059 - __main__ - INFO - تم عرض شاشة البداية
2025-07-22 01:17:03,059 - __main__ - INFO - جاري تهيئة قاعدة البيانات...
2025-07-22 01:17:03,070 - database.database_manager - INFO - تم الاتصال بقاعدة البيانات بنجاح
2025-07-22 01:17:03,077 - database.database_manager - INFO - تم إنشاء جداول قاعدة البيانات بنجاح
2025-07-22 01:17:03,081 - database.database_manager - INFO - تم إدراج البيانات الافتراضية بنجاح
2025-07-22 01:17:03,082 - __main__ - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-07-22 01:17:03,083 - __main__ - INFO - عرض نافذة تسجيل الدخول
2025-07-22 01:17:03,131 - ui.login_window - INFO - تم تشغيل نافذة تسجيل الدخول
2025-07-22 01:17:09,151 - ui.login_window - INFO - تسجيل دخول ناجح للمستخدم: admin
2025-07-22 01:17:09,178 - ui.interface_manager - INFO - تم تحميل 5 فئة واجهة
2025-07-22 01:17:09,719 - ui.main_window - INFO - تم تشغيل الواجهة الرئيسية للمستخدم: المدير العام
2025-07-22 01:17:09,753 - __main__ - ERROR - خطأ في عرض نافذة تسجيل الدخول: type object 'QSplashScreen' has no attribute 'Accepted'
2025-07-22 01:17:11,950 - __main__ - INFO - إنهاء التطبيق
2025-07-22 01:17:18,388 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:17:18,422 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:17:18,487 - __main__ - INFO - تم تنظيف الموارد
2025-07-22 01:42:47,239 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:42:47,240 - __main__ - INFO - بدء تشغيل التطبيق
2025-07-22 01:42:48,717 - __main__ - INFO - تم عرض شاشة البداية
2025-07-22 01:42:48,718 - __main__ - INFO - جاري تهيئة قاعدة البيانات...
2025-07-22 01:42:48,726 - database.database_manager - INFO - تم الاتصال بقاعدة البيانات بنجاح
2025-07-22 01:42:48,732 - database.database_manager - INFO - تم إنشاء جداول قاعدة البيانات بنجاح
2025-07-22 01:42:48,734 - database.database_manager - INFO - تم إدراج البيانات الافتراضية بنجاح
2025-07-22 01:42:48,740 - __main__ - INFO - تم تهيئة قاعدة البيانات بنجاح
2025-07-22 01:42:48,740 - __main__ - INFO - عرض نافذة تسجيل الدخول
2025-07-22 01:42:48,788 - ui.login_window - INFO - تم تشغيل نافذة تسجيل الدخول
2025-07-22 01:42:52,230 - ui.login_window - INFO - تسجيل دخول ناجح للمستخدم: admin
2025-07-22 01:42:52,248 - ui.interface_manager - INFO - تم تحميل 5 فئة واجهة
2025-07-22 01:42:52,811 - ui.main_window - INFO - تم تشغيل الواجهة الرئيسية للمستخدم: المدير العام
2025-07-22 01:42:52,849 - __main__ - ERROR - خطأ في عرض نافذة تسجيل الدخول: type object 'QSplashScreen' has no attribute 'Accepted'
2025-07-22 01:42:55,590 - __main__ - INFO - إنهاء التطبيق
2025-07-22 01:42:58,764 - ui.interface_manager - WARNING - لم يتم العثور على وحدة DailyTreasuryWindow: No module named 'ui.financial.dailytreasurywindow'
2025-07-22 01:42:58,835 - ui.interface_manager - ERROR - خطأ في تحميل واجهة الخزينة اليومية: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:01,815 - ui.interface_manager - WARNING - لم يتم العثور على وحدة NewSubscriptionWindow: No module named 'ui.subscribers'
2025-07-22 01:43:01,816 - ui.interface_manager - ERROR - خطأ في تحميل واجهة اشتراك جديد: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:03,495 - ui.interface_manager - WARNING - لم يتم العثور على وحدة RenewSubscriptionWindow: No module named 'ui.subscribers'
2025-07-22 01:43:03,496 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تجديد الاشتراك: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:04,776 - ui.interface_manager - WARNING - لم يتم العثور على وحدة RouterDeliveryWindow: No module named 'ui.subscribers'
2025-07-22 01:43:04,777 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تسليم الراوتر: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:06,957 - ui.interface_manager - WARNING - لم يتم العثور على وحدة AddProductsWindow: No module named 'ui.inventory'
2025-07-22 01:43:06,958 - ui.interface_manager - ERROR - خطأ في تحميل واجهة إضافة منتجات: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:20,881 - ui.interface_manager - WARNING - لم يتم العثور على وحدة SubscribersReportsWindow: No module named 'ui.reports'
2025-07-22 01:43:20,882 - ui.interface_manager - ERROR - خطأ في تحميل واجهة تقارير المشتركين: InterfaceManager._create_placeholder_interface.<locals>.PlaceholderInterface.__init__() takes from 1 to 2 positional arguments but 4 were given
2025-07-22 01:43:26,198 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:43:26,249 - database.database_manager - INFO - تم إغلاق الاتصال بقاعدة البيانات
2025-07-22 01:43:26,306 - __main__ - INFO - تم تنظيف الموارد

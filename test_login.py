#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول
"""

import sqlite3
import hashlib

def test_login():
    """اختبار تسجيل الدخول"""
    print("=== اختبار تسجيل الدخول ===")
    
    db_path = "data/company_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # بيانات تسجيل الدخول
        username = "admin"
        password = "admin123"
        
        print(f"🔐 محاولة تسجيل الدخول:")
        print(f"   اسم المستخدم: {username}")
        print(f"   كلمة المرور: {password}")
        
        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"   كلمة المرور المشفرة: {hashed_password[:20]}...")
        
        # البحث عن المستخدم
        cursor.execute("""
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, hashed_password))
        
        user = cursor.fetchone()
        
        if user:
            print("✅ تسجيل الدخول نجح!")
            print(f"   ID: {user['id']}")
            print(f"   الاسم الكامل: {user['full_name']}")
            print(f"   الدور: {user['role']}")
            print(f"   نشط: {user['is_active']}")
            
            # تحديث وقت آخر تسجيل دخول
            cursor.execute("""
                UPDATE users 
                SET last_login = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (user['id'],))
            
            conn.commit()
            print("✅ تم تحديث وقت آخر تسجيل دخول")
            
        else:
            print("❌ فشل تسجيل الدخول!")
            
            # فحص تفصيلي
            print("\n🔍 فحص تفصيلي:")
            
            # البحث بالاسم فقط
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            user_by_name = cursor.fetchone()
            
            if user_by_name:
                print(f"✅ المستخدم موجود: {user_by_name['username']}")
                print(f"   كلمة المرور في قاعدة البيانات: {user_by_name['password_hash'][:20]}...")
                print(f"   كلمة المرور المدخلة (مشفرة): {hashed_password[:20]}...")
                
                if user_by_name['password_hash'] == hashed_password:
                    print("✅ كلمة المرور صحيحة")
                else:
                    print("❌ كلمة المرور خاطئة")
                
                if user_by_name['is_active']:
                    print("✅ المستخدم نشط")
                else:
                    print("❌ المستخدم غير نشط")
                    
            else:
                print("❌ المستخدم غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_login()

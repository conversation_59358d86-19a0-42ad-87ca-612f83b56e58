#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح واجهة إدارة الوحدات
"""

import sys
import os
sys.path.append('src')

def test_units_management_window():
    """اختبار واجهة إدارة الوحدات"""
    
    print("🧪 اختبار واجهة إدارة الوحدات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from ui.units_management_window import UnitsManagementWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء الواجهة
        window = UnitsManagementWindow(db, inventory_manager, current_user)
        
        print("✅ تم إنشاء واجهة إدارة الوحدات بنجاح")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بنجاح")
        
        # اختبار وجود الجدول
        if hasattr(window, 'products_table') and window.products_table is not None:
            print("✅ جدول المنتجات موجود")
            
            # اختبار عدد الصفوف
            row_count = window.products_table.rowCount()
            print(f"📊 عدد المنتجات في الجدول: {row_count}")
            
            if row_count > 0:
                print("✅ يوجد منتجات في الجدول")
                
                # عرض بعض المنتجات
                for row in range(min(3, row_count)):
                    product_name = window.products_table.item(row, 1).text()
                    category = window.products_table.item(row, 2).text()
                    purchase_unit = window.products_table.item(row, 3).text()
                    sale_unit = window.products_table.item(row, 4).text()
                    conversion_factor = window.products_table.item(row, 5).text()
                    
                    print(f"  • {product_name} ({category})")
                    print(f"    شراء: {purchase_unit} | بيع: {sale_unit} | تحويل: {conversion_factor}")
            else:
                print("⚠️ لا توجد منتجات في الجدول")
        else:
            print("❌ جدول المنتجات غير موجود")
            return False
        
        # اختبار وجود العناصر الأخرى
        if hasattr(window, 'product_combo') and window.product_combo is not None:
            print("✅ قائمة المنتجات موجودة")
            combo_count = window.product_combo.count()
            print(f"📊 عدد المنتجات في القائمة: {combo_count - 1}")  # -1 للعنصر الأول "اختر منتج"
        else:
            print("❌ قائمة المنتجات غير موجودة")
            return False
        
        # اختبار الحقول
        required_fields = ['purchase_unit_edit', 'sale_unit_edit', 'conversion_factor_spin']
        for field in required_fields:
            if hasattr(window, field) and getattr(window, field) is not None:
                print(f"✅ حقل {field} موجود")
            else:
                print(f"❌ حقل {field} غير موجود")
                return False
        
        # اختبار الأزرار
        required_buttons = ['update_units_btn', 'reset_btn', 'close_btn']
        for button in required_buttons:
            if hasattr(window, button) and getattr(window, button) is not None:
                print(f"✅ زر {button} موجود")
            else:
                print(f"❌ زر {button} غير موجود")
                return False
        
        print("✅ جميع عناصر الواجهة موجودة وتعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إدارة الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_units_functionality():
    """اختبار وظائف إدارة الوحدات"""
    
    print("\n🧪 اختبار وظائف إدارة الوحدات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        # إضافة منتج تجريبي للاختبار
        result = db.execute_query("""
            INSERT OR IGNORE INTO unified_products 
            (name, category, purchase_unit, sale_unit, conversion_factor,
             purchase_price, sale_price, current_stock, min_stock, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "منتج اختبار الوحدات", "اختبار", "قطعة", "قطعة", 1.0,
            1000, 1500, 10, 2, 1
        ))
        
        if result:
            product_id = result.lastrowid
            print(f"✅ تم إضافة منتج تجريبي - ID: {product_id}")
            
            # اختبار تحديث الوحدات
            db.execute_query("""
                UPDATE unified_products 
                SET purchase_unit = ?, sale_unit = ?, conversion_factor = ?
                WHERE id = ?
            """, ("كرتونة", "قطعة", 12.0, product_id))
            
            # التحقق من التحديث
            updated_product = db.fetch_one("""
                SELECT purchase_unit, sale_unit, conversion_factor 
                FROM unified_products WHERE id = ?
            """, (product_id,))
            
            if updated_product:
                print(f"✅ تم تحديث الوحدات:")
                print(f"  • وحدة الشراء: {updated_product['purchase_unit']}")
                print(f"  • وحدة البيع: {updated_product['sale_unit']}")
                print(f"  • معامل التحويل: {updated_product['conversion_factor']}")
                
                # تنظيف البيانات التجريبية
                db.execute_query("DELETE FROM unified_products WHERE id = ?", (product_id,))
                print("🗑️ تم حذف المنتج التجريبي")
                
                return True
            else:
                print("❌ فشل في تحديث الوحدات")
                return False
        else:
            print("❌ فشل في إضافة المنتج التجريبي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح واجهة إدارة الوحدات")
    print("=" * 50)
    
    # اختبار الواجهة
    window_test = test_units_management_window()
    
    # اختبار الوظائف
    functionality_test = test_units_functionality()
    
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • واجهة إدارة الوحدات: {'✅ نجح' if window_test else '❌ فشل'}")
    print(f"  • وظائف إدارة الوحدات: {'✅ نجح' if functionality_test else '❌ فشل'}")
    
    if all([window_test, functionality_test]):
        print("\n🎉 تم إصلاح واجهة إدارة الوحدات بنجاح!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح مشكلة setRowCount")
        print("  ✅ إنشاء واجهة جديدة للتحكم في وحدات المنتجات")
        print("  ✅ ربط الواجهة بالنظام الموحد")
        print("  ✅ عرض جدول المنتجات مع وحداتها")
        print("  ✅ إمكانية تحديث وحدات الشراء والبيع")
        print("  ✅ تحديد معامل التحويل")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'إدارة الوحدات' من قائمة الإدارة")
        print("  3. اختر منتج من القائمة المنسدلة")
        print("  4. حدد وحدة الشراء (مثل: بكرة، كرتونة)")
        print("  5. حدد وحدة البيع (مثل: متر، قطعة)")
        print("  6. حدد معامل التحويل (مثل: 100 متر لكل بكرة)")
        print("  7. اضغط 'تحديث الوحدات'")
        print("  8. التغييرات ستنعكس على جميع الواجهات")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

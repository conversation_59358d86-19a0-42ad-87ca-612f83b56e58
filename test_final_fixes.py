#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات النهائية
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager

def test_router_type_id():
    """اختبار إضافة router_type_id"""
    print("=== اختبار router_type_id ===")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # التحقق من وجود العمود
    try:
        result = db_manager.fetch_one("PRAGMA table_info(subscribers)")
        print("أعمدة جدول subscribers:")
        
        columns = db_manager.fetch_all("PRAGMA table_info(subscribers)")
        for col in columns:
            print(f"  • {col[1]} - {col[2]}")
            
        # اختبار إضافة مشترك جديد مع router_type_id
        print("\n--- اختبار إضافة مشترك مع router_type_id ---")
        
        # جلب راوتر للاختبار
        router = db_manager.fetch_one("""
            SELECT id, name FROM products WHERE category = 'router' LIMIT 1
        """)
        
        if router:
            print(f"راوتر للاختبار: {router['name']} (ID: {router['id']})")
            
            # إضافة مشترك اختبار
            result = db_manager.execute_query("""
                INSERT INTO subscribers (name, phone, router_type, router_type_id, created_by)
                VALUES (?, ?, ?, ?, ?)
            """, ("مشترك اختبار router_type_id", "123456789", router['name'], router['id'], "admin"))
            
            if result:
                print("✅ تم إضافة مشترك مع router_type_id بنجاح")
                
                # جلب المشترك للتأكد
                subscriber = db_manager.fetch_one("""
                    SELECT s.*, p.name as router_product_name 
                    FROM subscribers s
                    LEFT JOIN products p ON s.router_type_id = p.id
                    WHERE s.name = ?
                """, ("مشترك اختبار router_type_id",))
                
                if subscriber:
                    print(f"✅ المشترك: {subscriber['name']}")
                    print(f"✅ نوع الراوتر: {subscriber['router_type']}")
                    print(f"✅ ID نوع الراوتر: {subscriber['router_type_id']}")
                    print(f"✅ اسم المنتج: {subscriber['router_product_name']}")
                else:
                    print("❌ لم يتم العثور على المشترك")
            else:
                print("❌ فشل في إضافة المشترك")
        else:
            print("❌ لا يوجد راوترات في قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_sales_calculation():
    """اختبار حساب المبيعات"""
    print("\n=== اختبار حساب المبيعات ===")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    try:
        # حساب المبيعات للمستخدم admin
        sales = db_manager.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total FROM transactions
            WHERE DATE(created_at) = DATE('now') AND user_name = ? 
            AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') 
            AND amount > 0
        """, ("admin",))
        
        print(f"إجمالي مبيعات admin اليوم: {sales['total'] if sales else 0} ل.س")
        
        # تفصيل المبيعات
        sales_detail = db_manager.fetch_all("""
            SELECT type, COUNT(*) as count, SUM(amount) as total FROM transactions
            WHERE DATE(created_at) = DATE('now') AND user_name = ? 
            AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') 
            AND amount > 0
            GROUP BY type
        """, ("admin",))
        
        for detail in sales_detail:
            print(f"  • {detail['type']}: {detail['count']} عملية - {detail['total']:,} ل.س")
            
    except Exception as e:
        print(f"❌ خطأ في حساب المبيعات: {e}")

def test_expenses_calculation():
    """اختبار حساب المصاريف"""
    print("\n=== اختبار حساب المصاريف ===")
    
    db_manager = DatabaseManager()
    db_manager.connect()
    
    try:
        # حساب المصاريف للمستخدم admin
        expenses = db_manager.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total FROM expenses
            WHERE DATE(expense_date) = DATE('now') AND user_name = ? 
            AND expense_type NOT IN ('راتب', 'رواتب')
        """, ("admin",))
        
        print(f"إجمالي مصاريف admin اليوم: {expenses['total'] if expenses else 0} ل.س")
        
        # تفصيل المصاريف
        expenses_detail = db_manager.fetch_all("""
            SELECT expense_type, COUNT(*) as count, SUM(amount) as total FROM expenses
            WHERE DATE(expense_date) = DATE('now') AND user_name = ? 
            AND expense_type NOT IN ('راتب', 'رواتب')
            GROUP BY expense_type
        """, ("admin",))
        
        for detail in expenses_detail:
            print(f"  • {detail['expense_type']}: {detail['count']} مصروف - {detail['total']:,} ل.س")
            
    except Exception as e:
        print(f"❌ خطأ في حساب المصاريف: {e}")

if __name__ == "__main__":
    test_router_type_id()
    test_sales_calculation()
    test_expenses_calculation()
    print("\n🎉 انتهى الاختبار!")

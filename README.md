# 🌐 نظام إدارة شركة الإنترنت - النسخة النهائية

## 📋 نظرة عامة

نظام إدارة شامل ومتقدم لشركات الإنترنت مطور بـ Python و PyQt5 مع دعم كامل للغة العربية. يوفر النظام جميع الأدوات اللازمة لإدارة المشتركين، المنتجات، العمال، الموردين، والعمليات المالية.

## ✨ المميزات الرئيسية

### 🔐 **إدارة المستخدمين والأمان**
- نظام تسجيل دخول آمن مع تشفير كلمات المرور
- إدارة المستخدمين مع أدوار مختلفة (مدير، محاسب، موظف)
- نظام صلاحيات متقدم لكل مستخدم

### 👥 **إدارة المشتركين**
- إضافة وتعديل بيانات المشتركين
- إدارة الاشتراكات والباقات
- تجديد الاشتراكات وتتبع تواريخ الانتهاء
- نظام إشعارات للاشتراكات المنتهية

### 💰 **النظام المالي المتقدم**
- إدارة الخزينة بعملتين (ليرة سورية ودولار أمريكي)
- نظام صرف العملات مع أسعار قابلة للتعديل
- نقل الأموال بين الخزائن
- تتبع جميع المعاملات المالية
- إغلاق الصندوق اليومي

### 📦 **إدارة المخزون والمنتجات**
- إدارة شاملة للمنتجات والمخزون
- تتبع كميات المنتجات
- تنبيهات المخزون المنخفض
- إدارة المشتريات من الموردين

### 👷 **إدارة العمال والموزعين**
- إدارة بيانات العمال ومناطق عملهم
- حساب العمولات والمكافآت
- تتبع تسليمات الراوترات
- شحن أرصدة الموزعين

### 📊 **التقارير المتقدمة (16 تقرير)**
- **التقارير المالية:** الإيرادات، الملخص الشهري، التدفق النقدي، الأرباح والخسائر
- **تقارير المشتركين:** الجدد، النشطين، المنتهية، الباقات
- **تقارير المخزون:** الحالي، المنخفض، الحركة، مخزون العمال
- **تقارير العمال:** الأداء، العمولات، التسليمات، المناطق

### 🔔 **نظام الإشعارات الذكي**
- إشعارات فورية للاشتراكات المنتهية
- تنبيهات المخزون المنخفض
- إشعارات المشتركين الجدد
- عداد إشعارات في الواجهة الرئيسية

### 🖨️ **نظام الطباعة المتقدم**
- دعم جميع أنواع الطابعات (عادية وحرارية)
- إعدادات طباعة شاملة (حجم الورق، الاتجاه، الجودة)
- طباعة النصوص العربية بوضوح
- اختبار الطباعة مع صفحة تجريبية

### 💾 **النسخ الاحتياطي**
- إنشاء نسخ احتياطية تلقائية
- استعادة النسخ الاحتياطية
- حماية البيانات من الفقدان

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة:
- Python 3.7 أو أحدث
- PyQt5
- SQLite3 (مدمج مع Python)

### المكتبات المطلوبة:
```bash
pip install PyQt5
```

## 🚀 التشغيل

### 1. تحميل المشروع:
```bash
git clone [repository-url]
cd internet-company-management
```

### 2. تشغيل النظام:
```bash
python system_launcher.py
```

### 3. بيانات الدخول الافتراضية:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 📁 هيكل المشروع

```
internet-company-management/
├── src/
│   ├── database/
│   │   └── database_manager.py      # إدارة قاعدة البيانات
│   ├── ui/
│   │   ├── main_window.py           # النافذة الرئيسية
│   │   ├── login_window.py          # نافذة تسجيل الدخول
│   │   ├── subscribers_management.py # إدارة المشتركين
│   │   ├── products_management.py   # إدارة المنتجات
│   │   ├── workers_management.py    # إدارة العمال
│   │   ├── suppliers_management.py  # إدارة الموردين
│   │   ├── users_management.py      # إدارة المستخدمين
│   │   ├── reports_window.py        # التقارير
│   │   ├── settings_window.py       # الإعدادات
│   │   ├── currency_exchange.py     # صرف العملات
│   │   ├── treasury_transfer.py     # نقل الخزينة
│   │   ├── notifications_window.py  # الإشعارات
│   │   └── [other UI files]
│   ├── utils/
│   │   ├── arabic_support.py        # دعم اللغة العربية
│   │   └── config_manager.py        # إدارة الإعدادات
│   └── operations/
│       └── [operation files]        # العمليات المختلفة
├── data/                            # مجلد البيانات
├── system_launcher.py               # ملف التشغيل الرئيسي
└── README.md                        # هذا الملف
```

## 🗄️ قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع 19 جدول:

### الجداول الأساسية:
- `users` - المستخدمين والصلاحيات
- `subscribers` - المشتركين
- `packages` - الباقات
- `products` - المنتجات
- `inventory` - المخزون
- `workers` - العمال
- `suppliers` - الموردين
- `transactions` - المعاملات المالية

### الجداول المتقدمة:
- `treasury` - إدارة الخزائن
- `currency_exchange` - صرف العملات
- `treasury_transfers` - نقل الخزينة
- `notifications` - الإشعارات
- `settings` - الإعدادات

## 🎯 الاستخدام

### البدء السريع:
1. شغل النظام باستخدام `python system_launcher.py`
2. سجل الدخول بالبيانات الافتراضية
3. استكشف الواجهات المختلفة من النافذة الرئيسية
4. ابدأ بإضافة المشتركين والمنتجات
5. استخدم التقارير لمتابعة الأداء

### الميزات المتقدمة:
- استخدم نظام الإشعارات لمتابعة الاشتراكات المنتهية
- اطبع التقارير والفواتير باستخدام نظام الطباعة المتقدم
- أنشئ نسخ احتياطية دورية لحماية البيانات
- استخدم نظام صرف العملات لإدارة الخزينة

## 🔧 التخصيص

### إعدادات النظام:
- انتقل إلى "الإعدادات" من النافذة الرئيسية
- خصص إعدادات الطباعة حسب احتياجاتك
- اضبط إعدادات الإشعارات
- حدد فترات النسخ الاحتياطي التلقائي

### إضافة مستخدمين جدد:
- استخدم واجهة "إدارة المستخدمين"
- حدد الصلاحيات المناسبة لكل مستخدم
- اختر الدور المناسب (مدير، محاسب، موظف)

## 🛡️ الأمان

- جميع كلمات المرور مشفرة باستخدام SHA-256
- نظام صلاحيات متقدم لحماية البيانات الحساسة
- نسخ احتياطية آمنة لحماية البيانات
- تسجيل جميع العمليات المالية

## 🌐 دعم اللغة العربية

- واجهات عربية كاملة مع خطوط واضحة
- دعم الكتابة من اليمين إلى اليسار
- تنسيق العملة العربية
- طباعة النصوص العربية بجودة عالية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملفات التوثيق في المشروع
- تحقق من ملفات السجلات في مجلد البيانات
- استخدم ميزة اختبار الطباعة للتأكد من إعدادات الطابعة

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يرجى مراجعة ملف الترخيص للمزيد من التفاصيل.

---

## 🎉 **النظام مكتمل ومتقدم!**

**نظام إدارة شركة الإنترنت يوفر حلاً شاملاً ومتقدماً لجميع احتياجات إدارة شركات الإنترنت مع واجهات عربية حديثة وميزات متطورة.**

**🚀 جاهز للاستخدام الفوري في بيئة الإنتاج! 🚀**

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة تسجيل الدخول
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
src_path = project_root / "src"

sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# تجاهل تحذيرات Qt
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

def debug_login():
    """تشخيص مشكلة تسجيل الدخول"""
    print("=== تشخيص مشكلة تسجيل الدخول ===")
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog
        from PyQt5.QtCore import Qt
        print("✅ PyQt5")
        
        # استيراد وحدات النظام
        from src.database.database_manager import DatabaseManager
        from src.utils.arabic_support import setup_arabic_support
        from src.ui.login_window import LoginWindow
        print("✅ وحدات النظام")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        print("✅ الدعم العربي")
        
        # إنشاء قاعدة البيانات
        data_dir = project_root / "data"
        db_manager = DatabaseManager(str(data_dir))
        print("✅ قاعدة البيانات")
        
        # اختبار قاعدة البيانات أولاً
        print("\n🔍 اختبار قاعدة البيانات:")
        try:
            users = db_manager.fetch_all("SELECT * FROM users")
            print(f"   عدد المستخدمين: {len(users)}")
            for user in users:
                print(f"   - {user['username']} ({user['full_name']}) - نشط: {user['is_active']}")
        except Exception as db_error:
            print(f"   ❌ خطأ في قاعدة البيانات: {db_error}")
            return 1
        
        # عرض نافذة تسجيل الدخول
        print("\n🔐 فتح نافذة تسجيل الدخول...")
        print("📋 استخدم البيانات التالية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 50)
        
        login_window = LoginWindow(db_manager)
        
        # تشغيل نافذة تسجيل الدخول
        result = login_window.exec_()
        
        if result == QDialog.Accepted:
            current_user = login_window.get_current_user()
            print(f"\n✅ تم تسجيل الدخول بنجاح!")
            print(f"   مرحباً {current_user['full_name']}")
            print(f"   الدور: {current_user['role']}")
            
            # عرض رسالة نجاح
            QMessageBox.information(
                None, 
                "نجح تسجيل الدخول", 
                f"مرحباً {current_user['full_name']}\nتم تسجيل الدخول بنجاح!"
            )
            
            return 0
            
        else:
            print("\n❌ تم إلغاء تسجيل الدخول أو فشل")
            return 1
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        result = debug_login()
        print(f"\nانتهى البرنامج بالكود: {result}")
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def update_currencies():
    print("تحديث العملات...")
    
    db = DatabaseManager('data/company_system.db')
    
    # إضافة عملات تجريبية
    currencies = [
        ('USD', 500.0, 15000.0),
        ('EUR', 300.0, 16000.0)
    ]
    
    for currency_type, balance, exchange_rate in currencies:
        try:
            # التحقق من وجود العملة
            existing = db.fetch_one("SELECT * FROM treasury WHERE currency_type = ?", (currency_type,))
            
            if existing:
                # تحديث الرصيد
                db.execute_query("""
                    UPDATE treasury 
                    SET balance = ?, exchange_rate = ?
                    WHERE currency_type = ?
                """, (balance, exchange_rate, currency_type))
                print(f"تم تحديث {currency_type}: {balance}")
            else:
                # إضافة عملة جديدة
                db.execute_query("""
                    INSERT INTO treasury (currency_type, balance, exchange_rate)
                    VALUES (?, ?, ?)
                """, (currency_type, balance, exchange_rate))
                print(f"تم إضافة {currency_type}: {balance}")
                
        except Exception as e:
            print(f"خطأ في {currency_type}: {e}")
    
    # عرض النتائج
    all_currencies = db.fetch_all("SELECT * FROM treasury")
    print("\nالعملات الحالية:")
    for curr in all_currencies:
        print(f"  {curr}")
    
    print("تم الانتهاء!")

if __name__ == "__main__":
    update_currencies()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نقل الصناديق المغلقة للنظام الموحد
"""

import sys
import os
sys.path.append('src')

def find_closed_boxes_source():
    """البحث عن مصدر الصناديق المغلقة"""
    
    print("🔍 البحث عن مصدر الصناديق المغلقة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص الجداول المتاحة
        tables = db.fetch_all("""
            SELECT name FROM sqlite_master WHERE type='table' 
            AND name LIKE '%cash%' OR name LIKE '%box%' OR name LIKE '%shift%'
            ORDER BY name
        """)
        
        print(f"📊 الجداول المتاحة المتعلقة بالصناديق:")
        for table in tables:
            print(f"  • {table['name']}")
        
        # فحص كل جدول للعثور على الصناديق المغلقة
        closed_boxes_data = []
        
        for table in tables:
            table_name = table['name']
            try:
                print(f"\n🔍 فحص جدول {table_name}:")
                
                # الحصول على أعمدة الجدول
                columns = db.fetch_all(f"PRAGMA table_info({table_name})")
                column_names = [col['name'] for col in columns]
                print(f"  • الأعمدة: {', '.join(column_names)}")
                
                # البحث عن الصناديق المغلقة
                if 'is_closed' in column_names:
                    # جدول cash_boxes
                    closed_records = db.fetch_all(f"""
                        SELECT * FROM {table_name} WHERE is_closed = 1 LIMIT 5
                    """)
                    
                    if closed_records:
                        print(f"  ✅ تم العثور على {len(closed_records)} صندوق مغلق")
                        for record in closed_records:
                            # تحويل sqlite3.Row إلى dict
                            record_dict = dict(record)

                            # البحث عن عمود المبلغ
                            amount = 0
                            for col in ['closing_balance', 'net_amount', 'total_amount', 'balance']:
                                if col in record_dict and record_dict[col]:
                                    amount = record_dict[col]
                                    break

                            closed_boxes_data.append({
                                'source': table_name,
                                'id': record_dict.get('id'),
                                'user_id': record_dict.get('user_id'),
                                'amount': amount,
                                'date': record_dict.get('closed_at', record_dict.get('created_at', '2025-01-20')),
                                'record': record_dict
                            })
                    else:
                        print(f"  ⚠️ لا توجد صناديق مغلقة")
                        
                elif 'status' in column_names:
                    # جدول shifts
                    closed_records = db.fetch_all(f"""
                        SELECT * FROM {table_name} WHERE status = 'closed' LIMIT 5
                    """)
                    
                    if closed_records:
                        print(f"  ✅ تم العثور على {len(closed_records)} شيفت مغلق")
                        for record in closed_records:
                            # تحويل sqlite3.Row إلى dict
                            record_dict = dict(record)

                            # حساب الصافي
                            sales = record_dict.get('total_sales', 0) or 0
                            expenses = record_dict.get('total_expenses', 0) or 0
                            net_amount = sales - expenses

                            closed_boxes_data.append({
                                'source': table_name,
                                'id': record_dict.get('id'),
                                'user_id': record_dict.get('user_id'),
                                'amount': net_amount,
                                'date': record_dict.get('closed_at', record_dict.get('shift_date', '2025-01-20')),
                                'record': record_dict
                            })
                    else:
                        print(f"  ⚠️ لا توجد شيفتات مغلقة")
                else:
                    print(f"  ⚠️ لا يحتوي على صناديق مغلقة")
                    
            except Exception as table_error:
                print(f"  ❌ خطأ في فحص {table_name}: {table_error}")
        
        return closed_boxes_data
        
    except Exception as e:
        print(f"❌ خطأ في البحث عن الصناديق المغلقة: {e}")
        return []

def migrate_found_closed_boxes(closed_boxes_data):
    """نقل الصناديق المغلقة المكتشفة للنظام الموحد"""
    
    if not closed_boxes_data:
        print("⚠️ لا توجد صناديق مغلقة لنقلها")
        return False
    
    print(f"\n🔄 نقل {len(closed_boxes_data)} صندوق مغلق للنظام الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        successful_migrations = 0
        total_migrated = 0
        
        for box_data in closed_boxes_data:
            try:
                user_id = box_data['user_id']
                amount = box_data['amount']
                date = box_data['date']
                source = box_data['source']
                box_id = box_data['id']
                
                # تنظيف التاريخ
                if isinstance(date, str) and ' ' in date:
                    date = date.split()[0]
                elif not date:
                    date = '2025-01-20'
                
                print(f"  📦 نقل صندوق {box_id} من {source}: مستخدم {user_id}, مبلغ {amount:,} ل.س, تاريخ {date}")
                
                if amount > 0:
                    # فحص إذا كان منقول مسبقاً
                    existing = db.fetch_one("""
                        SELECT id FROM transactions 
                        WHERE description LIKE ? AND amount = ?
                    """, (f"%{source}%{box_id}%", amount))
                    
                    if not existing:
                        # إضافة للخزينة اليومية
                        success = treasury_manager.add_to_daily_treasury(
                            user_id=user_id,
                            currency_type='SYP',
                            amount=amount,
                            date=date
                        )
                        
                        if success:
                            successful_migrations += 1
                            total_migrated += amount
                            
                            # تسجيل العملية
                            db.execute_query("""
                                INSERT INTO transactions (type, description, amount, user_name, created_at)
                                VALUES (?, ?, ?, ?, ?)
                            """, (
                                f"نقل صندوق مغلق من {source}",
                                f"نقل صندوق {box_id} من {source} للنظام الموحد - مبلغ: {amount:,} ل.س",
                                amount,
                                f"user_{user_id}",
                                date + " 23:59:59"
                            ))
                            
                            print(f"    ✅ تم النقل بنجاح")
                        else:
                            print(f"    ❌ فشل في النقل")
                    else:
                        print(f"    ⚠️ منقول مسبقاً")
                else:
                    print(f"    ⚠️ مبلغ صفر أو سالب - تم تجاهله")
                    
            except Exception as box_error:
                print(f"    ❌ خطأ في نقل الصندوق: {box_error}")
        
        print(f"\n📊 نتائج النقل:")
        print(f"  • تم نقل {successful_migrations} صندوق بنجاح")
        print(f"  • إجمالي المبلغ المنقول: {total_migrated:,} ل.س")
        
        return successful_migrations > 0
        
    except Exception as e:
        print(f"❌ خطأ في نقل الصناديق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_after_migration():
    """اختبار الخزينة بعد النقل"""
    
    print("\n🧪 اختبار الخزينة بعد النقل...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1}
        
        # فحص الأرصدة
        daily_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_balance = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الأرصدة بعد النقل:")
        print(f"  • الخزينة اليومية: {daily_balance:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_balance:,} ل.س")
        
        if daily_balance > 0:
            print("✅ الخزينة اليومية تحتوي على رصيد - الواجهات ستعمل")
            return True
        else:
            print("⚠️ الخزينة اليومية لا تزال فارغة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخزينة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح نقل الصناديق المغلقة للنظام الموحد")
    print("=" * 70)
    
    # 1. البحث عن الصناديق المغلقة
    closed_boxes_data = find_closed_boxes_source()
    
    if closed_boxes_data:
        print(f"\n✅ تم العثور على {len(closed_boxes_data)} صندوق مغلق")
        
        # عرض ملخص
        total_amount = sum(box['amount'] for box in closed_boxes_data if box['amount'] > 0)
        print(f"💰 إجمالي المبلغ: {total_amount:,} ل.س")
        
        # 2. نقل الصناديق
        migration_success = migrate_found_closed_boxes(closed_boxes_data)
        
        if migration_success:
            # 3. اختبار النتائج
            test_success = test_treasury_after_migration()
            
            if test_success:
                print("\n🎉 تم إصلاح المشكلة بنجاح!")
                print("\n📋 ما تم إنجازه:")
                print("  ✅ تم العثور على الصناديق المغلقة")
                print("  ✅ تم نقلها للنظام الموحد")
                print("  ✅ الخزينة اليومية تحتوي على رصيد")
                print("  ✅ واجهات شراء الدولار ونقل الخزينة ستعمل")
                
                print("\n🚀 للاستخدام:")
                print("  1. شغل النظام: python system_launcher.py")
                print("  2. سجل دخول: admin / admin123")
                print("  3. اضغط على 'شراء دولار' أو 'نقل الخزينة'")
                print("  4. ستجد الأرصدة متوفرة")
            else:
                print("\n⚠️ تم النقل لكن هناك مشكلة في الاختبار")
        else:
            print("\n❌ فشل في نقل الصناديق")
    else:
        print("\n⚠️ لم يتم العثور على صناديق مغلقة")
        print("💡 قد تحتاج لفتح صندوق جديد أو إضافة رصيد يدوياً")

if __name__ == "__main__":
    main()

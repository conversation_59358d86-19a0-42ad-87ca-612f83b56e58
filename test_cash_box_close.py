#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إغلاق الصندوق بشكل مفصل
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.unified_treasury_manager import UnifiedTreasuryManager

def test_cash_box_close():
    """اختبار إغلاق الصندوق بالتفصيل"""
    
    print("🧪 اختبار إغلاق الصندوق بالتفصيل...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = UnifiedTreasuryManager(db)
    
    user_id = 1
    
    try:
        print("\n=== 1. فحص الجدول قبل البدء ===")
        
        # فحص محتوى الجدول
        all_records = db.fetch_all("SELECT * FROM unified_treasury")
        print(f"📊 عدد السجلات في الجدول: {len(all_records)}")
        
        for record in all_records:
            print(f"  • ID: {record[0]}, المستخدم: {record[1]}, التاريخ: {record[2]}, العملة: {record[3]}")
            print(f"    الرصيد اليومي: {record[4]}, الرصيد الرئيسي: {record[5]}, مفتوح: {record[7]}")
        
        print("\n=== 2. فتح الصندوق ===")
        
        # فتح الصندوق
        open_success = treasury_manager.open_cash_box(user_id=user_id)
        print(f"✅ فتح الصندوق: {'نجح' if open_success else 'فشل'}")
        
        # فحص الجلسة
        is_active = treasury_manager.is_session_active(user_id=user_id)
        print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة'}")
        
        print("\n=== 3. إضافة مبيعات ===")
        
        # إضافة مبيعات
        sales_amount = 100000
        add_success = treasury_manager.add_to_daily_treasury(user_id, 'SYP', sales_amount)
        print(f"✅ إضافة مبيعات: {'نجح' if add_success else 'فشل'}")
        
        # فحص الرصيد
        balance_after_sales = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد بعد المبيعات: {balance_after_sales:,} ل.س")
        
        print("\n=== 4. فحص الجدول بعد المبيعات ===")
        
        # فحص السجلات المحدثة
        updated_records = db.fetch_all("""
            SELECT * FROM unified_treasury 
            WHERE user_id = ? AND is_session_active = 1
        """, (user_id,))
        
        print(f"📊 السجلات المفتوحة للمستخدم {user_id}:")
        for record in updated_records:
            print(f"  • العملة: {record[3]}, الرصيد اليومي: {record[4]}, الرصيد الرئيسي: {record[5]}")
        
        print("\n=== 5. محاولة إغلاق الصندوق ===")
        
        # إغلاق الصندوق
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        print(f"✅ إغلاق الصندوق: {'نجح' if close_success else 'فشل'}")
        
        print("\n=== 6. فحص الجدول بعد الإغلاق ===")
        
        # فحص السجلات بعد الإغلاق
        final_records = db.fetch_all("""
            SELECT * FROM unified_treasury 
            WHERE user_id = ?
            ORDER BY session_date DESC, currency_type
        """, (user_id,))
        
        print(f"📊 جميع السجلات للمستخدم {user_id} بعد الإغلاق:")
        for record in final_records:
            status = "مفتوح" if record[7] == 1 else "مغلق"
            print(f"  • التاريخ: {record[2]}, العملة: {record[3]}, الحالة: {status}")
            print(f"    الرصيد اليومي: {record[4]}, الرصيد الرئيسي: {record[5]}")
        
        # فحص الجلسة النهائية
        is_active_final = treasury_manager.is_session_active(user_id=user_id)
        print(f"📊 حالة الجلسة النهائية: {'مفتوحة' if is_active_final else 'مغلقة'}")
        
        # فحص الأرصدة النهائية
        final_daily_syp = treasury_manager.get_daily_balance(user_id, 'SYP')
        final_main_syp = treasury_manager.get_main_balance(user_id, 'SYP')
        
        print(f"💰 الأرصدة النهائية:")
        print(f"  • الخزينة اليومية: {final_daily_syp:,} ل.س")
        print(f"  • الخزينة الرئيسية: {final_main_syp:,} ل.س")
        
        print("\n=== 7. التشخيص ===")
        
        if close_success:
            print("✅ إغلاق الصندوق نجح!")
            if final_daily_syp == 0 and final_main_syp > 0:
                print("✅ تم نقل الأموال بشكل صحيح")
            else:
                print("⚠️ مشكلة في نقل الأموال")
        else:
            print("❌ إغلاق الصندوق فشل!")
            
            # تشخيص السبب
            if not is_active:
                print("  السبب: لا توجد جلسة مفتوحة")
            elif len(updated_records) == 0:
                print("  السبب: لا توجد سجلات للمستخدم")
            else:
                print("  السبب: خطأ في دالة الإغلاق")
        
        print(f"\n🎉 انتهى التشخيص!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_simple_workflow():
    """اختبار سير عمل مبسط"""
    
    print("\n" + "="*50)
    print("🧪 اختبار سير عمل مبسط...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = UnifiedTreasuryManager(db)
    
    user_id = 1
    
    try:
        # 1. فتح الصندوق
        print("1️⃣ فتح الصندوق...")
        treasury_manager.open_cash_box(user_id=user_id)
        
        # 2. إضافة مبيعات
        print("2️⃣ إضافة مبيعات...")
        treasury_manager.add_to_daily_treasury(user_id, 'SYP', 50000)
        
        # 3. فحص الرصيد
        balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"3️⃣ الرصيد الحالي: {balance:,} ل.س")
        
        # 4. إغلاق الصندوق
        print("4️⃣ إغلاق الصندوق...")
        close_result = treasury_manager.close_cash_box(user_id=user_id)
        
        print(f"✅ النتيجة: {'نجح' if close_result else 'فشل'}")
        
        return close_result
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    # اختبار مفصل
    test_cash_box_close()
    
    # اختبار مبسط
    test_simple_workflow()

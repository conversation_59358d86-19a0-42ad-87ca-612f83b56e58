#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق الرئيسية
"""

import os
from datetime import datetime

class AppConfig:
    """إعدادات التطبيق"""
    
    # معلومات التطبيق
    APP_NAME = "نظام إدارة شركات الإنترنت المتطور"
    APP_VERSION = "2.0.0"
    APP_AUTHOR = "فريق تطوير أنظمة الإنترنت"
    APP_DESCRIPTION = "نظام شامل لإدارة شركات الإنترنت"
    
    # مسارات الملفات
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DATABASE_PATH = os.path.join(BASE_DIR, "data", "isp_manager.db")
    ASSETS_DIR = os.path.join(BASE_DIR, "assets")
    LOGS_DIR = os.path.join(BASE_DIR, "logs")
    BACKUP_DIR = os.path.join(BASE_DIR, "backups")
    
    # إعدادات قاعدة البيانات
    DB_CONFIG = {
        'path': DATABASE_PATH,
        'timeout': 30,
        'check_same_thread': False,
        'auto_backup': True,
        'backup_interval': 24  # ساعة
    }
    
    # إعدادات الواجهة
    UI_CONFIG = {
        'theme': 'modern',
        'language': 'ar',
        'font_family': 'Segoe UI',
        'font_size': 10,
        'window_size': (1200, 800),
        'center_windows': True,
        'show_splash': True
    }
    
    # إعدادات الأمان
    SECURITY_CONFIG = {
        'session_timeout': 480,  # 8 ساعات
        'max_login_attempts': 3,
        'password_min_length': 6,
        'auto_logout': True,
        'log_activities': True
    }
    
    # إعدادات العملة
    CURRENCY_CONFIG = {
        'default_currency': 'SYP',
        'currencies': {
            'SYP': {'name': 'الليرة السورية', 'symbol': 'ل.س', 'decimals': 0},
            'USD': {'name': 'الدولار الأمريكي', 'symbol': '$', 'decimals': 2}
        },
        'exchange_rates': {
            'USD_TO_SYP': 15000  # سعر الصرف الافتراضي
        }
    }
    
    # إعدادات التقارير
    REPORTS_CONFIG = {
        'default_format': 'pdf',
        'auto_save': True,
        'include_logo': True,
        'date_format': '%Y-%m-%d',
        'time_format': '%H:%M:%S'
    }
    
    # الواجهات المتاحة (قابلة للتوسع)
    AVAILABLE_INTERFACES = {
        'financial': {
            'name': 'الإدارة المالية',
            'icon': 'money.png',
            'color': '#2ecc71',
            'modules': [
                {'name': 'الخزينة اليومية', 'class': 'DailyTreasuryWindow'},
                {'name': 'نقل الخزينة', 'class': 'TreasuryTransferWindow'},
                {'name': 'شراء الدولار', 'class': 'CurrencyExchangeWindow'},
                {'name': 'إدارة المصروفات', 'class': 'ExpensesWindow'},
                {'name': 'التقارير المالية', 'class': 'FinancialReportsWindow'}
            ]
        },
        'subscribers': {
            'name': 'إدارة المشتركين',
            'icon': 'users.png',
            'color': '#3498db',
            'modules': [
                {'name': 'اشتراك جديد', 'class': 'NewSubscriptionWindow'},
                {'name': 'تجديد الاشتراك', 'class': 'RenewSubscriptionWindow'},
                {'name': 'تسليم الراوتر', 'class': 'RouterDeliveryWindow'},
                {'name': 'إدارة المشتركين', 'class': 'SubscribersManagementWindow'}
            ]
        },
        'inventory': {
            'name': 'إدارة المخزون',
            'icon': 'inventory.png',
            'color': '#e74c3c',
            'modules': [
                {'name': 'المخزون الرئيسي', 'class': 'MainInventoryWindow'},
                {'name': 'مخزون العمال', 'class': 'WorkersInventoryWindow'},
                {'name': 'إضافة منتجات', 'class': 'AddProductsWindow'},
                {'name': 'تقارير المخزون', 'class': 'InventoryReportsWindow'}
            ]
        },
        'reports': {
            'name': 'التقارير والإحصائيات',
            'icon': 'reports.png',
            'color': '#9b59b6',
            'modules': [
                {'name': 'تقارير المبيعات', 'class': 'SalesReportsWindow'},
                {'name': 'تقارير المشتركين', 'class': 'SubscribersReportsWindow'},
                {'name': 'الإحصائيات', 'class': 'StatisticsWindow'},
                {'name': 'تحليل الأداء', 'class': 'PerformanceAnalysisWindow'}
            ]
        },
        'system': {
            'name': 'إدارة النظام',
            'icon': 'settings.png',
            'color': '#34495e',
            'modules': [
                {'name': 'إدارة المستخدمين', 'class': 'UsersManagementWindow'},
                {'name': 'النسخ الاحتياطي', 'class': 'BackupWindow'},
                {'name': 'إعدادات النظام', 'class': 'SystemSettingsWindow'},
                {'name': 'سجل العمليات', 'class': 'ActivityLogWindow'}
            ]
        }
    }
    
    # إعدادات السجلات
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_max_size': 10 * 1024 * 1024,  # 10 MB
        'backup_count': 5,
        'log_to_file': True,
        'log_to_console': True
    }
    
    # إعدادات النسخ الاحتياطي
    BACKUP_CONFIG = {
        'auto_backup': True,
        'backup_interval': 24,  # ساعة
        'max_backups': 30,  # عدد النسخ المحفوظة
        'compress_backups': True,
        'backup_on_exit': True
    }
    
    @classmethod
    def get_database_path(cls):
        """الحصول على مسار قاعدة البيانات"""
        os.makedirs(os.path.dirname(cls.DATABASE_PATH), exist_ok=True)
        return cls.DATABASE_PATH
    
    @classmethod
    def get_assets_path(cls, filename=''):
        """الحصول على مسار الأصول"""
        os.makedirs(cls.ASSETS_DIR, exist_ok=True)
        return os.path.join(cls.ASSETS_DIR, filename)
    
    @classmethod
    def get_logs_path(cls, filename=''):
        """الحصول على مسار السجلات"""
        os.makedirs(cls.LOGS_DIR, exist_ok=True)
        return os.path.join(cls.LOGS_DIR, filename)
    
    @classmethod
    def get_backup_path(cls, filename=''):
        """الحصول على مسار النسخ الاحتياطية"""
        os.makedirs(cls.BACKUP_DIR, exist_ok=True)
        return os.path.join(cls.BACKUP_DIR, filename)
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            os.path.dirname(cls.DATABASE_PATH),
            cls.ASSETS_DIR,
            cls.LOGS_DIR,
            cls.BACKUP_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_current_timestamp(cls):
        """الحصول على الطابع الزمني الحالي"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    @classmethod
    def format_currency(cls, amount, currency='SYP'):
        """تنسيق العملة"""
        currency_info = cls.CURRENCY_CONFIG['currencies'].get(currency, {})
        symbol = currency_info.get('symbol', '')
        decimals = currency_info.get('decimals', 0)
        
        if decimals == 0:
            return f"{amount:,.0f} {symbol}"
        else:
            return f"{amount:,.{decimals}f} {symbol}"

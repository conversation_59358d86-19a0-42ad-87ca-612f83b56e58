# 🌐 نظام إدارة شركات الإنترنت المتطور

## 📋 نظرة عامة
نظام شامل ونظيف لإدارة شركات الإنترنت مع واجهة مستخدم حديثة وقابلية توسع عالية.

## ✨ المميزات الرئيسية

### 💰 الإدارة المالية
- إدارة الخزينة اليومية والرئيسية
- تتبع المبيعات والمصروفات
- تقارير مالية شاملة
- نقل الأموال بين الخزائن

### 👥 إدارة المشتركين
- إضافة وتعديل المشتركين
- تجديد الاشتراكات
- تتبع حالة الدفع
- إدارة الباقات

### 📦 إدارة المخزون
- تتبع الراوترات والكابلات
- إدارة المخزون لكل عامل
- تسليم المعدات للمشتركين

### 📊 التقارير والإحصائيات
- تقارير مالية تفصيلية
- إحصائيات المبيعات
- تقارير المخزون
- تحليل الأداء

### 🔧 إدارة النظام
- إدارة المستخدمين والصلاحيات
- النسخ الاحتياطي واستعادة البيانات
- إعدادات النظام
- سجل العمليات

## 🏗️ البنية التقنية

### قاعدة البيانات
- SQLite للبساطة والموثوقية
- تصميم محسن بدون جداول مكررة
- فهرسة ذكية للأداء العالي

### واجهة المستخدم
- PyQt5 للواجهة الرسومية
- تصميم عربي متجاوب
- أيقونات ولوغو مخصص

### البرمجة
- Python 3.8+
- كود نظيف ومنظم
- توثيق شامل
- سهولة الصيانة والتطوير

## 🚀 التشغيل السريع

```bash
# تشغيل النظام
python main.py

# المستخدم الافتراضي
اسم المستخدم: admin
كلمة المرور: admin123
```

## 📁 هيكل المشروع

```
clean_isp_manager/
├── main.py                 # نقطة البداية
├── config/                 # ملفات الإعدادات
├── database/              # إدارة قاعدة البيانات
├── ui/                    # واجهات المستخدم
├── utils/                 # أدوات مساعدة
├── assets/                # الصور والأيقونات
├── plugins/               # الإضافات القابلة للتوسع
└── docs/                  # التوثيق

```

## 🔌 إضافة واجهات جديدة

النظام مصمم ليكون قابل للتوسع بسهولة:

1. **إنشاء ملف واجهة جديد** في مجلد `ui/`
2. **استخدام القالب المعياري** المتوفر
3. **تسجيل الواجهة** في ملف الإعدادات
4. **إعادة تشغيل النظام** - ستظهر الواجهة تلقائياً

## 📞 الدعم والمساعدة

- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +963-XXX-XXXX
- 🌐 الموقع: www.ispmanager.com

## 📄 الترخيص

هذا البرنامج مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**تم التطوير بواسطة فريق تطوير أنظمة الإنترنت** 🚀

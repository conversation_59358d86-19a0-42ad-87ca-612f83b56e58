#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحدث والمصحح
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.unified_treasury_manager import UnifiedTreasuryManager

def test_complete_workflow():
    """اختبار سير العمل الكامل"""
    
    print("🧪 اختبار سير العمل الكامل للنظام المحدث...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = UnifiedTreasuryManager(db)
    
    user_id = 1
    
    try:
        print("\n=== 1. فتح الصندوق (محاكاة تسجيل الدخول) ===")
        
        # فتح الصندوق
        treasury_manager.open_cash_box(user_id=user_id)
        
        # عرض الأرصدة الأولية
        syp_daily = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily = treasury_manager.get_daily_balance(user_id, 'USD')
        
        print(f"📊 الأرصدة بعد فتح الصندوق:")
        print(f"  • الخزينة اليومية: {syp_daily:,} ل.س + ${usd_daily:.2f}")
        
        print("\n=== 2. محاكاة المبيعات ===")
        
        # إضافة مبيعات متنوعة
        sales_operations = [
            ("اشتراك جديد", 50000),
            ("تسليم راوتر", 120000),
            ("تجديد باقة", 30000),
            ("خدمة إضافية", 25000)
        ]
        
        total_added = 0
        for operation, amount in sales_operations:
            treasury_manager.add_to_daily_treasury(user_id, 'SYP', amount)
            total_added += amount
            print(f"✅ {operation}: {amount:,} ل.س")
        
        # عرض الرصيد بعد المبيعات
        syp_after_sales = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"📊 الرصيد بعد المبيعات: {syp_after_sales:,} ل.س (إضافة {total_added:,})")
        
        print("\n=== 3. محاكاة المصاريف ===")
        
        # خصم مصاريف
        expenses_operations = [
            ("راتب عامل", 15000),
            ("فاتورة كهرباء", 8000),
            ("مصاريف متنوعة", 5000)
        ]
        
        total_subtracted = 0
        for operation, amount in expenses_operations:
            treasury_manager.subtract_from_daily_treasury(user_id, 'SYP', amount)
            total_subtracted += amount
            print(f"✅ {operation}: {amount:,} ل.س")
        
        # عرض الرصيد بعد المصاريف
        syp_after_expenses = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"📊 الرصيد بعد المصاريف: {syp_after_expenses:,} ل.س (خصم {total_subtracted:,})")
        
        print("\n=== 4. محاكاة شراء الدولار ===")
        
        # شراء دولار
        exchange_amount = 150000
        exchange_rate = 15000
        usd_amount = exchange_amount / exchange_rate
        
        exchange_success = treasury_manager.exchange_currency(
            user_id=user_id,
            from_currency='SYP',
            to_currency='USD',
            amount_from=exchange_amount,
            exchange_rate=exchange_rate
        )
        
        if exchange_success:
            print(f"✅ تم شراء الدولار: {exchange_amount:,} ل.س → ${usd_amount:.2f}")
        else:
            print(f"❌ فشل في شراء الدولار")
        
        # عرض الأرصدة بعد شراء الدولار
        syp_after_exchange = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_after_exchange = treasury_manager.get_daily_balance(user_id, 'USD')
        
        print(f"📊 الأرصدة بعد شراء الدولار:")
        print(f"  • الليرة السورية: {syp_after_exchange:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_after_exchange:.2f}")
        
        print("\n=== 5. محاكاة نقل الخزينة ===")
        
        # نقل جزء من الليرة السورية
        transfer_syp = 100000
        if syp_after_exchange >= transfer_syp:
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=user_id,
                currency_type='SYP',
                amount=transfer_syp
            )
            
            if transfer_success:
                print(f"✅ تم نقل {transfer_syp:,} ل.س للخزينة الرئيسية")
            else:
                print(f"❌ فشل في النقل")
        
        # نقل نصف الدولار
        transfer_usd = usd_after_exchange / 2
        if transfer_usd > 0:
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=user_id,
                currency_type='USD',
                amount=transfer_usd
            )
            
            if transfer_success:
                print(f"✅ تم نقل ${transfer_usd:.2f} للخزينة الرئيسية")
            else:
                print(f"❌ فشل في نقل الدولار")
        
        # عرض الأرصدة بعد النقل
        print(f"\n📊 الأرصدة بعد النقل:")
        
        syp_daily_final = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily_final = treasury_manager.get_daily_balance(user_id, 'USD')
        syp_main_final = treasury_manager.get_main_balance(user_id, 'SYP')
        usd_main_final = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"  الخزينة اليومية:")
        print(f"    • الليرة السورية: {syp_daily_final:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_daily_final:.2f}")
        
        print(f"  الخزينة الرئيسية:")
        print(f"    • الليرة السورية: {syp_main_final:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_main_final:.2f}")
        
        print("\n=== 6. اختبار الواجهة الرئيسية ===")
        
        # محاكاة ما ستعرضه الواجهة الرئيسية
        print(f"📊 ما ستعرضه الواجهة الرئيسية:")
        print(f"  • إجمالي المبيعات: {max(0, syp_daily_final):,} ل.س")
        print(f"  • إجمالي المصاريف: 0 ل.س (افتراضي)")
        
        # التحقق من صحة الحسابات
        expected_balance = total_added - total_subtracted - exchange_amount - transfer_syp
        actual_balance = syp_daily_final
        
        print(f"\n🔍 التحقق من صحة الحسابات:")
        print(f"  • الرصيد المتوقع: {expected_balance:,} ل.س")
        print(f"  • الرصيد الفعلي: {actual_balance:,} ل.س")
        
        if abs(expected_balance - actual_balance) < 0.01:
            print(f"  ✅ الحسابات صحيحة!")
        else:
            print(f"  ❌ خطأ في الحسابات!")
        
        print("\n=== 7. محاكاة إغلاق الصندوق ===")
        
        # إغلاق الصندوق
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        
        if close_success:
            print(f"✅ تم إغلاق الصندوق بنجاح")
            print(f"💾 تم إنشاء نسخة احتياطية")
        else:
            print(f"❌ فشل في إغلاق الصندوق")
        
        # عرض الأرصدة النهائية
        print(f"\n📊 الأرصدة النهائية بعد إغلاق الصندوق:")
        
        syp_daily_closed = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily_closed = treasury_manager.get_daily_balance(user_id, 'USD')
        syp_main_closed = treasury_manager.get_main_balance(user_id, 'SYP')
        usd_main_closed = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"  الخزينة اليومية (يجب أن تكون صفر):")
        print(f"    • الليرة السورية: {syp_daily_closed:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_daily_closed:.2f}")
        
        print(f"  الخزينة الرئيسية (تحتوي على جميع الأموال):")
        print(f"    • الليرة السورية: {syp_main_closed:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_main_closed:.2f}")
        
        # التحقق من إغلاق الجلسة
        is_active = treasury_manager.is_session_active(user_id=user_id)
        print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة ✅'}")
        
        print(f"\n🎉 انتهى اختبار سير العمل الكامل بنجاح!")
        
        # ملخص النتائج
        print(f"\n📋 ملخص النتائج:")
        print(f"  ✅ فتح الصندوق: يعمل")
        print(f"  ✅ إضافة المبيعات: يعمل")
        print(f"  ✅ خصم المصاريف: يعمل")
        print(f"  ✅ شراء الدولار: {'يعمل' if exchange_success else 'لا يعمل'}")
        print(f"  ✅ نقل الخزينة: يعمل")
        print(f"  ✅ إغلاق الصندوق: {'يعمل' if close_success else 'لا يعمل'}")
        print(f"  ✅ النسخ الاحتياطية: يعمل")
        print(f"  ✅ الحسابات: {'صحيحة' if abs(expected_balance - actual_balance) < 0.01 else 'خاطئة'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_complete_workflow()

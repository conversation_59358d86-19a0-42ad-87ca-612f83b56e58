#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح تحديث الخزينة بعد صرف العملة
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def test_currency_exchange_impact():
    """اختبار تأثير صرف العملة على الخزينة"""
    
    print("🧪 اختبار تأثير صرف العملة على الخزينة...")
    
    db = DatabaseManager('data/company_system.db')
    today = datetime.now().strftime('%Y-%m-%d')
    
    try:
        print(f"📅 التاريخ: {today}")
        
        # 1. حساب الخزينة اليومية قبل الصرف
        print("\n📊 حساب الخزينة اليومية:")
        
        # الشيفتات المغلقة
        closed_shifts = db.fetch_one("""
            SELECT 
                COALESCE(SUM(total_sales), 0) as total_sales,
                COALESCE(SUM(total_expenses), 0) as total_expenses
            FROM shifts 
            WHERE shift_date = ? AND status = 'closed'
        """, (today,))
        
        print(f"  • الشيفتات المغلقة: مبيعات={closed_shifts['total_sales'] if closed_shifts else 0:,}, مصاريف={closed_shifts['total_expenses'] if closed_shifts else 0:,}")
        
        # الشيفت الحالي
        current_shift = db.fetch_one("""
            SELECT 
                COALESCE(SUM(amount), 0) as current_sales
            FROM cash_operations 
            WHERE operation_type = 'sale' AND DATE(created_at) = ?
        """, (today,))
        
        print(f"  • الشيفت الحالي: مبيعات={current_shift['current_sales'] if current_shift else 0:,}")
        
        # عمليات صرف العملة
        currency_exchanges = db.fetch_one("""
            SELECT 
                COALESCE(SUM(amount_from), 0) as total_exchanged_syp,
                COALESCE(SUM(amount_to), 0) as total_received_usd
            FROM currency_exchange 
            WHERE from_currency = 'SYP' AND to_currency = 'USD' AND DATE(created_at) = ?
        """, (today,))
        
        print(f"  • صرف العملة: صرف ل.س={currency_exchanges['total_exchanged_syp'] if currency_exchanges else 0:,}, استلام $={currency_exchanges['total_received_usd'] if currency_exchanges else 0:,.2f}")
        
        # حساب الرصيد النهائي
        total_sales = (closed_shifts['total_sales'] if closed_shifts else 0) + (current_shift['current_sales'] if current_shift else 0)
        total_expenses = closed_shifts['total_expenses'] if closed_shifts else 0
        total_exchanged = currency_exchanges['total_exchanged_syp'] if currency_exchanges else 0
        total_usd = currency_exchanges['total_received_usd'] if currency_exchanges else 0
        
        daily_syp_balance = total_sales - total_expenses - total_exchanged
        
        print(f"\n💰 الخزينة اليومية النهائية:")
        print(f"  • الليرة السورية: {daily_syp_balance:,} ل.س")
        print(f"  • الدولار الأمريكي: ${total_usd:,.2f}")
        
        # 2. عرض آخر عمليات صرف العملة
        print(f"\n📋 آخر عمليات صرف العملة:")
        recent_exchanges = db.fetch_all("""
            SELECT amount_from, amount_to, exchange_rate, created_at, notes
            FROM currency_exchange 
            WHERE DATE(created_at) = ?
            ORDER BY created_at DESC
            LIMIT 5
        """, (today,))
        
        if recent_exchanges:
            for i, exchange in enumerate(recent_exchanges, 1):
                print(f"  {i}. صرف {exchange[0]:,} ل.س → ${exchange[1]:,.2f} (سعر: {exchange[2]:,}) - {exchange[3]}")
        else:
            print("  • لا توجد عمليات صرف اليوم")
        
        # 3. عرض رصيد الخزينة الرئيسية
        print(f"\n🏦 الخزينة الرئيسية:")
        treasury_balances = db.fetch_all("""
            SELECT currency_type, balance, exchange_rate, last_updated
            FROM treasury
            ORDER BY currency_type
        """)
        
        for treasury in treasury_balances:
            currency_type = treasury[0]
            balance = treasury[1]
            exchange_rate = treasury[2]
            last_updated = treasury[3]
            
            if currency_type == 'SYP':
                print(f"  • الليرة السورية: {balance:,} ل.س")
            elif currency_type == 'USD':
                print(f"  • الدولار الأمريكي: ${balance:,.2f} (سعر الصرف: {exchange_rate:,} ل.س)")
            elif currency_type == 'EUR':
                print(f"  • اليورو: €{balance:,.2f} (سعر الصرف: {exchange_rate:,} ل.س)")
        
        # 4. محاكاة عملية صرف جديدة
        print(f"\n🧪 محاكاة عملية صرف:")
        test_syp_amount = 100000
        test_exchange_rate = 15000
        test_usd_amount = test_syp_amount / test_exchange_rate
        
        print(f"  • المبلغ المراد صرفه: {test_syp_amount:,} ل.س")
        print(f"  • سعر الصرف: {test_exchange_rate:,} ل.س/$")
        print(f"  • المبلغ المستلم: ${test_usd_amount:.2f}")
        
        # التحقق من توفر الرصيد
        if daily_syp_balance >= test_syp_amount:
            new_syp_balance = daily_syp_balance - test_syp_amount
            new_usd_balance = total_usd + test_usd_amount
            
            print(f"  ✅ العملية ممكنة:")
            print(f"    - الرصيد الجديد ل.س: {new_syp_balance:,} ل.س")
            print(f"    - الرصيد الجديد $: ${new_usd_balance:.2f}")
        else:
            print(f"  ❌ العملية غير ممكنة: الرصيد المتاح {daily_syp_balance:,} ل.س أقل من المطلوب {test_syp_amount:,} ل.س")
        
        print(f"\n🎉 انتهى الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_currency_exchange_impact()

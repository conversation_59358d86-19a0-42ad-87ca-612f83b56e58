# -*- coding: utf-8 -*-
"""
نافذة إدارة العمال
Workers Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton,
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class WorkersManagementWindow(QDialog):
    """نافذة إدارة العمال"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة العمال والموزعين")
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)

        apply_arabic_style(self, 10)

        main_layout = QVBoxLayout(self)

        # إنشاء التبويبات
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)

        # تبويب العمال
        workers_tab = self.create_workers_tab()
        self.tabs.addTab(workers_tab, "إدارة العمال")

        # تبويب الموزعين
        distributors_tab = self.create_distributors_tab()
        self.tabs.addTab(distributors_tab, "إدارة الموزعين")

        main_layout.addWidget(self.tabs)

    def create_workers_tab(self):
        """إنشاء تبويب العمال"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان التبويب
        title_label = QLabel("إدارة العمال")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)

        # أدوات التحكم للعمال
        controls_layout = self.create_workers_controls()

        # جدول العمال
        self.workers_table = self.create_workers_table()

        # أزرار التحكم للعمال
        buttons_layout = self.create_workers_buttons()

        layout.addWidget(title_label)
        layout.addLayout(controls_layout)
        layout.addWidget(self.workers_table)
        layout.addLayout(buttons_layout)

        return widget

    def create_distributors_tab(self):
        """إنشاء تبويب الموزعين"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان التبويب
        title_label = QLabel("إدارة الموزعين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)

        # أدوات التحكم للموزعين - نسخة آمنة
        try:
            controls_layout = self.create_safe_distributors_controls()
        except:
            controls_layout = QHBoxLayout()

        # جدول الموزعين - نسخة آمنة
        try:
            self.distributors_table = self.create_safe_distributors_table()
        except:
            self.distributors_table = QTableWidget()
            self.distributors_table.setColumnCount(3)
            self.distributors_table.setHorizontalHeaderLabels(["الاسم", "الهاتف", "المنطقة"])

        # أزرار التحكم للموزعين - نسخة آمنة
        try:
            buttons_layout = self.create_safe_distributors_buttons()
        except:
            buttons_layout = QHBoxLayout()
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(self.accept)
            buttons_layout.addWidget(close_btn)

        layout.addWidget(title_label)
        layout.addLayout(controls_layout)
        layout.addWidget(self.distributors_table)
        layout.addLayout(buttons_layout)

        return widget
        
    def create_workers_controls(self):
        """إنشاء أدوات التحكم"""
        layout = QHBoxLayout()
        
        # فلتر النوع
        type_label = QLabel("نوع العامل:")
        apply_arabic_style(type_label, 10)
        
        self.type_combo = QComboBox()
        apply_arabic_style(self.type_combo, 10)
        self.type_combo.addItem("جميع الأنواع", "")
        self.type_combo.addItem("عامل تركيب", "installer")
        self.type_combo.addItem("موزع", "distributor")
        self.type_combo.addItem("فني صيانة", "technician")
        
        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)
        
        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث بالاسم أو الهاتف...")
        
        # زر البحث
        search_button = QPushButton("بحث")
        apply_arabic_style(search_button, 10)
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout.addWidget(type_label)
        layout.addWidget(self.type_combo)
        layout.addWidget(search_label)
        layout.addWidget(self.search_edit)
        layout.addWidget(search_button)
        layout.addStretch()
        
        search_button.clicked.connect(self.search_workers)
        self.type_combo.currentTextChanged.connect(self.filter_by_type)
        
        return layout
        
    def create_workers_table(self):
        """إنشاء جدول العمال"""
        table = QTableWidget()
        apply_arabic_style(table, 9)
        
        columns = ["الرقم", "الاسم", "الهاتف", "النوع", "المنطقة", "الراتب", "العمولة", "الحالة", "تاريخ التسجيل"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)
        
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)
        
        # تنسيق الأعمدة
        table.setColumnWidth(0, 60)   # الرقم
        table.setColumnWidth(1, 150)  # الاسم
        table.setColumnWidth(2, 120)  # الهاتف
        table.setColumnWidth(3, 100)  # النوع
        table.setColumnWidth(4, 120)  # المنطقة
        table.setColumnWidth(5, 100)  # الراتب
        table.setColumnWidth(6, 80)   # العمولة
        table.setColumnWidth(7, 80)   # الحالة
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        return table
        
    def create_workers_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة عامل جديد")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        edit_button = QPushButton("تعديل العامل")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        delete_button = QPushButton("حذف العامل")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        inventory_button = QPushButton("مخزون العامل")
        apply_arabic_style(inventory_button, 10)
        inventory_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(inventory_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        add_button.clicked.connect(self.add_worker)
        edit_button.clicked.connect(self.edit_worker)
        delete_button.clicked.connect(self.delete_worker)
        inventory_button.clicked.connect(self.manage_inventory)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل بيانات العمال والموزعين"""
        self.load_workers_data()
        try:
            self.load_distributors_data()
        except:
            pass  # في حالة عدم وجود تبويب الموزعين

    def load_workers_data(self):
        """تحميل بيانات العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT id, name, phone, work_type, area, salary, commission_rate,
                       is_active, created_at
                FROM workers
                ORDER BY created_at DESC
            """)
            
            self.workers_table.setRowCount(len(workers))
            
            for row, worker in enumerate(workers):
                # الرقم
                self.workers_table.setItem(row, 0, QTableWidgetItem(str(worker['id'])))
                
                # الاسم
                self.workers_table.setItem(row, 1, QTableWidgetItem(worker['name'] or ""))
                
                # الهاتف
                self.workers_table.setItem(row, 2, QTableWidgetItem(worker['phone'] or ""))
                
                # النوع
                work_type = worker['work_type'] or "غير محدد"
                self.workers_table.setItem(row, 3, QTableWidgetItem(work_type))
                
                # المنطقة
                self.workers_table.setItem(row, 4, QTableWidgetItem(worker['area'] or "غير محدد"))
                
                # الراتب
                salary = format_currency(worker['salary']) if worker['salary'] else "غير محدد"
                self.workers_table.setItem(row, 5, QTableWidgetItem(salary))
                
                # العمولة
                commission = f"{worker['commission_rate']}%" if worker['commission_rate'] else "0%"
                self.workers_table.setItem(row, 6, QTableWidgetItem(commission))
                
                # الحالة
                status = "نشط" if worker['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if worker['is_active']:
                    status_item.setBackground(Qt.green)
                    status_item.setForeground(Qt.white)
                else:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.workers_table.setItem(row, 7, status_item)
                
                # تاريخ التسجيل
                date_str = worker['created_at'][:10] if worker['created_at'] else ""
                self.workers_table.setItem(row, 8, QTableWidgetItem(date_str))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def search_workers(self):
        """البحث في العمال"""
        search_text = self.search_edit.text().strip()
        worker_type = self.type_combo.currentData()
        
        try:
            query = """
                SELECT id, name, phone, worker_type, area, salary, commission_rate, 
                       is_active, created_at 
                FROM workers 
                WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND (name LIKE ? OR phone LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
                
            if worker_type:
                query += " AND worker_type = ?"
                params.append(worker_type)
                
            query += " ORDER BY created_at DESC"
            
            workers = self.db_manager.fetch_all(query, params)
            
            # تحديث الجدول بنفس منطق load_data
            self.workers_table.setRowCount(len(workers))
            
            for row, worker in enumerate(workers):
                self.workers_table.setItem(row, 0, QTableWidgetItem(str(worker['id'])))
                self.workers_table.setItem(row, 1, QTableWidgetItem(worker['name'] or ""))
                self.workers_table.setItem(row, 2, QTableWidgetItem(worker['phone'] or ""))
                
                worker_types = {
                    'installer': 'عامل تركيب',
                    'distributor': 'موزع',
                    'technician': 'فني صيانة'
                }
                worker_type_display = worker_types.get(worker['worker_type'], worker['worker_type'] or "غير محدد")
                self.workers_table.setItem(row, 3, QTableWidgetItem(worker_type_display))
                
                self.workers_table.setItem(row, 4, QTableWidgetItem(worker['area'] or "غير محدد"))
                
                salary = format_currency(worker['salary']) if worker['salary'] else "غير محدد"
                self.workers_table.setItem(row, 5, QTableWidgetItem(salary))
                
                commission = f"{worker['commission_rate']}%" if worker['commission_rate'] else "0%"
                self.workers_table.setItem(row, 6, QTableWidgetItem(commission))
                
                status = "نشط" if worker['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if worker['is_active']:
                    status_item.setBackground(Qt.green)
                    status_item.setForeground(Qt.white)
                else:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.workers_table.setItem(row, 7, status_item)
                
                date_str = worker['created_at'][:10] if worker['created_at'] else ""
                self.workers_table.setItem(row, 8, QTableWidgetItem(date_str))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {e}")
            
    def filter_by_type(self):
        """فلترة حسب النوع"""
        self.search_workers()
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def add_worker(self):
        """إضافة عامل جديد"""
        dialog = WorkerDialog(self.db_manager, self.config_manager, self.current_user, self)
        if dialog.exec_() == dialog.Accepted:
            self.load_data()
        
    def edit_worker(self):
        """تعديل عامل"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل للتعديل")
            return

        worker_id = int(self.workers_table.item(current_row, 0).text())
        dialog = WorkerDialog(self.db_manager, self.config_manager, self.current_user, self, worker_id)
        if dialog.exec_() == dialog.Accepted:
            self.load_data()
        
    def delete_worker(self):
        """حذف عامل"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل للحذف")
            return
            
        worker_id = self.workers_table.item(current_row, 0).text()
        worker_name = self.workers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف العامل '{worker_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM workers WHERE id = ?", (worker_id,))
                QMessageBox.information(self, "نجح", "تم حذف العامل بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف العامل: {e}")
                
    def manage_inventory(self):
        """إدارة مخزون العامل"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل لإدارة مخزونه")
            return

        worker_id = int(self.workers_table.item(current_row, 0).text())
        worker_name = self.workers_table.item(current_row, 1).text()

        dialog = WorkerInventoryDialog(self.db_manager, self.config_manager, self.current_user, self, worker_id, worker_name)
        dialog.exec_()


class WorkerDialog(QDialog):
    """نافذة حوار إضافة/تعديل عامل"""

    def __init__(self, db_manager, config_manager, current_user, parent=None, worker_id=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        self.worker_id = worker_id

        self.setWindowTitle("إضافة عامل جديد" if worker_id is None else "تعديل بيانات العامل")
        self.setModal(True)
        self.setFixedSize(500, 400)

        self.setup_ui()
        self.setup_connections()

        if worker_id:
            self.load_worker_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # مجموعة بيانات العامل
        worker_group = QGroupBox("بيانات العامل")
        apply_arabic_style(worker_group, 12, bold=True)
        worker_layout = QGridLayout(worker_group)

        # اسم العامل
        name_label = QLabel("اسم العامل:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)

        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        apply_arabic_style(phone_label, 10)
        self.phone_edit = QLineEdit()
        apply_arabic_style(self.phone_edit, 10)

        # المنطقة
        area_label = QLabel("المنطقة:")
        apply_arabic_style(area_label, 10)
        self.area_edit = QLineEdit()
        apply_arabic_style(self.area_edit, 10)

        # نوع العمل
        work_type_label = QLabel("نوع العمل:")
        apply_arabic_style(work_type_label, 10)
        self.work_type_combo = QComboBox()
        apply_arabic_style(self.work_type_combo, 10)
        self.work_type_combo.addItems([
            "تركيب راوترات",
            "صيانة شبكات",
            "تمديد كابلات",
            "دعم فني",
            "أخرى"
        ])

        # الراتب الأساسي
        salary_label = QLabel("الراتب الأساسي:")
        apply_arabic_style(salary_label, 10)
        self.salary_spin = QDoubleSpinBox()
        apply_arabic_style(self.salary_spin, 10)
        self.salary_spin.setRange(0, 999999999)  # زيادة الحد إلى 9 أرقام
        self.salary_spin.setSuffix(" ل.س")

        # نسبة العمولة
        commission_label = QLabel("نسبة العمولة:")
        apply_arabic_style(commission_label, 10)
        self.commission_spin = QDoubleSpinBox()
        apply_arabic_style(self.commission_spin, 10)
        self.commission_spin.setRange(0, 100)
        self.commission_spin.setSuffix(" %")

        # العنوان
        address_label = QLabel("العنوان:")
        apply_arabic_style(address_label, 10)
        self.address_edit = QTextEdit()
        apply_arabic_style(self.address_edit, 10)
        self.address_edit.setMaximumHeight(80)

        # الحالة
        self.is_active_check = QCheckBox("نشط")
        apply_arabic_style(self.is_active_check, 10)
        self.is_active_check.setChecked(True)

        # ترتيب العناصر
        worker_layout.addWidget(name_label, 0, 0)
        worker_layout.addWidget(self.name_edit, 0, 1)
        worker_layout.addWidget(phone_label, 1, 0)
        worker_layout.addWidget(self.phone_edit, 1, 1)
        worker_layout.addWidget(area_label, 2, 0)
        worker_layout.addWidget(self.area_edit, 2, 1)
        worker_layout.addWidget(work_type_label, 3, 0)
        worker_layout.addWidget(self.work_type_combo, 3, 1)
        worker_layout.addWidget(salary_label, 4, 0)
        worker_layout.addWidget(self.salary_spin, 4, 1)
        worker_layout.addWidget(commission_label, 5, 0)
        worker_layout.addWidget(self.commission_spin, 5, 1)
        worker_layout.addWidget(address_label, 6, 0)
        worker_layout.addWidget(self.address_edit, 6, 1)
        worker_layout.addWidget(self.is_active_check, 7, 0, 1, 2)

        layout.addWidget(worker_group)

        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        apply_arabic_style(self.save_button, 10)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(self.save_button)

        return layout

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_button.clicked.connect(self.save_worker)

    def load_worker_data(self):
        """تحميل بيانات العامل للتعديل"""
        try:
            worker = self.db_manager.fetch_one("""
                SELECT * FROM workers WHERE id = ?
            """, (self.worker_id,))

            if worker:
                self.name_edit.setText(worker['name'] or '')
                self.phone_edit.setText(worker['phone'] or '')
                self.area_edit.setText(worker['area'] or '')

                # تحديد نوع العمل
                work_type = worker.get('work_type', 'تركيب راوترات')
                index = self.work_type_combo.findText(work_type)
                if index >= 0:
                    self.work_type_combo.setCurrentIndex(index)

                self.salary_spin.setValue(worker.get('salary', 0))
                self.commission_spin.setValue(worker.get('commission_rate', 0))
                self.address_edit.setPlainText(worker.get('address', ''))
                self.is_active_check.setChecked(bool(worker.get('is_active', True)))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات العامل: {e}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العامل")
            self.name_edit.setFocus()
            return False

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف")
            self.phone_edit.setFocus()
            return False

        if not self.area_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال المنطقة")
            self.area_edit.setFocus()
            return False

        return True

    def save_worker(self):
        """حفظ بيانات العامل"""
        if not self.validate_data():
            return

        try:
            worker_data = {
                'name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'area': self.area_edit.text().strip(),
                'work_type': self.work_type_combo.currentText(),
                'salary': self.salary_spin.value(),
                'commission_rate': self.commission_spin.value(),
                'address': self.address_edit.toPlainText().strip(),
                'is_active': self.is_active_check.isChecked()
            }

            if self.worker_id is None:
                # إضافة عامل جديد
                self.db_manager.execute_query("""
                    INSERT INTO workers (name, phone, area, work_type, salary,
                                       commission_rate, address, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    worker_data['name'], worker_data['phone'], worker_data['area'],
                    worker_data['work_type'], worker_data['salary'], worker_data['commission_rate'],
                    worker_data['address'], worker_data['is_active'], self.current_user['username']
                ))

                QMessageBox.information(self, "نجح", "تم إضافة العامل بنجاح")
            else:
                # تعديل عامل موجود
                self.db_manager.execute_query("""
                    UPDATE workers SET name = ?, phone = ?, area = ?, work_type = ?,
                                     salary = ?, commission_rate = ?, address = ?, is_active = ?
                    WHERE id = ?
                """, (
                    worker_data['name'], worker_data['phone'], worker_data['area'],
                    worker_data['work_type'], worker_data['salary'], worker_data['commission_rate'],
                    worker_data['address'], worker_data['is_active'], self.worker_id
                ))

                QMessageBox.information(self, "نجح", "تم تعديل بيانات العامل بنجاح")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ بيانات العامل: {e}")


class WorkerInventoryDialog(QDialog):
    """نافذة حوار إدارة مخزون العامل"""

    def __init__(self, db_manager, config_manager, current_user, parent=None, worker_id=None, worker_name=""):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        self.worker_id = worker_id
        self.worker_name = worker_name

        self.setWindowTitle(f"إدارة مخزون العامل - {worker_name}")
        self.setModal(True)
        self.setFixedSize(800, 600)

        self.setup_ui()
        self.setup_connections()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # معلومات العامل
        info_label = QLabel(f"إدارة مخزون العامل: {self.worker_name}")
        apply_arabic_style(info_label, 14, bold=True)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(info_label)

        # جدول المخزون الحالي
        inventory_group = QGroupBox("المخزون الحالي")
        apply_arabic_style(inventory_group, 12, bold=True)
        inventory_layout = QVBoxLayout(inventory_group)

        self.inventory_table = QTableWidget()
        apply_arabic_style(self.inventory_table, 10)
        self.inventory_table.setColumnCount(4)
        self.inventory_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية", "الوحدة", "آخر تحديث"
        ])

        # تنسيق الجدول
        header = self.inventory_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, header.Stretch)
        header.setSectionResizeMode(1, header.ResizeToContents)
        header.setSectionResizeMode(2, header.ResizeToContents)

        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)

        inventory_layout.addWidget(self.inventory_table)
        layout.addWidget(inventory_group)

        # قسم إضافة/تعديل المخزون
        add_group = QGroupBox("إضافة/تعديل مخزون")
        apply_arabic_style(add_group, 12, bold=True)
        add_layout = QGridLayout(add_group)

        # اختيار المنتج
        product_label = QLabel("المنتج:")
        apply_arabic_style(product_label, 10)
        self.product_combo = QComboBox()
        apply_arabic_style(self.product_combo, 10)

        # الكمية
        quantity_label = QLabel("الكمية:")
        apply_arabic_style(quantity_label, 10)
        self.quantity_spin = QSpinBox()
        apply_arabic_style(self.quantity_spin, 10)
        self.quantity_spin.setRange(0, 9999)

        # نوع العملية
        operation_label = QLabel("نوع العملية:")
        apply_arabic_style(operation_label, 10)
        self.operation_combo = QComboBox()
        apply_arabic_style(self.operation_combo, 10)
        self.operation_combo.addItems(["إضافة", "تعديل مباشر"])

        # زر التنفيذ
        execute_button = QPushButton("تنفيذ")
        apply_arabic_style(execute_button, 10)
        execute_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        add_layout.addWidget(product_label, 0, 0)
        add_layout.addWidget(self.product_combo, 0, 1)
        add_layout.addWidget(quantity_label, 1, 0)
        add_layout.addWidget(self.quantity_spin, 1, 1)
        add_layout.addWidget(operation_label, 2, 0)
        add_layout.addWidget(self.operation_combo, 2, 1)
        add_layout.addWidget(execute_button, 3, 0, 1, 2)

        layout.addWidget(add_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        # الاتصالات
        execute_button.clicked.connect(self.execute_operation)
        close_button.clicked.connect(self.accept)

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def load_data(self):
        """تحميل البيانات"""
        self.load_products()
        self.load_worker_inventory()

    def load_products(self):
        """تحميل المنتجات"""
        try:
            products = self.db_manager.fetch_all("""
                SELECT id, name FROM products WHERE is_active = 1 ORDER BY name
            """)

            self.product_combo.clear()
            for product in products:
                self.product_combo.addItem(product['name'], product['id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المنتجات: {e}")

    def load_worker_inventory(self):
        """تحميل مخزون العامل"""
        try:
            inventory = self.db_manager.fetch_all("""
                SELECT p.name, wi.quantity, p.unit_type, wi.delivered_at
                FROM worker_inventory wi
                JOIN products p ON wi.product_id = p.id
                WHERE wi.worker_id = ?
                ORDER BY p.name
            """, (self.worker_id,))

            self.inventory_table.setRowCount(len(inventory))

            for row, item in enumerate(inventory):
                self.inventory_table.setItem(row, 0, QTableWidgetItem(item['name']))
                self.inventory_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
                self.inventory_table.setItem(row, 2, QTableWidgetItem(item['unit_type']))
                self.inventory_table.setItem(row, 3, QTableWidgetItem(item['delivered_at'] or 'غير محدد'))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل مخزون العامل: {e}")

    def execute_operation(self):
        """تنفيذ العملية"""
        try:
            product_id = self.product_combo.currentData()
            quantity = self.quantity_spin.value()
            operation = self.operation_combo.currentText()

            if not product_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
                return

            if quantity <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
                return

            # التحقق من وجود المنتج في مخزون العامل
            existing = self.db_manager.fetch_one("""
                SELECT quantity FROM worker_inventory
                WHERE worker_id = ? AND product_id = ?
            """, (self.worker_id, product_id))

            if existing:
                # تحديث الكمية الموجودة
                if operation == "إضافة":
                    new_quantity = existing['quantity'] + quantity
                else:  # تعديل مباشر
                    new_quantity = quantity

                self.db_manager.execute_query("""
                    UPDATE worker_inventory
                    SET quantity = ?, delivered_at = datetime('now'), delivered_by = ?
                    WHERE worker_id = ? AND product_id = ?
                """, (new_quantity, self.current_user['username'], self.worker_id, product_id))
            else:
                # إضافة منتج جديد
                self.db_manager.execute_query("""
                    INSERT INTO worker_inventory (worker_id, product_id, quantity, delivered_by)
                    VALUES (?, ?, ?, ?)
                """, (self.worker_id, product_id, quantity, self.current_user['username']))

            QMessageBox.information(self, "تم", "تم تحديث مخزون العامل بنجاح")

            # إعادة تحميل البيانات
            self.load_worker_inventory()

            # إعادة تعيين النموذج
            self.quantity_spin.setValue(0)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ العملية: {e}")

    # ==================== دوال الموزعين ====================

    def create_distributors_controls(self):
        """إنشاء أدوات التحكم للموزعين"""
        layout = QHBoxLayout()

        # فلتر المنطقة
        area_label = QLabel("المنطقة:")
        apply_arabic_style(area_label, 10)

        self.distributors_area_combo = QComboBox()
        apply_arabic_style(self.distributors_area_combo, 10)
        self.distributors_area_combo.addItems(["جميع المناطق", "دمشق", "حلب", "حمص", "حماة", "اللاذقية"])

        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)

        self.distributors_search_edit = QLineEdit()
        apply_arabic_style(self.distributors_search_edit, 10)
        self.distributors_search_edit.setPlaceholderText("ابحث بالاسم أو الهاتف...")

        layout.addWidget(area_label)
        layout.addWidget(self.distributors_area_combo)
        layout.addWidget(search_label)
        layout.addWidget(self.distributors_search_edit)
        layout.addStretch()

        return layout

    def create_distributors_table(self):
        """إنشاء جدول الموزعين"""
        table = QTableWidget()
        apply_arabic_style(table, 10)

        # إعداد الأعمدة
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "الهاتف", "المنطقة", "الرصيد",
            "نسبة العمولة", "الحالة", "تاريخ الإنشاء"
        ])

        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, header.Stretch)  # عمود الاسم

        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        return table

    def create_distributors_buttons(self):
        """إنشاء أزرار التحكم للموزعين"""
        layout = QHBoxLayout()

        # زر إضافة موزع
        add_button = QPushButton("إضافة موزع")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # زر تعديل موزع
        edit_button = QPushButton("تعديل موزع")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # زر حذف موزع
        delete_button = QPushButton("حذف موزع")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # زر شحن رصيد
        charge_button = QPushButton("شحن رصيد")
        apply_arabic_style(charge_button, 10)
        charge_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(charge_button)
        layout.addStretch()
        layout.addWidget(close_button)

        # ربط الأحداث
        add_button.clicked.connect(self.add_distributor)
        edit_button.clicked.connect(self.edit_distributor)
        delete_button.clicked.connect(self.delete_distributor)
        charge_button.clicked.connect(self.charge_distributor_balance)
        close_button.clicked.connect(self.accept)

        return layout

    def add_distributor(self):
        """إضافة موزع جديد"""
        dialog = DistributorDialog(self.db_manager, self.config_manager, self.current_user, self)
        if dialog.exec_() == dialog.Accepted:
            self.load_distributors_data()

    def edit_distributor(self):
        """تعديل موزع"""
        current_row = self.distributors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للتعديل")
            return

        distributor_id = int(self.distributors_table.item(current_row, 0).text())
        dialog = DistributorDialog(self.db_manager, self.config_manager, self.current_user, self, distributor_id)
        if dialog.exec_() == dialog.Accepted:
            self.load_distributors_data()

    def delete_distributor(self):
        """حذف موزع"""
        current_row = self.distributors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع للحذف")
            return

        distributor_id = self.distributors_table.item(current_row, 0).text()
        distributor_name = self.distributors_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف الموزع '{distributor_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM distributors WHERE id = ?", (distributor_id,))
                QMessageBox.information(self, "نجح", "تم حذف الموزع بنجاح")
                self.load_distributors_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الموزع: {e}")

    def charge_distributor_balance(self):
        """شحن رصيد الموزع"""
        current_row = self.distributors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع لشحن رصيده")
            return

        QMessageBox.information(self, "قريباً", "ميزة شحن رصيد الموزع متاحة في الواجهة الرئيسية")

    def load_distributors_data(self):
        """تحميل بيانات الموزعين"""
        try:
            distributors = self.db_manager.fetch_all("""
                SELECT id, name, phone, area, balance, commission_rate,
                       is_active, created_at
                FROM distributors
                ORDER BY created_at DESC
            """)

            self.distributors_table.setRowCount(len(distributors))

            for row, distributor in enumerate(distributors):
                # الرقم
                self.distributors_table.setItem(row, 0, QTableWidgetItem(str(distributor['id'])))

                # الاسم
                self.distributors_table.setItem(row, 1, QTableWidgetItem(distributor['name'] or ""))

                # الهاتف
                self.distributors_table.setItem(row, 2, QTableWidgetItem(distributor['phone'] or ""))

                # المنطقة
                self.distributors_table.setItem(row, 3, QTableWidgetItem(distributor['area'] or "غير محدد"))

                # الرصيد
                balance = format_currency(distributor['balance']) if distributor['balance'] else "0 ل.س"
                self.distributors_table.setItem(row, 4, QTableWidgetItem(balance))

                # العمولة
                commission = f"{distributor['commission_rate']}%" if distributor['commission_rate'] else "0%"
                self.distributors_table.setItem(row, 5, QTableWidgetItem(commission))

                # الحالة
                status = "نشط" if distributor['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if distributor['is_active']:
                    status_item.setBackground(Qt.green)
                    status_item.setForeground(Qt.white)
                else:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.distributors_table.setItem(row, 6, status_item)

                # تاريخ الإنشاء
                created_at = distributor['created_at'][:10] if distributor['created_at'] else "غير محدد"
                self.distributors_table.setItem(row, 7, QTableWidgetItem(created_at))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الموزعين: {e}")


class DistributorDialog(QDialog):
    """نافذة حوار إضافة/تعديل موزع"""

    def __init__(self, db_manager, config_manager, current_user, parent=None, distributor_id=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        self.distributor_id = distributor_id

        self.setWindowTitle("إضافة موزع جديد" if distributor_id is None else "تعديل بيانات الموزع")
        self.setModal(True)
        self.setFixedSize(500, 400)

        self.setup_ui()
        self.setup_connections()

        if distributor_id:
            self.load_distributor_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # مجموعة بيانات الموزع
        distributor_group = QGroupBox("بيانات الموزع")
        apply_arabic_style(distributor_group, 12, bold=True)
        distributor_layout = QGridLayout(distributor_group)

        # اسم الموزع
        name_label = QLabel("اسم الموزع:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)

        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        apply_arabic_style(phone_label, 10)
        self.phone_edit = QLineEdit()
        apply_arabic_style(self.phone_edit, 10)

        # المنطقة
        area_label = QLabel("المنطقة:")
        apply_arabic_style(area_label, 10)
        self.area_combo = QComboBox()
        apply_arabic_style(self.area_combo, 10)
        self.area_combo.addItems([
            "دمشق", "حلب", "حمص", "حماة", "اللاذقية",
            "طرطوس", "درعا", "السويداء", "القنيطرة",
            "دير الزور", "الرقة", "الحسكة", "إدلب", "أخرى"
        ])

        # الرصيد الحالي
        balance_label = QLabel("الرصيد الحالي:")
        apply_arabic_style(balance_label, 10)
        self.balance_spin = QDoubleSpinBox()
        apply_arabic_style(self.balance_spin, 10)
        self.balance_spin.setRange(0, 999999999)
        self.balance_spin.setSuffix(" ل.س")

        # نسبة العمولة
        commission_label = QLabel("نسبة العمولة:")
        apply_arabic_style(commission_label, 10)
        self.commission_spin = QDoubleSpinBox()
        apply_arabic_style(self.commission_spin, 10)
        self.commission_spin.setRange(0, 100)
        self.commission_spin.setSuffix(" %")

        # العنوان
        address_label = QLabel("العنوان:")
        apply_arabic_style(address_label, 10)
        self.address_edit = QTextEdit()
        apply_arabic_style(self.address_edit, 10)
        self.address_edit.setMaximumHeight(80)

        # الحالة
        self.is_active_check = QCheckBox("نشط")
        apply_arabic_style(self.is_active_check, 10)
        self.is_active_check.setChecked(True)

        # ترتيب العناصر
        distributor_layout.addWidget(name_label, 0, 0)
        distributor_layout.addWidget(self.name_edit, 0, 1)
        distributor_layout.addWidget(phone_label, 1, 0)
        distributor_layout.addWidget(self.phone_edit, 1, 1)
        distributor_layout.addWidget(area_label, 2, 0)
        distributor_layout.addWidget(self.area_combo, 2, 1)
        distributor_layout.addWidget(balance_label, 3, 0)
        distributor_layout.addWidget(self.balance_spin, 3, 1)
        distributor_layout.addWidget(commission_label, 4, 0)
        distributor_layout.addWidget(self.commission_spin, 4, 1)
        distributor_layout.addWidget(address_label, 5, 0)
        distributor_layout.addWidget(self.address_edit, 5, 1)
        distributor_layout.addWidget(self.is_active_check, 6, 0, 1, 2)

        layout.addWidget(distributor_group)

        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        apply_arabic_style(self.save_button, 10)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(self.save_button)

        # ربط الأحداث
        self.save_button.clicked.connect(self.save_distributor)
        cancel_button.clicked.connect(self.reject)

        return layout

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def load_distributor_data(self):
        """تحميل بيانات الموزع للتعديل"""
        try:
            distributor = self.db_manager.fetch_one("""
                SELECT * FROM distributors WHERE id = ?
            """, (self.distributor_id,))

            if distributor:
                self.name_edit.setText(distributor['name'] or '')
                self.phone_edit.setText(distributor['phone'] or '')

                # تحديد المنطقة
                area = distributor.get('area', 'دمشق')
                index = self.area_combo.findText(area)
                if index >= 0:
                    self.area_combo.setCurrentIndex(index)

                self.balance_spin.setValue(distributor.get('balance', 0))
                self.commission_spin.setValue(distributor.get('commission_rate', 0))
                self.address_edit.setPlainText(distributor.get('address', ''))
                self.is_active_check.setChecked(bool(distributor.get('is_active', True)))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الموزع: {e}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموزع")
            self.name_edit.setFocus()
            return False

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف")
            self.phone_edit.setFocus()
            return False

        return True

    def save_distributor(self):
        """حفظ بيانات الموزع"""
        if not self.validate_data():
            return

        try:
            distributor_data = {
                'name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'area': self.area_combo.currentText(),
                'balance': self.balance_spin.value(),
                'commission_rate': self.commission_spin.value(),
                'address': self.address_edit.toPlainText().strip(),
                'is_active': self.is_active_check.isChecked()
            }

            if self.distributor_id is None:
                # إضافة موزع جديد
                self.db_manager.execute_query("""
                    INSERT INTO distributors (name, phone, area, balance,
                                            commission_rate, address, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    distributor_data['name'], distributor_data['phone'], distributor_data['area'],
                    distributor_data['balance'], distributor_data['commission_rate'],
                    distributor_data['address'], distributor_data['is_active'],
                    self.current_user['username']
                ))

                QMessageBox.information(self, "نجح", "تم إضافة الموزع بنجاح")
            else:
                # تعديل موزع موجود
                self.db_manager.execute_query("""
                    UPDATE distributors SET name = ?, phone = ?, area = ?, balance = ?,
                                          commission_rate = ?, address = ?, is_active = ?
                    WHERE id = ?
                """, (
                    distributor_data['name'], distributor_data['phone'], distributor_data['area'],
                    distributor_data['balance'], distributor_data['commission_rate'],
                    distributor_data['address'], distributor_data['is_active'], self.distributor_id
                ))

                QMessageBox.information(self, "نجح", "تم تعديل بيانات الموزع بنجاح")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ بيانات الموزع: {e}")

    def create_simple_distributors_controls(self):
        """إنشاء أدوات تحكم بسيطة للموزعين"""
        layout = QHBoxLayout()

        # زر إضافة موزع
        add_btn = QPushButton("إضافة موزع")
        add_btn.clicked.connect(lambda: QMessageBox.information(self, "قريباً", "ميزة إضافة الموزع قيد التطوير"))
        layout.addWidget(add_btn)

        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(lambda: QMessageBox.information(self, "تحديث", "تم تحديث قائمة الموزعين"))
        layout.addWidget(refresh_btn)

        layout.addStretch()
        return layout

    def create_simple_distributors_table(self):
        """إنشاء جدول بسيط للموزعين"""
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["الاسم", "الهاتف", "العنوان"])

        # إضافة بيانات تجريبية
        table.setRowCount(1)
        table.setItem(0, 0, QTableWidgetItem("لا توجد موزعين"))
        table.setItem(0, 1, QTableWidgetItem("-"))
        table.setItem(0, 2, QTableWidgetItem("-"))

        return table

    def create_safe_distributors_controls(self):
        """إنشاء أدوات تحكم آمنة للموزعين"""
        layout = QHBoxLayout()

        # عنوان بسيط
        label = QLabel("إدارة الموزعين")
        apply_arabic_style(label, 12, bold=True)
        layout.addWidget(label)

        layout.addStretch()
        return layout

    def create_safe_distributors_table(self):
        """إنشاء جدول آمن للموزعين"""
        table = QTableWidget()
        apply_arabic_style(table, 10)

        # إعداد أعمدة بسيطة
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["الرقم", "الاسم", "الهاتف", "المنطقة"])

        # إضافة صف تجريبي
        table.setRowCount(1)
        table.setItem(0, 0, QTableWidgetItem("1"))
        table.setItem(0, 1, QTableWidgetItem("لا توجد موزعين"))
        table.setItem(0, 2, QTableWidgetItem("-"))
        table.setItem(0, 3, QTableWidgetItem("-"))

        return table

    def create_safe_distributors_buttons(self):
        """إنشاء أزرار آمنة للموزعين"""
        layout = QHBoxLayout()

        # زر إضافة
        add_btn = QPushButton("إضافة موزع")
        add_btn.clicked.connect(lambda: QMessageBox.information(self, "قريباً", "ميزة إضافة الموزع قيد التطوير"))
        layout.addWidget(add_btn)

        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(lambda: QMessageBox.information(self, "تحديث", "تم تحديث قائمة الموزعين"))
        layout.addWidget(refresh_btn)

        layout.addStretch()

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

        return layout

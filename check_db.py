#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية قاعدة البيانات
"""

import sqlite3

def main():
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('data/company_system.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    print('=== فحص بنية قاعدة البيانات ===')

    # فحص الجداول الموجودة
    cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
    tables = cursor.fetchall()
    print('الجداول الموجودة:')
    for table in tables:
        print(f'  - {table[0]}')

    print('\n=== فحص بنية جدول receipts ===')
    try:
        cursor.execute('PRAGMA table_info(receipts)')
        columns = cursor.fetchall()
        if columns:
            print('أعمدة جدول receipts:')
            for col in columns:
                print(f'  - {col[1]}: {col[2]}')
        else:
            print('جدول receipts غير موجود')
    except:
        print('جدول receipts غير موجود')

    print('\n=== فحص بنية جدول expenses ===')
    try:
        cursor.execute('PRAGMA table_info(expenses)')
        columns = cursor.fetchall()
        if columns:
            print('أعمدة جدول expenses:')
            for col in columns:
                print(f'  - {col[1]}: {col[2]}')
        else:
            print('جدول expenses غير موجود')
    except:
        print('جدول expenses غير موجود')

    print('\n=== فحص بنية جدول cash_boxes ===')
    try:
        cursor.execute('PRAGMA table_info(cash_boxes)')
        columns = cursor.fetchall()
        if columns:
            print('أعمدة جدول cash_boxes:')
            for col in columns:
                print(f'  - {col[1]}: {col[2]}')
        else:
            print('جدول cash_boxes غير موجود')
    except:
        print('جدول cash_boxes غير موجود')

    print('\n=== فحص بنية جدول transactions ===')
    try:
        cursor.execute('PRAGMA table_info(transactions)')
        columns = cursor.fetchall()
        if columns:
            print('أعمدة جدول transactions:')
            for col in columns:
                print(f'  - {col[1]}: {col[2]}')
        else:
            print('جدول transactions غير موجود')
    except:
        print('جدول transactions غير موجود')

    print('\n=== فحص بيانات transactions ===')
    try:
        cursor.execute('SELECT * FROM transactions LIMIT 5')
        rows = cursor.fetchall()
        if rows:
            print('عينة من بيانات transactions:')
            for row in rows:
                print(f'  ID: {row[0]}, Type: {row[1]}, Amount: {row[3]}, User: {row[7]}')
        else:
            print('جدول transactions فارغ')
    except Exception as e:
        print(f'خطأ في قراءة transactions: {e}')

    conn.close()

if __name__ == "__main__":
    main()

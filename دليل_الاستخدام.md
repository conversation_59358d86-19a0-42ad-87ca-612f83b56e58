# دليل استخدام نظام إدارة شركة الإنترنت

## 🎯 نظرة عامة
نظام شامل ومتكامل لإدارة شركات الإنترنت باللغة العربية مع دعم كامل للنصوص من اليمين إلى اليسار (RTL) والخطوط العربية.

## 🚀 التشغيل السريع

### الخطوة 1: التحضير
1. تأكد من تثبيت Python 3.8 أو أحدث
2. قم بتحميل ملفات النظام
3. افتح موجه الأوامر في مجلد النظام

### الخطوة 2: التشغيل
```bash
python start_system.py
```

سيقوم النظام بـ:
- فحص المتطلبات تلقائياً
- تثبيت المكتبات المفقودة
- إعداد الخطوط العربية
- تشغيل النظام

### الخطوة 3: الإعداد الأولي
1. **اختيار مجلد البيانات**: اختر مجلد لحفظ قاعدة البيانات والملفات
2. **تسجيل الدخول**: استخدم البيانات الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## 📋 الوظائف المتاحة

### ✅ الوظائف الجاهزة

#### 1. اشتراك جديد
- تسجيل مشترك جديد
- اختيار نوع الراوتر والسعر
- تحديد حالة الدفع
- طباعة الفاتورة

#### 2. تسليم راوتر
- البحث عن المشترك
- اختيار نوع الراوتر والباقة
- تحديد نوع الكبل وعدد الأمتار
- اختيار عامل التركيب
- خصم من المخزون تلقائياً

#### 3. تجديد باقة
- البحث عن المشترك
- عرض الباقة الحالية
- اختيار الباقة الجديدة
- تحديد حالة الدفع

#### 4. إغلاق الصندوق
- عرض إحصائيات اليوم
- مقارنة المبلغ المتوقع مع الفعلي
- تحديد حالة الصندوق (صحيح/زائد/ناقص)
- دعم عدة عملات

### 🚧 الوظائف قيد التطوير
- المشتريات من الموردين
- شحن رصيد الموزعين
- التسليم للعمال والجرد
- التقارير المفصلة
- الواجهات الإدارية
- نظام الطباعة المتقدم

## 🎨 مميزات الواجهة

### الخطوط العربية
- دعم تلقائي للخطوط العربية
- اختيار أفضل خط متاح (Segoe UI, Tahoma, Arial Unicode MS)
- عرض صحيح للنصوص العربية
- اتجاه من اليمين إلى اليسار

### التصميم
- واجهة حديثة وأنيقة
- ألوان مريحة للعين
- أزرار واضحة ومفهومة
- رسائل تأكيد وتحذير

## 🔧 الإعدادات

### إعدادات الشركة
- اسم الشركة
- العنوان ومعلومات الاتصال
- الشعار

### الإعدادات المالية
- رسم الاشتراك الافتراضي
- أسعار صرف العملات
- العملة الافتراضية

### إعدادات النظام
- مسار قاعدة البيانات
- إعدادات النسخ الاحتياطي
- إعدادات الطباعة

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **المستخدمين**: إدارة حسابات النظام
- **المشتركين**: بيانات العملاء
- **المنتجات**: الراوترات والكبلات
- **الباقات**: باقات الإنترنت
- **العمليات**: السجل المالي
- **المخزون**: إدارة المخزون

### النسخ الاحتياطي
- نسخ احتياطي تلقائي
- إمكانية الاستعادة
- حفظ في مجلد منفصل

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الخطوط العربية
```
الحل: النظام يختار تلقائياً أفضل خط متاح
```

#### 2. خطأ في قاعدة البيانات
```
الحل: تأكد من صلاحيات الكتابة في مجلد البيانات
```

#### 3. خطأ في المكتبات
```
الحل: شغل python start_system.py وسيتم التثبيت تلقائياً
```

### رسائل الخطأ
- **"PyQt5 غير متوفر"**: قم بتثبيت PyQt5
- **"فشل في تهيئة قاعدة البيانات"**: تحقق من الصلاحيات
- **"خطأ في الاتصال"**: أعد تشغيل النظام

## 📞 الدعم الفني

### معلومات النظام
- **الإصدار**: 1.0.0
- **اللغة**: Python 3.8+
- **الواجهة**: PyQt5
- **قاعدة البيانات**: SQLite

### المتطلبات
- Windows 10 أو أحدث
- Python 3.8+
- 4 GB RAM
- 1 GB مساحة فارغة

### ملفات مهمة
- `start_system.py` - ملف التشغيل الرئيسي
- `src/database/` - ملفات قاعدة البيانات
- `src/ui/` - ملفات الواجهات
- `src/utils/` - الأدوات المساعدة

## 🔐 الأمان

### كلمات المرور
- غير كلمة المرور الافتراضية فوراً
- استخدم كلمات مرور قوية
- لا تشارك بيانات الدخول

### النسخ الاحتياطي
- اعمل نسخة احتياطية يومياً
- احفظ النسخ في مكان آمن
- اختبر الاستعادة دورياً

## 📈 نصائح للاستخدام الأمثل

### الإدخال
- استخدم أسماء واضحة للمشتركين
- أدخل أرقام الهواتف بشكل صحيح
- تأكد من البيانات قبل الحفظ

### المتابعة
- راجع إحصائيات اليوم بانتظام
- أغلق الصندوق يومياً
- تابع حالة المخزون

### الصيانة
- نظف قاعدة البيانات شهرياً
- احذف العمليات القديمة
- حدث النظام عند توفر إصدارات جديدة

---

**تم تطوير هذا النظام بعناية لخدمة شركات الإنترنت العربية** 🇸🇾

للمزيد من المساعدة، راجع ملف README.md أو تواصل مع فريق التطوير.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح سند القبض
"""

import sys
import os
sys.path.append('src')

def test_receipt_to_cash_box():
    """اختبار إضافة سند قبض للصندوق"""
    
    print("🧪 اختبار إضافة سند قبض للصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # 1. فتح شيفت جديد
        print("\n1️⃣ فتح شيفت جديد...")
        shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
        
        if shift_opened:
            print("✅ تم فتح شيفت جديد")
        else:
            print("⚠️ الشيفت مفتوح مسبقاً")
        
        # 2. فحص رصيد الصندوق قبل إضافة سند القبض
        cash_box_balance_before = treasury_manager.get_cash_box_balance(current_user['id'])
        print(f"💰 رصيد الصندوق قبل سند القبض: {cash_box_balance_before:,} ل.س")
        
        # 3. إضافة سند قبض (محاكاة)
        print("\n2️⃣ إضافة سند قبض...")
        receipt_amount = 75000
        receipt_success = treasury_manager.add_to_cash_box(
            user_id=current_user['id'],
            amount=receipt_amount,
            description="سند قبض من موزع الشام: دفعة شهر يناير",
            transaction_type="receipt"
        )
        
        if receipt_success:
            print(f"✅ تم إضافة سند قبض بقيمة {receipt_amount:,} ل.س")
            
            # فحص رصيد الصندوق بعد إضافة سند القبض
            cash_box_balance_after = treasury_manager.get_cash_box_balance(current_user['id'])
            print(f"💰 رصيد الصندوق بعد سند القبض: {cash_box_balance_after:,} ل.س")
            
            # التحقق من الزيادة
            expected_increase = receipt_amount
            actual_increase = cash_box_balance_after - cash_box_balance_before
            
            if abs(actual_increase - expected_increase) < 1:
                print(f"✅ تم إضافة المبلغ بشكل صحيح: زيادة {actual_increase:,} ل.س")
                return True
            else:
                print(f"❌ خطأ في الإضافة: متوقع {expected_increase:,} ل.س، فعلي {actual_increase:,} ل.س")
                return False
        else:
            print("❌ فشل في إضافة سند القبض")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سند القبض: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voucher_from_cash_box():
    """اختبار خصم سند دفع من الصندوق"""
    
    print("\n🧪 اختبار خصم سند دفع من الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص رصيد الصندوق قبل خصم سند الدفع
        cash_box_balance_before = treasury_manager.get_cash_box_balance(current_user['id'])
        print(f"💰 رصيد الصندوق قبل سند الدفع: {cash_box_balance_before:,} ل.س")
        
        if cash_box_balance_before > 0:
            # خصم سند دفع
            voucher_amount = min(30000, cash_box_balance_before)
            voucher_success = treasury_manager.subtract_from_cash_box(
                user_id=current_user['id'],
                amount=voucher_amount,
                description="سند دفع لمورد الكهرباء: فاتورة شهر يناير",
                transaction_type="voucher"
            )
            
            if voucher_success:
                print(f"✅ تم خصم سند دفع بقيمة {voucher_amount:,} ل.س")
                
                # فحص رصيد الصندوق بعد خصم سند الدفع
                cash_box_balance_after = treasury_manager.get_cash_box_balance(current_user['id'])
                print(f"💰 رصيد الصندوق بعد سند الدفع: {cash_box_balance_after:,} ل.س")
                
                # التحقق من النقصان
                expected_decrease = voucher_amount
                actual_decrease = cash_box_balance_before - cash_box_balance_after
                
                if abs(actual_decrease - expected_decrease) < 1:
                    print(f"✅ تم خصم المبلغ بشكل صحيح: نقصان {actual_decrease:,} ل.س")
                    return True
                else:
                    print(f"❌ خطأ في الخصم: متوقع {expected_decrease:,} ل.س، فعلي {actual_decrease:,} ل.س")
                    return False
            else:
                print("❌ فشل في خصم سند الدفع")
                return False
        else:
            print("⚠️ لا يوجد رصيد في الصندوق للخصم")
            return True  # لا نعتبرها فشل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سند الدفع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cash_box_closure_with_receipts():
    """اختبار إغلاق الصندوق مع سندات القبض والدفع"""
    
    print("\n🧪 اختبار إغلاق الصندوق مع السندات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص رصيد الصندوق قبل الإغلاق
        cash_box_balance_before_closure = treasury_manager.get_cash_box_balance(current_user['id'])
        print(f"💰 رصيد الصندوق قبل الإغلاق: {cash_box_balance_before_closure:,} ل.س")
        
        # فحص الخزينة اليومية قبل الإغلاق
        daily_balance_before_closure = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"💰 الخزينة اليومية قبل الإغلاق: {daily_balance_before_closure:,} ل.س")
        
        # إغلاق الصندوق
        print("\n3️⃣ إغلاق الصندوق...")
        closure_success = treasury_manager.close_cash_box_to_daily_treasury(
            user_id=current_user['id'],
            actual_cash=cash_box_balance_before_closure,
            notes="اختبار إغلاق الصندوق مع السندات"
        )
        
        if closure_success:
            print("✅ تم إغلاق الصندوق بنجاح")
            
            # فحص الخزينة اليومية بعد الإغلاق
            daily_balance_after_closure = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
            print(f"💰 الخزينة اليومية بعد الإغلاق: {daily_balance_after_closure:,} ل.س")
            
            # التحقق من النقل
            if cash_box_balance_before_closure > 0:
                expected_increase = cash_box_balance_before_closure
                actual_increase = daily_balance_after_closure - daily_balance_before_closure
                
                if abs(actual_increase - expected_increase) < 1:
                    print(f"✅ تم نقل المبلغ بشكل صحيح: {actual_increase:,} ل.س")
                    return True
                else:
                    print(f"❌ خطأ في النقل: متوقع {expected_increase:,} ل.س، فعلي {actual_increase:,} ل.س")
                    return False
            else:
                print("✅ تم إغلاق الصندوق الفارغ بنجاح")
                return True
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح سند القبض والدفع")
    print("=" * 60)
    
    # اختبار إضافة سند قبض
    receipt_test = test_receipt_to_cash_box()
    
    # اختبار خصم سند دفع
    voucher_test = test_voucher_from_cash_box()
    
    # اختبار إغلاق الصندوق مع السندات
    closure_test = test_cash_box_closure_with_receipts()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • إضافة سند قبض للصندوق: {'✅ يعمل' if receipt_test else '❌ لا يعمل'}")
    print(f"  • خصم سند دفع من الصندوق: {'✅ يعمل' if voucher_test else '❌ لا يعمل'}")
    print(f"  • إغلاق الصندوق مع السندات: {'✅ يعمل' if closure_test else '❌ لا يعمل'}")
    
    if all([receipt_test, voucher_test, closure_test]):
        print("\n🎉 تم إصلاح مشكلة السندات بالكامل!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح واجهة سند القبض")
        print("    • تستخدم النظام الموحد بدلاً من جدول transactions")
        print("    • تضيف المبلغ للصندوق مباشرة")
        print("    • تظهر في واجهة إغلاق الصندوق")
        
        print("  ✅ إصلاح واجهة سند الدفع")
        print("    • تستخدم النظام الموحد")
        print("    • تخصم المبلغ من الصندوق مباشرة")
        print("    • تظهر في واجهة إغلاق الصندوق")
        
        print("  ✅ إضافة دالة subtract_from_cash_box")
        print("    • لخصم المبالغ من الصندوق")
        print("    • تعمل مع النظام الموحد")
        print("    • تسجل المعاملات بشكل صحيح")
        
        print("\n🚀 النظام الآن:")
        print("  • سند القبض يضيف للصندوق ويظهر في الإغلاق")
        print("  • سند الدفع يخصم من الصندوق ويظهر في الإغلاق")
        print("  • إغلاق الصندوق ينقل جميع المبالغ للخزينة اليومية")
        print("  • التكامل الكامل مع النظام الموحد")
        
        print("\n🎯 للاستخدام:")
        print("  1. أضف سند قبض → يظهر في رصيد الصندوق")
        print("  2. أضف سند دفع → يخصم من رصيد الصندوق")
        print("  3. أغلق الصندوق → جميع المبالغ تنتقل للخزينة اليومية")
        print("  4. الأرصدة تظهر بشكل صحيح في جميع الواجهات")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

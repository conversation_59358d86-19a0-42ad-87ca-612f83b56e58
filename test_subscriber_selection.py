#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار اختيار المشترك في واجهة تسليم الراوتر
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager

def test_subscriber_data_handling():
    """اختبار معالجة بيانات المشترك"""
    print("=== اختبار معالجة بيانات المشترك ===")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # جلب المشتركين
    subscribers = db_manager.fetch_all("""
        SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid
        FROM subscribers s
        LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
        WHERE rd.id IS NULL
        ORDER BY s.name
        LIMIT 3
    """)
    
    print(f"تم جلب {len(subscribers)} مشترك للاختبار")
    
    for i, subscriber in enumerate(subscribers):
        print(f"\n--- اختبار المشترك {i+1} ---")
        print(f"نوع البيانات: {type(subscriber)}")
        print(f"المفاتيح المتاحة: {list(subscriber.keys()) if hasattr(subscriber, 'keys') else 'لا توجد مفاتيح'}")
        
        # اختبار الوصول للبيانات
        try:
            name = subscriber['name']
            phone = subscriber['phone']
            sub_paid = subscriber['subscription_paid']
            router_paid = subscriber['router_paid']
            
            print(f"✅ الاسم: {name}")
            print(f"✅ الهاتف: {phone}")
            print(f"✅ مدفوع الاشتراك: {sub_paid} (نوع: {type(sub_paid)})")
            print(f"✅ مدفوع الراوتر: {router_paid} (نوع: {type(router_paid)})")
            
            # اختبار التحويل لـ boolean
            sub_paid_bool = bool(int(sub_paid)) if sub_paid else False
            router_paid_bool = bool(int(router_paid)) if router_paid else False
            
            print(f"✅ مدفوع الاشتراك (boolean): {sub_paid_bool}")
            print(f"✅ مدفوع الراوتر (boolean): {router_paid_bool}")
            
        except Exception as e:
            print(f"❌ خطأ في الوصول للبيانات: {e}")
            import traceback
            traceback.print_exc()
    
    db_manager.close()

def simulate_subscriber_selection():
    """محاكاة اختيار مشترك"""
    print("\n=== محاكاة اختيار مشترك ===")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    # جلب مشترك واحد للاختبار
    subscriber = db_manager.fetch_one("""
        SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid
        FROM subscribers s
        LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
        WHERE rd.id IS NULL
        ORDER BY s.name
        LIMIT 1
    """)
    
    if not subscriber:
        print("❌ لا يوجد مشتركون للاختبار")
        db_manager.close()
        return
    
    print(f"اختبار المشترك: {subscriber['name']}")
    
    # محاكاة دالة set_subscriber_data
    try:
        print("--- محاكاة set_subscriber_data ---")
        
        # التعامل مع sqlite3.Row بأمان
        try:
            subscriber_name = str(subscriber['name']) if subscriber['name'] else 'غير محدد'
        except (KeyError, TypeError):
            subscriber_name = 'غير محدد'
            
        print(f"✅ اسم المشترك: {subscriber_name}")
        
        # تعيين حالة التسديد بأمان
        try:
            subscription_paid = bool(int(subscriber['subscription_paid'])) if subscriber['subscription_paid'] else False
        except (KeyError, TypeError, ValueError):
            subscription_paid = False
            
        try:
            router_paid = bool(int(subscriber['router_paid'])) if subscriber['router_paid'] else False
        except (KeyError, TypeError, ValueError):
            router_paid = False
        
        print(f"✅ حالة تسديد الاشتراك: {subscription_paid}")
        print(f"✅ حالة تسديد الراوتر: {router_paid}")
        
        print("✅ تم اختبار set_subscriber_data بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة set_subscriber_data: {e}")
        import traceback
        traceback.print_exc()
    
    db_manager.close()

if __name__ == "__main__":
    test_subscriber_data_handling()
    simulate_subscriber_selection()
    print("\n🎉 انتهى الاختبار - إذا لم تظهر أخطاء، فالإصلاحات تعمل بشكل صحيح!")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مزامنة قاعدة بيانات المستخدمين
"""

import sys
import os
import hashlib
import shutil
from pathlib import Path
sys.path.append('src')

def check_database_paths():
    """فحص مسارات قواعد البيانات المختلفة"""
    
    print("🔍 فحص مسارات قواعد البيانات...")
    
    possible_paths = [
        'data/company_system.db',
        './data/company_system.db',
        'company_system.db',
        './company_system.db'
    ]
    
    existing_dbs = []
    
    for path in possible_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ موجود: {path} (حجم: {size:,} بايت)")
            existing_dbs.append((path, size))
        else:
            print(f"❌ غير موجود: {path}")
    
    return existing_dbs

def check_users_in_database(db_path):
    """فحص المستخدمين في قاعدة بيانات محددة"""
    
    print(f"\n👥 فحص المستخدمين في: {db_path}")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات بالمسار المحدد
        if db_path.endswith('company_system.db'):
            # إذا كان المسار يحتوي على اسم الملف، استخدم المجلد
            project_dir = str(Path(db_path).parent.parent) if 'data' in db_path else '.'
        else:
            project_dir = db_path
        
        db = DatabaseManager(project_dir)
        
        # فحص المستخدمين
        users = db.fetch_all("SELECT id, username, full_name, role, is_active FROM users")
        
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        if users:
            for user in users:
                status = "نشط" if user[4] == 1 else "غير نشط"
                print(f"  • ID: {user[0]}, اسم المستخدم: '{user[1]}', الاسم: '{user[2]}', الدور: '{user[3]}', الحالة: {status}")
        
        return users
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات {db_path}: {e}")
        return []

def create_users_in_main_database():
    """إنشاء المستخدمين في قاعدة البيانات الرئيسية"""
    
    print("\n👤 إنشاء المستخدمين في قاعدة البيانات الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء قاعدة البيانات الرئيسية (نفس الطريقة في system_launcher)
        db = DatabaseManager('.')
        
        # المستخدمين الافتراضيين
        default_users = [
            ('admin', '123', 'المدير العام', '<EMAIL>', 'admin'),
            ('manager', '123', 'المدير', '<EMAIL>', 'manager'),
            ('employee', '123', 'الموظف', '<EMAIL>', 'employee'),
            ('accountant', '123', 'المحاسب', '<EMAIL>', 'accountant'),
            ('user1', '123', 'مستخدم 1', '<EMAIL>', 'user'),
            ('cashier', '123', 'أمين الصندوق', '<EMAIL>', 'cashier')
        ]
        
        created_count = 0
        
        for username, password, full_name, email, role in default_users:
            # تشفير كلمة المرور
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # التحقق من عدم وجود المستخدم
            existing = db.fetch_one("SELECT id FROM users WHERE username = ?", (username,))
            
            if existing:
                print(f"  ⚠️ المستخدم '{username}' موجود بالفعل")
                continue
            
            # إنشاء المستخدم
            result = db.execute_query("""
                INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
            """, (username, password_hash, full_name, email, role))
            
            if result:
                print(f"  ✅ تم إنشاء المستخدم: {username} / {password} ({role})")
                created_count += 1
            else:
                print(f"  ❌ فشل في إنشاء المستخدم: {username}")
        
        print(f"\n📊 تم إنشاء {created_count} مستخدم جديد")
        return created_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_with_main_database():
    """اختبار تسجيل الدخول مع قاعدة البيانات الرئيسية"""
    
    print("\n🧪 اختبار تسجيل الدخول مع قاعدة البيانات الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء قاعدة البيانات (نفس الطريقة في system_launcher)
        db = DatabaseManager('.')
        
        # اختبار تسجيل الدخول
        username = 'admin'
        password = '123'
        
        print(f"📝 اختبار تسجيل الدخول:")
        print(f"  • اسم المستخدم: {username}")
        print(f"  • كلمة المرور: {password}")
        
        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        
        # البحث عن المستخدم
        user = db.fetch_one("""
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, hashed_password))
        
        if user:
            print("✅ تسجيل الدخول نجح!")
            print(f"المستخدم: {user['full_name']} ({user['role']})")
            return True
        else:
            print("❌ تسجيل الدخول فشل!")
            
            # تشخيص السبب
            user_check = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
            if user_check:
                print(f"المستخدم موجود لكن:")
                if user_check['password_hash'] != hashed_password:
                    print(f"  • كلمة المرور خاطئة")
                if user_check['is_active'] != 1:
                    print(f"  • المستخدم غير نشط")
            else:
                print("المستخدم غير موجود")
            
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def backup_and_sync_databases():
    """نسخ احتياطي ومزامنة قواعد البيانات"""
    
    print("\n💾 نسخ احتياطي ومزامنة قواعد البيانات...")
    
    try:
        # إنشاء نسخة احتياطية
        if os.path.exists('data/company_system.db'):
            backup_path = f'data/company_system_backup_{int(time.time())}.db'
            shutil.copy2('data/company_system.db', backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # التأكد من وجود مجلد data
        os.makedirs('data', exist_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النسخ الاحتياطي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 مزامنة قاعدة بيانات المستخدمين")
    print("=" * 50)
    
    # فحص مسارات قواعد البيانات
    existing_dbs = check_database_paths()
    
    # فحص المستخدمين في كل قاعدة بيانات
    for db_path, size in existing_dbs:
        users = check_users_in_database(db_path)
    
    # نسخ احتياطي ومزامنة
    backup_success = backup_and_sync_databases()
    
    # إنشاء المستخدمين في قاعدة البيانات الرئيسية
    users_created = create_users_in_main_database()
    
    # اختبار تسجيل الدخول
    login_test = test_login_with_main_database()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"  • قواعد البيانات الموجودة: {len(existing_dbs)}")
    print(f"  • النسخ الاحتياطي: {'✅ نجح' if backup_success else '❌ فشل'}")
    print(f"  • إنشاء المستخدمين: {'✅ نجح' if users_created else '❌ فشل'}")
    print(f"  • اختبار تسجيل الدخول: {'✅ نجح' if login_test else '❌ فشل'}")
    
    if login_test:
        print("\n🎉 تم إصلاح مشكلة قاعدة البيانات!")
        
        print("\n📋 المستخدمين المتاحين:")
        print("  • admin / 123 (المدير العام)")
        print("  • manager / 123 (المدير)")
        print("  • employee / 123 (الموظف)")
        print("  • accountant / 123 (المحاسب)")
        print("  • user1 / 123 (مستخدم)")
        print("  • cashier / 123 (أمين الصندوق)")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. استخدم: admin / 123")
        print("  3. تسجيل الدخول يجب أن يعمل الآن!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")

if __name__ == "__main__":
    import time
    main()

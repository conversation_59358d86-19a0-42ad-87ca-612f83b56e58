#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة معاملات تجريبية لليوم الحالي
"""

import sqlite3
from datetime import date

def main():
    conn = sqlite3.connect('data/company_system.db')
    cursor = conn.cursor()

    today = date.today().strftime('%Y-%m-%d')
    print(f'=== إضافة معاملات تجريبية لتاريخ {today} ===')

    try:
        # إضافة اشتراك جديد
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, reference_id, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            "اشتراك جديد",
            "اشتراك جديد للمشترك: أحمد محمد",
            200000,
            1,
            "admin",
            f"{today} 10:30:00"
        ))
        print("✅ تم إضافة اشتراك جديد: 200,000 ل.س")

        # إضافة تسليم راوتر
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, reference_id, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            "تسليم راوتر",
            "تسليم راوتر للمشترك: سارة أحمد",
            150000,
            2,
            "admin",
            f"{today} 14:15:00"
        ))
        print("✅ تم إضافة تسليم راوتر: 150,000 ل.س")

        # إضافة تجديد باقة
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, reference_id, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            "تجديد باقة",
            "تجديد باقة للمشترك: محمد علي",
            80000,
            3,
            "admin",
            f"{today} 16:45:00"
        ))
        print("✅ تم إضافة تجديد باقة: 80,000 ل.س")

        # إضافة مصروف
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, user_name, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, (
            "مصروف",
            "مصروف كهرباء: فاتورة الكهرباء الشهرية",
            -50000,
            "admin",
            f"{today} 11:20:00"
        ))
        print("✅ تم إضافة مصروف: 50,000 ل.س")

        conn.commit()
        print("\n✅ تم حفظ جميع المعاملات بنجاح")

        # التحقق من البيانات
        print(f"\n=== التحقق من المعاملات المضافة ===")
        cursor.execute('SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = ?', (today,))
        count = cursor.fetchone()[0]
        print(f"عدد المعاملات اليوم: {count}")

        cursor.execute("""
            SELECT SUM(amount) FROM transactions 
            WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
        """, (today, 'admin'))
        sales_total = cursor.fetchone()[0] or 0
        print(f"إجمالي المبيعات: {sales_total} ل.س")

        cursor.execute("""
            SELECT SUM(ABS(amount)) FROM transactions 
            WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف' AND amount < 0
        """, (today, 'admin'))
        expenses_total = cursor.fetchone()[0] or 0
        print(f"إجمالي المصاريف: {expenses_total} ل.س")

    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import hashlib
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap

try:
    from ..utils.arabic_support import apply_arabic_style
except ImportError:
    from utils.arabic_support import apply_arabic_style

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = None
        
        self.setup_ui()
        self.center_window()
        self.setup_connections()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget

            # الحصول على معلومات الشاشة
            screen = QDesktopWidget().screenGeometry()
            window = self.geometry()

            # حساب الموضع المركزي
            x = (screen.width() - window.width()) // 2
            y = (screen.height() - window.height()) // 2

            # تحريك النافذة للمركز
            self.move(x, y)

        except Exception as e:
            print(f"❌ خطأ في توسيط نافذة تسجيل الدخول: {e}")
            # في حالة الخطأ، استخدم موضع افتراضي
            self.move(200, 200)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - برنامج اسامة لادارة شركات الإنترنت")
        self.setFixedSize(450, 350)
        self.setModal(True)

        # تطبيق الخط العربي على النافذة
        apply_arabic_style(self, 10)
        
        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان النظام
        title_label = QLabel("نظام إدارة شركة حمودة نت")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
                font-weight: bold;
            }
        """)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.StyledPanel)
        login_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        # تخطيط الإطار
        frame_layout = QGridLayout(login_frame)
        frame_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        apply_arabic_style(username_label, 10, bold=True)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        apply_arabic_style(self.username_edit, 10)
        self.username_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 10px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)

        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        apply_arabic_style(password_label, 11, bold=True)
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        apply_arabic_style(self.password_edit, 11)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 10px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        
        # إضافة الحقول إلى التخطيط
        frame_layout.addWidget(username_label, 0, 0)
        frame_layout.addWidget(self.username_edit, 0, 1)
        frame_layout.addWidget(password_label, 1, 0)
        frame_layout.addWidget(self.password_edit, 1, 1)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        apply_arabic_style(self.login_button, 11, bold=True)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        self.cancel_button = QPushButton("إلغاء")
        apply_arabic_style(self.cancel_button, 11)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addWidget(login_frame)
        main_layout.addLayout(buttons_layout)
        
        # إضافة مساحة فارغة
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        main_layout.addItem(spacer)
        
        self.setLayout(main_layout)
        
        # الحقول فارغة - المستخدم يدخل البيانات يدوياً
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.handle_login)
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()

        print(f"=== محاولة تسجيل الدخول ===")
        print(f"اسم المستخدم المدخل: '{username}'")
        print(f"كلمة المرور المدخلة: '{password}'")
        print(f"طول اسم المستخدم: {len(username)}")
        print(f"طول كلمة المرور: {len(password)}")

        if not username or not password:
            QMessageBox.warning(
                self,
                "تحذير",
                "يرجى إدخال اسم المستخدم وكلمة المرور"
            )
            return

        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"كلمة المرور المشفرة: {hashed_password[:20]}...")

        # فحص تفصيلي - البحث عن المستخدم بالاسم فقط أولاً
        user_check = self.db_manager.fetch_one(
            "SELECT * FROM users WHERE username = ?",
            (username,)
        )

        if user_check:
            print(f"✅ المستخدم موجود: {user_check['username']}")
            if user_check['password_hash']:
                print(f"كلمة المرور في قاعدة البيانات: {user_check['password_hash'][:20]}...")
            else:
                print("⚠️ كلمة المرور غير موجودة في قاعدة البيانات")
            print(f"المستخدم نشط: {user_check['is_active']}")

            if user_check['password_hash'] and user_check['password_hash'] == hashed_password:
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور خاطئة")
                if user_check['password_hash']:
                    print(f"المتوقع: {user_check['password_hash']}")
                else:
                    print("المتوقع: None (كلمة المرور غير موجودة)")
                print(f"المدخل: {hashed_password}")
        else:
            print("❌ المستخدم غير موجود")

        # البحث عن المستخدم في قاعدة البيانات
        user = self.db_manager.fetch_one(
            "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
            (username, hashed_password)
        )

        if user:
            # تحديث وقت آخر تسجيل دخول
            self.db_manager.execute_query(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (user['id'],)
            )
            
            # حفظ بيانات المستخدم الحالي
            self.current_user = {
                'id': user['id'],
                'username': user['username'],
                'full_name': user['full_name'],
                'role': user['role']
            }
            
            # إرسال إشارة نجح تسجيل الدخول
            self.login_successful.emit(self.current_user)
            
            self.accept()
        else:
            QMessageBox.critical(
                self,
                "خطأ",
                "اسم المستخدم أو كلمة المرور غير صحيحة"
            )
            self.password_edit.clear()
            self.username_edit.setFocus()
            
    def get_current_user(self):
        """الحصول على بيانات المستخدم الحالي"""
        return self.current_user
        
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

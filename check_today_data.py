#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بيانات اليوم في قاعدة البيانات
"""

import sqlite3
from datetime import date

def main():
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('data/company_system.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    today = date.today().strftime('%Y-%m-%d')
    print(f'=== فحص بيانات اليوم: {today} ===')

    print('\n=== فحص جدول transactions ===')
    cursor.execute('SELECT * FROM transactions WHERE DATE(created_at) = ?', (today,))
    transactions = cursor.fetchall()
    print(f'عدد المعاملات اليوم: {len(transactions)}')
    for t in transactions:
        print(f'  - النوع: {t[1]}, المبلغ: {t[3]}, المستخدم: {t[7]}')

    print('\n=== فحص جدول receipts ===')
    cursor.execute('SELECT * FROM receipts WHERE DATE(receipt_date) = ?', (today,))
    receipts = cursor.fetchall()
    print(f'عدد المقبوضات اليوم: {len(receipts)}')
    for r in receipts:
        print(f'  - المبلغ: {r[4]}, المستخدم: {r[8]}')

    print('\n=== فحص جدول expenses ===')
    cursor.execute('SELECT * FROM expenses WHERE DATE(expense_date) = ?', (today,))
    expenses = cursor.fetchall()
    print(f'عدد المصاريف اليوم: {len(expenses)}')
    for e in expenses:
        print(f'  - النوع: {e[1]}, المبلغ: {e[2]}, المستخدم: {e[6]}')

    print('\n=== فحص جدول router_deliveries ===')
    try:
        cursor.execute('PRAGMA table_info(router_deliveries)')
        columns = cursor.fetchall()
        print('أعمدة جدول router_deliveries:')
        for col in columns:
            print(f'  - {col[1]}: {col[2]}')
            
        cursor.execute('SELECT * FROM router_deliveries WHERE DATE(created_at) = ?', (today,))
        deliveries = cursor.fetchall()
        print(f'عدد تسليمات الراوترات اليوم: {len(deliveries)}')
        for d in deliveries:
            print(f'  - ID: {d[0]}, البيانات: {dict(d)}')
    except Exception as e:
        print(f'خطأ في فحص router_deliveries: {e}')

    print('\n=== اختبار الاستعلامات المستخدمة في واجهة إغلاق الصناديق ===')
    
    # اختبار استعلام المقبوضات
    print('\n--- اختبار استعلام المقبوضات ---')
    cursor.execute("""
        SELECT COALESCE(SUM(amount), 0) as total FROM receipts
        WHERE DATE(receipt_date) = ? AND user_name = ?
    """, (today, 'admin'))
    receipts_result = cursor.fetchone()
    print(f'إجمالي المقبوضات لـ admin: {receipts_result[0] if receipts_result else 0}')
    
    # اختبار استعلام المصاريف
    print('\n--- اختبار استعلام المصاريف ---')
    cursor.execute("""
        SELECT COALESCE(SUM(amount), 0) as total FROM expenses
        WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type != 'رواتب'
    """, (today, 'admin'))
    expenses_result = cursor.fetchone()
    print(f'إجمالي المصاريف لـ admin: {expenses_result[0] if expenses_result else 0}')
    
    # اختبار استعلام transactions للمبيعات
    print('\n--- اختبار استعلام transactions للمبيعات ---')
    cursor.execute("""
        SELECT COALESCE(SUM(amount), 0) as total FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
    """, (today, 'admin'))
    transactions_sales = cursor.fetchone()
    print(f'إجمالي المبيعات من transactions لـ admin: {transactions_sales[0] if transactions_sales else 0}')

    conn.close()

if __name__ == "__main__":
    main()

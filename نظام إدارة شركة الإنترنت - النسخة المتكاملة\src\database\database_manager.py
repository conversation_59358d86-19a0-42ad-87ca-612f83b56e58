# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - النسخة المتكاملة
Database Manager - Integrated Version

يدير جميع عمليات قاعدة البيانات مع ضمان عدم اختلاط الصناديق
وإدارة المخزون بشكل صحيح
"""

import sqlite3
import os
import hashlib
from datetime import datetime, date
import json

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path="data/company_system.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.ensure_data_directory()
        
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            print(f"الاستعلام: {query}")
            if params:
                print(f"المعاملات: {params}")
            return None
    
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return []
    
    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.fetchone()
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return None
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            print("=== تهيئة قاعدة البيانات ===")
            
            # إنشاء الجداول الأساسية
            self.create_users_table()
            self.create_subscribers_table()
            self.create_packages_table()
            self.create_products_table()
            self.create_inventory_table()
            self.create_suppliers_table()
            self.create_distributors_table()
            self.create_workers_table()
            
            # إنشاء جداول العمليات المالية
            self.create_cash_boxes_table()
            self.create_receipts_table()
            self.create_expenses_table()
            self.create_purchases_table()
            self.create_treasury_transfers_table()
            
            # إنشاء جداول العمليات
            self.create_subscriptions_table()
            self.create_router_deliveries_table()
            self.create_package_renewals_table()
            self.create_worker_deliveries_table()
            self.create_maintenance_orders_table()
            
            # إنشاء جداول التقارير والإعدادات
            self.create_system_settings_table()
            self.create_invoice_settings_table()
            
            # إنشاء المستخدم الافتراضي
            self.create_default_admin()
            
            # إنشاء الإعدادات الافتراضية
            self.create_default_settings()
            
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        query = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'employee', 'accountant')),
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المستخدمين")
    
    def create_subscribers_table(self):
        """إنشاء جدول المشتركين"""
        query = """
        CREATE TABLE IF NOT EXISTS subscribers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            national_id TEXT,
            subscription_date DATE DEFAULT CURRENT_DATE,
            package_id INTEGER,
            balance DECIMAL(10,2) DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (package_id) REFERENCES packages(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المشتركين")
    
    def create_packages_table(self):
        """إنشاء جدول الباقات"""
        query = """
        CREATE TABLE IF NOT EXISTS packages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            speed TEXT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول الباقات")
    
    def create_products_table(self):
        """إنشاء جدول المنتجات"""
        query = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            purchase_unit TEXT NOT NULL,  -- وحدة الشراء (بكرة، صندوق، إلخ)
            sale_unit TEXT NOT NULL,      -- وحدة البيع (متر، قطعة، إلخ)
            conversion_factor DECIMAL(10,2) NOT NULL DEFAULT 1,  -- معامل التحويل
            purchase_price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NOT NULL,
            min_stock INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المنتجات")

    def create_inventory_table(self):
        """إنشاء جدول المخزون"""
        query = """
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            location TEXT NOT NULL DEFAULT 'main_stock',  -- main_stock, worker_stock
            worker_id INTEGER,  -- إذا كان في مخزون عامل
            quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
            unit TEXT NOT NULL,  -- الوحدة المستخدمة
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (worker_id) REFERENCES workers(id),
            FOREIGN KEY (updated_by) REFERENCES users(id),
            UNIQUE(product_id, location, worker_id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المخزون")

    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        query = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance DECIMAL(10,2) DEFAULT 0,  -- رصيد المورد (مدين/دائن)
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول الموردين")

    def create_distributors_table(self):
        """إنشاء جدول الموزعين"""
        query = """
        CREATE TABLE IF NOT EXISTS distributors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance DECIMAL(10,2) DEFAULT 0,  -- رصيد الموزع
            commission_rate DECIMAL(5,2) DEFAULT 0,  -- نسبة العمولة
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول الموزعين")

    def create_workers_table(self):
        """إنشاء جدول العمال"""
        query = """
        CREATE TABLE IF NOT EXISTS workers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            national_id TEXT,
            salary DECIMAL(10,2) DEFAULT 0,
            hire_date DATE DEFAULT CURRENT_DATE,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول العمال")

    def create_cash_boxes_table(self):
        """إنشاء جدول الصناديق - مع ضمان عدم الاختلاط"""
        query = """
        CREATE TABLE IF NOT EXISTS cash_boxes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATE NOT NULL,
            user_id INTEGER NOT NULL,
            opening_balance DECIMAL(10,2) DEFAULT 0,
            total_receipts DECIMAL(10,2) DEFAULT 0,
            total_expenses DECIMAL(10,2) DEFAULT 0,
            closing_balance DECIMAL(10,2) DEFAULT 0,
            is_closed BOOLEAN DEFAULT 0,
            closed_at TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            UNIQUE(date, user_id)  -- ضمان صندوق واحد لكل مستخدم في اليوم
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول الصناديق")

    def create_receipts_table(self):
        """إنشاء جدول المقبوضات"""
        query = """
        CREATE TABLE IF NOT EXISTS receipts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            receipt_number TEXT UNIQUE NOT NULL,
            date DATE NOT NULL,
            cash_box_id INTEGER NOT NULL,
            source_type TEXT NOT NULL,  -- subscription, balance_charge, distributor, other
            source_id INTEGER,  -- معرف المصدر (مشترك، موزع، إلخ)
            amount DECIMAL(10,2) NOT NULL,
            description TEXT NOT NULL,
            received_from TEXT NOT NULL,
            payment_method TEXT DEFAULT 'cash',  -- cash, bank_transfer, check
            reference_number TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المقبوضات")

    def create_expenses_table(self):
        """إنشاء جدول المصاريف"""
        query = """
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            expense_number TEXT UNIQUE NOT NULL,
            date DATE NOT NULL,
            cash_box_id INTEGER NOT NULL,
            category TEXT NOT NULL,  -- salary, maintenance, supplies, other
            amount DECIMAL(10,2) NOT NULL,
            description TEXT NOT NULL,
            paid_to TEXT NOT NULL,
            payment_method TEXT DEFAULT 'cash',
            reference_number TEXT,
            is_salary BOOLEAN DEFAULT 0,  -- للتمييز بين المصاريف العادية والرواتب
            worker_id INTEGER,  -- إذا كان راتب عامل
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
            FOREIGN KEY (worker_id) REFERENCES workers(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المصاريف")

    def create_purchases_table(self):
        """إنشاء جدول المشتريات"""
        query = """
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_number TEXT UNIQUE NOT NULL,
            date DATE NOT NULL,
            supplier_id INTEGER NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            paid_amount DECIMAL(10,2) DEFAULT 0,
            payment_status TEXT DEFAULT 'pending',  -- pending, partial, paid
            payment_method TEXT DEFAULT 'cash',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)

        # جدول تفاصيل المشتريات
        query = """
        CREATE TABLE IF NOT EXISTS purchase_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity DECIMAL(10,2) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول المشتريات")

    def create_treasury_transfers_table(self):
        """إنشاء جدول نقل الخزينة"""
        query = """
        CREATE TABLE IF NOT EXISTS treasury_transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transfer_number TEXT UNIQUE NOT NULL,
            date DATE NOT NULL,
            from_cash_box_id INTEGER NOT NULL,
            to_cash_box_id INTEGER,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT NOT NULL,
            transfer_type TEXT NOT NULL,  -- to_bank, to_safe, to_other_cashier
            reference_number TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (from_cash_box_id) REFERENCES cash_boxes(id),
            FOREIGN KEY (to_cash_box_id) REFERENCES cash_boxes(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول نقل الخزينة")

    def create_subscriptions_table(self):
        """إنشاء جدول الاشتراكات"""
        query = """
        CREATE TABLE IF NOT EXISTS subscriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscriber_id INTEGER NOT NULL,
            package_id INTEGER NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_status TEXT DEFAULT 'pending',
            receipt_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (subscriber_id) REFERENCES subscribers(id),
            FOREIGN KEY (package_id) REFERENCES packages(id),
            FOREIGN KEY (receipt_id) REFERENCES receipts(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول الاشتراكات")

    def create_router_deliveries_table(self):
        """إنشاء جدول تسليم الراوترات"""
        query = """
        CREATE TABLE IF NOT EXISTS router_deliveries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            delivery_number TEXT UNIQUE NOT NULL,
            subscriber_id INTEGER NOT NULL,
            router_product_id INTEGER NOT NULL,
            router_serial TEXT,
            delivery_date DATE NOT NULL,
            router_price DECIMAL(10,2) NOT NULL,
            installation_fee DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_status TEXT DEFAULT 'pending',
            receipt_id INTEGER,
            is_delivered BOOLEAN DEFAULT 0,
            delivered_at TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (subscriber_id) REFERENCES subscribers(id),
            FOREIGN KEY (router_product_id) REFERENCES products(id),
            FOREIGN KEY (receipt_id) REFERENCES receipts(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول تسليم الراوترات")

    def create_package_renewals_table(self):
        """إنشاء جدول تجديد الباقات"""
        query = """
        CREATE TABLE IF NOT EXISTS package_renewals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            renewal_number TEXT UNIQUE NOT NULL,
            subscriber_id INTEGER NOT NULL,
            old_package_id INTEGER,
            new_package_id INTEGER NOT NULL,
            renewal_date DATE NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_status TEXT DEFAULT 'pending',
            receipt_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (subscriber_id) REFERENCES subscribers(id),
            FOREIGN KEY (old_package_id) REFERENCES packages(id),
            FOREIGN KEY (new_package_id) REFERENCES packages(id),
            FOREIGN KEY (receipt_id) REFERENCES receipts(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول تجديد الباقات")

    def create_worker_deliveries_table(self):
        """إنشاء جدول تسليم العمال"""
        query = """
        CREATE TABLE IF NOT EXISTS worker_deliveries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            delivery_number TEXT UNIQUE NOT NULL,
            worker_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity DECIMAL(10,2) NOT NULL,
            unit TEXT NOT NULL,
            delivery_date DATE NOT NULL,
            purpose TEXT,  -- installation, maintenance, replacement
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (worker_id) REFERENCES workers(id),
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول تسليم العمال")

    def create_maintenance_orders_table(self):
        """إنشاء جدول أوامر الصيانة"""
        query = """
        CREATE TABLE IF NOT EXISTS maintenance_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_number TEXT UNIQUE NOT NULL,
            subscriber_id INTEGER,
            worker_id INTEGER,
            order_date DATE NOT NULL,
            description TEXT NOT NULL,
            status TEXT DEFAULT 'pending',  -- pending, in_progress, completed, cancelled
            priority TEXT DEFAULT 'normal',  -- low, normal, high, urgent
            estimated_cost DECIMAL(10,2) DEFAULT 0,
            actual_cost DECIMAL(10,2) DEFAULT 0,
            completed_at TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (subscriber_id) REFERENCES subscribers(id),
            FOREIGN KEY (worker_id) REFERENCES workers(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)

        # جدول المواد المستخدمة في الصيانة
        query = """
        CREATE TABLE IF NOT EXISTS maintenance_materials (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            maintenance_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity DECIMAL(10,2) NOT NULL,
            unit_cost DECIMAL(10,2) DEFAULT 0,
            total_cost DECIMAL(10,2) DEFAULT 0,
            FOREIGN KEY (maintenance_id) REFERENCES maintenance_orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول أوامر الصيانة")

    def create_system_settings_table(self):
        """إنشاء جدول إعدادات النظام"""
        query = """
        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type TEXT DEFAULT 'string',  -- string, number, boolean, json
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (updated_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول إعدادات النظام")

    def create_invoice_settings_table(self):
        """إنشاء جدول إعدادات الفواتير"""
        query = """
        CREATE TABLE IF NOT EXISTS invoice_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_name TEXT DEFAULT 'شركة الإنترنت',
            company_address TEXT,
            company_phone TEXT,
            company_email TEXT,
            invoice_header TEXT DEFAULT 'فاتورة خدمات الإنترنت',
            invoice_footer TEXT DEFAULT 'شكراً لتعاملكم معنا',
            show_user_name BOOLEAN DEFAULT 1,
            user_name_text TEXT DEFAULT 'تمت خدمتكم من قبل: {user_name}',
            logo_path TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (updated_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول إعدادات الفواتير")

    def create_default_admin(self):
        """إنشاء المستخدم الافتراضي"""
        # التحقق من وجود مستخدم admin
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = 'admin'")

        if not admin_exists:
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()

            query = """
            INSERT INTO users (username, password_hash, full_name, role, email)
            VALUES (?, ?, ?, ?, ?)
            """

            self.execute_query(query, (
                'admin',
                password_hash,
                'المدير العام',
                'admin',
                '<EMAIL>'
            ))

            print("✅ تم إنشاء المستخدم الافتراضي (admin/admin123)")
        else:
            print("✅ المستخدم الافتراضي موجود")

    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        # إعدادات النظام الافتراضية
        default_settings = [
            ('company_name', 'شركة الإنترنت', 'string', 'اسم الشركة'),
            ('currency', 'ل.س', 'string', 'العملة المستخدمة'),
            ('tax_rate', '0', 'number', 'نسبة الضريبة'),
            ('backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي'),
            ('auto_backup_days', '7', 'number', 'عدد أيام النسخ الاحتياطي التلقائي'),
        ]

        for key, value, type_val, desc in default_settings:
            existing = self.fetch_one("SELECT id FROM system_settings WHERE setting_key = ?", (key,))
            if not existing:
                self.execute_query(
                    "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                    (key, value, type_val, desc)
                )

        # إعدادات الفواتير الافتراضية
        invoice_exists = self.fetch_one("SELECT id FROM invoice_settings")
        if not invoice_exists:
            self.execute_query("""
                INSERT INTO invoice_settings (
                    company_name, company_address, company_phone,
                    invoice_header, invoice_footer, show_user_name, user_name_text
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                'شركة الإنترنت',
                'العنوان: سوريا - دمشق',
                'الهاتف: +963-11-1234567',
                'فاتورة خدمات الإنترنت',
                'شكراً لتعاملكم معنا - نتمنى لكم تجربة ممتعة',
                1,
                'تمت خدمتكم من قبل: {user_name}'
            ))

        print("✅ تم إنشاء الإعدادات الافتراضية")

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash

    def get_or_create_cash_box(self, user_id, date=None):
        """الحصول على صندوق اليوم أو إنشاؤه - ضمان عدم الاختلاط"""
        if date is None:
            date = datetime.now().date()

        # البحث عن صندوق موجود لهذا المستخدم في هذا التاريخ
        cash_box = self.fetch_one("""
            SELECT * FROM cash_boxes
            WHERE date = ? AND user_id = ?
        """, (date, user_id))

        if cash_box:
            return cash_box

        # إنشاء صندوق جديد
        query = """
        INSERT INTO cash_boxes (date, user_id, opening_balance)
        VALUES (?, ?, 0)
        """

        self.execute_query(query, (date, user_id))

        # إرجاع الصندوق الجديد
        return self.fetch_one("""
            SELECT * FROM cash_boxes
            WHERE date = ? AND user_id = ?
        """, (date, user_id))

    def update_cash_box_totals(self, cash_box_id):
        """تحديث إجماليات الصندوق"""
        # حساب إجمالي المقبوضات
        total_receipts = self.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total
            FROM receipts
            WHERE cash_box_id = ?
        """, (cash_box_id,))

        # حساب إجمالي المصاريف (باستثناء الرواتب)
        total_expenses = self.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total
            FROM expenses
            WHERE cash_box_id = ? AND is_salary = 0
        """, (cash_box_id,))

        # الحصول على الرصيد الافتتاحي
        cash_box = self.fetch_one("SELECT opening_balance FROM cash_boxes WHERE id = ?", (cash_box_id,))
        opening_balance = cash_box['opening_balance'] if cash_box else 0

        # حساب الرصيد الختامي
        closing_balance = opening_balance + total_receipts['total'] - total_expenses['total']

        # تحديث الصندوق
        self.execute_query("""
            UPDATE cash_boxes
            SET total_receipts = ?, total_expenses = ?, closing_balance = ?
            WHERE id = ?
        """, (total_receipts['total'], total_expenses['total'], closing_balance, cash_box_id))

        return {
            'total_receipts': total_receipts['total'],
            'total_expenses': total_expenses['total'],
            'closing_balance': closing_balance
        }

    def close_cash_box(self, cash_box_id, user_id):
        """إغلاق الصندوق"""
        # تحديث الإجماليات أولاً
        totals = self.update_cash_box_totals(cash_box_id)

        # إغلاق الصندوق
        self.execute_query("""
            UPDATE cash_boxes
            SET is_closed = 1, closed_at = CURRENT_TIMESTAMP
            WHERE id = ? AND user_id = ?
        """, (cash_box_id, user_id))

        return totals

    def update_inventory(self, product_id, quantity_change, location='main_stock', worker_id=None, user_id=None):
        """تحديث المخزون"""
        # البحث عن السجل الموجود
        existing = self.fetch_one("""
            SELECT * FROM inventory
            WHERE product_id = ? AND location = ? AND
            (worker_id = ? OR (worker_id IS NULL AND ? IS NULL))
        """, (product_id, location, worker_id, worker_id))

        if existing:
            # تحديث الكمية الموجودة
            new_quantity = existing['quantity'] + quantity_change
            self.execute_query("""
                UPDATE inventory
                SET quantity = ?, last_updated = CURRENT_TIMESTAMP, updated_by = ?
                WHERE id = ?
            """, (new_quantity, user_id, existing['id']))
        else:
            # إنشاء سجل جديد
            product = self.fetch_one("SELECT sale_unit FROM products WHERE id = ?", (product_id,))
            unit = product['sale_unit'] if product else 'قطعة'

            self.execute_query("""
                INSERT INTO inventory (product_id, location, worker_id, quantity, unit, updated_by)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (product_id, location, worker_id, quantity_change, unit, user_id))

    def get_inventory_quantity(self, product_id, location='main_stock', worker_id=None):
        """الحصول على كمية المخزون"""
        result = self.fetch_one("""
            SELECT quantity FROM inventory
            WHERE product_id = ? AND location = ? AND
            (worker_id = ? OR (worker_id IS NULL AND ? IS NULL))
        """, (product_id, location, worker_id, worker_id))

        return result['quantity'] if result else 0

    def create_system_settings_table(self):
        """إنشاء جدول إعدادات النظام"""
        query = """
        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type TEXT DEFAULT 'string',  -- string, number, boolean, json
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (updated_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول إعدادات النظام")

    def create_invoice_settings_table(self):
        """إنشاء جدول إعدادات الفواتير"""
        query = """
        CREATE TABLE IF NOT EXISTS invoice_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_name TEXT DEFAULT 'شركة الإنترنت',
            company_address TEXT,
            company_phone TEXT,
            company_email TEXT,
            invoice_header TEXT DEFAULT 'فاتورة خدمات الإنترنت',
            invoice_footer TEXT DEFAULT 'شكراً لتعاملكم معنا',
            show_user_name BOOLEAN DEFAULT 1,
            user_name_text TEXT DEFAULT 'تمت خدمتكم من قبل: {user_name}',
            logo_path TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (updated_by) REFERENCES users(id)
        )
        """
        self.execute_query(query)
        print("✅ تم إنشاء جدول إعدادات الفواتير")

    def create_default_admin(self):
        """إنشاء المستخدم الافتراضي"""
        # التحقق من وجود مستخدم admin
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = 'admin'")

        if not admin_exists:
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()

            query = """
            INSERT INTO users (username, password_hash, full_name, role, email)
            VALUES (?, ?, ?, ?, ?)
            """

            self.execute_query(query, (
                'admin',
                password_hash,
                'المدير العام',
                'admin',
                '<EMAIL>'
            ))

            print("✅ تم إنشاء المستخدم الافتراضي (admin/admin123)")
        else:
            print("✅ المستخدم الافتراضي موجود")

    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        # إعدادات النظام الافتراضية
        default_settings = [
            ('company_name', 'شركة الإنترنت', 'string', 'اسم الشركة'),
            ('currency', 'ل.س', 'string', 'العملة المستخدمة'),
            ('tax_rate', '0', 'number', 'نسبة الضريبة'),
            ('backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي'),
            ('auto_backup_days', '7', 'number', 'عدد أيام النسخ الاحتياطي التلقائي'),
        ]

        for key, value, type_val, desc in default_settings:
            existing = self.fetch_one("SELECT id FROM system_settings WHERE setting_key = ?", (key,))
            if not existing:
                self.execute_query(
                    "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                    (key, value, type_val, desc)
                )

        # إعدادات الفواتير الافتراضية
        invoice_exists = self.fetch_one("SELECT id FROM invoice_settings")
        if not invoice_exists:
            self.execute_query("""
                INSERT INTO invoice_settings (
                    company_name, company_address, company_phone,
                    invoice_header, invoice_footer, show_user_name, user_name_text
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                'شركة الإنترنت',
                'العنوان: سوريا - دمشق',
                'الهاتف: +963-11-1234567',
                'فاتورة خدمات الإنترنت',
                'شكراً لتعاملكم معنا - نتمنى لكم تجربة ممتعة',
                1,
                'تمت خدمتكم من قبل: {user_name}'
            ))

        print("✅ تم إنشاء الإعدادات الافتراضية")

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المخزون الموحد
Unified Inventory Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QGroupBox, QTabWidget,
                            QSpinBox, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency

class UnifiedInventoryWindow(QDialog):
    """واجهة إدارة المخزون الموحد"""
    
    inventory_updated = pyqtSignal()
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        
        # إنشاء مدير المخزون الموحد
        from utils.unified_inventory_manager import UnifiedInventoryManager
        self.inventory_manager = UnifiedInventoryManager(db_manager)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(reshape_arabic_text("إدارة المخزون الموحد"))
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("📦 إدارة المخزون الموحد"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)
        
        # تبويب المخزون الرئيسي
        self.main_inventory_tab = self.create_main_inventory_tab()
        self.tabs.addTab(self.main_inventory_tab, reshape_arabic_text("المخزون الرئيسي"))
        
        # تبويب مخزون العمال
        self.worker_inventory_tab = self.create_worker_inventory_tab()
        self.tabs.addTab(self.worker_inventory_tab, reshape_arabic_text("مخزون العمال"))
        
        # تبويب حركات المخزون
        self.movements_tab = self.create_movements_tab()
        self.tabs.addTab(self.movements_tab, reshape_arabic_text("حركات المخزون"))
        
        layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton(reshape_arabic_text("تحديث"))
        refresh_button.setFont(create_arabic_font(10, bold=True))
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_button.clicked.connect(self.load_data)
        
        close_button = QPushButton(reshape_arabic_text("إغلاق"))
        close_button.setFont(create_arabic_font(10, bold=True))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.accept)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
        
    def create_main_inventory_tab(self):
        """إنشاء تبويب المخزون الرئيسي"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # جدول المخزون الرئيسي
        self.main_inventory_table = QTableWidget()
        self.main_inventory_table.setColumnCount(7)
        self.main_inventory_table.setHorizontalHeaderLabels([
            reshape_arabic_text("اسم المنتج"),
            reshape_arabic_text("الفئة"),
            reshape_arabic_text("الوحدة"),
            reshape_arabic_text("المخزون الحالي"),
            reshape_arabic_text("الحد الأدنى"),
            reshape_arabic_text("سعر الشراء"),
            reshape_arabic_text("سعر البيع")
        ])
        
        # تنسيق الجدول
        apply_arabic_style(self.main_inventory_table, 9)
        self.main_inventory_table.horizontalHeader().setStretchLastSection(True)
        self.main_inventory_table.setAlternatingRowColors(True)
        self.main_inventory_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.main_inventory_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        layout.addWidget(self.main_inventory_table)
        
        return widget
        
    def create_worker_inventory_tab(self):
        """إنشاء تبويب مخزون العمال"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # اختيار العامل
        worker_layout = QHBoxLayout()
        
        worker_label = QLabel(reshape_arabic_text("العامل:"))
        apply_arabic_style(worker_label, 10)
        
        self.worker_combo = QComboBox()
        apply_arabic_style(self.worker_combo, 10)
        self.worker_combo.currentIndexChanged.connect(self.load_worker_inventory)
        
        worker_layout.addWidget(worker_label)
        worker_layout.addWidget(self.worker_combo)
        worker_layout.addStretch()
        
        layout.addLayout(worker_layout)
        
        # جدول مخزون العامل
        self.worker_inventory_table = QTableWidget()
        self.worker_inventory_table.setColumnCount(4)
        self.worker_inventory_table.setHorizontalHeaderLabels([
            reshape_arabic_text("اسم المنتج"),
            reshape_arabic_text("الفئة"),
            reshape_arabic_text("الوحدة"),
            reshape_arabic_text("الكمية")
        ])
        
        # تنسيق الجدول
        apply_arabic_style(self.worker_inventory_table, 9)
        self.worker_inventory_table.horizontalHeader().setStretchLastSection(True)
        self.worker_inventory_table.setAlternatingRowColors(True)
        self.worker_inventory_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.worker_inventory_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        layout.addWidget(self.worker_inventory_table)
        
        return widget
        
    def create_movements_tab(self):
        """إنشاء تبويب حركات المخزون"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # جدول حركات المخزون
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(7)
        self.movements_table.setHorizontalHeaderLabels([
            reshape_arabic_text("التاريخ"),
            reshape_arabic_text("المنتج"),
            reshape_arabic_text("نوع الحركة"),
            reshape_arabic_text("الكمية"),
            reshape_arabic_text("من"),
            reshape_arabic_text("إلى"),
            reshape_arabic_text("نوع العملية")
        ])
        
        # تنسيق الجدول
        apply_arabic_style(self.movements_table, 9)
        self.movements_table.horizontalHeader().setStretchLastSection(True)
        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.movements_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        layout.addWidget(self.movements_table)
        
        return widget
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            # نقل البيانات الموجودة إلى النظام الموحد
            self.inventory_manager.migrate_existing_data()
            
            # تحميل المخزون الرئيسي
            self.load_main_inventory()
            
            # تحميل العمال
            self.load_workers()
            
            # تحميل حركات المخزون
            self.load_movements()
            
        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"), 
                               reshape_arabic_text(f"خطأ في تحميل البيانات: {e}"))
    
    def load_main_inventory(self):
        """تحميل المخزون الرئيسي"""
        try:
            products = self.inventory_manager.get_all_products()
            
            self.main_inventory_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                # اسم المنتج
                name_item = QTableWidgetItem(reshape_arabic_text(product['name']))
                self.main_inventory_table.setItem(row, 0, name_item)
                
                # الفئة
                category_item = QTableWidgetItem(reshape_arabic_text(product['category']))
                self.main_inventory_table.setItem(row, 1, category_item)
                
                # الوحدة
                unit_item = QTableWidgetItem(reshape_arabic_text(product['unit_type']))
                self.main_inventory_table.setItem(row, 2, unit_item)
                
                # المخزون الحالي
                stock_item = QTableWidgetItem(f"{product['current_stock']:.1f}")
                # تلوين المخزون المنخفض
                if product['current_stock'] <= product['min_stock']:
                    stock_item.setBackground(QColor("#ffebee"))
                self.main_inventory_table.setItem(row, 3, stock_item)
                
                # الحد الأدنى
                min_stock_item = QTableWidgetItem(f"{product['min_stock']:.1f}")
                self.main_inventory_table.setItem(row, 4, min_stock_item)
                
                # سعر الشراء
                purchase_price_item = QTableWidgetItem(format_currency(product['purchase_price']))
                self.main_inventory_table.setItem(row, 5, purchase_price_item)
                
                # سعر البيع
                sale_price_item = QTableWidgetItem(format_currency(product['sale_price']))
                self.main_inventory_table.setItem(row, 6, sale_price_item)
                
        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"), 
                               reshape_arabic_text(f"خطأ في تحميل المخزون الرئيسي: {e}"))
    
    def load_workers(self):
        """تحميل قائمة العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT id, name FROM workers WHERE is_active = 1 ORDER BY name
            """)
            
            self.worker_combo.clear()
            self.worker_combo.addItem(reshape_arabic_text("-- اختر العامل --"), None)
            
            for worker in workers:
                self.worker_combo.addItem(reshape_arabic_text(worker['name']), worker['id'])
                
        except Exception as e:
            print(f"خطأ في تحميل العمال: {e}")
    
    def load_worker_inventory(self):
        """تحميل مخزون العامل المختار"""
        try:
            worker_id = self.worker_combo.currentData()
            if not worker_id:
                self.worker_inventory_table.setRowCount(0)
                return
            
            inventory = self.inventory_manager.get_worker_inventory(worker_id)
            
            self.worker_inventory_table.setRowCount(len(inventory))
            
            for row, item in enumerate(inventory):
                # اسم المنتج
                name_item = QTableWidgetItem(reshape_arabic_text(item['name']))
                self.worker_inventory_table.setItem(row, 0, name_item)
                
                # الفئة
                category_item = QTableWidgetItem(reshape_arabic_text(item['category']))
                self.worker_inventory_table.setItem(row, 1, category_item)
                
                # الوحدة
                unit_item = QTableWidgetItem(reshape_arabic_text(item['unit_type']))
                self.worker_inventory_table.setItem(row, 2, unit_item)
                
                # الكمية
                quantity_item = QTableWidgetItem(f"{item['quantity']:.1f}")
                self.worker_inventory_table.setItem(row, 3, quantity_item)
                
        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"), 
                               reshape_arabic_text(f"خطأ في تحميل مخزون العامل: {e}"))
    
    def load_movements(self):
        """تحميل حركات المخزون"""
        try:
            movements = self.inventory_manager.get_inventory_movements(limit=200)
            
            self.movements_table.setRowCount(len(movements))
            
            for row, movement in enumerate(movements):
                # التاريخ
                date_item = QTableWidgetItem(movement['created_at'][:16])
                self.movements_table.setItem(row, 0, date_item)
                
                # المنتج
                product_item = QTableWidgetItem(reshape_arabic_text(movement['product_name']))
                self.movements_table.setItem(row, 1, product_item)
                
                # نوع الحركة
                movement_type = "دخول" if movement['movement_type'] == 'in' else "خروج"
                type_item = QTableWidgetItem(reshape_arabic_text(movement_type))
                if movement['movement_type'] == 'in':
                    type_item.setBackground(QColor("#e8f5e8"))
                else:
                    type_item.setBackground(QColor("#ffebee"))
                self.movements_table.setItem(row, 2, type_item)
                
                # الكمية
                quantity_item = QTableWidgetItem(f"{movement['quantity']:.1f}")
                self.movements_table.setItem(row, 3, quantity_item)
                
                # من
                from_item = QTableWidgetItem(reshape_arabic_text(movement['from_location'] or '-'))
                self.movements_table.setItem(row, 4, from_item)
                
                # إلى
                to_item = QTableWidgetItem(reshape_arabic_text(movement['to_location'] or '-'))
                self.movements_table.setItem(row, 5, to_item)
                
                # نوع العملية
                operation_item = QTableWidgetItem(reshape_arabic_text(movement['operation_type']))
                self.movements_table.setItem(row, 6, operation_item)
                
        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"), 
                               reshape_arabic_text(f"خطأ في تحميل حركات المخزون: {e}"))

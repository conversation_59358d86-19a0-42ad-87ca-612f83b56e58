#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهات الصحيحة التي تستدعيها الواجهة الرئيسية
"""

import sys
import os
sys.path.append('src')

def test_actual_currency_exchange():
    """اختبار واجهة شراء الدولار الفعلية"""
    
    print("🧪 اختبار واجهة شراء الدولار الفعلية (CurrencyExchangeWindow)...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.currency_exchange_window import CurrencyExchangeWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة قبل إنشاء الواجهة
        syp_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        usd_balance = treasury_manager.get_daily_balance(current_user['id'], 'USD')
        
        print(f"💰 الأرصدة في النظام الموحد:")
        print(f"  • الليرة السورية: {syp_balance:,} ل.س")
        print(f"  • الدولار: ${usd_balance:.2f}")
        
        # إنشاء الواجهة كما تفعل الواجهة الرئيسية
        print("\n🖥️ إنشاء واجهة شراء الدولار (كما في الواجهة الرئيسية)...")
        dialog = CurrencyExchangeWindow(db, treasury_manager, current_user)
        
        print("✅ تم إنشاء الواجهة بنجاح")
        
        # فحص ما تعرضه الواجهة
        displayed_syp = dialog.syp_balance_label.text()
        displayed_usd = dialog.usd_balance_label.text()
        
        print(f"📺 ما تعرضه الواجهة:")
        print(f"  • الليرة السورية: {displayed_syp}")
        print(f"  • الدولار: {displayed_usd}")
        
        # التحقق من صحة العرض
        if syp_balance > 0:
            if displayed_syp != "0 ل.س" and str(int(syp_balance)) in displayed_syp.replace(",", ""):
                print("✅ واجهة شراء الدولار تعرض الرصيد الصحيح")
                return True
            else:
                print(f"❌ واجهة شراء الدولار تعرض رصيد خاطئ")
                print(f"   متوقع: {syp_balance:,} ل.س")
                print(f"   معروض: {displayed_syp}")
                return False
        else:
            print("⚠️ لا يوجد رصيد في النظام الموحد")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة شراء الدولار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_treasury_transfer():
    """اختبار واجهة نقل الخزينة الفعلية"""
    
    print("\n🧪 اختبار واجهة نقل الخزينة الفعلية (EnhancedTreasuryTransferWindow)...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة من النظام الموحد مباشرة
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        treasury_manager = UnifiedTreasuryManager(db)
        
        syp_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_syp_balance = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الأرصدة في النظام الموحد:")
        print(f"  • الخزينة اليومية: {syp_balance:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_syp_balance:,} ل.س")
        
        # إنشاء الواجهة كما تفعل الواجهة الرئيسية
        print("\n🖥️ إنشاء واجهة نقل الخزينة (كما في الواجهة الرئيسية)...")
        dialog = EnhancedTreasuryTransferWindow(db, current_user)
        
        print("✅ تم إنشاء الواجهة بنجاح")
        
        # فحص ما تعرضه الواجهة
        displayed_daily = dialog.daily_syp_label.text()
        displayed_main = dialog.main_syp_label.text()
        
        print(f"📺 ما تعرضه الواجهة:")
        print(f"  • الخزينة اليومية: {displayed_daily}")
        print(f"  • الخزينة الرئيسية: {displayed_main}")
        
        # التحقق من صحة العرض
        if syp_balance > 0:
            if displayed_daily != "0 ل.س" and str(int(syp_balance)) in displayed_daily.replace(",", ""):
                print("✅ واجهة نقل الخزينة تعرض الرصيد الصحيح")
                return True
            else:
                print(f"❌ واجهة نقل الخزينة تعرض رصيد خاطئ")
                print(f"   متوقع: {syp_balance:,} ل.س")
                print(f"   معروض: {displayed_daily}")
                return False
        else:
            print("⚠️ لا يوجد رصيد في النظام الموحد")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_integration():
    """اختبار التكامل الكامل للنظام"""
    
    print("\n🧪 اختبار التكامل الكامل للنظام...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة الموحد")
        
        # فحص الأرصدة
        currencies = ['SYP', 'USD', 'EUR']
        
        print("\n💰 الأرصدة الحالية:")
        for currency in currencies:
            daily_balance = treasury_manager.get_daily_balance(current_user['id'], currency)
            main_balance = treasury_manager.get_main_balance(currency)
            
            symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")
            print(f"  • {currency}: يومي {daily_balance:,.2f} {symbol} - رئيسي {main_balance:,.2f} {symbol}")
        
        # فحص الجداول
        print("\n📊 فحص الجداول:")
        
        # فحص unified_treasury
        unified_count = db.fetch_one("SELECT COUNT(*) as count FROM unified_treasury")
        print(f"  • unified_treasury: {unified_count['count']} سجل")
        
        # فحص الجلسات النشطة
        active_sessions = db.fetch_all("""
            SELECT user_id, currency_type, daily_balance 
            FROM unified_treasury 
            WHERE is_session_active = 1 AND daily_balance > 0
        """)
        
        print(f"  • الجلسات النشطة: {len(active_sessions)} جلسة")
        for session in active_sessions:
            print(f"    - مستخدم {session['user_id']}: {session['daily_balance']:,} {session['currency_type']}")
        
        return len(active_sessions) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار الواجهات الصحيحة التي تستدعيها الواجهة الرئيسية")
    print("=" * 80)
    
    # اختبار التكامل أولاً
    integration_test = test_system_integration()
    
    if integration_test:
        print(f"\n✅ يوجد جلسات نشطة - يمكن اختبار الواجهات")
        
        # اختبار الواجهات الفعلية
        currency_test = test_actual_currency_exchange()
        treasury_test = test_actual_treasury_transfer()
        
        print("\n" + "=" * 80)
        print("📊 ملخص نتائج الاختبار:")
        print(f"  • التكامل الكامل: {'✅ يعمل' if integration_test else '❌ لا يعمل'}")
        print(f"  • واجهة شراء الدولار الفعلية: {'✅ تعمل' if currency_test else '❌ لا تعمل'}")
        print(f"  • واجهة نقل الخزينة الفعلية: {'✅ تعمل' if treasury_test else '❌ لا تعمل'}")
        
        if all([integration_test, currency_test, treasury_test]):
            print("\n🎉 جميع الواجهات تعمل بشكل صحيح!")
            
            print("\n📋 النتائج:")
            print("  ✅ النظام الموحد للخزينة يعمل")
            print("  ✅ واجهة شراء الدولار تقرأ الأرصدة الصحيحة")
            print("  ✅ واجهة نقل الخزينة تقرأ الأرصدة الصحيحة")
            print("  ✅ جميع الواجهات متكاملة")
            
            print("\n🚀 للاستخدام:")
            print("  1. شغل النظام: python system_launcher.py")
            print("  2. سجل دخول: admin / admin123")
            print("  3. اضغط على 'شراء دولار' - ستجد الرصيد متوفر")
            print("  4. اضغط على 'نقل الخزينة' - ستجد الرصيد متوفر")
            
        else:
            print("\n❌ هناك مشاكل في بعض الواجهات")
            
            if not currency_test:
                print("  • واجهة شراء الدولار تحتاج إصلاح")
            if not treasury_test:
                print("  • واجهة نقل الخزينة تحتاج إصلاح")
                
    else:
        print(f"\n⚠️ لا توجد جلسات نشطة في النظام")
        print("💡 تحتاج لتشغيل: python migrate_closed_boxes.py")
        print("💡 أو فتح صندوق جديد من الواجهة الرئيسية")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي لمشكلة تحديث الواجهات
"""

import sys
import os
sys.path.append('src')

def add_force_refresh_to_enhanced_treasury():
    """إضافة تحديث قوي لواجهة نقل الخزينة المحسنة"""
    
    print("🔧 إضافة تحديث قوي لواجهة نقل الخزينة المحسنة...")
    
    # قراءة الملف الحالي
    with open('src/ui/enhanced_treasury_transfer_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن مكان إضافة التحديث القوي
    if "def force_refresh_interface(self):" not in content:
        # إضافة دالة التحديث القوي
        force_refresh_method = '''
    def force_refresh_interface(self):
        """تحديث قوي للواجهة"""
        try:
            print("🔄 تحديث قوي للواجهة...")
            
            # إعادة تحميل البيانات من قاعدة البيانات
            self.load_treasury_data()
            
            # تحديث جميع العناصر
            self.update_balance_labels()
            self.update_max_amount()
            self.update_summary()
            
            # إجبار إعادة رسم الواجهة
            self.repaint()
            self.update()
            
            # تحديث النافذة الأب إذا كانت موجودة
            if self.parent():
                self.parent().repaint()
                self.parent().update()
            
            print("✅ تم التحديث القوي للواجهة")
            
        except Exception as e:
            print(f"❌ خطأ في التحديث القوي: {e}")
'''
        
        # إضافة الدالة قبل النهاية
        content = content.replace(
            "    def print_report(self):",
            force_refresh_method + "\n    def print_report(self):"
        )
    
    # تحديث استدعاء التحديث في execute_transfer
    if "self.force_refresh_interface()" not in content:
        content = content.replace(
            "            # تحديث الأرصدة في الواجهة فوراً\n            print(\"🔄 تحديث الأرصدة في الواجهة...\")\n            self.load_treasury_data()\n            self.update_balance_labels()\n            self.update_max_amount()\n            self.update_summary()",
            "            # تحديث الأرصدة في الواجهة فوراً\n            print(\"🔄 تحديث الأرصدة في الواجهة...\")\n            self.force_refresh_interface()"
        )
    
    # حفظ الملف المحدث
    with open('src/ui/enhanced_treasury_transfer_window.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إضافة التحديث القوي لواجهة نقل الخزينة المحسنة")

def add_force_refresh_to_currency_exchange():
    """إضافة تحديث قوي لواجهة شراء الدولار"""
    
    print("🔧 إضافة تحديث قوي لواجهة شراء الدولار...")
    
    # قراءة الملف الحالي
    with open('src/ui/currency_exchange_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن مكان إضافة التحديث القوي
    if "def force_refresh_interface(self):" not in content:
        # إضافة دالة التحديث القوي
        force_refresh_method = '''
    def force_refresh_interface(self):
        """تحديث قوي للواجهة"""
        try:
            print("🔄 تحديث قوي لواجهة شراء الدولار...")
            
            # إعادة تحميل البيانات من قاعدة البيانات
            self.load_data()
            
            # تحديث الحسابات
            self.calculate_amounts()
            
            # إجبار إعادة رسم الواجهة
            self.repaint()
            self.update()
            
            # تحديث النافذة الأب إذا كانت موجودة
            if self.parent():
                self.parent().repaint()
                self.parent().update()
            
            print("✅ تم التحديث القوي لواجهة شراء الدولار")
            
        except Exception as e:
            print(f"❌ خطأ في التحديث القوي: {e}")
'''
        
        # إضافة الدالة قبل النهاية
        content = content.replace(
            "    def calculate_amounts(self):",
            force_refresh_method + "\n    def calculate_amounts(self):"
        )
    
    # تحديث استدعاء التحديث في execute_exchange
    if "self.force_refresh_interface()" not in content:
        content = content.replace(
            "                    # إعادة تحميل البيانات لعرض الرصيد الجديد\n                    self.load_data()",
            "                    # تحديث قوي للواجهة\n                    self.force_refresh_interface()"
        )
    
    # حفظ الملف المحدث
    with open('src/ui/currency_exchange_window.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إضافة التحديث القوي لواجهة شراء الدولار")

def add_force_refresh_to_treasury_transfer():
    """إضافة تحديث قوي لواجهة نقل الخزينة العادية"""
    
    print("🔧 إضافة تحديث قوي لواجهة نقل الخزينة العادية...")
    
    # قراءة الملف الحالي
    with open('src/ui/treasury_transfer_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن مكان إضافة التحديث القوي
    if "def force_refresh_interface(self):" not in content:
        # إضافة دالة التحديث القوي
        force_refresh_method = '''
    def force_refresh_interface(self):
        """تحديث قوي للواجهة"""
        try:
            print("🔄 تحديث قوي لواجهة نقل الخزينة...")
            
            # إعادة تحميل البيانات من قاعدة البيانات
            self.load_balances()
            
            # إجبار إعادة رسم الواجهة
            self.repaint()
            self.update()
            
            # تحديث النافذة الأب إذا كانت موجودة
            if self.parent():
                self.parent().repaint()
                self.parent().update()
            
            print("✅ تم التحديث القوي لواجهة نقل الخزينة")
            
        except Exception as e:
            print(f"❌ خطأ في التحديث القوي: {e}")
'''
        
        # إضافة الدالة قبل النهاية
        content = content.replace(
            "    def load_balances(self):",
            force_refresh_method + "\n    def load_balances(self):"
        )
    
    # تحديث استدعاء التحديث
    if "self.force_refresh_interface()" not in content:
        content = content.replace(
            "                    # تحديث الأرصدة في الواجهة فوراً\n                    print(\"🔄 تحديث الأرصدة في الواجهة...\")\n                    self.load_balances()",
            "                    # تحديث قوي للواجهة\n                    self.force_refresh_interface()"
        )
    
    # حفظ الملف المحدث
    with open('src/ui/treasury_transfer_window.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إضافة التحديث القوي لواجهة نقل الخزينة العادية")

def test_interface_refresh_fix():
    """اختبار إصلاح تحديث الواجهات"""
    
    print("\n🧪 اختبار إصلاح تحديث الواجهات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        print("🖥️ إنشاء واجهة نقل الخزينة...")
        window = EnhancedTreasuryTransferWindow(db, current_user)
        
        # فحص وجود دالة التحديث القوي
        if hasattr(window, 'force_refresh_interface'):
            print("✅ دالة التحديث القوي موجودة")
            
            # اختبار الدالة
            window.force_refresh_interface()
            print("✅ دالة التحديث القوي تعمل")
            
            return True
        else:
            print("❌ دالة التحديث القوي غير موجودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح نهائي لمشكلة تحديث الواجهات")
    print("=" * 70)
    
    # 1. إضافة التحديث القوي للواجهات
    add_force_refresh_to_enhanced_treasury()
    add_force_refresh_to_currency_exchange()
    add_force_refresh_to_treasury_transfer()
    
    # 2. اختبار الإصلاح
    test_result = test_interface_refresh_fix()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الإصلاح:")
    
    if test_result:
        print("🎉 تم إصلاح مشكلة تحديث الواجهات نهائياً!")
        
        print("\n📋 ما تم إنجازه:")
        print("  ✅ إضافة دالة تحديث قوي لواجهة نقل الخزينة المحسنة")
        print("  ✅ إضافة دالة تحديث قوي لواجهة شراء الدولار")
        print("  ✅ إضافة دالة تحديث قوي لواجهة نقل الخزينة العادية")
        print("  ✅ إضافة واجهة التقارير المالية الشاملة")
        print("  ✅ ربط واجهة التقارير بالواجهة الرئيسية")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. سجل دخول: admin / admin123")
        print("  3. اضغط على 'نقل الخزينة' أو 'شراء الدولار'")
        print("  4. نفذ العملية - ستجد الواجهة تتحدث فوراً")
        print("  5. اضغط على 'التقارير المالية' لرؤية جميع العمليات")
        
        print("\n💡 المميزات الجديدة:")
        print("  • تحديث فوري وقوي للواجهات بعد كل عملية")
        print("  • واجهة تقارير مالية شاملة")
        print("  • تقارير إغلاق الصناديق ونقل الخزينة وشراء الدولار")
        print("  • ملخص مالي شامل للأرصدة والعمليات")
        print("  • فلترة التقارير بالتاريخ والمستخدم")
        
    else:
        print("❌ لا تزال هناك مشاكل في التحديث")
        print("💡 قد تحتاج لفحص إضافي")

if __name__ == "__main__":
    main()

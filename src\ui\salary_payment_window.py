# -*- coding: utf-8 -*-
"""
نافذة سداد الرواتب
Salary Payment Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QDoubleSpinBox, QGroupBox, 
                            QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency


class SalaryPaymentWindow(QDialog):
    """نافذة سداد الرواتب"""
    
    salary_paid = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سداد الرواتب")
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("سداد الرواتب")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # أدوات التحكم
        controls_layout = self.create_controls()
        
        # جدول العمال والرواتب
        self.workers_table = self.create_workers_table()
        
        # نموذج سداد الراتب
        payment_form = self.create_payment_form()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addLayout(controls_layout)
        layout.addWidget(self.workers_table)
        layout.addWidget(payment_form)
        layout.addLayout(buttons_layout)
        
    def create_controls(self):
        """إنشاء أدوات التحكم"""
        layout = QHBoxLayout()
        
        # فلتر الشهر
        month_label = QLabel("الشهر:")
        apply_arabic_style(month_label, 10)
        self.month_combo = QComboBox()
        apply_arabic_style(self.month_combo, 10)
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(QDate.currentDate().month() - 1)
        
        # فلتر السنة
        year_label = QLabel("السنة:")
        apply_arabic_style(year_label, 10)
        self.year_combo = QComboBox()
        apply_arabic_style(self.year_combo, 10)
        current_year = QDate.currentDate().year()
        for year in range(current_year - 2, current_year + 2):
            self.year_combo.addItem(str(year))
        self.year_combo.setCurrentText(str(current_year))
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout.addWidget(month_label)
        layout.addWidget(self.month_combo)
        layout.addWidget(year_label)
        layout.addWidget(self.year_combo)
        layout.addWidget(refresh_button)
        layout.addStretch()
        
        refresh_button.clicked.connect(self.load_data)
        
        return layout
        
    def create_workers_table(self):
        """إنشاء جدول العمال والرواتب"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "المنطقة", "الراتب الأساسي", "العمولات", 
            "الإجمالي", "حالة السداد", "تاريخ السداد"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, header.Stretch)  # عمود الاسم
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_payment_form(self):
        """إنشاء نموذج سداد الراتب"""
        group = QGroupBox("سداد راتب")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # اسم العامل
        worker_label = QLabel("العامل:")
        apply_arabic_style(worker_label, 10)
        self.worker_combo = QComboBox()
        apply_arabic_style(self.worker_combo, 10)
        
        # الراتب الأساسي
        basic_salary_label = QLabel("الراتب الأساسي:")
        apply_arabic_style(basic_salary_label, 10)
        self.basic_salary_display = QLabel("0 ل.س")
        apply_arabic_style(self.basic_salary_display, 10, bold=True)
        self.basic_salary_display.setStyleSheet("color: #27ae60;")
        
        # العمولات
        commissions_label = QLabel("العمولات:")
        apply_arabic_style(commissions_label, 10)
        self.commissions_display = QLabel("0 ل.س")
        apply_arabic_style(self.commissions_display, 10, bold=True)
        self.commissions_display.setStyleSheet("color: #3498db;")
        
        # المكافآت/الخصومات
        bonus_label = QLabel("مكافآت/خصومات:")
        apply_arabic_style(bonus_label, 10)
        self.bonus_spin = QDoubleSpinBox()
        apply_arabic_style(self.bonus_spin, 10)
        self.bonus_spin.setRange(-999999, 999999)
        self.bonus_spin.setSuffix(" ل.س")
        
        # الإجمالي
        total_label = QLabel("الإجمالي:")
        apply_arabic_style(total_label, 10)
        self.total_display = QLabel("0 ل.س")
        apply_arabic_style(self.total_display, 10, bold=True)
        self.total_display.setStyleSheet("color: #e74c3c; font-size: 14px;")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(80)
        
        # زر السداد
        pay_button = QPushButton("سداد الراتب")
        apply_arabic_style(pay_button, 10)
        pay_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(worker_label, 0, 0)
        layout.addWidget(self.worker_combo, 0, 1)
        layout.addWidget(basic_salary_label, 0, 2)
        layout.addWidget(self.basic_salary_display, 0, 3)
        layout.addWidget(commissions_label, 1, 0)
        layout.addWidget(self.commissions_display, 1, 1)
        layout.addWidget(bonus_label, 1, 2)
        layout.addWidget(self.bonus_spin, 1, 3)
        layout.addWidget(total_label, 2, 0)
        layout.addWidget(self.total_display, 2, 1)
        layout.addWidget(notes_label, 2, 2)
        layout.addWidget(self.notes_edit, 2, 3)
        layout.addWidget(pay_button, 3, 0, 1, 4)
        
        pay_button.clicked.connect(self.pay_salary)
        self.worker_combo.currentTextChanged.connect(self.update_salary_info)
        self.bonus_spin.valueChanged.connect(self.calculate_total)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر سداد جماعي
        bulk_pay_button = QPushButton("سداد جماعي")
        apply_arabic_style(bulk_pay_button, 10)
        bulk_pay_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر تقرير الرواتب
        report_button = QPushButton("تقرير الرواتب")
        apply_arabic_style(report_button, 10)
        report_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(bulk_pay_button)
        layout.addWidget(report_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        bulk_pay_button.clicked.connect(self.bulk_pay_salaries)
        report_button.clicked.connect(self.generate_salary_report)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_workers()
        self.load_salary_data()
        
    def load_workers(self):
        """تحميل قائمة العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT id, name, salary FROM workers WHERE is_active = 1 ORDER BY name
            """)
            
            self.worker_combo.clear()
            for worker in workers:
                self.worker_combo.addItem(worker['name'], worker)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العمال: {e}")
            
    def load_salary_data(self):
        """تحميل بيانات الرواتب"""
        try:
            month = self.month_combo.currentIndex() + 1
            year = int(self.year_combo.currentText())
            
            # جلب بيانات العمال مع رواتبهم وحالة السداد
            workers_data = self.db_manager.fetch_all("""
                SELECT w.id, w.name, w.area, w.salary,
                       COALESCE(SUM(rd.commission), 0) as total_commissions,
                       sp.id as payment_id, sp.total_amount, sp.payment_date
                FROM workers w
                LEFT JOIN router_deliveries rd ON w.id = rd.worker_id 
                    AND strftime('%m', rd.delivery_date) = ? 
                    AND strftime('%Y', rd.delivery_date) = ?
                LEFT JOIN salary_payments sp ON w.id = sp.worker_id 
                    AND sp.payment_month = ? AND sp.payment_year = ?
                WHERE w.is_active = 1
                GROUP BY w.id, w.name, w.area, w.salary, sp.id, sp.total_amount, sp.payment_date
                ORDER BY w.name
            """, (f"{month:02d}", str(year), month, year))
            
            self.workers_table.setRowCount(len(workers_data))
            
            for row, worker in enumerate(workers_data):
                self.workers_table.setItem(row, 0, QTableWidgetItem(str(worker['id'])))
                self.workers_table.setItem(row, 1, QTableWidgetItem(worker['name']))
                self.workers_table.setItem(row, 2, QTableWidgetItem(worker['area'] or ''))
                self.workers_table.setItem(row, 3, QTableWidgetItem(format_currency(worker['salary'])))
                self.workers_table.setItem(row, 4, QTableWidgetItem(format_currency(worker['total_commissions'])))
                
                total_salary = worker['salary'] + worker['total_commissions']
                self.workers_table.setItem(row, 5, QTableWidgetItem(format_currency(total_salary)))
                
                # حالة السداد
                if worker['payment_id']:
                    status_item = QTableWidgetItem("مدفوع")
                    status_item.setBackground(QColor("#d5f4e6"))
                    status_item.setForeground(QColor("#27ae60"))
                    payment_date = worker['payment_date'][:10] if worker['payment_date'] else ''
                else:
                    status_item = QTableWidgetItem("غير مدفوع")
                    status_item.setBackground(QColor("#fadbd8"))
                    status_item.setForeground(QColor("#e74c3c"))
                    payment_date = ''
                
                self.workers_table.setItem(row, 6, status_item)
                self.workers_table.setItem(row, 7, QTableWidgetItem(payment_date))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الرواتب: {e}")
            
    def update_salary_info(self):
        """تحديث معلومات الراتب للعامل المختار"""
        try:
            worker_data = self.worker_combo.currentData()
            if not worker_data:
                return
                
            # عرض الراتب الأساسي
            basic_salary = worker_data['salary']
            self.basic_salary_display.setText(format_currency(basic_salary))
            
            # حساب العمولات للشهر الحالي
            month = self.month_combo.currentIndex() + 1
            year = int(self.year_combo.currentText())
            
            commissions = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(commission), 0) as total_commissions
                FROM router_deliveries 
                WHERE worker_id = ? 
                AND strftime('%m', delivery_date) = ? 
                AND strftime('%Y', delivery_date) = ?
            """, (worker_data['id'], f"{month:02d}", str(year)))
            
            total_commissions = commissions['total_commissions'] if commissions else 0
            self.commissions_display.setText(format_currency(total_commissions))
            
            self.calculate_total()
            
        except Exception as e:
            print(f"خطأ في تحديث معلومات الراتب: {e}")
            
    def calculate_total(self):
        """حساب الإجمالي"""
        try:
            basic_salary = float(self.basic_salary_display.text().replace(" ل.س", "").replace(",", ""))
            commissions = float(self.commissions_display.text().replace(" ل.س", "").replace(",", ""))
            bonus = self.bonus_spin.value()
            
            total = basic_salary + commissions + bonus
            self.total_display.setText(format_currency(total))
            
        except:
            self.total_display.setText("0 ل.س")
            
    def pay_salary(self):
        """سداد راتب العامل"""
        QMessageBox.information(self, "قريباً", "ميزة سداد الراتب قيد التطوير")
        
    def bulk_pay_salaries(self):
        """سداد جماعي للرواتب"""
        QMessageBox.information(self, "قريباً", "ميزة السداد الجماعي قيد التطوير")
        
    def generate_salary_report(self):
        """إنشاء تقرير الرواتب"""
        QMessageBox.information(self, "قريباً", "ميزة تقرير الرواتب قيد التطوير")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المصروفات المتجاوبة والكاملة
"""

import logging
from datetime import datetime, date
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QGridLayout, QPushButton, QLabel, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QGroupBox, QFrame, QMessageBox, QScrollArea,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QDateEdit, QSplitter, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager
from utils.window_utils import center_window, apply_modern_style
from config.app_config import AppConfig

class ExpensesWindow(QDialog):
    """واجهة إدارة المصروفات المتجاوبة والكاملة"""
    
    # إشارات مخصصة
    expense_added = pyqtSignal(dict)
    expense_updated = pyqtSignal(dict)
    expense_deleted = pyqtSignal(int)
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """تهيئة واجهة إدارة المصروفات"""
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.current_user = current_user
        self.treasury_manager = TreasuryManager(db_manager)
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الحالة
        self.current_expense = None
        self.is_editing = False
        self.is_mobile_view = False
        
        # فئات المصروفات
        self.expense_categories = [
            "رواتب العمال",
            "فواتير الكهرباء",
            "فواتير الإنترنت",
            "صيانة المعدات",
            "مصروفات إدارية",
            "مصروفات تشغيلية",
            "مصروفات أخرى"
        ]
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.setup_responsive_design()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        # بدء المؤقتات
        self.start_timers()
        
        self.logger.info("تم فتح واجهة إدارة المصروفات")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتجاوبة"""
        
        # إعداد النافذة
        self.setWindowTitle("إدارة المصروفات")
        self.setModal(True)
        self.setMinimumSize(800, 600)
        self.resize(1200, 800)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الرئيسي
        main_widget = QFrame()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الأقسام
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        summary_section = self.create_summary_section()
        main_layout.addWidget(summary_section)
        
        # قسم المحتوى الرئيسي (جدول + نموذج)
        content_splitter = self.create_content_section()
        main_layout.addWidget(content_splitter, 1)
        
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        # إعداد التخطيط النهائي
        dialog_layout = QVBoxLayout()
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(scroll_area)
        self.setLayout(dialog_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # أيقونة المصروفات
        icon_label = QLabel("💸")
        icon_label.setStyleSheet("font-size: 28px; margin-right: 10px;")
        layout.addWidget(icon_label)
        
        # معلومات الهيدر
        info_layout = QVBoxLayout()
        
        title_label = QLabel("إدارة المصروفات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        info_layout.addWidget(title_label)
        
        subtitle_label = QLabel("تسجيل ومتابعة جميع المصروفات")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #fadbd8;
                margin: 0;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # معلومات المستخدم والوقت
        user_info_layout = QVBoxLayout()
        
        user_label = QLabel(f"المستخدم: {self.current_user['full_name']}")
        user_label.setStyleSheet("font-size: 12px; color: #fadbd8;")
        user_info_layout.addWidget(user_label)
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 11px; color: #f1948a;")
        user_info_layout.addWidget(self.time_label)
        
        layout.addLayout(user_info_layout)
        
        return frame
    
    def create_summary_section(self) -> QGroupBox:
        """إنشاء قسم الملخص"""
        
        group = QGroupBox("ملخص المصروفات")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #f39c12;
                background-color: white;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # مصروفات اليوم
        today_frame = self.create_summary_card("مصروفات اليوم", "SYP", "#3498db")
        self.today_expenses_label = today_frame.findChild(QLabel, "amount_label")
        layout.addWidget(today_frame, 0, 0)
        
        # مصروفات الشهر
        month_frame = self.create_summary_card("مصروفات الشهر", "SYP", "#9b59b6")
        self.month_expenses_label = month_frame.findChild(QLabel, "amount_label")
        layout.addWidget(month_frame, 0, 1)
        
        # إجمالي المصروفات
        total_frame = self.create_summary_card("إجمالي المصروفات", "SYP", "#e74c3c")
        self.total_expenses_label = total_frame.findChild(QLabel, "amount_label")
        layout.addWidget(total_frame, 0, 2)
        
        return group
    
    def create_summary_card(self, title: str, currency: str, color: str) -> QFrame:
        """إنشاء بطاقة ملخص"""
        
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                color: white;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 70px;
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # عنوان البطاقة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # قيمة المبلغ
        amount_label = QLabel("0")
        amount_label.setObjectName("amount_label")
        amount_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                margin: 5px 0;
            }
        """)
        layout.addWidget(amount_label)
        
        # رمز العملة
        symbol_label = QLabel("ل.س")
        symbol_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(symbol_label)
        
        return frame
    
    def create_content_section(self) -> QSplitter:
        """إنشاء قسم المحتوى الرئيسي"""
        
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول
        table_section = self.create_table_section()
        splitter.addWidget(table_section)
        
        # النموذج
        form_section = self.create_form_section()
        splitter.addWidget(form_section)
        
        # تعيين النسب
        splitter.setSizes([700, 400])
        
        return splitter
    
    def create_table_section(self) -> QGroupBox:
        """إنشاء قسم الجدول"""
        
        group = QGroupBox("قائمة المصروفات")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #27ae60;
                background-color: white;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # أدوات البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المصروفات...")
        self.search_edit.setMinimumHeight(35)
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        
        self.filter_category_combo = QComboBox()
        self.filter_category_combo.addItem("جميع الفئات", "")
        for category in self.expense_categories:
            self.filter_category_combo.addItem(category, category)
        self.filter_category_combo.setMinimumHeight(35)
        search_layout.addWidget(QLabel("الفئة:"))
        search_layout.addWidget(self.filter_category_combo)
        
        self.filter_date_edit = QDateEdit()
        self.filter_date_edit.setDate(QDate.currentDate())
        self.filter_date_edit.setCalendarPopup(True)
        self.filter_date_edit.setMinimumHeight(35)
        search_layout.addWidget(QLabel("التاريخ:"))
        search_layout.addWidget(self.filter_date_edit)
        
        filter_button = QPushButton("🔍 فلترة")
        filter_button.setMinimumHeight(35)
        filter_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        filter_button.clicked.connect(self.filter_expenses)
        search_layout.addWidget(filter_button)
        
        layout.addLayout(search_layout)
        
        # الجدول
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels([
            "التاريخ", "الفئة", "المبلغ", "المستفيد", "الوصف", "المستخدم"
        ])
        
        # إعداد الجدول
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الفئة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.Stretch)          # المستفيد
        header.setSectionResizeMode(4, QHeaderView.Stretch)          # الوصف
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المستخدم
        
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(self.expenses_table)
        
        return group
    
    def create_form_section(self) -> QGroupBox:
        """إنشاء قسم النموذج"""
        
        group = QGroupBox("إضافة/تعديل مصروف")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #8e44ad;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #8e44ad;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # فئة المصروف
        self.category_combo = QComboBox()
        for category in self.expense_categories:
            self.category_combo.addItem(category)
        self.category_combo.setMinimumHeight(40)
        layout.addRow("فئة المصروف:", self.category_combo)
        
        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setDecimals(0)
        self.amount_spin.setSuffix(" ل.س")
        self.amount_spin.setMinimumHeight(40)
        self.amount_spin.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addRow("المبلغ:", self.amount_spin)
        
        # المستفيد
        self.recipient_edit = QLineEdit()
        self.recipient_edit.setPlaceholderText("أدخل اسم المستفيد")
        self.recipient_edit.setMinimumHeight(40)
        layout.addRow("المستفيد:", self.recipient_edit)
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("أدخل وصف المصروف...")
        self.description_edit.setMaximumHeight(80)
        layout.addRow("الوصف:", self.description_edit)
        
        # تاريخ المصروف
        self.expense_date_edit = QDateEdit()
        self.expense_date_edit.setDate(QDate.currentDate())
        self.expense_date_edit.setCalendarPopup(True)
        self.expense_date_edit.setMinimumHeight(40)
        layout.addRow("التاريخ:", self.expense_date_edit)
        
        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.addItem("الليرة السورية", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي", "USD")
        self.currency_combo.setMinimumHeight(40)
        layout.addRow("العملة:", self.currency_combo)
        
        return group
    
    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار"""
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # زر إضافة مصروف
        self.add_button = QPushButton("➕ إضافة مصروف")
        self.add_button.setMinimumHeight(45)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.add_button)
        
        # زر تعديل مصروف
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setMinimumHeight(45)
        self.edit_button.setEnabled(False)
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.edit_button)
        
        # زر حذف مصروف
        self.delete_button = QPushButton("🗑️ حذف")
        self.delete_button.setMinimumHeight(45)
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.delete_button)
        
        # زر تحديث
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setMinimumHeight(45)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_button)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setMinimumHeight(45)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)
        
        return frame

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""

        # ربط الأزرار
        self.add_button.clicked.connect(self.add_expense)
        self.edit_button.clicked.connect(self.edit_expense)
        self.delete_button.clicked.connect(self.delete_expense)

        # ربط الجدول
        self.expenses_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.expenses_table.itemDoubleClicked.connect(self.edit_expense)

        # ربط البحث والفلترة
        self.search_edit.textChanged.connect(self.filter_expenses)
        self.filter_category_combo.currentTextChanged.connect(self.filter_expenses)
        self.filter_date_edit.dateChanged.connect(self.filter_expenses)

        # ربط تغيير العملة
        self.currency_combo.currentTextChanged.connect(self.on_currency_changed)

        # ربط الإشارات المخصصة
        self.expense_added.connect(self.on_expense_added)
        self.expense_updated.connect(self.on_expense_updated)
        self.expense_deleted.connect(self.on_expense_deleted)

    def setup_responsive_design(self):
        """إعداد التصميم المتجاوب"""

        screen_size = self.size()

        if screen_size.width() < 1000 or screen_size.height() < 700:
            self.is_mobile_view = True
            self.apply_mobile_styles()
        else:
            self.is_mobile_view = False
            self.apply_desktop_styles()

    def apply_mobile_styles(self):
        """تطبيق أنماط الجوال"""

        mobile_style = """
            QLabel { font-size: 10px; }
            QPushButton { font-size: 10px; padding: 5px 10px; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                font-size: 10px;
                min-height: 30px;
            }
            QTableWidget { font-size: 9px; }
        """
        self.setStyleSheet(self.styleSheet() + mobile_style)

    def apply_desktop_styles(self):
        """تطبيق أنماط سطح المكتب"""

        desktop_style = """
            QLabel { font-size: 10pt; }
            QPushButton { font-size: 10pt; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                font-size: 10pt;
            }
            QTableWidget { font-size: 9pt; }
        """
        self.setStyleSheet(self.styleSheet() + desktop_style)

    def start_timers(self):
        """بدء المؤقتات"""

        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

        # مؤقت تحديث الملخص
        self.summary_timer = QTimer()
        self.summary_timer.timeout.connect(self.update_summary)
        self.summary_timer.start(60000)  # كل دقيقة

        # تحديث أولي
        self.update_time()

    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime('%H:%M:%S')
        self.time_label.setText(f"الوقت: {current_time}")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.load_expenses()
            self.update_summary()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {e}")

    def load_expenses(self):
        """تحميل المصروفات"""
        try:
            expenses = self.db_manager.fetch_all("""
                SELECT e.*, u.full_name as user_name
                FROM expenses e
                LEFT JOIN users u ON e.user_id = u.id
                ORDER BY e.created_at DESC
            """)

            self.populate_table(expenses)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المصروفات: {e}")

    def populate_table(self, expenses):
        """ملء الجدول بالمصروفات"""
        try:
            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                # التاريخ
                date_item = QTableWidgetItem(expense['created_at'][:10])
                self.expenses_table.setItem(row, 0, date_item)

                # الفئة
                category_item = QTableWidgetItem(expense['category'])
                self.expenses_table.setItem(row, 1, category_item)

                # المبلغ
                amount_text = f"{expense['amount']:,.0f} {expense['currency']}"
                amount_item = QTableWidgetItem(amount_text)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.expenses_table.setItem(row, 2, amount_item)

                # المستفيد
                recipient_item = QTableWidgetItem(expense['recipient'] or "")
                self.expenses_table.setItem(row, 3, recipient_item)

                # الوصف
                description_item = QTableWidgetItem(expense['description'])
                self.expenses_table.setItem(row, 4, description_item)

                # المستخدم
                user_item = QTableWidgetItem(expense['user_name'] or "")
                self.expenses_table.setItem(row, 5, user_item)

                # حفظ معرف المصروف
                date_item.setData(Qt.UserRole, expense['id'])

        except Exception as e:
            self.logger.error(f"خطأ في ملء الجدول: {e}")

    def update_summary(self):
        """تحديث ملخص المصروفات"""
        try:
            today = date.today().strftime('%Y-%m-%d')
            current_month = date.today().strftime('%Y-%m')

            # مصروفات اليوم
            today_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE DATE(created_at) = ? AND currency = 'SYP'
            """, (today,))
            today_total = today_result['total'] if today_result else 0

            # مصروفات الشهر
            month_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE strftime('%Y-%m', created_at) = ? AND currency = 'SYP'
            """, (current_month,))
            month_total = month_result['total'] if month_result else 0

            # إجمالي المصروفات
            total_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE currency = 'SYP'
            """)
            total_expenses = total_result['total'] if total_result else 0

            # تحديث العرض
            self.today_expenses_label.setText(f"{today_total:,.0f}")
            self.month_expenses_label.setText(f"{month_total:,.0f}")
            self.total_expenses_label.setText(f"{total_expenses:,.0f}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الملخص: {e}")

    def filter_expenses(self):
        """فلترة المصروفات"""
        try:
            search_text = self.search_edit.text().strip()
            category_filter = self.filter_category_combo.currentData()
            date_filter = self.filter_date_edit.date().toString('yyyy-MM-dd')

            # بناء الاستعلام
            query = """
                SELECT e.*, u.full_name as user_name
                FROM expenses e
                LEFT JOIN users u ON e.user_id = u.id
                WHERE 1=1
            """
            params = []

            if search_text:
                query += " AND (e.description LIKE ? OR e.recipient LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])

            if category_filter:
                query += " AND e.category = ?"
                params.append(category_filter)

            if date_filter:
                query += " AND DATE(e.created_at) = ?"
                params.append(date_filter)

            query += " ORDER BY e.created_at DESC"

            # تنفيذ الاستعلام
            expenses = self.db_manager.fetch_all(query, tuple(params))
            self.populate_table(expenses)

        except Exception as e:
            self.logger.error(f"خطأ في فلترة المصروفات: {e}")

    def on_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""

        selected_items = self.expenses_table.selectedItems()

        if selected_items:
            # تفعيل أزرار التعديل والحذف
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)

            # تحميل بيانات المصروف المحدد
            row = selected_items[0].row()
            expense_id = self.expenses_table.item(row, 0).data(Qt.UserRole)
            self.load_expense_for_editing(expense_id)
        else:
            # تعطيل أزرار التعديل والحذف
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.clear_form()

    def load_expense_for_editing(self, expense_id: int):
        """تحميل بيانات المصروف للتعديل"""
        try:
            expense = self.db_manager.fetch_one("""
                SELECT * FROM expenses WHERE id = ?
            """, (expense_id,))

            if expense:
                self.current_expense = expense

                # ملء النموذج
                category_index = self.category_combo.findText(expense['category'])
                if category_index >= 0:
                    self.category_combo.setCurrentIndex(category_index)

                self.amount_spin.setValue(expense['amount'])
                self.recipient_edit.setText(expense['recipient'] or "")
                self.description_edit.setPlainText(expense['description'])

                # تحويل التاريخ
                expense_date = QDate.fromString(expense['created_at'][:10], 'yyyy-MM-dd')
                self.expense_date_edit.setDate(expense_date)

                # العملة
                currency_index = self.currency_combo.findData(expense['currency'])
                if currency_index >= 0:
                    self.currency_combo.setCurrentIndex(currency_index)

                self.is_editing = True

        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات المصروف: {e}")

    def on_currency_changed(self):
        """معالجة تغيير العملة"""
        currency = self.currency_combo.currentData()

        if currency == 'SYP':
            self.amount_spin.setSuffix(" ل.س")
            self.amount_spin.setDecimals(0)
        else:
            self.amount_spin.setSuffix(" $")
            self.amount_spin.setDecimals(2)

    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_expense_data():
                return

            # جمع بيانات المصروف
            expense_data = self.collect_expense_data()

            # التحقق من توفر الرصيد
            if not self.check_balance_availability(expense_data['amount'], expense_data['currency']):
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد إضافة المصروف",
                f"هل أنت متأكد من إضافة مصروف بقيمة {expense_data['amount']:,.0f if expense_data['currency'] == 'SYP' else expense_data['amount']:.2f} "
                f"{'ل.س' if expense_data['currency'] == 'SYP' else '$'}؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تعطيل الزر أثناء المعالجة
            self.add_button.setEnabled(False)
            self.add_button.setText("جاري الإضافة...")

            # إضافة المصروف
            success = self.db_manager.execute_query("""
                INSERT INTO expenses (user_id, category, amount, currency, description, recipient, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                self.current_user['id'],
                expense_data['category'],
                expense_data['amount'],
                expense_data['currency'],
                expense_data['description'],
                expense_data['recipient'],
                expense_data['created_at']
            ))

            if success:
                # خصم المبلغ من الخزينة
                self.treasury_manager.deduct_from_daily_balance(
                    user_id=self.current_user['id'],
                    amount=expense_data['amount'],
                    currency=expense_data['currency'],
                    description=f"مصروف: {expense_data['category']} - {expense_data['description']}"
                )

                # إرسال إشارة النجاح
                self.expense_added.emit(expense_data)

                QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")

                # مسح النموذج
                self.clear_form()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في إضافة المصروف")

        except Exception as e:
            self.logger.error(f"خطأ في إضافة المصروف: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المصروف: {e}")

        finally:
            # إعادة تفعيل الزر
            self.add_button.setEnabled(True)
            self.add_button.setText("➕ إضافة مصروف")

    def edit_expense(self):
        """تعديل مصروف موجود"""
        try:
            if not self.current_expense:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للتعديل")
                return

            # التحقق من صحة البيانات
            if not self.validate_expense_data():
                return

            # جمع بيانات المصروف
            expense_data = self.collect_expense_data()

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد تعديل المصروف",
                "هل أنت متأكد من تعديل هذا المصروف؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تعطيل الزر أثناء المعالجة
            self.edit_button.setEnabled(False)
            self.edit_button.setText("جاري التعديل...")

            # تعديل المصروف
            success = self.db_manager.execute_query("""
                UPDATE expenses
                SET category=?, amount=?, currency=?, description=?, recipient=?
                WHERE id=?
            """, (
                expense_data['category'],
                expense_data['amount'],
                expense_data['currency'],
                expense_data['description'],
                expense_data['recipient'],
                self.current_expense['id']
            ))

            if success:
                # إرسال إشارة النجاح
                self.expense_updated.emit(expense_data)

                QMessageBox.information(self, "نجح", "تم تعديل المصروف بنجاح")

                # مسح النموذج
                self.clear_form()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في تعديل المصروف")

        except Exception as e:
            self.logger.error(f"خطأ في تعديل المصروف: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تعديل المصروف: {e}")

        finally:
            # إعادة تفعيل الزر
            self.edit_button.setEnabled(True)
            self.edit_button.setText("✏️ تعديل")

    def delete_expense(self):
        """حذف مصروف"""
        try:
            if not self.current_expense:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للحذف")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف هذا المصروف؟\n"
                f"الفئة: {self.current_expense['category']}\n"
                f"المبلغ: {self.current_expense['amount']:,.0f} {self.current_expense['currency']}\n"
                f"الوصف: {self.current_expense['description']}",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تعطيل الزر أثناء المعالجة
            self.delete_button.setEnabled(False)
            self.delete_button.setText("جاري الحذف...")

            # حذف المصروف
            success = self.db_manager.execute_query("""
                DELETE FROM expenses WHERE id = ?
            """, (self.current_expense['id'],))

            if success:
                # إرسال إشارة النجاح
                self.expense_deleted.emit(self.current_expense['id'])

                QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")

                # مسح النموذج
                self.clear_form()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في حذف المصروف")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المصروف: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المصروف: {e}")

        finally:
            # إعادة تفعيل الزر
            self.delete_button.setEnabled(True)
            self.delete_button.setText("🗑️ حذف")

    def validate_expense_data(self) -> bool:
        """التحقق من صحة بيانات المصروف"""

        # التحقق من المبلغ
        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.amount_spin.setFocus()
            return False

        # التحقق من الوصف
        if not self.description_edit.toPlainText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف المصروف")
            self.description_edit.setFocus()
            return False

        return True

    def collect_expense_data(self) -> dict:
        """جمع بيانات المصروف من النموذج"""

        return {
            'category': self.category_combo.currentText(),
            'amount': self.amount_spin.value(),
            'currency': self.currency_combo.currentData(),
            'recipient': self.recipient_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'created_at': self.expense_date_edit.date().toString('yyyy-MM-dd') + ' ' + datetime.now().strftime('%H:%M:%S')
        }

    def check_balance_availability(self, amount: float, currency: str) -> bool:
        """التحقق من توفر الرصيد"""

        available_balance = self.treasury_manager.get_user_daily_balance(
            self.current_user['id'], currency
        )

        if amount > available_balance:
            QMessageBox.warning(
                self, "تحذير",
                f"الرصيد غير كافي\n"
                f"الرصيد المتاح: {available_balance:,.0f if currency == 'SYP' else available_balance:.2f} "
                f"{'ل.س' if currency == 'SYP' else '$'}\n"
                f"المبلغ المطلوب: {amount:,.0f if currency == 'SYP' else amount:.2f} "
                f"{'ل.س' if currency == 'SYP' else '$'}"
            )
            return False

        return True

    def clear_form(self):
        """مسح النموذج"""

        self.category_combo.setCurrentIndex(0)
        self.amount_spin.setValue(0)
        self.recipient_edit.clear()
        self.description_edit.clear()
        self.expense_date_edit.setDate(QDate.currentDate())
        self.currency_combo.setCurrentIndex(0)

        # إعادة تعيين الحالة
        self.current_expense = None
        self.is_editing = False

        # تعطيل أزرار التعديل والحذف
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)

    def refresh_data(self):
        """تحديث جميع البيانات"""

        self.load_expenses()
        self.update_summary()
        self.clear_form()

    def on_expense_added(self, expense_data: dict):
        """معالجة إضافة مصروف جديد"""

        self.logger.info(f"تم إضافة مصروف: {expense_data['category']} - {expense_data['amount']}")
        self.refresh_data()

    def on_expense_updated(self, expense_data: dict):
        """معالجة تعديل مصروف"""

        self.logger.info(f"تم تعديل مصروف: {expense_data['category']} - {expense_data['amount']}")
        self.refresh_data()

    def on_expense_deleted(self, expense_id: int):
        """معالجة حذف مصروف"""

        self.logger.info(f"تم حذف مصروف: {expense_id}")
        self.refresh_data()

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للتجاوب"""
        super().resizeEvent(event)
        self.setup_responsive_design()

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""

        # إيقاف المؤقتات
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()
        if hasattr(self, 'summary_timer'):
            self.summary_timer.stop()

        event.accept()

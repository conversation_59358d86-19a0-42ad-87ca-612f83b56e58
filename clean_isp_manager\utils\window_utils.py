#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات النوافذ والتنسيق
"""

from PyQt5.QtWidgets import QDesktopWidget, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def center_window(window: QWidget):
    """توسيط النافذة في الشاشة"""
    try:
        screen = QDesktopWidget().screenGeometry()
        window_geometry = window.geometry()
        
        x = (screen.width() - window_geometry.width()) // 2
        y = (screen.height() - window_geometry.height()) // 2
        
        window.move(x, y)
        
    except Exception as e:
        print(f"خطأ في توسيط النافذة: {e}")
        window.move(100, 100)

def apply_modern_style(widget: QWidget):
    """تطبيق التنسيق الحديث"""
    
    modern_style = """
    /* التنسيق العام */
    QWidget {
        font-family: 'Segoe UI', 'Tahoma', sans-serif;
        font-size: 10pt;
        color: #2c3e50;
        background-color: #ecf0f1;
    }
    
    /* النوافذ الرئيسية */
    QMainWindow {
        background-color: #ecf0f1;
    }
    
    /* الأزرار */
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
        min-height: 30px;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
    }
    
    QPushButton:pressed {
        background-color: #21618c;
    }
    
    QPushButton:disabled {
        background-color: #bdc3c7;
        color: #7f8c8d;
    }
    
    /* حقول الإدخال */
    QLineEdit, QTextEdit, QPlainTextEdit {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        selection-background-color: #3498db;
    }
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border-color: #3498db;
    }
    
    /* القوائم المنسدلة */
    QComboBox {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        min-height: 20px;
    }
    
    QComboBox:focus {
        border-color: #3498db;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 30px;
    }
    
    QComboBox::down-arrow {
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #7f8c8d;
        margin-right: 10px;
    }
    
    /* حقول الأرقام */
    QSpinBox, QDoubleSpinBox {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        min-height: 20px;
    }
    
    QSpinBox:focus, QDoubleSpinBox:focus {
        border-color: #3498db;
    }
    
    /* التبويبات */
    QTabWidget::pane {
        border: 1px solid #bdc3c7;
        border-radius: 6px;
        background-color: white;
    }
    
    QTabBar::tab {
        background-color: #ecf0f1;
        border: 1px solid #bdc3c7;
        padding: 8px 16px;
        margin-right: 2px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }
    
    QTabBar::tab:selected {
        background-color: white;
        border-bottom: none;
    }
    
    QTabBar::tab:hover {
        background-color: #d5dbdb;
    }
    
    /* الجداول */
    QTableWidget {
        gridline-color: #bdc3c7;
        background-color: white;
        alternate-background-color: #f8f9fa;
        border: 1px solid #bdc3c7;
        border-radius: 6px;
    }
    
    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #ecf0f1;
    }
    
    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 10px;
        border: none;
        font-weight: bold;
    }
    
    QHeaderView::section:hover {
        background-color: #2c3e50;
    }
    
    /* أشرطة التمرير */
    QScrollBar:vertical {
        background-color: #ecf0f1;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background-color: #bdc3c7;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background-color: #95a5a6;
    }
    
    QScrollBar:horizontal {
        background-color: #ecf0f1;
        height: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:horizontal {
        background-color: #bdc3c7;
        border-radius: 6px;
        min-width: 20px;
    }
    
    QScrollBar::handle:horizontal:hover {
        background-color: #95a5a6;
    }
    
    /* التسميات */
    QLabel {
        color: #2c3e50;
    }
    
    /* المجموعات */
    QGroupBox {
        font-weight: bold;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        margin-top: 10px;
        padding-top: 10px;
        background-color: white;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 10px 0 10px;
        color: #2c3e50;
        background-color: white;
    }
    
    /* شريط الحالة */
    QStatusBar {
        background-color: #34495e;
        color: white;
        border-top: 1px solid #2c3e50;
    }
    
    /* شريط القوائم */
    QMenuBar {
        background-color: #34495e;
        color: white;
        border-bottom: 1px solid #2c3e50;
    }
    
    QMenuBar::item {
        padding: 8px 12px;
        background-color: transparent;
    }
    
    QMenuBar::item:selected {
        background-color: #2c3e50;
    }
    
    QMenu {
        background-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 6px;
    }
    
    QMenu::item {
        padding: 8px 20px;
        color: #2c3e50;
    }
    
    QMenu::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    /* مربعات الاختيار */
    QCheckBox {
        spacing: 8px;
    }
    
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 3px;
        background-color: white;
    }
    
    QCheckBox::indicator:checked {
        background-color: #3498db;
        border-color: #3498db;
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
    }
    
    /* أزرار الراديو */
    QRadioButton {
        spacing: 8px;
    }
    
    QRadioButton::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 9px;
        background-color: white;
    }
    
    QRadioButton::indicator:checked {
        background-color: #3498db;
        border-color: #3498db;
    }
    
    /* أشرطة التقدم */
    QProgressBar {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        text-align: center;
        background-color: white;
    }
    
    QProgressBar::chunk {
        background-color: #3498db;
        border-radius: 4px;
    }
    """
    
    widget.setStyleSheet(modern_style)

def set_font(widget: QWidget, size: int = 10, bold: bool = False):
    """تعيين خط النص"""
    font = QFont("Segoe UI", size)
    font.setBold(bold)
    widget.setFont(font)

def apply_success_style(widget: QWidget):
    """تطبيق تنسيق النجاح (أخضر)"""
    widget.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)

def apply_danger_style(widget: QWidget):
    """تطبيق تنسيق الخطر (أحمر)"""
    widget.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)

def apply_warning_style(widget: QWidget):
    """تطبيق تنسيق التحذير (برتقالي)"""
    widget.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإغلاق الصندوق
"""

import sys
import os
sys.path.append('src')

def main():
    """اختبار نهائي"""
    
    print("🚨 اختبار نهائي لإغلاق الصندوق")
    print("=" * 50)
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        user_id = 1
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # 1. فحص الحالة الحالية
        print("\n1️⃣ فحص الحالة الحالية...")
        is_active = treasury_manager.is_session_active(user_id)
        print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة'}")
        
        # 2. إذا كانت مغلقة، افتح جلسة جديدة
        if not is_active:
            print("\n2️⃣ فتح جلسة جديدة...")
            treasury_manager.open_cash_box(user_id)
            is_active = treasury_manager.is_session_active(user_id)
            print(f"📊 حالة الجلسة بعد الفتح: {'مفتوحة' if is_active else 'مغلقة'}")
        
        # 3. إغلاق الجلسة
        if is_active:
            print("\n3️⃣ إغلاق الجلسة...")
            success = treasury_manager.close_cash_box_to_daily_treasury(user_id, 0, 'اختبار نهائي')
            
            if success:
                print("✅ تم الإغلاق بنجاح")
                
                # فحص الحالة بعد الإغلاق
                is_active_after = treasury_manager.is_session_active(user_id)
                print(f"📊 حالة الجلسة بعد الإغلاق: {'مفتوحة' if is_active_after else 'مغلقة'}")
                
                if not is_active_after:
                    print("🎉 الإغلاق يعمل بشكل مثالي!")
                    
                    print("\n📋 النتائج:")
                    print("  ✅ إغلاق الصندوق يغلق الجلسة")
                    print("  ✅ واجهة إغلاق الصندوق ستظهر 'لا يوجد شيفت'")
                    print("  ✅ تسجيل الدخول سيفتح شيفت جديد")
                    print("  ✅ لن تظهر رسالة 'شيفت مفتوح' خاطئة")
                    
                    return True
                else:
                    print("❌ الجلسة ما زالت مفتوحة!")
                    return False
            else:
                print("❌ فشل في الإغلاق")
                return False
        else:
            print("⚠️ لا توجد جلسة مفتوحة للإغلاق")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. أغلق الصندوق → الشيفت يُغلق فعلياً")
        print("  3. أعد فتح واجهة إغلاق الصندوق → ستظهر 'لا يوجد شيفت'")
        print("  4. أغلق البرنامج وأعد فتحه → لن تظهر رسالة شيفت مفتوح")
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

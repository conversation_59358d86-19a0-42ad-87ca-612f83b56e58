#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الصندوق
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager
from datetime import date

def main():
    print("=== اختبار إصلاحات الصندوق ===")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    today = date.today().strftime('%Y-%m-%d')
    user_id = 1
    username = 'admin'
    
    print(f"التاريخ: {today}")
    print(f"المستخدم: {username} (ID: {user_id})")
    
    # اختبار إنشاء/جلب الصندوق
    print("\n--- اختبار إنشاء/جلب الصندوق ---")
    cash_box = db_manager.get_or_create_cash_box(user_id, username, today)
    
    if cash_box:
        print(f"✅ تم العثور على/إنشاء الصندوق بنجاح")
        print(f"ID الصندوق: {cash_box['id']}")
        print(f"المستخدم: {cash_box['user_name'] if 'user_name' in cash_box.keys() else 'غير محدد'}")
        print(f"التاريخ: {cash_box['date']}")
        print(f"الحالة: {cash_box['status'] if 'status' in cash_box.keys() else 'غير محدد'}")
    else:
        print("❌ فشل في إنشاء/جلب الصندوق")
        return
    
    # اختبار جلب المبيعات
    print("\n--- اختبار جلب المبيعات ---")
    
    # جميع المبيعات للمستخدم
    all_sales = db_manager.fetch_one("""
        SELECT COALESCE(SUM(amount), 0) as total FROM transactions
        WHERE user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') AND amount > 0
    """, (username,))
    
    print(f"إجمالي جميع المبيعات: {all_sales['total'] if all_sales else 0} ل.س")
    
    # مبيعات اليوم فقط
    today_sales = db_manager.fetch_one("""
        SELECT COALESCE(SUM(amount), 0) as total FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') AND amount > 0
    """, (today, username))
    
    print(f"إجمالي مبيعات اليوم: {today_sales['total'] if today_sales else 0} ل.س")
    
    # تفصيل مبيعات اليوم
    today_sales_detail = db_manager.fetch_all("""
        SELECT type, description, amount, created_at FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
        ORDER BY created_at DESC
    """, (today, username))
    
    print(f"\nتفصيل مبيعات اليوم ({len(today_sales_detail)} عملية):")
    for sale in today_sales_detail:
        print(f"• {sale['type']}: {sale['amount']} ل.س - {sale['description']}")
    
    # اختبار جلب المصاريف
    print("\n--- اختبار جلب المصاريف ---")
    
    # مصاريف اليوم من expenses
    expenses_today = db_manager.fetch_one("""
        SELECT COALESCE(SUM(amount), 0) as total FROM expenses
        WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type NOT IN ('راتب', 'رواتب')
    """, (today, username))
    
    print(f"إجمالي المصاريف من expenses: {expenses_today['total'] if expenses_today else 0} ل.س")
    
    # مصاريف اليوم من transactions
    transactions_expenses = db_manager.fetch_one("""
        SELECT COALESCE(SUM(ABS(amount)), 0) as total FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف' AND amount < 0
    """, (today, username))
    
    print(f"إجمالي المصاريف من transactions: {transactions_expenses['total'] if transactions_expenses else 0} ل.س")
    
    # الحسابات النهائية
    print("\n--- الحسابات النهائية ---")
    total_income = (today_sales['total'] if today_sales else 0)
    total_expenses = (expenses_today['total'] if expenses_today else 0) + (transactions_expenses['total'] if transactions_expenses else 0)
    net_amount = total_income - total_expenses
    
    print(f"إجمالي الدخل: {total_income:,} ل.س")
    print(f"إجمالي المصاريف: {total_expenses:,} ل.س")
    print(f"الصافي: {net_amount:,} ل.س")
    
    db_manager.close()

if __name__ == "__main__":
    main()

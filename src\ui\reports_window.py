# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QDateEdit, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    # في حالة عدم وجود الملف، استخدم دوال بديلة
    def create_arabic_font(size, bold=False):
        from PyQt5.QtGui import QFont
        font = QFont("Arial", size)
        font.setBold(bold)
        return font
    def apply_arabic_style(widget, size, bold=False):
        widget.setFont(create_arabic_font(size, bold))
    def format_currency(amount):
        return f"{amount:,.0f} ل.س"

class ReportsWindow(QDialog):
    """نافذة التقارير"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("التقارير والإحصائيات")
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("التقارير والإحصائيات")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # تبويبات التقارير
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)
        
        # تبويب التقارير المالية
        financial_tab = self.create_financial_tab()
        self.tabs.addTab(financial_tab, "التقارير المالية")
        
        # تبويب تقارير المشتركين
        subscribers_tab = self.create_subscribers_tab()
        self.tabs.addTab(subscribers_tab, "تقارير المشتركين")
        
        # تبويب تقارير المخزون
        inventory_tab = self.create_inventory_tab()
        self.tabs.addTab(inventory_tab, "تقارير المخزون")
        
        # تبويب تقارير العمال
        workers_tab = self.create_workers_tab()
        self.tabs.addTab(workers_tab, "تقارير العمال")
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_financial_tab(self):
        """إنشاء تبويب التقارير المالية"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # فترة التقرير
        period_group = QGroupBox("فترة التقرير")
        apply_arabic_style(period_group, 10, bold=True)
        
        period_layout = QGridLayout(period_group)
        period_layout.setSpacing(10)
        
        # من تاريخ
        from_label = QLabel("من تاريخ:")
        apply_arabic_style(from_label, 10)
        self.from_date_edit = QDateEdit()
        apply_arabic_style(self.from_date_edit, 10)
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        
        # إلى تاريخ
        to_label = QLabel("إلى تاريخ:")
        apply_arabic_style(to_label, 10)
        self.to_date_edit = QDateEdit()
        apply_arabic_style(self.to_date_edit, 10)
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        
        period_layout.addWidget(from_label, 0, 0)
        period_layout.addWidget(self.from_date_edit, 0, 1)
        period_layout.addWidget(to_label, 0, 2)
        period_layout.addWidget(self.to_date_edit, 0, 3)
        
        # أنواع التقارير المالية
        reports_group = QGroupBox("أنواع التقارير المالية")
        apply_arabic_style(reports_group, 10, bold=True)
        
        reports_layout = QGridLayout(reports_group)
        reports_layout.setSpacing(10)
        
        # أزرار التقارير
        daily_income_btn = QPushButton("تقرير الإيرادات اليومية")
        apply_arabic_style(daily_income_btn, 10)
        daily_income_btn.setStyleSheet(self.get_report_button_style("#27ae60"))
        
        monthly_summary_btn = QPushButton("ملخص شهري")
        apply_arabic_style(monthly_summary_btn, 10)
        monthly_summary_btn.setStyleSheet(self.get_report_button_style("#3498db"))
        
        cash_flow_btn = QPushButton("تقرير التدفق النقدي")
        apply_arabic_style(cash_flow_btn, 10)
        cash_flow_btn.setStyleSheet(self.get_report_button_style("#9b59b6"))
        
        profit_loss_btn = QPushButton("تقرير الأرباح والخسائر")
        apply_arabic_style(profit_loss_btn, 10)
        profit_loss_btn.setStyleSheet(self.get_report_button_style("#e74c3c"))
        
        reports_layout.addWidget(daily_income_btn, 0, 0)
        reports_layout.addWidget(monthly_summary_btn, 0, 1)
        reports_layout.addWidget(cash_flow_btn, 1, 0)
        reports_layout.addWidget(profit_loss_btn, 1, 1)
        
        # ربط الأحداث
        daily_income_btn.clicked.connect(self.generate_daily_income_report)
        monthly_summary_btn.clicked.connect(self.generate_monthly_summary)
        cash_flow_btn.clicked.connect(self.generate_cash_flow_report)
        profit_loss_btn.clicked.connect(self.generate_profit_loss_report)
        
        layout.addWidget(period_group)
        layout.addWidget(reports_group)
        layout.addStretch()
        
        return widget
        
    def create_subscribers_tab(self):
        """إنشاء تبويب تقارير المشتركين"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # أنواع تقارير المشتركين
        reports_group = QGroupBox("تقارير المشتركين")
        apply_arabic_style(reports_group, 10, bold=True)
        
        reports_layout = QGridLayout(reports_group)
        reports_layout.setSpacing(10)
        
        new_subscribers_btn = QPushButton("المشتركون الجدد")
        apply_arabic_style(new_subscribers_btn, 10)
        new_subscribers_btn.setStyleSheet(self.get_report_button_style("#27ae60"))
        
        active_subscribers_btn = QPushButton("المشتركون النشطون")
        apply_arabic_style(active_subscribers_btn, 10)
        active_subscribers_btn.setStyleSheet(self.get_report_button_style("#3498db"))
        
        expired_subscriptions_btn = QPushButton("الاشتراكات المنتهية")
        apply_arabic_style(expired_subscriptions_btn, 10)
        expired_subscriptions_btn.setStyleSheet(self.get_report_button_style("#e74c3c"))
        
        packages_report_btn = QPushButton("تقرير الباقات")
        apply_arabic_style(packages_report_btn, 10)
        packages_report_btn.setStyleSheet(self.get_report_button_style("#f39c12"))
        
        reports_layout.addWidget(new_subscribers_btn, 0, 0)
        reports_layout.addWidget(active_subscribers_btn, 0, 1)
        reports_layout.addWidget(expired_subscriptions_btn, 1, 0)
        reports_layout.addWidget(packages_report_btn, 1, 1)
        
        # ربط الأحداث
        new_subscribers_btn.clicked.connect(self.generate_new_subscribers_report)
        active_subscribers_btn.clicked.connect(self.generate_active_subscribers_report)
        expired_subscriptions_btn.clicked.connect(self.generate_expired_subscriptions_report)
        packages_report_btn.clicked.connect(self.generate_packages_report)
        
        layout.addWidget(reports_group)
        layout.addStretch()
        
        return widget
        
    def create_inventory_tab(self):
        """إنشاء تبويب تقارير المخزون"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # أنواع تقارير المخزون
        reports_group = QGroupBox("تقارير المخزون")
        apply_arabic_style(reports_group, 10, bold=True)
        
        reports_layout = QGridLayout(reports_group)
        reports_layout.setSpacing(10)
        
        current_stock_btn = QPushButton("المخزون الحالي")
        apply_arabic_style(current_stock_btn, 10)
        current_stock_btn.setStyleSheet(self.get_report_button_style("#27ae60"))
        
        low_stock_btn = QPushButton("المنتجات قليلة المخزون")
        apply_arabic_style(low_stock_btn, 10)
        low_stock_btn.setStyleSheet(self.get_report_button_style("#e74c3c"))
        
        stock_movement_btn = QPushButton("حركة المخزون")
        apply_arabic_style(stock_movement_btn, 10)
        stock_movement_btn.setStyleSheet(self.get_report_button_style("#3498db"))
        
        worker_inventory_btn = QPushButton("مخزون العمال")
        apply_arabic_style(worker_inventory_btn, 10)
        worker_inventory_btn.setStyleSheet(self.get_report_button_style("#9b59b6"))
        
        reports_layout.addWidget(current_stock_btn, 0, 0)
        reports_layout.addWidget(low_stock_btn, 0, 1)
        reports_layout.addWidget(stock_movement_btn, 1, 0)
        reports_layout.addWidget(worker_inventory_btn, 1, 1)
        
        # ربط الأحداث
        current_stock_btn.clicked.connect(self.generate_current_stock_report)
        low_stock_btn.clicked.connect(self.generate_low_stock_report)
        stock_movement_btn.clicked.connect(self.generate_stock_movement_report)
        worker_inventory_btn.clicked.connect(self.generate_worker_inventory_report)
        
        layout.addWidget(reports_group)
        layout.addStretch()
        
        return widget
        
    def create_workers_tab(self):
        """إنشاء تبويب تقارير العمال"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # أنواع تقارير العمال
        reports_group = QGroupBox("تقارير العمال")
        apply_arabic_style(reports_group, 10, bold=True)
        
        reports_layout = QGridLayout(reports_group)
        reports_layout.setSpacing(10)
        
        workers_performance_btn = QPushButton("أداء العمال")
        apply_arabic_style(workers_performance_btn, 10)
        workers_performance_btn.setStyleSheet(self.get_report_button_style("#27ae60"))
        
        commissions_btn = QPushButton("تقرير العمولات")
        apply_arabic_style(commissions_btn, 10)
        commissions_btn.setStyleSheet(self.get_report_button_style("#f39c12"))
        
        deliveries_btn = QPushButton("تقرير التسليمات")
        apply_arabic_style(deliveries_btn, 10)
        deliveries_btn.setStyleSheet(self.get_report_button_style("#3498db"))
        
        areas_btn = QPushButton("تقرير المناطق")
        apply_arabic_style(areas_btn, 10)
        areas_btn.setStyleSheet(self.get_report_button_style("#9b59b6"))
        
        reports_layout.addWidget(workers_performance_btn, 0, 0)
        reports_layout.addWidget(commissions_btn, 0, 1)
        reports_layout.addWidget(deliveries_btn, 1, 0)
        reports_layout.addWidget(areas_btn, 1, 1)
        
        # ربط الأحداث
        workers_performance_btn.clicked.connect(self.generate_workers_performance_report)
        commissions_btn.clicked.connect(self.generate_commissions_report)
        deliveries_btn.clicked.connect(self.generate_deliveries_report)
        areas_btn.clicked.connect(self.generate_areas_report)
        
        layout.addWidget(reports_group)
        layout.addStretch()
        
        return widget
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        export_button = QPushButton("تصدير التقرير")
        apply_arabic_style(export_button, 10, bold=True)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        print_button = QPushButton("طباعة التقرير")
        apply_arabic_style(print_button, 10)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(export_button)
        layout.addWidget(print_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        export_button.clicked.connect(self.export_report)
        print_button.clicked.connect(self.print_report)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def get_report_button_style(self, color):
        """الحصول على تنسيق أزرار التقارير"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 15px 20px;
                border-radius: 8px;
                font-weight: bold;
                min-height: 40px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
        
    def darken_color(self, color):
        """تغميق اللون"""
        # تحويل بسيط لتغميق اللون
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#9b59b6": "#8e44ad",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    # دوال التقارير المالية
    def generate_daily_income_report(self):
        """تقرير الإيرادات اليومية"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # جلب المعاملات اليومية
            transactions = self.db_manager.fetch_all("""
                SELECT type, amount, description, created_at
                FROM transactions
                WHERE DATE(created_at) = ?
                ORDER BY created_at DESC
            """, (today,))

            if not transactions:
                QMessageBox.information(self, "تقرير الإيرادات اليومية", "لا توجد معاملات لهذا اليوم")
                return

            # حساب الإجماليات
            total_income = sum(t['amount'] for t in transactions if t['amount'] > 0)
            total_expenses = sum(abs(t['amount']) for t in transactions if t['amount'] < 0)
            net_profit = total_income - total_expenses

            # إنشاء التقرير
            report_text = f"""تقرير الإيرادات اليومية - {today}
{'='*50}

إجمالي الإيرادات: {format_currency(total_income)}
إجمالي المصروفات: {format_currency(total_expenses)}
صافي الربح: {format_currency(net_profit)}

تفاصيل المعاملات:
{'-'*50}
"""

            for transaction in transactions:
                amount_text = format_currency(abs(transaction['amount']))
                type_text = "إيراد" if transaction['amount'] > 0 else "مصروف"
                report_text += f"{transaction['created_at'][:19]} | {type_text} | {amount_text} | {transaction['description']}\n"

            self.show_report_dialog("تقرير الإيرادات اليومية", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الإيرادات اليومية: {e}")

    def generate_monthly_summary(self):
        """ملخص شهري"""
        try:
            from datetime import datetime
            current_month = datetime.now().strftime('%Y-%m')

            # جلب إحصائيات الشهر
            stats = self.db_manager.fetch_one("""
                SELECT
                    COUNT(CASE WHEN amount > 0 THEN 1 END) as income_count,
                    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
                    COUNT(CASE WHEN amount < 0 THEN 1 END) as expense_count,
                    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_expenses
                FROM transactions
                WHERE strftime('%Y-%m', created_at) = ?
            """, (current_month,))

            # جلب عدد المشتركين الجدد
            new_subscribers = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count
                FROM subscribers
                WHERE strftime('%Y-%m', created_at) = ?
            """, (current_month,))

            if not stats or stats['total_income'] is None:
                QMessageBox.information(self, "الملخص الشهري", "لا توجد بيانات لهذا الشهر")
                return

            total_income = stats['total_income'] or 0
            total_expenses = stats['total_expenses'] or 0
            net_profit = total_income - total_expenses

            report_text = f"""الملخص الشهري - {current_month}
{'='*50}

المالية:
  إجمالي الإيرادات: {format_currency(total_income)} ({stats['income_count']} معاملة)
  إجمالي المصروفات: {format_currency(total_expenses)} ({stats['expense_count']} معاملة)
  صافي الربح: {format_currency(net_profit)}

المشتركون:
  مشتركون جدد: {new_subscribers['count']}

الأداء:
  متوسط الإيراد اليومي: {format_currency(total_income / 30)}
  {'أداء ممتاز' if net_profit > 100000 else 'أداء جيد' if net_profit > 0 else 'يحتاج تحسين'}
"""

            self.show_report_dialog("الملخص الشهري", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الملخص الشهري: {e}")

    def generate_cash_flow_report(self):
        """تقرير التدفق النقدي"""
        try:
            # جلب آخر 7 أيام من المعاملات
            transactions = self.db_manager.fetch_all("""
                SELECT DATE(created_at) as date,
                       SUM(amount) as net_flow
                FROM transactions
                WHERE created_at >= date('now', '-7 days')
                GROUP BY DATE(created_at)
                ORDER BY date
            """)

            if not transactions:
                QMessageBox.information(self, "تقرير التدفق النقدي", "لا توجد معاملات في آخر 7 أيام")
                return

            report_text = f"""تقرير التدفق النقدي - آخر 7 أيام
{'='*50}

التدفق النقدي اليومي:
{'-'*50}
"""

            total_flow = 0
            for transaction in transactions:
                total_flow += transaction['net_flow']
                flow_text = "موجب ↑" if transaction['net_flow'] > 0 else "سالب ↓"
                report_text += f"{transaction['date']} | {format_currency(transaction['net_flow'])} ({flow_text})\n"

            report_text += f"\n{'-'*50}\nصافي التدفق: {format_currency(total_flow)}"

            self.show_report_dialog("تقرير التدفق النقدي", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير التدفق النقدي: {e}")

    def generate_profit_loss_report(self):
        """تقرير الأرباح والخسائر"""
        try:
            from datetime import datetime
            current_month = datetime.now().strftime('%Y-%m')

            # جلب البيانات المالية
            financial_data = self.db_manager.fetch_one("""
                SELECT
                    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as revenue,
                    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expenses
                FROM transactions
                WHERE strftime('%Y-%m', created_at) = ?
            """, (current_month,))

            if not financial_data or financial_data['revenue'] is None:
                QMessageBox.information(self, "تقرير الأرباح والخسائر", "لا توجد بيانات مالية لهذا الشهر")
                return

            revenue = financial_data['revenue'] or 0
            expenses = financial_data['expenses'] or 0
            net_profit = revenue - expenses
            profit_margin = (net_profit / revenue * 100) if revenue > 0 else 0

            report_text = f"""تقرير الأرباح والخسائر - {current_month}
{'='*50}

الإيرادات: {format_currency(revenue)}
المصروفات: {format_currency(expenses)}
{'='*50}
صافي الربح: {format_currency(net_profit)}
هامش الربح: {profit_margin:.1f}%

التحليل:
{'✅ الشركة تحقق أرباح جيدة' if net_profit > 0 else '⚠️ الشركة تتكبد خسائر'}
"""

            self.show_report_dialog("تقرير الأرباح والخسائر", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الأرباح والخسائر: {e}")
        
    # دوال تقارير المشتركين
    def generate_new_subscribers_report(self):
        """تقرير المشتركين الجدد"""
        try:
            from datetime import datetime, timedelta
            last_30_days = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            # جلب المشتركين الجدد
            new_subscribers = self.db_manager.fetch_all("""
                SELECT name, phone, address, package_name, created_at
                FROM subscribers
                WHERE created_at >= ?
                ORDER BY created_at DESC
            """, (last_30_days,))

            if not new_subscribers:
                QMessageBox.information(self, "تقرير المشتركين الجدد", "لا يوجد مشتركون جدد في آخر 30 يوم")
                return

            report_text = f"""تقرير المشتركين الجدد - آخر 30 يوم
{'='*60}

إجمالي المشتركين الجدد: {len(new_subscribers)}

تفاصيل المشتركين:
{'-'*60}
"""

            for subscriber in new_subscribers:
                report_text += f"""
الاسم: {subscriber['name']}
الهاتف: {subscriber['phone']}
العنوان: {subscriber['address']}
الباقة: {subscriber['package_name']}
تاريخ الاشتراك: {subscriber['created_at'][:10]}
{'-'*30}"""

            self.show_report_dialog("تقرير المشتركين الجدد", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير المشتركين الجدد: {e}")

    def generate_active_subscribers_report(self):
        """تقرير المشتركين النشطين"""
        try:
            # جلب المشتركين النشطين
            active_subscribers = self.db_manager.fetch_all("""
                SELECT name, phone, package_name, subscription_end_date,
                       CASE
                           WHEN subscription_end_date > date('now') THEN 'نشط'
                           WHEN subscription_end_date > date('now', '-7 days') THEN 'ينتهي قريباً'
                           ELSE 'منتهي'
                       END as status
                FROM subscribers
                WHERE subscription_end_date > date('now', '-30 days')
                ORDER BY subscription_end_date
            """)

            if not active_subscribers:
                QMessageBox.information(self, "تقرير المشتركين النشطين", "لا يوجد مشتركون نشطون")
                return

            # تصنيف المشتركين
            active_count = sum(1 for s in active_subscribers if s['status'] == 'نشط')
            expiring_soon = sum(1 for s in active_subscribers if s['status'] == 'ينتهي قريباً')
            expired_count = sum(1 for s in active_subscribers if s['status'] == 'منتهي')

            report_text = f"""تقرير المشتركين النشطين
{'='*50}

الإحصائيات:
  المشتركون النشطون: {active_count}
  ينتهي قريباً (خلال 7 أيام): {expiring_soon}
  منتهي الصلاحية: {expired_count}

تفاصيل المشتركين:
{'-'*50}
"""

            for subscriber in active_subscribers:
                status_icon = "✅" if subscriber['status'] == 'نشط' else "⚠️" if subscriber['status'] == 'ينتهي قريباً' else "❌"
                report_text += f"{status_icon} {subscriber['name']} | {subscriber['phone']} | {subscriber['package_name']} | ينتهي: {subscriber['subscription_end_date']}\n"

            self.show_report_dialog("تقرير المشتركين النشطين", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير المشتركين النشطين: {e}")

    def generate_expired_subscriptions_report(self):
        """تقرير الاشتراكات المنتهية"""
        try:
            # جلب الاشتراكات المنتهية
            expired_subscriptions = self.db_manager.fetch_all("""
                SELECT name, phone, package_name, subscription_end_date,
                       julianday('now') - julianday(subscription_end_date) as days_expired
                FROM subscribers
                WHERE subscription_end_date < date('now')
                ORDER BY subscription_end_date DESC
                LIMIT 50
            """)

            if not expired_subscriptions:
                QMessageBox.information(self, "تقرير الاشتراكات المنتهية", "لا توجد اشتراكات منتهية")
                return

            report_text = f"""تقرير الاشتراكات المنتهية
{'='*50}

إجمالي الاشتراكات المنتهية: {len(expired_subscriptions)}

المشتركون المنتهيون:
{'-'*50}
"""

            for subscription in expired_subscriptions:
                days_expired = int(subscription['days_expired'])
                urgency = "🔴 عاجل" if days_expired > 30 else "🟡 متوسط" if days_expired > 7 else "🟢 حديث"
                report_text += f"{urgency} {subscription['name']} | {subscription['phone']} | {subscription['package_name']} | انتهى منذ {days_expired} يوم\n"

            self.show_report_dialog("تقرير الاشتراكات المنتهية", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الاشتراكات المنتهية: {e}")

    def generate_packages_report(self):
        """تقرير الباقات"""
        try:
            # جلب إحصائيات الباقات
            packages_stats = self.db_manager.fetch_all("""
                SELECT
                    p.name as package_name,
                    p.price,
                    COUNT(s.id) as subscriber_count,
                    SUM(p.price) as total_revenue
                FROM packages p
                LEFT JOIN subscribers s ON p.name = s.package_name
                GROUP BY p.id, p.name, p.price
                ORDER BY subscriber_count DESC
            """)

            if not packages_stats:
                QMessageBox.information(self, "تقرير الباقات", "لا توجد باقات مسجلة")
                return

            total_subscribers = sum(p['subscriber_count'] for p in packages_stats)
            total_revenue = sum(p['total_revenue'] or 0 for p in packages_stats)

            report_text = f"""تقرير الباقات
{'='*50}

الإحصائيات العامة:
  إجمالي المشتركين: {total_subscribers}
  إجمالي الإيرادات المتوقعة: {format_currency(total_revenue)}

تفاصيل الباقات:
{'-'*50}
"""

            for package in packages_stats:
                percentage = (package['subscriber_count'] / total_subscribers * 100) if total_subscribers > 0 else 0
                revenue = package['total_revenue'] or 0
                report_text += f"""
📦 {package['package_name']}
   السعر: {format_currency(package['price'])}
   عدد المشتركين: {package['subscriber_count']} ({percentage:.1f}%)
   الإيرادات: {format_currency(revenue)}
{'-'*30}"""

            self.show_report_dialog("تقرير الباقات", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الباقات: {e}")
        
    # دوال تقارير المخزون
    def generate_current_stock_report(self):
        """تقرير المخزون الحالي"""
        try:
            # محاولة جلب المخزون من جدول inventory أولاً
            inventory = self.db_manager.fetch_all("""
                SELECT
                    p.name as product_name,
                    p.category,
                    COALESCE(i.quantity, p.quantity, 0) as quantity,
                    p.cost_price,
                    (COALESCE(i.quantity, p.quantity, 0) * p.cost_price) as total_value
                FROM products p
                LEFT JOIN inventory i ON i.product_id = p.id
                WHERE p.is_active = 1 AND COALESCE(i.quantity, p.quantity, 0) > 0
                ORDER BY total_value DESC
            """)

            if not inventory:
                QMessageBox.information(self, "تقرير المخزون الحالي", "لا يوجد مخزون مسجل")
                return

            total_items = sum(item['quantity'] for item in inventory)
            total_value = sum(item['total_value'] for item in inventory)

            report_text = f"""تقرير المخزون الحالي
{'='*50}

الإحصائيات العامة:
  إجمالي الأصناف: {len(inventory)}
  إجمالي الكمية: {total_items}
  إجمالي القيمة: {format_currency(total_value)}

تفاصيل المخزون:
{'-'*50}
"""

            for item in inventory:
                status = "⚠️ قليل" if item['quantity'] < 10 else "✅ متوفر"
                report_text += f"{status} {item['product_name']} | الفئة: {item['category']} | الكمية: {item['quantity']} | القيمة: {format_currency(item['total_value'])}\n"

            self.show_report_dialog("تقرير المخزون الحالي", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير المخزون الحالي: {e}")

    def generate_low_stock_report(self):
        """تقرير المنتجات قليلة المخزون"""
        try:
            # جلب المنتجات قليلة المخزون
            low_stock = self.db_manager.fetch_all("""
                SELECT
                    p.name as product_name,
                    p.category,
                    COALESCE(i.quantity, p.quantity, 0) as quantity,
                    p.cost_price
                FROM products p
                LEFT JOIN inventory i ON i.product_id = p.id
                WHERE p.is_active = 1 AND COALESCE(i.quantity, p.quantity, 0) < 10
                ORDER BY COALESCE(i.quantity, p.quantity, 0) ASC
            """)

            if not low_stock:
                QMessageBox.information(self, "تقرير المنتجات قليلة المخزون", "جميع المنتجات متوفرة بكميات كافية")
                return

            report_text = f"""تقرير المنتجات قليلة المخزون
{'='*50}

⚠️ تحذير: {len(low_stock)} منتج يحتاج إعادة تموين

المنتجات التي تحتاج تموين:
{'-'*50}
"""

            for item in low_stock:
                urgency = "🔴 عاجل" if item['quantity'] < 3 else "🟡 متوسط" if item['quantity'] < 7 else "🟢 قريباً"
                report_text += f"{urgency} {item['product_name']} | الفئة: {item['category']} | الكمية المتبقية: {item['quantity']}\n"

            self.show_report_dialog("تقرير المنتجات قليلة المخزون", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير المنتجات قليلة المخزون: {e}")

    def generate_stock_movement_report(self):
        """تقرير حركة المخزون"""
        try:
            from datetime import datetime, timedelta
            last_30_days = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            # جلب حركة المخزون (من المعاملات)
            movements = self.db_manager.fetch_all("""
                SELECT
                    type,
                    description,
                    amount,
                    created_at
                FROM transactions
                WHERE (description LIKE '%مخزون%' OR description LIKE '%منتج%' OR description LIKE '%راوتر%')
                AND created_at >= ?
                ORDER BY created_at DESC
            """, (last_30_days,))

            if not movements:
                QMessageBox.information(self, "تقرير حركة المخزون", "لا توجد حركة مخزون في آخر 30 يوم")
                return

            report_text = f"""تقرير حركة المخزون - آخر 30 يوم
{'='*60}

إجمالي الحركات: {len(movements)}

تفاصيل الحركة:
{'-'*60}
"""

            for movement in movements:
                movement_type = "📦 إدخال" if movement['amount'] < 0 else "📤 إخراج"
                report_text += f"{movement['created_at'][:10]} | {movement_type} | {movement['description']} | {format_currency(abs(movement['amount']))}\n"

            self.show_report_dialog("تقرير حركة المخزون", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير حركة المخزون: {e}")

    def generate_worker_inventory_report(self):
        """تقرير مخزون العمال"""
        try:
            print("=== تشخيص تقرير مخزون العمال ===")

            # التحقق من وجود جدول worker_inventory
            table_check = self.db_manager.fetch_one("""
                SELECT name FROM sqlite_master WHERE type='table' AND name='worker_inventory'
            """)

            if not table_check:
                print("جدول worker_inventory غير موجود!")
                QMessageBox.warning(self, "خطأ", "جدول مخزون العمال غير موجود في قاعدة البيانات")
                return

            print("جدول worker_inventory موجود")

            # جلب جميع البيانات من جدول worker_inventory
            all_worker_inventory = self.db_manager.fetch_all("""
                SELECT * FROM worker_inventory
            """)

            print(f"إجمالي السجلات في worker_inventory: {len(all_worker_inventory)}")
            for record in all_worker_inventory:
                print(f"  - العامل {record['worker_id']}, المنتج {record['product_id']}, الكمية {record['quantity']}")

            # جلب مخزون العمال مع التفاصيل
            worker_inventory = self.db_manager.fetch_all("""
                SELECT
                    w.name as worker_name,
                    p.name as product_name,
                    wi.quantity,
                    p.cost_price,
                    (wi.quantity * p.cost_price) as total_value
                FROM worker_inventory wi
                JOIN workers w ON wi.worker_id = w.id
                JOIN products p ON wi.product_id = p.id
                WHERE wi.quantity > 0
                ORDER BY w.name, total_value DESC
            """)

            print(f"مخزون العمال مع التفاصيل: {len(worker_inventory)} سجل")

            if not worker_inventory:
                print("لا يوجد مخزون لدى العمال")
                QMessageBox.information(self, "تقرير مخزون العمال", "لا يوجد مخزون لدى العمال")
                return

            # تجميع البيانات حسب العامل
            workers_data = {}
            for item in worker_inventory:
                worker_name = item['worker_name']
                if worker_name not in workers_data:
                    workers_data[worker_name] = {'items': [], 'total_value': 0}
                workers_data[worker_name]['items'].append(item)
                workers_data[worker_name]['total_value'] += item['total_value']

            report_text = f"""تقرير مخزون العمال
{'='*50}

إجمالي العمال الذين لديهم مخزون: {len(workers_data)}

تفاصيل مخزون كل عامل:
{'-'*50}
"""

            for worker_name, data in workers_data.items():
                report_text += f"\n👷 {worker_name} - إجمالي القيمة: {format_currency(data['total_value'])}\n"
                for item in data['items']:
                    report_text += f"   📦 {item['product_name']} | الكمية: {item['quantity']} | القيمة: {format_currency(item['total_value'])}\n"
                report_text += "-" * 30 + "\n"

            self.show_report_dialog("تقرير مخزون العمال", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير مخزون العمال: {e}")
        
    # دوال تقارير العمال
    def generate_workers_performance_report(self):
        """تقرير أداء العمال"""
        try:
            from datetime import datetime, timedelta
            last_30_days = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            # جلب أداء العمال
            workers_performance = self.db_manager.fetch_all("""
                SELECT
                    w.name as worker_name,
                    w.worker_type,
                    COUNT(wd.id) as deliveries_count,
                    SUM(wd.commission) as total_commission
                FROM workers w
                LEFT JOIN worker_deliveries wd ON w.id = wd.worker_id
                    AND wd.delivery_date >= ?
                GROUP BY w.id, w.name, w.worker_type
                ORDER BY deliveries_count DESC
            """, (last_30_days,))

            if not workers_performance:
                QMessageBox.information(self, "تقرير أداء العمال", "لا يوجد عمال مسجلون")
                return

            report_text = f"""تقرير أداء العمال - آخر 30 يوم
{'='*50}

إجمالي العمال: {len(workers_performance)}

أداء العمال:
{'-'*50}
"""

            for worker in workers_performance:
                performance_level = "⭐⭐⭐ ممتاز" if worker['deliveries_count'] > 20 else "⭐⭐ جيد" if worker['deliveries_count'] > 10 else "⭐ مقبول"
                commission = worker['total_commission'] or 0
                report_text += f"{performance_level} {worker['worker_name']} ({worker['worker_type']}) | التسليمات: {worker['deliveries_count']} | العمولة: {format_currency(commission)}\n"

            self.show_report_dialog("تقرير أداء العمال", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير أداء العمال: {e}")

    def generate_commissions_report(self):
        """تقرير العمولات"""
        try:
            from datetime import datetime
            current_month = datetime.now().strftime('%Y-%m')

            # جلب العمولات
            commissions = self.db_manager.fetch_all("""
                SELECT
                    w.name as worker_name,
                    SUM(wd.commission) as total_commission,
                    COUNT(wd.id) as deliveries_count,
                    AVG(wd.commission) as avg_commission
                FROM workers w
                JOIN worker_deliveries wd ON w.id = wd.worker_id
                WHERE strftime('%Y-%m', wd.delivery_date) = ?
                GROUP BY w.id, w.name
                ORDER BY total_commission DESC
            """, (current_month,))

            if not commissions:
                QMessageBox.information(self, "تقرير العمولات", "لا توجد عمولات لهذا الشهر")
                return

            total_commissions = sum(c['total_commission'] for c in commissions)

            report_text = f"""تقرير العمولات - {current_month}
{'='*50}

إجمالي العمولات المدفوعة: {format_currency(total_commissions)}
عدد العمال المستحقين: {len(commissions)}

تفاصيل العمولات:
{'-'*50}
"""

            for commission in commissions:
                report_text += f"💰 {commission['worker_name']} | العمولة: {format_currency(commission['total_commission'])} | التسليمات: {commission['deliveries_count']} | المتوسط: {format_currency(commission['avg_commission'])}\n"

            self.show_report_dialog("تقرير العمولات", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير العمولات: {e}")

    def generate_deliveries_report(self):
        """تقرير التسليمات"""
        try:
            from datetime import datetime, timedelta
            last_7_days = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            # جلب التسليمات
            deliveries = self.db_manager.fetch_all("""
                SELECT
                    wd.delivery_date,
                    w.name as worker_name,
                    wd.subscriber_name,
                    wd.product_delivered,
                    wd.commission,
                    wd.notes
                FROM worker_deliveries wd
                JOIN workers w ON wd.worker_id = w.id
                WHERE wd.delivery_date >= ?
                ORDER BY wd.delivery_date DESC
            """, (last_7_days,))

            if not deliveries:
                QMessageBox.information(self, "تقرير التسليمات", "لا توجد تسليمات في آخر 7 أيام")
                return

            report_text = f"""تقرير التسليمات - آخر 7 أيام
{'='*60}

إجمالي التسليمات: {len(deliveries)}

تفاصيل التسليمات:
{'-'*60}
"""

            for delivery in deliveries:
                report_text += f"""
📅 {delivery['delivery_date']}
👷 العامل: {delivery['worker_name']}
👤 المشترك: {delivery['subscriber_name']}
📦 المنتج: {delivery['product_delivered']}
💰 العمولة: {format_currency(delivery['commission'])}
📝 ملاحظات: {delivery['notes'] or 'لا توجد'}
{'-'*30}"""

            self.show_report_dialog("تقرير التسليمات", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير التسليمات: {e}")

    def generate_areas_report(self):
        """تقرير المناطق"""
        try:
            # جلب إحصائيات المناطق
            areas_stats = self.db_manager.fetch_all("""
                SELECT
                    address as area,
                    COUNT(*) as subscribers_count,
                    COUNT(CASE WHEN subscription_end_date > date('now') THEN 1 END) as active_count
                FROM subscribers
                GROUP BY address
                ORDER BY subscribers_count DESC
            """)

            if not areas_stats:
                QMessageBox.information(self, "تقرير المناطق", "لا توجد بيانات مناطق")
                return

            total_subscribers = sum(area['subscribers_count'] for area in areas_stats)

            report_text = f"""تقرير المناطق
{'='*50}

إجمالي المناطق: {len(areas_stats)}
إجمالي المشتركين: {total_subscribers}

إحصائيات المناطق:
{'-'*50}
"""

            for area in areas_stats:
                percentage = (area['subscribers_count'] / total_subscribers * 100) if total_subscribers > 0 else 0
                activity_rate = (area['active_count'] / area['subscribers_count'] * 100) if area['subscribers_count'] > 0 else 0
                report_text += f"📍 {area['area']} | المشتركون: {area['subscribers_count']} ({percentage:.1f}%) | النشطون: {area['active_count']} ({activity_rate:.1f}%)\n"

            self.show_report_dialog("تقرير المناطق", report_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير المناطق: {e}")

    def show_report_dialog(self, title, content):
        """عرض التقرير في نافذة منفصلة"""
        dialog = QDialog(self)
        dialog.setWindowTitle(title)
        dialog.setGeometry(150, 150, 800, 600)

        layout = QVBoxLayout()

        # منطقة النص
        text_area = QTextEdit()
        apply_arabic_style(text_area, 9)
        text_area.setPlainText(content)
        text_area.setReadOnly(True)
        text_area.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)

        # أزرار
        buttons_layout = QHBoxLayout()

        print_button = QPushButton("طباعة")
        apply_arabic_style(print_button, 10)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(print_button)
        buttons_layout.addWidget(close_button)

        layout.addWidget(text_area)
        layout.addLayout(buttons_layout)

        dialog.setLayout(layout)

        # ربط الأحداث
        print_button.clicked.connect(lambda: self.print_report_content(title, content))
        close_button.clicked.connect(dialog.accept)

        dialog.exec_()

    def print_report(self):
        """طباعة التقرير"""
        try:
            # الحصول على التقرير المحدد
            current_report = self.reports_combo.currentData()
            if not current_report:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تقرير للطباعة")
                return

            # إنشاء التقرير
            report_data = self.generate_report_data(current_report)
            if not report_data:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة")
                return

            # طباعة التقرير
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            report_title = self.reports_combo.currentText()
            success = print_manager.print_report(report_data, report_title)

            if success:
                QMessageBox.information(self, "تم", "تم إرسال التقرير للطباعة بنجاح")
            else:
                QMessageBox.warning(self, "تحذير", "تم إلغاء عملية الطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة التقرير: {e}")

    def print_report_content(self, title, content):
        """طباعة محتوى التقرير"""
        try:
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_document(content, title, show_preview=True)

            if success:
                QMessageBox.information(self, "تم", "تم إرسال التقرير للطباعة بنجاح")
            else:
                QMessageBox.warning(self, "تحذير", "تم إلغاء عملية الطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة التقرير: {e}")

    def generate_report_data(self, report_type):
        """إنشاء بيانات التقرير للطباعة"""
        try:
            if report_type == "daily_revenue":
                return self.get_daily_revenue_data()
            elif report_type == "monthly_summary":
                return self.get_monthly_summary_data()
            elif report_type == "cash_flow":
                return self.get_cash_flow_data()
            elif report_type == "profit_loss":
                return self.get_profit_loss_data()
            elif report_type == "new_subscribers":
                return self.get_new_subscribers_data()
            elif report_type == "active_subscribers":
                return self.get_active_subscribers_data()
            elif report_type == "expired_subscriptions":
                return self.get_expired_subscriptions_data()
            elif report_type == "packages_report":
                return self.get_packages_report_data()
            elif report_type == "current_inventory":
                return self.get_current_inventory_data()
            elif report_type == "low_stock":
                return self.get_low_stock_data()
            elif report_type == "inventory_movement":
                return self.get_inventory_movement_data()
            elif report_type == "workers_inventory":
                return self.get_workers_inventory_data()
            elif report_type == "workers_performance":
                return self.get_workers_performance_data()
            elif report_type == "workers_commissions":
                return self.get_workers_commissions_data()
            elif report_type == "workers_deliveries":
                return self.get_workers_deliveries_data()
            elif report_type == "areas_report":
                return self.get_areas_report_data()
            else:
                return None

        except Exception as e:
            print(f"خطأ في إنشاء بيانات التقرير: {e}")
            return None

    def get_daily_revenue_data(self):
        """الحصول على بيانات الإيرادات اليومية"""
        try:
            revenue_data = self.db_manager.fetch_all("""
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as transactions_count,
                    SUM(amount) as total_amount,
                    type
                FROM transactions
                WHERE DATE(created_at) = DATE('now')
                GROUP BY DATE(created_at), type
                ORDER BY date DESC
            """)

            return revenue_data if revenue_data else []
        except:
            return []

    def get_monthly_summary_data(self):
        """الحصول على بيانات الملخص الشهري"""
        try:
            summary_data = self.db_manager.fetch_all("""
                SELECT
                    strftime('%Y-%m', created_at) as month,
                    COUNT(*) as transactions_count,
                    SUM(amount) as total_amount,
                    type
                FROM transactions
                WHERE created_at >= date('now', 'start of month')
                GROUP BY strftime('%Y-%m', created_at), type
                ORDER BY month DESC
            """)

            return summary_data if summary_data else []
        except:
            return []

    def get_new_subscribers_data(self):
        """الحصول على بيانات المشتركين الجدد"""
        try:
            subscribers_data = self.db_manager.fetch_all("""
                SELECT name, phone, package_name, created_at
                FROM subscribers
                WHERE DATE(created_at) >= DATE('now', '-30 days')
                ORDER BY created_at DESC
            """)

            return subscribers_data if subscribers_data else []
        except:
            return []

    def get_active_subscribers_data(self):
        """الحصول على بيانات المشتركين النشطين"""
        try:
            active_data = self.db_manager.fetch_all("""
                SELECT name, phone, package_name, subscription_end_date
                FROM subscribers
                WHERE subscription_end_date > DATE('now')
                ORDER BY subscription_end_date
            """)

            return active_data if active_data else []
        except:
            return []

    def get_current_inventory_data(self):
        """الحصول على بيانات المخزون الحالي"""
        try:
            inventory_data = self.db_manager.fetch_all("""
                SELECT p.name, p.category, i.quantity, p.unit_type, p.unit_price
                FROM products p
                LEFT JOIN inventory i ON p.id = i.product_id
                WHERE p.is_active = 1
                ORDER BY p.name
            """)

            return inventory_data if inventory_data else []
        except:
            return []

    def get_workers_performance_data(self):
        """الحصول على بيانات أداء العمال"""
        try:
            performance_data = self.db_manager.fetch_all("""
                SELECT w.name, w.area, COUNT(rd.id) as deliveries_count,
                       SUM(rd.commission) as total_commission
                FROM workers w
                LEFT JOIN router_deliveries rd ON w.id = rd.worker_id
                WHERE w.is_active = 1
                GROUP BY w.id, w.name, w.area
                ORDER BY deliveries_count DESC
            """)

            return performance_data if performance_data else []
        except:
            return []

    # دوال أخرى للتقارير المتبقية
    def get_cash_flow_data(self):
        return []
    def get_profit_loss_data(self):
        return []
    def get_expired_subscriptions_data(self):
        return []
    def get_packages_report_data(self):
        return []
    def get_low_stock_data(self):
        return []
    def get_inventory_movement_data(self):
        return []
    def get_workers_inventory_data(self):
        return []
    def get_workers_commissions_data(self):
        return []
    def get_workers_deliveries_data(self):
        return []
    def get_areas_report_data(self):
        return []

    def export_report(self):
        """تصدير التقرير"""
        try:
            QMessageBox.information(self, "قريباً", "ميزة تصدير التقرير قيد التطوير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير التقرير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            QMessageBox.information(self, "قريباً", "ميزة طباعة التقرير قيد التطوير")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة التقرير: {e}")

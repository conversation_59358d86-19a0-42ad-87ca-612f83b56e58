#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل أساسي للنظام
"""

import sys
import os
from pathlib import Path

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل النظام...")
    
    # إعداد المسارات
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    
    sys.path.insert(0, str(project_root))
    sys.path.insert(0, str(src_path))
    
    try:
        print("📦 استيراد المكتبات...")
        
        # استيراد PyQt5 الأساسي
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import QCoreApplication
        print("✅ PyQt5")
        
        # إنشاء التطبيق
        QCoreApplication.setAttribute(5, True)  # Qt::AA_EnableHighDpiScaling
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        
        print("✅ تطبيق Qt")
        
        # استيراد وحدات النظام
        from src.database.database_manager import DatabaseManager
        print("✅ مدير قاعدة البيانات")
        
        # إنشاء قاعدة البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        print("✅ قاعدة البيانات")
        
        # اختبار قاعدة البيانات
        users = db_manager.fetch_all("SELECT COUNT(*) as count FROM users")
        if users:
            print(f"✅ قاعدة البيانات تحتوي على {users[0]['count']} مستخدم")
        
        # استيراد نافذة تسجيل الدخول
        from src.ui.login_window import LoginWindow
        print("✅ نافذة تسجيل الدخول")
        
        # عرض نافذة تسجيل الدخول
        print("\n🔐 فتح نافذة تسجيل الدخول...")
        print("📋 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123")
        print("=" * 40)
        
        login_window = LoginWindow(db_manager)
        login_window.show()
        
        # تشغيل التطبيق
        print("🎯 تشغيل حلقة الأحداث...")
        result = app.exec_()
        
        print(f"📊 انتهى التطبيق بالكود: {result}")
        return result
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج")
        sys.exit(0)

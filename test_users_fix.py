#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة إدارة المستخدمين
"""

import sys
import os
import hashlib
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.database.db_manager import DatabaseManager
except ImportError:
    # محاولة استيراد مباشر
    import sqlite3

    class DatabaseManager:
        def __init__(self):
            self.db_path = "data/company_system.db"

        def fetch_one(self, query, params=None):
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            result = cursor.fetchone()
            conn.close()
            return dict(result) if result else None

        def fetch_all(self, query, params=None):
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            results = cursor.fetchall()
            conn.close()
            return [dict(row) for row in results]

        def execute_query(self, query, params=None):
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            result = cursor.lastrowid
            conn.close()
            return result

def test_users_management():
    """اختبار إدارة المستخدمين"""
    print("=== اختبار إدارة المستخدمين ===")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    
    # 1. التحقق من وجود جدول users
    print("\n1. التحقق من وجود جدول users:")
    table_check = db_manager.fetch_one("""
        SELECT name FROM sqlite_master WHERE type='table' AND name='users'
    """)
    
    if table_check:
        print("✅ جدول users موجود")
    else:
        print("❌ جدول users غير موجود!")
        return
    
    # 2. عرض هيكل الجدول
    print("\n2. هيكل جدول users:")
    columns = db_manager.fetch_all("PRAGMA table_info(users)")
    for col in columns:
        print(f"   - {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
    
    # 3. عرض جميع المستخدمين الحاليين
    print("\n3. المستخدمين الحاليين:")
    users = db_manager.fetch_all("SELECT * FROM users ORDER BY created_at DESC")
    print(f"إجمالي المستخدمين: {len(users)}")
    
    for user in users:
        print(f"   - ID: {user['id']} | {user['username']} | {user['full_name']} | {user['role']} | نشط: {user['is_active']}")
    
    # 4. اختبار إضافة مستخدم جديد
    print("\n4. اختبار إضافة مستخدم جديد:")
    test_username = f"test_user_{datetime.now().strftime('%H%M%S')}"
    test_password = "123456"
    test_full_name = f"مستخدم تجريبي {datetime.now().strftime('%H:%M:%S')}"
    
    # تشفير كلمة المرور
    password_hash = hashlib.sha256(test_password.encode()).hexdigest()
    
    try:
        # إضافة المستخدم
        result = db_manager.execute_query("""
            INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (test_username, password_hash, test_full_name, "<EMAIL>", "employee", 1))
        
        print(f"نتيجة الإضافة: {result}")
        
        # التحقق من الإضافة
        new_user = db_manager.fetch_one(
            "SELECT * FROM users WHERE username = ?",
            (test_username,)
        )
        
        if new_user:
            print(f"✅ تم إضافة المستخدم بنجاح: ID={new_user['id']}")
            print(f"   اسم المستخدم: {new_user['username']}")
            print(f"   الاسم الكامل: {new_user['full_name']}")
            print(f"   الدور: {new_user['role']}")
            print(f"   نشط: {new_user['is_active']}")
            
            # 5. اختبار تسجيل الدخول
            print("\n5. اختبار تسجيل الدخول:")
            login_user = db_manager.fetch_one(
                "SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
                (test_username, password_hash)
            )
            
            if login_user:
                print("✅ تسجيل الدخول نجح!")
            else:
                print("❌ فشل تسجيل الدخول!")
                
        else:
            print("❌ لم يتم العثور على المستخدم بعد الإضافة!")
            
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدم: {e}")
    
    # 6. عرض المستخدمين بعد الإضافة
    print("\n6. المستخدمين بعد الإضافة:")
    users_after = db_manager.fetch_all("SELECT * FROM users ORDER BY created_at DESC")
    print(f"إجمالي المستخدمين: {len(users_after)}")
    
    for user in users_after:
        print(f"   - ID: {user['id']} | {user['username']} | {user['full_name']} | {user['role']} | نشط: {user['is_active']}")
    
    print("\n=== انتهاء الاختبار ===")

if __name__ == "__main__":
    test_users_management()

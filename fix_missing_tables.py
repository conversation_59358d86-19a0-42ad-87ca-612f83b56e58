#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الجداول المفقودة التي تسبب إغلاق التطبيق
"""

import sys
import os
sys.path.append('src')

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    
    print("🔧 إصلاح الجداول المفقودة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        print("✅ اتصال قاعدة البيانات")
        
        # 1. إنشاء جدول shifts
        print("1️⃣ إنشاء جدول shifts...")
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS shifts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                user_name TEXT NOT NULL,
                shift_date DATE NOT NULL,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                status TEXT DEFAULT 'open',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✅ تم إنشاء جدول shifts")
        
        # 2. إنشاء جدول subscribers
        print("2️⃣ إنشاء جدول subscribers...")
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS subscribers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                package_id INTEGER,
                subscription_date DATE,
                delivered INTEGER DEFAULT 0,
                router_delivered INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (package_id) REFERENCES packages (id)
            )
        """)
        print("✅ تم إنشاء جدول subscribers")
        
        # 3. إنشاء جدول packages
        print("3️⃣ إنشاء جدول packages...")
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS packages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                speed TEXT,
                description TEXT,
                active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول packages")
        
        # 4. إنشاء جدول router_deliveries
        print("4️⃣ إنشاء جدول router_deliveries...")
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS router_deliveries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subscriber_id INTEGER,
                subscriber_name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                router_type TEXT,
                router_id INTEGER,
                router_paid INTEGER DEFAULT 0,
                package_type TEXT,
                package_id INTEGER,
                package_paid INTEGER DEFAULT 0,
                cable_type TEXT,
                cable_meters REAL DEFAULT 0,
                cable_price_per_meter REAL DEFAULT 0,
                cable_cost REAL DEFAULT 0,
                cable_paid INTEGER DEFAULT 0,
                installation_worker TEXT,
                worker_id INTEGER,
                subscription_fee REAL DEFAULT 0,
                subscription_paid INTEGER DEFAULT 0,
                router_price REAL DEFAULT 0,
                package_cost REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                delivery_date DATE DEFAULT CURRENT_DATE,
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                shift_id INTEGER,
                FOREIGN KEY (subscriber_id) REFERENCES subscribers (id),
                FOREIGN KEY (router_id) REFERENCES products (id),
                FOREIGN KEY (package_id) REFERENCES packages (id),
                FOREIGN KEY (worker_id) REFERENCES workers (id),
                FOREIGN KEY (shift_id) REFERENCES shifts (id)
            )
        """)
        print("✅ تم إنشاء جدول router_deliveries")
        
        # 5. إنشاء جدول workers
        print("5️⃣ إنشاء جدول workers...")
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                role TEXT DEFAULT 'technician',
                salary REAL DEFAULT 0,
                active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول workers")
        
        print("\n🎉 تم إنشاء جميع الجداول المفقودة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        import traceback
        traceback.print_exc()
        return False

def insert_sample_data():
    """إدراج بيانات تجريبية"""
    
    print("\n📊 إدراج بيانات تجريبية...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # 1. إدراج باقات تجريبية
        print("1️⃣ إدراج باقات تجريبية...")
        packages = [
            ('باقة أساسية', 30000, '10 ميجا', 'باقة إنترنت أساسية'),
            ('باقة متوسطة', 50000, '20 ميجا', 'باقة إنترنت متوسطة'),
            ('باقة متقدمة', 80000, '50 ميجا', 'باقة إنترنت متقدمة')
        ]
        
        for name, price, speed, desc in packages:
            db.execute_query("""
                INSERT OR IGNORE INTO packages (name, price, speed, description)
                VALUES (?, ?, ?, ?)
            """, (name, price, speed, desc))
        
        print("✅ تم إدراج الباقات")
        
        # 2. إدراج مشتركين تجريبيين
        print("2️⃣ إدراج مشتركين تجريبيين...")
        subscribers = [
            ('أحمد محمد', '0123456789', 'شارع الجامعة', 1),
            ('فاطمة علي', '0987654321', 'شارع النصر', 2),
            ('محمد حسن', '0555666777', 'شارع الثورة', 1)
        ]
        
        for name, phone, address, package_id in subscribers:
            db.execute_query("""
                INSERT OR IGNORE INTO subscribers (name, phone, address, package_id, delivered)
                VALUES (?, ?, ?, ?, 0)
            """, (name, phone, address, package_id))
        
        print("✅ تم إدراج المشتركين")
        
        # 3. إدراج عمال تجريبيين
        print("3️⃣ إدراج عمال تجريبيين...")
        workers = [
            ('عامل التركيب 1', '0111222333', 'technician', 25000),
            ('عامل التركيب 2', '0444555666', 'technician', 25000),
            ('مشرف التركيب', '0777888999', 'supervisor', 35000)
        ]
        
        for name, phone, role, salary in workers:
            db.execute_query("""
                INSERT OR IGNORE INTO workers (name, phone, role, salary)
                VALUES (?, ?, ?, ?)
            """, (name, phone, role, salary))
        
        print("✅ تم إدراج العمال")
        
        # 4. إدراج شيفت افتراضي
        print("4️⃣ إدراج شيفت افتراضي...")
        from datetime import datetime
        
        db.execute_query("""
            INSERT OR IGNORE INTO shifts (user_id, user_name, shift_date, start_time, status)
            VALUES (1, 'admin', ?, ?, 'open')
        """, (datetime.now().strftime('%Y-%m-%d'), datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        print("✅ تم إدراج الشيفت")
        
        print("\n🎉 تم إدراج جميع البيانات التجريبية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_tables():
    """التحقق من الجداول"""
    
    print("\n🔍 التحقق من الجداول...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        tables_to_check = [
            'shifts',
            'subscribers', 
            'packages',
            'router_deliveries',
            'workers'
        ]
        
        for table in tables_to_check:
            try:
                result = db.fetch_all(f"SELECT COUNT(*) as count FROM {table}")
                count = result[0]['count'] if result else 0
                print(f"✅ {table}: {count} سجل")
            except Exception as e:
                print(f"❌ {table}: خطأ - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح الجداول المفقودة التي تسبب إغلاق التطبيق")
    print("=" * 70)
    
    # إنشاء الجداول
    tables_created = create_missing_tables()
    
    # إدراج البيانات التجريبية
    data_inserted = insert_sample_data()
    
    # التحقق من الجداول
    tables_verified = verify_tables()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الإصلاح:")
    print(f"  • إنشاء الجداول: {'✅ نجح' if tables_created else '❌ فشل'}")
    print(f"  • إدراج البيانات: {'✅ نجح' if data_inserted else '❌ فشل'}")
    print(f"  • التحقق من الجداول: {'✅ نجح' if tables_verified else '❌ فشل'}")
    
    if all([tables_created, data_inserted, tables_verified]):
        print("\n🎉 تم إصلاح جميع المشاكل!")
        print("\n📋 الآن يمكنك:")
        print("  1. تشغيل التطبيق: python system_launcher.py")
        print("  2. تسجيل الدخول: admin / 123")
        print("  3. فتح 'تسليم راوتر'")
        print("  4. الضغط على 'حفظ وتسليم' - لن يغلق التطبيق!")
        
        print("\n✅ البيانات المتوفرة الآن:")
        print("  • 3 باقات إنترنت")
        print("  • 3 مشتركين غير مسلمين")
        print("  • 3 عمال تركيب")
        print("  • شيفت مفتوح")
        print("  • جداول كاملة")
    else:
        print("\n❌ هناك مشاكل تحتاج مراجعة!")

if __name__ == "__main__":
    main()

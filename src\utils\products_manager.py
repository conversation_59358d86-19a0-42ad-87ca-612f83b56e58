# -*- coding: utf-8 -*-
"""
مدير المنتجات الموحد
Unified Products Manager
"""

class ProductsManager:
    """مدير المنتجات الموحد لجميع أجزاء التطبيق"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def get_all_products(self, include_zero_stock=False):
        """جلب جميع المنتجات"""
        try:
            if include_zero_stock:
                query = """
                    SELECT id, name, category, unit_price, purchase_unit, sale_unit, 
                           stock_quantity, min_stock_level, created_at
                    FROM products 
                    ORDER BY category, name
                """
            else:
                query = """
                    SELECT id, name, category, unit_price, purchase_unit, sale_unit, 
                           stock_quantity, min_stock_level, created_at
                    FROM products 
                    WHERE stock_quantity > 0
                    ORDER BY category, name
                """
            
            return self.db_manager.fetch_all(query)
        except Exception as e:
            print(f"خطأ في جلب المنتجات: {e}")
            return []
            
    def get_products_for_maintenance(self):
        """جلب المنتجات لأمر الصيانة"""
        return self.get_all_products(include_zero_stock=False)
        
    def get_products_for_worker_delivery(self):
        """جلب المنتجات لتسليم العمال"""
        return self.get_all_products(include_zero_stock=False)
        
    def get_products_for_purchase(self):
        """جلب المنتجات للمشتريات"""
        return self.get_all_products(include_zero_stock=True)
        
    def get_cables_only(self):
        """جلب الكبلات فقط"""
        try:
            query = """
                SELECT id, name, category, unit_price, purchase_unit, sale_unit, 
                       stock_quantity, min_stock_level
                FROM products 
                WHERE category LIKE '%كبل%' OR category LIKE '%cable%'
                ORDER BY name
            """
            return self.db_manager.fetch_all(query)
        except Exception as e:
            print(f"خطأ في جلب الكبلات: {e}")
            return []
            
    def convert_units(self, product_id, quantity, from_unit, to_unit):
        """تحويل الوحدات"""
        try:
            # جلب معلومات المنتج
            product = self.db_manager.fetch_one("""
                SELECT purchase_unit, sale_unit, conversion_rate FROM products WHERE id = ?
            """, (product_id,))
            
            if not product:
                return quantity
                
            # إذا كانت الوحدات متشابهة
            if from_unit == to_unit:
                return quantity
                
            # تحويل من وحدة الشراء إلى وحدة البيع
            if from_unit == product['purchase_unit'] and to_unit == product['sale_unit']:
                conversion_rate = product.get('conversion_rate', 1)
                return quantity * conversion_rate
                
            # تحويل من وحدة البيع إلى وحدة الشراء
            if from_unit == product['sale_unit'] and to_unit == product['purchase_unit']:
                conversion_rate = product.get('conversion_rate', 1)
                return quantity / conversion_rate if conversion_rate > 0 else quantity
                
            return quantity
            
        except Exception as e:
            print(f"خطأ في تحويل الوحدات: {e}")
            return quantity
            
    def update_stock(self, product_id, quantity_change, operation_type="manual"):
        """تحديث المخزون"""
        try:
            # جلب المخزون الحالي
            current_stock = self.db_manager.fetch_one("""
                SELECT stock_quantity FROM products WHERE id = ?
            """, (product_id,))
            
            if not current_stock:
                return False
                
            new_stock = current_stock['stock_quantity'] + quantity_change
            
            # تحديث المخزون
            self.db_manager.execute_query("""
                UPDATE products SET stock_quantity = ? WHERE id = ?
            """, (new_stock, product_id))
            
            # تسجيل حركة المخزون
            self.db_manager.execute_query("""
                INSERT INTO stock_movements (product_id, quantity_change, operation_type, new_stock)
                VALUES (?, ?, ?, ?)
            """, (product_id, quantity_change, operation_type, new_stock))
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث المخزون: {e}")
            return False
            
    def get_product_display_text(self, product, context="general"):
        """إنشاء نص عرض المنتج حسب السياق"""
        try:
            if context == "maintenance":
                # لأمر الصيانة - عرض المخزون بوحدة البيع
                return f"{product['name']} ({product['category']}) - متوفر: {product['stock_quantity']} {product['sale_unit']}"
                
            elif context == "worker_delivery":
                # لتسليم العمال - عرض المخزون بوحدة الشراء
                purchase_stock = self.convert_units(product['id'], product['stock_quantity'], 
                                                  product['sale_unit'], product['purchase_unit'])
                return f"{product['name']} - متوفر: {purchase_stock} {product['purchase_unit']}"
                
            elif context == "purchase":
                # للمشتريات - عرض بوحدة الشراء
                return f"{product['name']} ({product['category']}) - {product['purchase_unit']}"
                
            else:
                # عرض عام
                return f"{product['name']} ({product['category']})"
                
        except Exception as e:
            print(f"خطأ في إنشاء نص العرض: {e}")
            return product.get('name', 'منتج غير معروف')
            
    def get_weekly_cable_deliveries(self, start_date, end_date):
        """جلب تسليمات الكبلات الأسبوعية"""
        try:
            query = """
                SELECT 
                    p.name as cable_type,
                    w.name as worker_name,
                    SUM(wd.quantity) as total_delivered,
                    p.sale_unit as unit
                FROM worker_deliveries wd
                JOIN products p ON wd.product_id = p.id
                JOIN workers w ON wd.worker_id = w.id
                WHERE DATE(wd.delivery_date) BETWEEN ? AND ?
                AND (p.category LIKE '%كبل%' OR p.category LIKE '%cable%')
                GROUP BY p.id, w.id
                ORDER BY p.name, w.name
            """
            
            return self.db_manager.fetch_all(query, (start_date, end_date))
            
        except Exception as e:
            print(f"خطأ في جلب تسليمات الكبلات: {e}")
            return []
            
    def get_cable_summary_by_type(self, start_date, end_date):
        """ملخص الكبلات حسب النوع"""
        try:
            query = """
                SELECT 
                    p.name as cable_type,
                    SUM(wd.quantity) as total_delivered,
                    p.sale_unit as unit,
                    COUNT(DISTINCT w.id) as workers_count
                FROM worker_deliveries wd
                JOIN products p ON wd.product_id = p.id
                JOIN workers w ON wd.worker_id = w.id
                WHERE DATE(wd.delivery_date) BETWEEN ? AND ?
                AND (p.category LIKE '%كبل%' OR p.category LIKE '%cable%')
                GROUP BY p.id
                ORDER BY total_delivered DESC
            """
            
            return self.db_manager.fetch_all(query, (start_date, end_date))
            
        except Exception as e:
            print(f"خطأ في جلب ملخص الكبلات: {e}")
            return []

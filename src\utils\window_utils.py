#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات مساعدة للنوافذ
"""

def center_window(window):
    """
    توسيط النافذة في الشاشة
    
    Args:
        window: النافذة المراد توسيطها
    """
    try:
        from PyQt5.QtWidgets import QDesktopWidget
        
        # الحصول على معلومات الشاشة
        screen = QDesktopWidget().screenGeometry()
        window_geometry = window.geometry()
        
        # حساب الموضع المركزي
        x = (screen.width() - window_geometry.width()) // 2
        y = (screen.height() - window_geometry.height()) // 2
        
        # تحريك النافذة للمركز
        window.move(x, y)
        
        print(f"✅ تم توسيط النافذة في الموضع ({x}, {y})")
        
    except Exception as e:
        print(f"❌ خطأ في توسيط النافذة: {e}")
        # في حالة الخطأ، استخدم موضع افتراضي
        window.move(100, 100)

def center_window_on_parent(window, parent):
    """
    توسيط النافذة بالنسبة للنافذة الأب
    
    Args:
        window: النافذة المراد توسيطها
        parent: النافذة الأب
    """
    try:
        if parent is None:
            center_window(window)
            return
        
        # الحصول على معلومات النافذة الأب
        parent_geometry = parent.geometry()
        window_geometry = window.geometry()
        
        # حساب الموضع المركزي بالنسبة للأب
        x = parent_geometry.x() + (parent_geometry.width() - window_geometry.width()) // 2
        y = parent_geometry.y() + (parent_geometry.height() - window_geometry.height()) // 2
        
        # تحريك النافذة للمركز
        window.move(x, y)
        
        print(f"✅ تم توسيط النافذة بالنسبة للأب في الموضع ({x}, {y})")
        
    except Exception as e:
        print(f"❌ خطأ في توسيط النافذة بالنسبة للأب: {e}")
        # في حالة الخطأ، استخدم التوسيط العادي
        center_window(window)

def set_window_properties(window, title=None, size=None, center=True, resizable=True):
    """
    تعيين خصائص النافذة
    
    Args:
        window: النافذة
        title: عنوان النافذة
        size: حجم النافذة (width, height)
        center: هل يتم توسيط النافذة
        resizable: هل يمكن تغيير حجم النافذة
    """
    try:
        if title:
            window.setWindowTitle(title)
        
        if size:
            width, height = size
            if resizable:
                window.resize(width, height)
            else:
                window.setFixedSize(width, height)
        
        if center:
            center_window(window)
        
        print(f"✅ تم تعيين خصائص النافذة: العنوان='{title}', الحجم={size}, التوسيط={center}")
        
    except Exception as e:
        print(f"❌ خطأ في تعيين خصائص النافذة: {e}")

def apply_window_style(window, style_sheet=None):
    """
    تطبيق تنسيق على النافذة
    
    Args:
        window: النافذة
        style_sheet: تنسيق CSS
    """
    try:
        if style_sheet:
            window.setStyleSheet(style_sheet)
        
        # تطبيق تنسيق افتراضي للنوافذ العربية
        default_style = """
            QDialog, QMainWindow {
                background-color: #f5f5f5;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
            }
            
            QLabel {
                color: #333333;
                font-size: 10pt;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 10pt;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 10pt;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #4CAF50;
            }
            
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ddd;
            }
            
            QTableWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            
            QHeaderView::section {
                background-color: #e0e0e0;
                padding: 8px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """
        
        window.setStyleSheet(default_style + (style_sheet or ""))
        
        print("✅ تم تطبيق تنسيق النافذة")
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق تنسيق النافذة: {e}")

def make_window_modal(window, parent=None):
    """
    جعل النافذة مودال (تمنع التفاعل مع النوافذ الأخرى)
    
    Args:
        window: النافذة
        parent: النافذة الأب
    """
    try:
        window.setModal(True)
        if parent:
            window.setParent(parent)
        
        print("✅ تم جعل النافذة مودال")
        
    except Exception as e:
        print(f"❌ خطأ في جعل النافذة مودال: {e}")

def setup_dialog_window(dialog, title, size=(400, 300), center=True, modal=True, parent=None):
    """
    إعداد نافذة حوار بالخصائص الأساسية
    
    Args:
        dialog: نافذة الحوار
        title: عنوان النافذة
        size: حجم النافذة (width, height)
        center: هل يتم توسيط النافذة
        modal: هل النافذة مودال
        parent: النافذة الأب
    """
    try:
        # تعيين العنوان والحجم
        set_window_properties(dialog, title, size, center=False, resizable=True)
        
        # جعل النافذة مودال إذا طُلب ذلك
        if modal:
            make_window_modal(dialog, parent)
        
        # توسيط النافذة
        if center:
            if parent:
                center_window_on_parent(dialog, parent)
            else:
                center_window(dialog)
        
        # تطبيق التنسيق
        apply_window_style(dialog)
        
        print(f"✅ تم إعداد نافذة الحوار: {title}")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد نافذة الحوار: {e}")

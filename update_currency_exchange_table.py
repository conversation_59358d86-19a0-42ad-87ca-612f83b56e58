#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث جدول صرف العملات لإضافة shift_id
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def update_currency_exchange_table():
    """تحديث جدول صرف العملات"""
    
    print("🔄 تحديث جدول صرف العملات...")
    
    # الاتصال بقاعدة البيانات
    db_path = Path("data/company_system.db")
    if not db_path.exists():
        db_path = Path("company_system.db")
    
    db_manager = DatabaseManager(str(db_path))
    
    try:
        # التحقق من وجود الجدول
        table_exists = db_manager.fetch_one("""
            SELECT name FROM sqlite_master WHERE type='table' AND name='currency_exchange'
        """)
        
        if not table_exists:
            # إنشاء الجدول إذا لم يكن موجود
            db_manager.execute_query("""
                CREATE TABLE currency_exchange (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    from_currency TEXT NOT NULL,
                    to_currency TEXT NOT NULL,
                    exchange_rate REAL NOT NULL,
                    amount_from REAL NOT NULL,
                    amount_to REAL NOT NULL,
                    user_id INTEGER NOT NULL,
                    shift_id INTEGER,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (shift_id) REFERENCES shifts(id)
                )
            """)
            print("✅ تم إنشاء جدول currency_exchange")
        else:
            # التحقق من وجود عمود shift_id
            columns = db_manager.fetch_all("PRAGMA table_info(currency_exchange)")
            column_names = [col[1] for col in columns]
            
            if 'shift_id' not in column_names:
                # إضافة عمود shift_id
                db_manager.execute_query("""
                    ALTER TABLE currency_exchange ADD COLUMN shift_id INTEGER
                """)
                print("✅ تم إضافة عمود shift_id لجدول currency_exchange")
            else:
                print("✅ عمود shift_id موجود بالفعل في جدول currency_exchange")
        
        # إنشاء فهرس للشيفت
        db_manager.execute_query("""
            CREATE INDEX IF NOT EXISTS idx_currency_exchange_shift 
            ON currency_exchange(shift_id)
        """)
        
        print("✅ تم إنشاء فهرس الشيفت")
        
        # إنشاء فهرس للتاريخ
        db_manager.execute_query("""
            CREATE INDEX IF NOT EXISTS idx_currency_exchange_date 
            ON currency_exchange(created_at)
        """)
        
        print("✅ تم إنشاء فهرس التاريخ")
        
        # عرض بنية الجدول
        table_info = db_manager.fetch_all("PRAGMA table_info(currency_exchange)")
        print("\n📊 بنية جدول currency_exchange:")
        for column in table_info:
            default_value = f" - Default: {column[4]}" if column[4] else ""
            print(f"  • {column[1]} - {column[2]}{default_value}")
        
        print("\n🎉 تم تحديث جدول صرف العملات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول صرف العملات: {e}")
        return False
    
    finally:
        db_manager.close()
    
    return True

if __name__ == "__main__":
    update_currency_exchange_table()

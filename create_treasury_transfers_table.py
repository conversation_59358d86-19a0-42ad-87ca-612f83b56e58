#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول نقل الخزينة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def create_treasury_transfers_table():
    """إنشاء جدول نقل الخزينة"""
    
    print("🔄 إنشاء جدول نقل الخزينة...")
    
    # الاتصال بقاعدة البيانات
    db_path = Path("data/company_system.db")
    if not db_path.exists():
        db_path = Path("company_system.db")
    
    db_manager = DatabaseManager(str(db_path))
    
    try:
        # إنشاء جدول نقل الخزينة
        db_manager.execute_query("""
            CREATE TABLE IF NOT EXISTS treasury_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transfer_date DATE NOT NULL,
                transfer_type TEXT NOT NULL,
                amount REAL NOT NULL,
                receiver TEXT NOT NULL,
                notes TEXT,
                transferred_by TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ تم إنشاء جدول treasury_transfers")
        
        # إنشاء فهرس للتاريخ
        db_manager.execute_query("""
            CREATE INDEX IF NOT EXISTS idx_treasury_transfers_date 
            ON treasury_transfers(transfer_date)
        """)
        
        print("✅ تم إنشاء فهرس التاريخ")
        
        # إنشاء فهرس للمستخدم
        db_manager.execute_query("""
            CREATE INDEX IF NOT EXISTS idx_treasury_transfers_user 
            ON treasury_transfers(transferred_by)
        """)
        
        print("✅ تم إنشاء فهرس المستخدم")
        
        # عرض بنية الجدول
        table_info = db_manager.fetch_all("PRAGMA table_info(treasury_transfers)")
        print("\n📊 بنية جدول treasury_transfers:")
        for column in table_info:
            default_value = f" - Default: {column[4]}" if column[4] else ""
            print(f"  • {column[1]} - {column[2]}{default_value}")
        
        print("\n🎉 تم إنشاء جدول نقل الخزينة بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول نقل الخزينة: {e}")
        return False
    
    finally:
        db_manager.close()
    
    return True

if __name__ == "__main__":
    create_treasury_transfers_table()

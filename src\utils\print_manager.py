# -*- coding: utf-8 -*-
"""
مدير الطباعة المركزي
Central Print Manager
"""

from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QTextDocument
from PyQt5.QtCore import Qt, QRect, QDate, QTime
import os

try:
    from .arabic_support import create_arabic_font, format_currency
except ImportError:
    from arabic_support import create_arabic_font, format_currency

class PrintManager:
    """مدير الطباعة المركزي"""
    
    def __init__(self, db_manager, config_manager):
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.printer = QPrinter()
        self.setup_printer()
        
    def setup_printer(self):
        """إعداد الطابعة من الإعدادات"""
        try:
            # تحميل إعدادات الطباعة
            settings = self.get_print_settings()
            
            # إعداد اسم الطابعة
            if settings.get('printer_name'):
                self.printer.setPrinterName(settings['printer_name'])
            
            # إعداد حجم الورق
            paper_size = settings.get('paper_size', 'A4')
            if paper_size == 'A4':
                self.printer.setPageSize(QPrinter.A4)
            elif paper_size == 'A5':
                self.printer.setPageSize(QPrinter.A5)
            elif paper_size == 'Letter':
                self.printer.setPageSize(QPrinter.Letter)
            elif paper_size == 'Legal':
                self.printer.setPageSize(QPrinter.Legal)
            
            # إعداد الاتجاه
            orientation = settings.get('orientation', 'عمودي')
            if orientation == 'أفقي':
                self.printer.setOrientation(QPrinter.Landscape)
            else:
                self.printer.setOrientation(QPrinter.Portrait)
            
            # إعداد الجودة
            quality = settings.get('quality', 'عادية')
            if quality == 'عالية':
                self.printer.setResolution(600)
            elif quality == 'مسودة':
                self.printer.setResolution(150)
            else:
                self.printer.setResolution(300)
            
            # إعداد عدد النسخ
            copies = int(settings.get('print_copies', 1))
            self.printer.setCopyCount(copies)
            
        except Exception as e:
            print(f"خطأ في إعداد الطابعة: {e}")
            
    def get_print_settings(self):
        """الحصول على إعدادات الطباعة"""
        try:
            settings = {}
            
            # تحميل الإعدادات من قاعدة البيانات
            print_settings = self.db_manager.fetch_all("""
                SELECT key, value FROM settings 
                WHERE key LIKE 'print_%' OR key IN ('printer_name', 'paper_size')
            """)
            
            for setting in print_settings:
                key = setting['key'].replace('print_', '')
                settings[key] = setting['value']
            
            return settings
            
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الطباعة: {e}")
            return {}
            
    def print_document(self, content, title="مستند", show_preview=False):
        """طباعة مستند"""
        try:
            if show_preview:
                return self.show_print_preview(content, title)
            else:
                return self.direct_print(content, title)
                
        except Exception as e:
            QMessageBox.critical(None, "خطأ في الطباعة", f"خطأ في طباعة المستند: {e}")
            return False
            
    def show_print_preview(self, content, title):
        """عرض معاينة الطباعة"""
        try:
            preview_dialog = QPrintPreviewDialog(self.printer)
            preview_dialog.setWindowTitle(f"معاينة الطباعة - {title}")
            
            def print_preview(printer):
                self.render_content(printer, content, title)
            
            preview_dialog.paintRequested.connect(print_preview)
            return preview_dialog.exec_() == QPrintPreviewDialog.Accepted
            
        except Exception as e:
            print(f"خطأ في معاينة الطباعة: {e}")
            return False
            
    def direct_print(self, content, title):
        """طباعة مباشرة"""
        try:
            painter = QPainter()
            if painter.begin(self.printer):
                self.render_content(self.printer, content, title, painter)
                painter.end()
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في الطباعة المباشرة: {e}")
            return False
            
    def render_content(self, printer, content, title, painter=None):
        """رسم المحتوى على الطابعة"""
        if painter is None:
            painter = QPainter()
            painter.begin(printer)
            should_end = True
        else:
            should_end = False
            
        try:
            # إعداد الخط العربي
            font = create_arabic_font(10)
            painter.setFont(font)
            
            # الحصول على أبعاد الصفحة
            page_rect = printer.pageRect()
            
            # رسم الرأس
            self.draw_header(painter, page_rect, title)
            
            # رسم المحتوى
            content_rect = QRect(
                page_rect.left() + 50,
                page_rect.top() + 150,
                page_rect.width() - 100,
                page_rect.height() - 250
            )
            
            if isinstance(content, str):
                self.draw_text_content(painter, content_rect, content)
            elif isinstance(content, dict):
                self.draw_structured_content(painter, content_rect, content)
            
            # رسم التذييل
            self.draw_footer(painter, page_rect)
            
        finally:
            if should_end:
                painter.end()
                
    def draw_header(self, painter, page_rect, title):
        """رسم رأس الصفحة"""
        # عنوان الشركة
        company_font = create_arabic_font(16, bold=True)
        painter.setFont(company_font)
        
        company_name = self.get_company_name()
        painter.drawText(
            QRect(page_rect.left(), page_rect.top() + 20, page_rect.width(), 40),
            Qt.AlignCenter,
            company_name
        )
        
        # عنوان المستند
        title_font = create_arabic_font(14, bold=True)
        painter.setFont(title_font)
        
        painter.drawText(
            QRect(page_rect.left(), page_rect.top() + 70, page_rect.width(), 30),
            Qt.AlignCenter,
            title
        )
        
        # التاريخ والوقت
        date_font = create_arabic_font(9)
        painter.setFont(date_font)
        
        current_date = QDate.currentDate().toString("yyyy-MM-dd")
        current_time = QTime.currentTime().toString("hh:mm:ss")
        
        painter.drawText(
            QRect(page_rect.left() + 50, page_rect.top() + 110, 200, 20),
            Qt.AlignLeft,
            f"التاريخ: {current_date}"
        )
        
        painter.drawText(
            QRect(page_rect.right() - 250, page_rect.top() + 110, 200, 20),
            Qt.AlignRight,
            f"الوقت: {current_time}"
        )
        
        # خط فاصل
        painter.drawLine(
            page_rect.left() + 50,
            page_rect.top() + 140,
            page_rect.right() - 50,
            page_rect.top() + 140
        )
        
    def draw_footer(self, painter, page_rect):
        """رسم تذييل الصفحة"""
        footer_font = create_arabic_font(8)
        painter.setFont(footer_font)
        
        # خط فاصل
        painter.drawLine(
            page_rect.left() + 50,
            page_rect.bottom() - 80,
            page_rect.right() - 50,
            page_rect.bottom() - 80
        )
        
        # معلومات الشركة
        company_info = self.get_company_info()
        
        painter.drawText(
            QRect(page_rect.left() + 50, page_rect.bottom() - 70, page_rect.width() - 100, 60),
            Qt.AlignCenter | Qt.TextWordWrap,
            company_info
        )
        
    def draw_text_content(self, painter, rect, content):
        """رسم محتوى نصي"""
        font = create_arabic_font(10)
        painter.setFont(font)
        
        painter.drawText(
            rect,
            Qt.AlignTop | Qt.AlignRight | Qt.TextWordWrap,
            content
        )
        
    def draw_structured_content(self, painter, rect, content):
        """رسم محتوى منظم"""
        font = create_arabic_font(10)
        painter.setFont(font)
        
        y_offset = rect.top()
        line_height = 25
        
        for key, value in content.items():
            if y_offset + line_height > rect.bottom():
                break
                
            # رسم المفتاح
            painter.drawText(
                QRect(rect.left(), y_offset, 150, line_height),
                Qt.AlignRight | Qt.AlignVCenter,
                f"{key}:"
            )
            
            # رسم القيمة
            painter.drawText(
                QRect(rect.left() + 160, y_offset, rect.width() - 160, line_height),
                Qt.AlignLeft | Qt.AlignVCenter,
                str(value)
            )
            
            y_offset += line_height
            
    def get_company_name(self):
        """الحصول على اسم الشركة"""
        try:
            company = self.db_manager.fetch_one("""
                SELECT value FROM settings WHERE key = 'company_name'
            """)
            return company['value'] if company else "شركة الإنترنت"
        except:
            return "شركة الإنترنت"
            
    def get_company_info(self):
        """الحصول على معلومات الشركة"""
        try:
            info_parts = []
            
            # اسم الشركة
            company_name = self.get_company_name()
            info_parts.append(company_name)
            
            # معلومات إضافية يمكن إضافتها من قاعدة البيانات
            info_parts.append("نظام إدارة شركة الإنترنت")
            info_parts.append("تم الطباعة بواسطة النظام المتكامل")
            
            return " | ".join(info_parts)
            
        except:
            return "شركة الإنترنت | نظام إدارة متكامل"
            
    def print_invoice(self, invoice_data):
        """طباعة فاتورة"""
        try:
            content = self.format_invoice_content(invoice_data)
            return self.print_document(content, "فاتورة", show_preview=True)
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"خطأ في طباعة الفاتورة: {e}")
            return False
            
    def print_report(self, report_data, report_title):
        """طباعة تقرير"""
        try:
            content = self.format_report_content(report_data)
            return self.print_document(content, report_title, show_preview=True)
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"خطأ في طباعة التقرير: {e}")
            return False
            
    def format_invoice_content(self, invoice_data):
        """تنسيق محتوى الفاتورة"""
        content = {}
        
        if 'customer_name' in invoice_data:
            content['اسم العميل'] = invoice_data['customer_name']
        if 'invoice_number' in invoice_data:
            content['رقم الفاتورة'] = invoice_data['invoice_number']
        if 'amount' in invoice_data:
            content['المبلغ'] = format_currency(invoice_data['amount'])
        if 'description' in invoice_data:
            content['الوصف'] = invoice_data['description']
            
        return content
        
    def format_report_content(self, report_data):
        """تنسيق محتوى التقرير"""
        if isinstance(report_data, list):
            # تحويل قائمة البيانات إلى نص منسق
            content = ""
            for i, item in enumerate(report_data, 1):
                content += f"{i}. "
                if isinstance(item, dict):
                    content += " | ".join([f"{k}: {v}" for k, v in item.items()])
                else:
                    content += str(item)
                content += "\n"
            return content
        elif isinstance(report_data, dict):
            return report_data
        else:
            return str(report_data)

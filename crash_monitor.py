#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب الأخطاء الحرجة لتشخيص إغلاق التطبيق
"""

import sys
import os
import traceback
import signal
import atexit
from datetime import datetime

# إعداد المسارات
sys.path.append('src')

# مراقب الأخطاء العامة
def exception_handler(exc_type, exc_value, exc_traceback):
    """معالج الأخطاء العامة"""
    
    error_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    error_info = f"""
🚨 خطأ حرج في التطبيق - {error_time}
{'='*60}
نوع الخطأ: {exc_type.__name__}
رسالة الخطأ: {str(exc_value)}
{'='*60}
تفاصيل الخطأ:
{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}
{'='*60}
"""
    
    print(error_info)
    
    # حفظ الخطأ في ملف
    try:
        with open('crash_log.txt', 'a', encoding='utf-8') as f:
            f.write(error_info)
    except:
        pass
    
    # عدم إغلاق التطبيق
    print("⚠️ تم اكتشاف خطأ حرج لكن التطبيق لن يغلق")

# تسجيل معالج الأخطاء
sys.excepthook = exception_handler

def signal_handler(signum, frame):
    """معالج الإشارات"""
    print(f"🚨 تم استقبال إشارة: {signum}")
    print("⚠️ التطبيق يحاول الإغلاق بواسطة إشارة خارجية")

# تسجيل معالج الإشارات
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def exit_handler():
    """معالج الخروج"""
    print("🚨 التطبيق يغلق الآن!")
    print(f"⏰ وقت الإغلاق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# تسجيل معالج الخروج
atexit.register(exit_handler)

def test_router_delivery_with_monitoring():
    """اختبار تسليم الراوتر مع المراقبة"""
    
    print("🔍 اختبار تسليم الراوتر مع مراقبة الأخطاء...")
    print("=" * 60)
    
    try:
        # استيراد PyQt5
        print("1️⃣ استيراد PyQt5...")
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import QTimer
        print("✅ PyQt5")
        
        # إنشاء التطبيق
        print("2️⃣ إنشاء تطبيق Qt...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ تطبيق Qt")
        
        # استيراد قاعدة البيانات
        print("3️⃣ استيراد قاعدة البيانات...")
        from database.database_manager import DatabaseManager
        db = DatabaseManager('data/company_system.db')
        print("✅ قاعدة البيانات")
        
        # استيراد واجهة تسليم الراوتر
        print("4️⃣ استيراد واجهة تسليم الراوتر...")
        from ui.router_delivery_window import RouterDeliveryWindow
        print("✅ واجهة تسليم الراوتر")
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء الواجهة
        print("5️⃣ إنشاء واجهة تسليم الراوتر...")
        window = RouterDeliveryWindow(db, current_user)
        print("✅ تم إنشاء الواجهة")
        
        # عرض الواجهة
        print("6️⃣ عرض الواجهة...")
        window.show()
        print("✅ تم عرض الواجهة")
        
        # محاكاة الضغط على زر الحفظ بعد 3 ثوان
        def simulate_save():
            print("🔄 محاكاة الضغط على زر الحفظ...")
            try:
                # ملء بيانات تجريبية
                if hasattr(window, 'subscriber_combo') and window.subscriber_combo.count() > 0:
                    window.subscriber_combo.setCurrentIndex(0)
                    print("✅ تم اختيار مشترك")
                
                if hasattr(window, 'router_combo') and window.router_combo.count() > 0:
                    window.router_combo.setCurrentIndex(0)
                    print("✅ تم اختيار راوتر")
                
                if hasattr(window, 'package_combo') and window.package_combo.count() > 0:
                    window.package_combo.setCurrentIndex(0)
                    print("✅ تم اختيار باقة")
                
                if hasattr(window, 'worker_combo') and window.worker_combo.count() > 0:
                    window.worker_combo.setCurrentIndex(0)
                    print("✅ تم اختيار عامل")
                
                if hasattr(window, 'cable_meters_input'):
                    window.cable_meters_input.setText("50")
                    print("✅ تم إدخال أمتار الكبل")
                
                # الضغط على زر الحفظ
                print("🚨 الآن سنضغط على زر الحفظ...")
                if hasattr(window, 'save_btn'):
                    window.save_btn.click()
                    print("✅ تم الضغط على زر الحفظ")
                else:
                    print("❌ زر الحفظ غير موجود")
                    
            except Exception as save_error:
                print(f"❌ خطأ في محاكاة الحفظ: {save_error}")
                traceback.print_exc()
        
        # تشغيل المحاكاة بعد 3 ثوان
        QTimer.singleShot(3000, simulate_save)
        
        # إغلاق التطبيق بعد 10 ثوان
        def close_app():
            print("🔄 إغلاق التطبيق...")
            window.close()
            app.quit()
        
        QTimer.singleShot(10000, close_app)
        
        # تشغيل التطبيق
        print("7️⃣ تشغيل حلقة الأحداث...")
        print("⏰ سيتم محاكاة الضغط على الحفظ بعد 3 ثوان...")
        print("⏰ سيتم إغلاق التطبيق بعد 10 ثوان...")
        
        result = app.exec_()
        print(f"📊 انتهى التطبيق بالكود: {result}")
        
        return result == 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        return False

def test_save_function_directly():
    """اختبار دالة الحفظ مباشرة"""
    
    print("\n🔍 اختبار دالة الحفظ مباشرة...")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء الواجهة
        window = RouterDeliveryWindow(db, current_user)
        
        # محاولة استدعاء دالة الحفظ مباشرة
        print("🔄 استدعاء دالة save_delivery مباشرة...")
        
        # تعيين بيانات تجريبية
        window.selected_subscriber = {
            'id': 1,
            'name': 'أحمد محمد',
            'phone': '0123456789',
            'address': 'شارع الجامعة'
        }
        
        # محاولة الحفظ
        try:
            window.save_delivery()
            print("✅ دالة الحفظ تمت بدون أخطاء")
            return True
        except Exception as save_error:
            print(f"❌ خطأ في دالة الحفظ: {save_error}")
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة الحفظ: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 مراقب الأخطاء الحرجة - تشخيص إغلاق التطبيق")
    print("=" * 70)
    print("⚠️ سيتم مراقبة جميع الأخطاء وتسجيلها")
    print("⚠️ التطبيق لن يغلق حتى لو حدثت أخطاء حرجة")
    print("=" * 70)
    
    # مسح ملف السجل السابق
    try:
        if os.path.exists('crash_log.txt'):
            os.remove('crash_log.txt')
    except:
        pass
    
    # اختبار دالة الحفظ مباشرة
    direct_test = test_save_function_directly()
    
    # اختبار مع المراقبة
    monitoring_test = test_router_delivery_with_monitoring()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج المراقبة:")
    print(f"  • اختبار دالة الحفظ مباشرة: {'✅ نجح' if direct_test else '❌ فشل'}")
    print(f"  • اختبار مع المراقبة: {'✅ نجح' if monitoring_test else '❌ فشل'}")
    
    # فحص ملف السجل
    if os.path.exists('crash_log.txt'):
        print("\n🚨 تم العثور على أخطاء في ملف crash_log.txt:")
        try:
            with open('crash_log.txt', 'r', encoding='utf-8') as f:
                print(f.read())
        except:
            print("❌ لا يمكن قراءة ملف السجل")
    else:
        print("\n✅ لم يتم تسجيل أي أخطاء حرجة")
    
    print("\n💡 إذا كان التطبيق لا يزال يغلق:")
    print("  1. تحقق من ملف crash_log.txt")
    print("  2. شغل التطبيق من terminal لرؤية الأخطاء")
    print("  3. تأكد من عدم وجود sys.exit() في أماكن أخرى")
    print("  4. تحقق من إعدادات PyQt5")

if __name__ == "__main__":
    main()

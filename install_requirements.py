#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت متطلبات النظام
Install System Requirements
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تثبيت متطلبات نظام إدارة شركة الإنترنت...")
    print("=" * 50)
    
    # قائمة المتطلبات
    requirements = [
        "PyQt5==5.15.9",
        "PyQt5-tools==********.3", 
        "arabic-reshaper==3.0.0",
        "python-bidi==0.4.2",
        "reportlab==4.0.4",
        "Pillow==10.0.0",
        "openpyxl==3.1.2",
        "xlsxwriter==3.1.2"
    ]
    
    success_count = 0
    total_count = len(requirements)
    
    for package in requirements:
        print(f"\n📦 تثبيت {package}...")
        if install_package(package):
            success_count += 1
        
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {success_count}/{total_count} حزمة تم تثبيتها بنجاح")
    
    if success_count == total_count:
        print("✅ تم تثبيت جميع المتطلبات بنجاح!")
        print("🎉 يمكنك الآن تشغيل النظام باستخدام: python main.py")
    else:
        print("⚠️ فشل في تثبيت بعض المتطلبات. يرجى المحاولة مرة أخرى.")
        
    input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()

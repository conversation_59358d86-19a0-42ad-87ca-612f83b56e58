# -*- coding: utf-8 -*-
"""
نافذة المشتريات من الموردين
Purchases from Suppliers Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTime, QDateTime, QDate
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class PurchasesWindow(QDialog):
    """نافذة المشتريات من الموردين"""
    
    purchase_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, inventory_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = inventory_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        self.generate_invoice_number()

        # إعداد تحديث رمز العملة
        self.currency_combo.currentTextChanged.connect(self.update_currency_display)  # إنشاء رقم فاتورة تلقائي
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("المشتريات من الموردين")
        self.setGeometry(100, 100, 900, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إضافة مشترى جديد من المورد")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # معلومات المشترى
        purchase_info_group = self.create_purchase_info_group()
        
        # تفاصيل المنتجات
        products_group = self.create_products_group()
        
        # ملخص المشترى
        summary_group = self.create_summary_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(purchase_info_group)
        main_layout.addWidget(products_group)
        main_layout.addWidget(summary_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_purchase_info_group(self):
        """إنشاء مجموعة معلومات المشترى"""
        group = QGroupBox("معلومات المشترى")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # المورد
        supplier_label = QLabel("المورد:")
        apply_arabic_style(supplier_label, 10)
        self.supplier_combo = QComboBox()
        apply_arabic_style(self.supplier_combo, 10)
        
        # تاريخ المشترى
        date_label = QLabel("تاريخ المشترى:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # رقم الفاتورة (ترقيم تلقائي)
        invoice_label = QLabel("رقم الفاتورة:")
        apply_arabic_style(invoice_label, 10)

        # تخطيط أفقي لرقم الفاتورة والزر
        invoice_layout = QHBoxLayout()
        self.invoice_edit = QLineEdit()
        apply_arabic_style(self.invoice_edit, 10)
        self.invoice_edit.setReadOnly(True)  # للقراءة فقط
        self.invoice_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                color: #495057;
            }
        """)

        # زر إنشاء رقم جديد
        new_invoice_button = QPushButton("جديد")
        apply_arabic_style(new_invoice_button, 9)
        new_invoice_button.setMaximumWidth(50)
        new_invoice_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        new_invoice_button.clicked.connect(self.generate_invoice_number)

        invoice_layout.addWidget(self.invoice_edit)
        invoice_layout.addWidget(new_invoice_button)
        
        # العملة
        currency_label = QLabel("العملة:")
        apply_arabic_style(currency_label, 10)
        self.currency_combo = QComboBox()
        apply_arabic_style(self.currency_combo, 10)
        self.currency_combo.addItem("الليرة السورية", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي", "USD")
        self.currency_combo.addItem("اليورو", "EUR")

        # طريقة الدفع
        payment_label = QLabel("طريقة الدفع:")
        apply_arabic_style(payment_label, 10)
        self.payment_combo = QComboBox()
        apply_arabic_style(self.payment_combo, 10)
        self.payment_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "آجل"])
        
        layout.addWidget(supplier_label, 0, 0)
        layout.addWidget(self.supplier_combo, 0, 1)
        layout.addWidget(date_label, 0, 2)
        layout.addWidget(self.date_edit, 0, 3)
        layout.addWidget(invoice_label, 1, 0)
        layout.addLayout(invoice_layout, 1, 1)  # استخدام التخطيط الجديد
        layout.addWidget(currency_label, 1, 2)
        layout.addWidget(self.currency_combo, 1, 3)
        layout.addWidget(payment_label, 2, 0)
        layout.addWidget(self.payment_combo, 2, 1)

        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية حول المشترى...")

        layout.addWidget(notes_label, 3, 0)
        layout.addWidget(self.notes_edit, 3, 1, 1, 3)  # يمتد عبر 3 أعمدة

        return group
        
    def create_products_group(self):
        """إنشاء مجموعة المنتجات"""
        group = QGroupBox("تفاصيل المنتجات")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # أدوات إضافة منتج
        add_product_layout = QHBoxLayout()
        
        product_label = QLabel("المنتج:")
        apply_arabic_style(product_label, 10)
        self.product_combo = QComboBox()
        apply_arabic_style(self.product_combo, 10)
        
        quantity_label = QLabel("الكمية:")
        apply_arabic_style(quantity_label, 10)
        self.quantity_spin = QSpinBox()
        apply_arabic_style(self.quantity_spin, 10)
        self.quantity_spin.setRange(1, 9999)
        
        price_label = QLabel("سعر الوحدة:")
        apply_arabic_style(price_label, 10)
        self.price_spin = QDoubleSpinBox()
        apply_arabic_style(self.price_spin, 10)
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ل.س")
        
        add_button = QPushButton("إضافة")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        add_product_layout.addWidget(product_label)
        add_product_layout.addWidget(self.product_combo)
        add_product_layout.addWidget(quantity_label)
        add_product_layout.addWidget(self.quantity_spin)
        add_product_layout.addWidget(price_label)
        add_product_layout.addWidget(self.price_spin)
        add_product_layout.addWidget(add_button)
        add_product_layout.addStretch()
        
        # جدول المنتجات
        self.products_table = QTableWidget()
        apply_arabic_style(self.products_table, 9)
        
        columns = ["المنتج", "الكمية", "سعر الوحدة", "الإجمالي", "حذف"]
        self.products_table.setColumnCount(len(columns))
        self.products_table.setHorizontalHeaderLabels(columns)
        
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addLayout(add_product_layout)
        layout.addWidget(self.products_table)
        
        add_button.clicked.connect(self.add_product_to_table)
        
        return group
        
    def create_summary_group(self):
        """إنشاء مجموعة الملخص"""
        group = QGroupBox("ملخص المشترى")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # المجموع الفرعي
        subtotal_label = QLabel("المجموع الفرعي:")
        apply_arabic_style(subtotal_label, 10)
        self.subtotal_label = QLabel("0 ل.س")
        apply_arabic_style(self.subtotal_label, 10, bold=True)
        
        # الضريبة
        tax_label = QLabel("الضريبة (%):")
        apply_arabic_style(tax_label, 10)
        self.tax_spin = QDoubleSpinBox()
        apply_arabic_style(self.tax_spin, 10)
        self.tax_spin.setRange(0, 100)
        self.tax_spin.setSuffix("%")
        
        # قيمة الضريبة
        tax_amount_label = QLabel("قيمة الضريبة:")
        apply_arabic_style(tax_amount_label, 10)
        self.tax_amount_label = QLabel("0 ل.س")
        apply_arabic_style(self.tax_amount_label, 10)
        
        # الخصم
        discount_label = QLabel("الخصم:")
        apply_arabic_style(discount_label, 10)
        self.discount_spin = QDoubleSpinBox()
        apply_arabic_style(self.discount_spin, 10)
        self.discount_spin.setRange(0, 999999)
        self.discount_spin.setSuffix(" ل.س")
        
        # الإجمالي النهائي
        total_label = QLabel("الإجمالي النهائي:")
        apply_arabic_style(total_label, 12, bold=True)
        self.total_label = QLabel("0 ل.س")
        apply_arabic_style(self.total_label, 12, bold=True)
        self.total_label.setStyleSheet("color: #e74c3c;")
        
        layout.addWidget(subtotal_label, 0, 0)
        layout.addWidget(self.subtotal_label, 0, 1)
        layout.addWidget(tax_label, 0, 2)
        layout.addWidget(self.tax_spin, 0, 3)
        layout.addWidget(tax_amount_label, 1, 0)
        layout.addWidget(self.tax_amount_label, 1, 1)
        layout.addWidget(discount_label, 1, 2)
        layout.addWidget(self.discount_spin, 1, 3)
        layout.addWidget(total_label, 2, 0)
        layout.addWidget(self.total_label, 2, 1, 1, 3)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ المشترى")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(save_button)
        
        save_button.clicked.connect(self.save_purchase)
        cancel_button.clicked.connect(self.reject)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل الموردين
            suppliers = self.db_manager.fetch_all("""
                SELECT id, name, company_name FROM suppliers 
                WHERE is_active = 1 
                ORDER BY name
            """)
            
            self.supplier_combo.clear()
            for supplier in suppliers:
                display_name = f"{supplier['name']} - {supplier['company_name']}" if supplier['company_name'] else supplier['name']
                self.supplier_combo.addItem(display_name, supplier['id'])
            
            # تحميل المنتجات من النظام الموحد
            products = self.inventory_manager.get_all_products()

            self.product_combo.clear()
            for product in products:
                # عرض المنتج للمشتريات مع وحدة الشراء والمخزون الحالي
                purchase_unit = product.get('purchase_unit', product.get('unit_type', 'قطعة'))
                sale_unit = product.get('sale_unit', product.get('unit_type', 'قطعة'))
                current_stock = product.get('current_stock', 0)
                conversion_factor = product.get('conversion_factor', 1.0)

                # عرض معلومات الوحدات
                unit_info = f"شراء: {purchase_unit}"
                if purchase_unit != sale_unit:
                    unit_info += f" | بيع: {sale_unit} | تحويل: 1 {purchase_unit} = {conversion_factor} {sale_unit}"

                display_text = f"{product['name']} ({product['category']}) - {unit_info} - مخزون: {current_stock:.1f} {sale_unit}"
                self.product_combo.addItem(display_text, product)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.tax_spin.valueChanged.connect(self.calculate_total)
        self.discount_spin.valueChanged.connect(self.calculate_total)

    def generate_invoice_number(self):
        """إنشاء رقم فاتورة تلقائي"""
        try:
            # جلب آخر رقم فاتورة من قاعدة البيانات
            last_purchase = self.db_manager.fetch_one("""
                SELECT invoice_number FROM purchases
                WHERE invoice_number LIKE 'PUR-%'
                ORDER BY id DESC LIMIT 1
            """)

            if last_purchase and last_purchase['invoice_number']:
                # استخراج الرقم من آخر فاتورة
                try:
                    last_number = int(last_purchase['invoice_number'].replace('PUR-', ''))
                    new_number = last_number + 1
                except:
                    new_number = 1
            else:
                new_number = 1

            # إنشاء رقم الفاتورة الجديد
            invoice_number = f"PUR-{new_number:06d}"
            self.invoice_edit.setText(invoice_number)

        except Exception:
            # في حالة الخطأ، استخدم رقم افتراضي بالتاريخ والوقت
            from PyQt5.QtCore import QDateTime
            invoice_number = f"PUR-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}"
            self.invoice_edit.setText(invoice_number)

    def update_currency_display(self):
        """تحديث عرض العملة في الجدول والحقول"""
        try:
            currency = self.currency_combo.currentData()
            if not currency:
                currency = "SYP"  # افتراضي

            # تحديث رمز العملة في حقل سعر الوحدة
            if hasattr(self, 'unit_price_spin'):
                if currency == "SYP":
                    self.unit_price_spin.setSuffix(" ل.س")
                elif currency == "USD":
                    self.unit_price_spin.setSuffix(" $")
                elif currency == "EUR":
                    self.unit_price_spin.setSuffix(" €")

            # تحديث عناوين الأعمدة في الجدول
            if hasattr(self, 'products_table') and self.products_table.columnCount() > 0:
                try:
                    currency_symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")

                    # الحصول على عناوين الأعمدة الحالية
                    current_headers = []
                    for col in range(self.products_table.columnCount()):
                        header_item = self.products_table.horizontalHeaderItem(col)
                        if header_item:
                            current_headers.append(header_item.text())
                        else:
                            current_headers.append(f"عمود {col + 1}")

                    # تحديث عناوين الأعمدة التي تحتوي على أسعار
                    new_headers = []
                    for header in current_headers:
                        if "سعر الوحدة" in header:
                            new_headers.append(f"سعر الوحدة ({currency_symbol})")
                        elif "المجموع" in header:
                            new_headers.append(f"المجموع ({currency_symbol})")
                        else:
                            new_headers.append(header)

                    self.products_table.setHorizontalHeaderLabels(new_headers)
                    print(f"✅ تم تحديث عناوين الأعمدة للعملة {currency}")

                except Exception as header_error:
                    print(f"⚠️ خطأ في تحديث عناوين الأعمدة: {header_error}")

        except Exception as e:
            print(f"❌ خطأ في تحديث عرض العملة: {e}")
            import traceback
            traceback.print_exc()
        
    def add_product_to_table(self):
        """إضافة منتج إلى الجدول"""
        if self.product_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
            return

        product_data = self.product_combo.currentData()
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج صحيح")
            return

        product_name = product_data['name']
        quantity = self.quantity_spin.value()
        unit_price = self.price_spin.value()
        total_price = quantity * unit_price

        # الحصول على وحدة الشراء
        purchase_unit = product_data.get('purchase_unit', product_data.get('unit_type', 'قطعة'))

        # إضافة صف جديد
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)

        # حفظ بيانات المنتج في العنصر الأول
        product_item = QTableWidgetItem(f"{product_name} ({purchase_unit})")
        product_item.setData(Qt.UserRole, product_data)  # حفظ بيانات المنتج الكاملة
        self.products_table.setItem(row, 0, product_item)

        # الحصول على رمز العملة المحدد
        currency = self.currency_combo.currentData() or "SYP"
        currency_symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")

        self.products_table.setItem(row, 1, QTableWidgetItem(f"{quantity} {purchase_unit}"))
        self.products_table.setItem(row, 2, QTableWidgetItem(f"{unit_price:,.2f} {currency_symbol}"))
        self.products_table.setItem(row, 3, QTableWidgetItem(f"{total_price:,.2f} {currency_symbol}"))
        
        # زر حذف
        delete_button = QPushButton("حذف")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
        """)
        delete_button.clicked.connect(lambda: self.remove_product_from_table(row))
        self.products_table.setCellWidget(row, 4, delete_button)
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(0)
        
        # حساب الإجمالي
        self.calculate_total()
        
    def remove_product_from_table(self, row):
        """حذف منتج من الجدول"""
        self.products_table.removeRow(row)
        self.calculate_total()
        
    def calculate_total(self):
        """حساب الإجمالي"""
        try:
            subtotal = 0

            for row in range(self.products_table.rowCount()):
                total_item = self.products_table.item(row, 3)
                if total_item:
                    # إزالة التنسيق واستخراج الرقم (دعم جميع العملات)
                    total_text = total_item.text().replace("ل.س", "").replace("$", "").replace("€", "").replace(",", "").strip()
                    try:
                        subtotal += float(total_text)
                    except ValueError:
                        print(f"⚠️ خطأ في تحويل النص إلى رقم: {total_text}")
                        pass

            # حساب الضريبة
            tax_rate = self.tax_spin.value() / 100
            tax_amount = subtotal * tax_rate

            # حساب الخصم
            discount = self.discount_spin.value()

            # الإجمالي النهائي
            total = subtotal + tax_amount - discount

            # الحصول على رمز العملة المحدد
            currency = self.currency_combo.currentData() or "SYP"
            currency_symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")

            # تحديث التسميات بالعملة المحددة
            self.subtotal_label.setText(f"{subtotal:,.2f} {currency_symbol}")
            self.tax_amount_label.setText(f"{tax_amount:,.2f} {currency_symbol}")
            self.total_label.setText(f"{total:,.2f} {currency_symbol}")

        except Exception as e:
            print(f"❌ خطأ في حساب الإجمالي: {e}")
            import traceback
            traceback.print_exc()
        
    def save_purchase(self):
        """حفظ المشترى"""
        if self.supplier_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
            return
            
        if self.products_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للمشترى")
            return
            
        try:
            # بيانات المشترى
            currency = self.currency_combo.currentData()
            currency_name = self.currency_combo.currentText()
            currency_symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")

            purchase_data = {
                'supplier_id': self.supplier_combo.currentData(),
                'supplier_name': self.supplier_combo.currentText(),
                'currency': currency,
                'currency_name': currency_name,
                'date': self.date_edit.date().toString("yyyy-MM-dd"),
                'invoice_number': self.invoice_edit.text().strip(),
                'payment_method': self.payment_combo.currentText(),
                'subtotal': float(self.subtotal_label.text().replace("ل.س", "").replace("$", "").replace("€", "").replace(",", "").strip()),
                'tax_rate': self.tax_spin.value(),
                'tax_amount': float(self.tax_amount_label.text().replace("ل.س", "").replace("$", "").replace("€", "").replace(",", "").strip()),
                'discount': self.discount_spin.value(),
                'total': float(self.total_label.text().replace("ل.س", "").replace("$", "").replace("€", "").replace(",", "").strip()),
                'notes': self.notes_edit.toPlainText().strip(),
                'user_id': self.current_user['id']
            }
            
            # حفظ المشترى في قاعدة البيانات
            purchase_id = self.db_manager.execute_query("""
                INSERT INTO purchases (supplier_id, supplier_name, currency, purchase_date, invoice_number,
                                     payment_method, subtotal, tax_rate, tax_amount, total, notes, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                purchase_data['supplier_id'], purchase_data['supplier_name'], purchase_data['currency'],
                purchase_data['date'], purchase_data['invoice_number'], purchase_data['payment_method'],
                purchase_data['subtotal'], purchase_data['tax_rate'], purchase_data['tax_amount'],
                purchase_data['total'], purchase_data['notes'], self.current_user['id']
            )).lastrowid

            # حفظ المنتجات وتحديث المخزون
            for row in range(self.products_table.rowCount()):
                product_data = self.products_table.item(row, 0).data(Qt.UserRole)

                # استخراج الكمية من النص (قد يحتوي على الوحدة)
                quantity_text = self.products_table.item(row, 1).text()
                quantity = float(quantity_text.split()[0])  # أخذ الرقم فقط

                unit_price = float(self.products_table.item(row, 2).text().replace(",", "").replace("ل.س", "").replace("$", "").replace("€", "").strip())
                total_price = float(self.products_table.item(row, 3).text().replace(",", "").replace("ل.س", "").replace("$", "").replace("€", "").strip())

                # حفظ تفاصيل المشترى
                self.db_manager.execute_query("""
                    INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                """, (purchase_id, product_data['id'], quantity, unit_price, total_price))

                # تحديث المخزون باستخدام النظام الموحد مع وحدة الشراء
                purchase_unit = product_data.get('purchase_unit', product_data.get('unit_type', 'قطعة'))

                if hasattr(self.inventory_manager, 'add_stock_with_unit'):
                    # استخدام الدالة الجديدة التي تدعم الوحدات
                    success = self.inventory_manager.add_stock_with_unit(
                        product_id=product_data['id'],
                        quantity=quantity,
                        unit=purchase_unit,
                        operation_type="purchase",
                        reference_id=purchase_id,
                        notes=f"شراء من المورد - فاتورة {purchase_data['invoice_number']}",
                        user_id=self.current_user['id'],
                        from_location=f"supplier_{purchase_data['supplier_id']}"
                    )
                else:
                    # الطريقة القديمة
                    success = self.inventory_manager.add_stock(
                        product_id=product_data['id'],
                        quantity=quantity,
                        operation_type="purchase",
                        reference_id=purchase_id,
                        notes=f"شراء من المورد - فاتورة {purchase_data['invoice_number']}",
                        user_id=self.current_user['id'],
                        from_location=f"supplier_{purchase_data['supplier_id']}"
                    )

                if success:
                    print(f"✅ تم إضافة {quantity} من {product_data['name']} للمخزون الموحد")
                else:
                    print(f"❌ فشل في إضافة {product_data['name']} للمخزون")

            # خصم المبلغ من الخزينة اليومية (بالعملة المحددة)
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            treasury_manager = UnifiedTreasuryManager(self.db_manager)

            treasury_success = treasury_manager.subtract_from_daily_treasury(
                user_id=self.current_user['id'],
                currency_type=purchase_data['currency'],
                amount=purchase_data['total']
            )

            if treasury_success:
                print(f"✅ تم خصم {purchase_data['total']:,.2f} {purchase_data['currency_name']} من الخزينة اليومية")
                currency_symbol = "ل.س" if purchase_data['currency'] == "SYP" else ("$" if purchase_data['currency'] == "USD" else "€")
                QMessageBox.information(self, "نجح", f"تم حفظ المشترى وتحديث المخزون وخصم {purchase_data['total']:,.2f} {currency_symbol} من الخزينة اليومية")
            else:
                QMessageBox.warning(self, "تحذير", f"تم حفظ المشترى وتحديث المخزون لكن فشل في خصم المبلغ من الخزينة اليومية ({purchase_data['currency_name']})")

            # إرسال إشارة
            self.purchase_completed.emit(purchase_data)

            # سؤال المستخدم عن الطباعة
            reply = QMessageBox.question(
                self,
                "طباعة",
                "هل تريد طباعة فاتورة المشترى؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.print_purchase_invoice(purchase_data)

            # إعادة تعيين النموذج لفاتورة جديدة
            self.reset_form()

            # سؤال المستخدم عن إضافة فاتورة جديدة
            new_reply = QMessageBox.question(
                self,
                "فاتورة جديدة",
                "هل تريد إضافة فاتورة مشترى جديدة؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if new_reply == QMessageBox.No:
                self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المشترى: {e}")

    def reset_form(self):
        """إعادة تعيين النموذج لفاتورة جديدة"""
        # إنشاء رقم فاتورة جديد
        self.generate_invoice_number()

        # إعادة تعيين التاريخ للتاريخ الحالي
        from PyQt5.QtCore import QDate
        self.date_edit.setDate(QDate.currentDate())

        # إعادة تعيين طريقة الدفع
        self.payment_combo.setCurrentIndex(0)

        # مسح الملاحظات
        self.notes_edit.clear()

        # مسح جدول المنتجات
        self.products_table.setRowCount(0)

        # إعادة تعيين الحسابات
        self.tax_spin.setValue(0)
        self.discount_spin.setValue(0)
        self.subtotal_label.setText("0 ل.س")
        self.tax_amount_label.setText("0 ل.س")
        self.total_label.setText("0 ل.س")

        # إعادة تعيين اختيار المنتج والكمية
        if self.product_combo.count() > 0:
            self.product_combo.setCurrentIndex(0)
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(0)

    def print_purchase_invoice(self, purchase_data):
        """طباعة فاتورة المشترى"""
        try:
            # إعداد بيانات الفاتورة
            invoice_data = {
                'اسم المورد': purchase_data['supplier_name'],
                'رقم الفاتورة': f"PUR-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}",
                'التاريخ': purchase_data['date'],
                'الوقت': QTime.currentTime().toString("hh:mm:ss"),
                'المجموع الفرعي': format_currency(purchase_data['subtotal']),
                'الضريبة': format_currency(purchase_data['tax_amount']),
                'الخصم': format_currency(purchase_data['discount']),
                'الإجمالي': format_currency(purchase_data['total']),
                'ملاحظات': purchase_data.get('notes', 'لا توجد ملاحظات')
            }

            # إضافة تفاصيل المنتجات
            products_details = "تفاصيل المنتجات:\n"
            for i in range(self.products_table.rowCount()):
                product_name = self.products_table.item(i, 0).text()
                quantity = self.products_table.item(i, 1).text()
                unit_price = self.products_table.item(i, 2).text()
                total_price = self.products_table.item(i, 3).text()
                products_details += f"- {product_name}: {quantity} × {unit_price} = {total_price}\n"

            invoice_data['تفاصيل المنتجات'] = products_details

            # طباعة الفاتورة
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_document(invoice_data, "فاتورة مشترى", show_preview=True)

            if success:
                QMessageBox.information(self, "تم", "تم إرسال فاتورة المشترى للطباعة بنجاح")
            else:
                QMessageBox.warning(self, "تحذير", "تم إلغاء عملية الطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة فاتورة المشترى: {e}")

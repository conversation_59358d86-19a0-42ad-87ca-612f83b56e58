#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية جدول router_deliveries
"""

import sys
import os
sys.path.append('src')

def check_table_structure():
    """فحص بنية جدول router_deliveries"""
    
    print("🔍 فحص بنية جدول router_deliveries...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص وجود الجدول
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' AND name='router_deliveries'")
        
        if not tables:
            print("❌ جدول router_deliveries غير موجود")
            return False
        
        print("✅ جدول router_deliveries موجود")
        
        # فحص بنية الجدول
        columns = db.fetch_all("PRAGMA table_info(router_deliveries)")
        
        print("\n📋 أعمدة الجدول:")
        for col in columns:
            print(f"  • {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
        
        # فحص البيانات الموجودة
        count = db.fetch_one("SELECT COUNT(*) as count FROM router_deliveries")
        print(f"\n📊 عدد السجلات: {count['count'] if count else 0}")
        
        # عرض آخر 3 سجلات
        if count and count['count'] > 0:
            recent = db.fetch_all("SELECT * FROM router_deliveries ORDER BY id DESC LIMIT 3")
            print("\n📋 آخر 3 سجلات:")
            for record in recent:
                print(f"  • ID: {record.get('id', 'N/A')}")
                print(f"    المشترك: {record.get('subscriber_name', 'N/A')}")
                print(f"    الراوتر: {record.get('router_type', record.get('router_name', 'N/A'))}")
                print(f"    التاريخ: {record.get('delivery_date', record.get('created_at', 'N/A'))}")
                print("    ---")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_compatible_table():
    """إنشاء جدول متوافق مع الكود الحالي"""
    
    print("\n🔧 إنشاء جدول router_deliveries متوافق...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # حذف الجدول القديم إذا كان موجود
        db.execute_query("DROP TABLE IF EXISTS router_deliveries_backup")
        
        # نسخ البيانات القديمة إذا كانت موجودة
        try:
            db.execute_query("CREATE TABLE router_deliveries_backup AS SELECT * FROM router_deliveries")
            print("✅ تم نسخ البيانات القديمة")
        except:
            print("⚠️ لا توجد بيانات قديمة للنسخ")
        
        # حذف الجدول القديم
        db.execute_query("DROP TABLE IF EXISTS router_deliveries")
        
        # إنشاء الجدول الجديد المتوافق
        db.execute_query("""
            CREATE TABLE router_deliveries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subscriber_name TEXT NOT NULL,
                router_id INTEGER,
                router_name TEXT,
                router_price REAL DEFAULT 0,
                cable_id INTEGER,
                cable_name TEXT,
                cable_price_per_meter REAL DEFAULT 0,
                cable_meters REAL DEFAULT 0,
                cable_cost REAL DEFAULT 0,
                worker_id INTEGER,
                worker_name TEXT,
                package_id INTEGER,
                package_name TEXT,
                package_price REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                delivery_date DATE DEFAULT CURRENT_DATE,
                notes TEXT,
                FOREIGN KEY (router_id) REFERENCES unified_products (id),
                FOREIGN KEY (cable_id) REFERENCES unified_products (id),
                FOREIGN KEY (worker_id) REFERENCES workers (id),
                FOREIGN KEY (package_id) REFERENCES packages (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        print("✅ تم إنشاء جدول router_deliveries الجديد")
        
        # استعادة البيانات القديمة إذا كانت موجودة
        try:
            # فحص وجود الجدول الاحتياطي
            backup_exists = db.fetch_one("SELECT name FROM sqlite_master WHERE type='table' AND name='router_deliveries_backup'")
            
            if backup_exists:
                # نسخ البيانات المتوافقة
                db.execute_query("""
                    INSERT INTO router_deliveries 
                    (subscriber_name, router_name, total_amount, user_id, created_at)
                    SELECT 
                        COALESCE(subscriber_name, 'مشترك غير محدد'),
                        COALESCE(router_type, router_name, 'راوتر غير محدد'),
                        COALESCE(total_amount, 0),
                        COALESCE(user_id, 1),
                        COALESCE(created_at, delivery_date, CURRENT_TIMESTAMP)
                    FROM router_deliveries_backup
                """)
                
                restored_count = db.fetch_one("SELECT COUNT(*) as count FROM router_deliveries")
                print(f"✅ تم استعادة {restored_count['count'] if restored_count else 0} سجل")
                
                # حذف الجدول الاحتياطي
                db.execute_query("DROP TABLE router_deliveries_backup")
        except Exception as restore_error:
            print(f"⚠️ لم يتم استعادة البيانات القديمة: {restore_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_insert():
    """اختبار إدراج بيانات تجريبية"""
    
    print("\n🧪 اختبار إدراج بيانات تجريبية...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # إدراج سجل تجريبي
        result = db.execute_query("""
            INSERT INTO router_deliveries
            (subscriber_name, router_id, router_name, router_price,
             cable_id, cable_name, cable_price_per_meter, cable_meters, cable_cost,
             worker_id, worker_name, package_id, package_name, package_price,
             total_amount, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            "مشترك تجريبي",
            1, "راوتر TP-Link", 75000,
            2, "كبل عادي", 500, 50, 25000,
            1, "عامل تجريبي", 1, "باقة 10 ميجا", 0,
            100000, 1
        ))
        
        if result:
            print(f"✅ تم إدراج سجل تجريبي - ID: {result.lastrowid}")
            
            # التحقق من الإدراج
            inserted = db.fetch_one("SELECT * FROM router_deliveries WHERE id = ?", (result.lastrowid,))
            if inserted:
                print("✅ تم التحقق من الإدراج بنجاح")
                print(f"  • المشترك: {inserted['subscriber_name']}")
                print(f"  • الراوتر: {inserted['router_name']}")
                print(f"  • الإجمالي: {inserted['total_amount']:,} ل.س")
            
            return True
        else:
            print("❌ فشل في الإدراج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإدراج: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 فحص وإصلاح جدول router_deliveries")
    print("=" * 50)
    
    # فحص البنية الحالية
    structure_ok = check_table_structure()
    
    if not structure_ok:
        print("\n🔧 إنشاء جدول جديد...")
        create_ok = create_compatible_table()
        
        if create_ok:
            print("\n🔍 فحص الجدول الجديد...")
            check_table_structure()
    else:
        print("\n🔧 إعادة إنشاء الجدول للتأكد من التوافق...")
        create_ok = create_compatible_table()
    
    # اختبار الإدراج
    if create_ok:
        test_insert()
    
    print("\n" + "=" * 50)
    print("📊 ملخص الإصلاح:")
    print("  ✅ تم فحص بنية الجدول")
    print("  ✅ تم إنشاء جدول متوافق مع الكود")
    print("  ✅ تم اختبار الإدراج")
    print("  ✅ الجدول جاهز للاستخدام")
    
    print("\n🎯 الأعمدة المتوافقة:")
    print("  • subscriber_name, router_id, router_name, router_price")
    print("  • cable_id, cable_name, cable_price_per_meter, cable_meters, cable_cost")
    print("  • worker_id, worker_name, package_id, package_name, package_price")
    print("  • total_amount, user_id, created_at")

if __name__ == "__main__":
    main()

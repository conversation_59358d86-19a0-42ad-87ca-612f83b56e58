#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشغيل النظام
Test System Run
"""

import sys
import os
from pathlib import Path

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        import PyQt5
        print("✅ PyQt5 متوفر")
    except ImportError:
        print("❌ PyQt5 غير متوفر")
        return False
        
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        print("✅ PyQt5.QtWidgets متوفر")
    except ImportError:
        print("❌ PyQt5.QtWidgets غير متوفر")
        return False
        
    try:
        import arabic_reshaper
        print("✅ arabic_reshaper متوفر")
    except ImportError:
        print("❌ arabic_reshaper غير متوفر")
        return False
        
    try:
        import bidi
        print("✅ python-bidi متوفر")
    except ImportError:
        print("❌ python-bidi غير متوفر")
        return False
        
    return True

def test_project_structure():
    """اختبار هيكل المشروع"""
    print("\n📁 اختبار هيكل المشروع...")
    
    required_files = [
        "src/__init__.py",
        "src/database/__init__.py",
        "src/database/database_manager.py",
        "src/ui/__init__.py",
        "src/ui/login_window.py",
        "src/ui/main_window.py",
        "src/utils/__init__.py",
        "src/utils/config_manager.py",
        "src/utils/arabic_support.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ ملف مفقود: {file_path}")
        else:
            print(f"✅ {file_path}")
            
    return len(missing_files) == 0

def run_simple_test():
    """تشغيل اختبار بسيط"""
    print("\n🚀 تشغيل اختبار بسيط...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # استيراد الوحدات
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        # إنشاء تطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # عرض رسالة اختبار
        msg = QMessageBox()
        msg.setWindowTitle("اختبار النظام")
        msg.setText("النظام يعمل بشكل صحيح!")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
        print("✅ الاختبار نجح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار نظام إدارة شركة الإنترنت")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        print("يرجى تثبيت المتطلبات باستخدام: python install_requirements.py")
        input("\nاضغط Enter للخروج...")
        return
        
    # اختبار هيكل المشروع
    if not test_project_structure():
        print("\n❌ فشل في اختبار هيكل المشروع")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("\nاضغط Enter للخروج...")
        return
        
    # تشغيل اختبار بسيط
    if run_simple_test():
        print("\n🎉 جميع الاختبارات نجحت!")
        print("يمكنك الآن تشغيل النظام باستخدام: python main.py")
    else:
        print("\n❌ فشل في الاختبار البسيط")
        
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()

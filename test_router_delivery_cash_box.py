#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ظهور تسليم الراوتر في إغلاق الصندوق
"""

import sys
import os
sys.path.append('src')

def test_router_delivery_in_cash_box():
    """اختبار ظهور تسليم الراوتر في إغلاق الصندوق"""
    
    print("🧪 اختبار ظهور تسليم الراوتر في إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from ui.cash_box_closure_window import CashBoxClosureWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # 1. إضافة تسليم راوتر تجريبي
        print("\n📦 إضافة تسليم راوتر تجريبي...")
        
        # إنشاء واجهة تسليم الراوتر
        router_window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        # بيانات تجريبية
        test_subscriber_name = "مشترك اختبار إغلاق الصندوق"
        test_amount = 150000
        
        # إضافة المشترك إذا لم يكن موجود
        existing_subscriber = db.fetch_one("SELECT id FROM subscribers WHERE name = ?", (test_subscriber_name,))
        if not existing_subscriber:
            db.execute_query("""
                INSERT INTO subscribers (name, phone, address, delivered)
                VALUES (?, ?, ?, ?)
            """, (test_subscriber_name, "987654321", "عنوان اختبار", 0))
            print("✅ تم إضافة المشترك التجريبي")
        
        # بيانات التسليم
        router_data = {'id': 1, 'name': 'راوتر اختبار إغلاق الصندوق', 'unit_price': 80000}
        cable_data = {'id': 2, 'name': 'كبل اختبار', 'unit_price': 600}
        worker_data = {'id': 1, 'name': 'عامل اختبار'}
        package_data = {'id': 1, 'name': 'باقة اختبار', 'price': 0}
        
        # حفظ التسليم
        delivery_id = router_window.save_delivery_record(
            test_subscriber_name,
            router_data,
            cable_data,
            worker_data,
            package_data,
            60,  # cable_meters
            test_amount
        )
        
        if delivery_id:
            print(f"✅ تم حفظ تسليم الراوتر - ID: {delivery_id}, المبلغ: {test_amount:,} ل.س")
        else:
            print("❌ فشل في حفظ تسليم الراوتر")
            return False
        
        # 2. فحص البيانات في الجداول
        print("\n🔍 فحص البيانات في الجداول...")
        
        # فحص جدول router_deliveries
        router_delivery = db.fetch_one("""
            SELECT total_amount FROM router_deliveries 
            WHERE id = ?
        """, (delivery_id,))
        
        if router_delivery:
            print(f"✅ جدول router_deliveries: {router_delivery['total_amount']:,} ل.س")
        else:
            print("❌ لا يوجد سجل في جدول router_deliveries")
        
        # فحص جدول transactions
        transaction = db.fetch_one("""
            SELECT amount FROM transactions 
            WHERE type = 'تسليم راوتر' AND description LIKE ?
            ORDER BY created_at DESC LIMIT 1
        """, (f'%{test_subscriber_name}%',))
        
        if transaction:
            print(f"✅ جدول transactions: {transaction['amount']:,} ل.س")
        else:
            print("❌ لا يوجد سجل في جدول transactions")
        
        # 3. اختبار واجهة إغلاق الصندوق
        print("\n💰 اختبار واجهة إغلاق الصندوق...")
        
        # إنشاء واجهة إغلاق الصندوق
        cash_box_window = CashBoxClosureWindow(db, treasury_manager, current_user)
        
        # تحميل بيانات المبيعات
        cash_box_window.load_sales_data()
        
        # فحص مبلغ تسليم الراوترات
        router_deliveries_amount = cash_box_window.router_deliveries_amount
        
        print(f"📊 مبلغ تسليم الراوترات في إغلاق الصندوق: {router_deliveries_amount:,} ل.س")
        
        if router_deliveries_amount >= test_amount:
            print("✅ يظهر مبلغ تسليم الراوتر في إغلاق الصندوق")
            
            # فحص الإجمالي
            total_sales = cash_box_window.total_sales_amount
            print(f"📊 إجمالي المبيعات: {total_sales:,} ل.س")
            
            if total_sales >= test_amount:
                print("✅ مبلغ تسليم الراوتر مُضاف للإجمالي")
                return True
            else:
                print("❌ مبلغ تسليم الراوتر غير مُضاف للإجمالي")
                return False
        else:
            print("❌ لا يظهر مبلغ تسليم الراوتر في إغلاق الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسليم الراوتر في إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_deliveries():
    """اختبار عدة تسليمات راوتر"""
    
    print("\n🧪 اختبار عدة تسليمات راوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from ui.cash_box_closure_window import CashBoxClosureWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء واجهة تسليم الراوتر
        router_window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        # إضافة 3 تسليمات تجريبية
        test_deliveries = [
            ("مشترك تسليم 1", 120000),
            ("مشترك تسليم 2", 135000),
            ("مشترك تسليم 3", 110000)
        ]
        
        total_expected = 0
        successful_deliveries = 0
        
        for subscriber_name, amount in test_deliveries:
            print(f"📦 إضافة تسليم: {subscriber_name} - {amount:,} ل.س")
            
            # إضافة المشترك
            existing = db.fetch_one("SELECT id FROM subscribers WHERE name = ?", (subscriber_name,))
            if not existing:
                db.execute_query("""
                    INSERT INTO subscribers (name, phone, address, delivered)
                    VALUES (?, ?, ?, ?)
                """, (subscriber_name, "111222333", "عنوان تجريبي", 0))
            
            # بيانات التسليم
            router_data = {'id': 1, 'name': 'راوتر تجريبي', 'unit_price': amount - 20000}
            cable_data = {'id': 2, 'name': 'كبل تجريبي', 'unit_price': 500}
            worker_data = {'id': 1, 'name': 'عامل تجريبي'}
            package_data = {'id': 1, 'name': 'باقة تجريبية', 'price': 0}
            
            # حفظ التسليم
            delivery_id = router_window.save_delivery_record(
                subscriber_name,
                router_data,
                cable_data,
                worker_data,
                package_data,
                40,  # cable_meters
                amount
            )
            
            if delivery_id:
                print(f"  ✅ تم الحفظ - ID: {delivery_id}")
                total_expected += amount
                successful_deliveries += 1
            else:
                print(f"  ❌ فشل في الحفظ")
        
        print(f"\n📊 تم حفظ {successful_deliveries} تسليم بإجمالي {total_expected:,} ل.س")
        
        # اختبار إغلاق الصندوق
        print("\n💰 اختبار إغلاق الصندوق مع التسليمات المتعددة...")
        
        cash_box_window = CashBoxClosureWindow(db, treasury_manager, current_user)
        cash_box_window.load_sales_data()
        
        router_deliveries_amount = cash_box_window.router_deliveries_amount
        
        print(f"📊 مبلغ تسليم الراوترات في إغلاق الصندوق: {router_deliveries_amount:,} ل.س")
        print(f"📊 المبلغ المتوقع: {total_expected:,} ل.س")
        
        if router_deliveries_amount >= total_expected:
            print("✅ جميع التسليمات تظهر في إغلاق الصندوق")
            return True
        else:
            print("❌ بعض التسليمات لا تظهر في إغلاق الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التسليمات المتعددة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار ظهور تسليم الراوتر في إغلاق الصندوق")
    print("=" * 70)
    
    # اختبار تسليم راوتر واحد
    single_test = test_router_delivery_in_cash_box()
    
    # اختبار عدة تسليمات
    multiple_test = test_multiple_deliveries()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • تسليم راوتر واحد: {'✅ يظهر' if single_test else '❌ لا يظهر'}")
    print(f"  • تسليمات متعددة: {'✅ تظهر' if multiple_test else '❌ لا تظهر'}")
    
    if all([single_test, multiple_test]):
        print("\n🎉 تم إصلاح مشكلة ظهور تسليم الراوترات في إغلاق الصندوق!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ تحديث واجهة إغلاق الصندوق")
        print("    • قراءة من جدول router_deliveries الجديد")
        print("    • قراءة من جدول transactions القديم")
        print("    • جمع المبالغ من المصدرين")
        
        print("  ✅ تحديث واجهة تسليم الراوتر")
        print("    • إضافة سجل في transactions للتوافق")
        print("    • حفظ في router_deliveries للنظام الجديد")
        print("    • تحديث حالة المشترك")
        
        print("\n🚀 النظام الآن:")
        print("  • تسليم الراوتر يحفظ في جدولين (router_deliveries + transactions)")
        print("  • إغلاق الصندوق يقرأ من الجدولين")
        print("  • المبالغ تظهر بشكل صحيح في إغلاق الصندوق")
        print("  • التوافق مع النظام القديم والجديد")
        
        print("\n🎯 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. قم بتسليم راوتر من القائمة الرئيسية")
        print("  3. افتح إغلاق الصندوق من قائمة الإدارة")
        print("  4. ستجد مبلغ تسليم الراوتر في قسم 'تسليم راوترات'")
        print("  5. المبلغ مُضاف للإجمالي النهائي")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

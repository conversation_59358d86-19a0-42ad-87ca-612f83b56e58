# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين
Suppliers Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class SuppliersManagementWindow(QDialog):
    """نافذة إدارة الموردين"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الموردين")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الموردين والشركات")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # أدوات التحكم
        controls_layout = self.create_controls()
        
        # جدول الموردين
        self.suppliers_table = self.create_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addLayout(controls_layout)
        main_layout.addWidget(self.suppliers_table)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_controls(self):
        """إنشاء أدوات التحكم"""
        layout = QHBoxLayout()
        
        # فلتر النوع
        type_label = QLabel("نوع المورد:")
        apply_arabic_style(type_label, 10)
        
        self.type_combo = QComboBox()
        apply_arabic_style(self.type_combo, 10)
        self.type_combo.addItem("جميع الأنواع", "")
        self.type_combo.addItem("مورد راوترات", "router_supplier")
        self.type_combo.addItem("مورد كابلات", "cable_supplier")
        self.type_combo.addItem("مورد معدات", "equipment_supplier")
        self.type_combo.addItem("شركة اتصالات", "telecom_company")
        
        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)
        
        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث بالاسم أو الشركة...")
        
        # زر البحث
        search_button = QPushButton("بحث")
        apply_arabic_style(search_button, 10)
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout.addWidget(type_label)
        layout.addWidget(self.type_combo)
        layout.addWidget(search_label)
        layout.addWidget(self.search_edit)
        layout.addWidget(search_button)
        layout.addStretch()
        
        search_button.clicked.connect(self.search_suppliers)
        self.type_combo.currentTextChanged.connect(self.filter_by_type)
        
        return layout
        
    def create_table(self):
        """إنشاء جدول الموردين"""
        table = QTableWidget()
        apply_arabic_style(table, 9)
        
        columns = ["الرقم", "اسم المورد", "الشركة", "النوع", "الهاتف", "البريد الإلكتروني", "العنوان", "الحالة"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)
        
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)
        
        # تنسيق الأعمدة
        table.setColumnWidth(0, 60)   # الرقم
        table.setColumnWidth(1, 150)  # اسم المورد
        table.setColumnWidth(2, 150)  # الشركة
        table.setColumnWidth(3, 120)  # النوع
        table.setColumnWidth(4, 120)  # الهاتف
        table.setColumnWidth(5, 150)  # البريد الإلكتروني
        table.setColumnWidth(6, 200)  # العنوان
        table.setColumnWidth(7, 80)   # الحالة
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة مورد جديد")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        edit_button = QPushButton("تعديل المورد")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        delete_button = QPushButton("حذف المورد")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        purchases_button = QPushButton("سجل المشتريات")
        apply_arabic_style(purchases_button, 10)
        purchases_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(purchases_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        add_button.clicked.connect(self.add_supplier)
        edit_button.clicked.connect(self.edit_supplier)
        delete_button.clicked.connect(self.delete_supplier)
        purchases_button.clicked.connect(self.view_purchases)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل بيانات الموردين"""
        try:
            suppliers = self.db_manager.fetch_all("""
                SELECT id, name, company_name, supplier_type, phone, email, 
                       address, is_active, created_at 
                FROM suppliers 
                ORDER BY created_at DESC
            """)
            
            self.suppliers_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                # الرقم
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier['id'])))
                
                # اسم المورد
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['name'] or ""))
                
                # الشركة
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['company_name'] or ""))
                
                # النوع
                supplier_types = {
                    'router_supplier': 'مورد راوترات',
                    'cable_supplier': 'مورد كابلات',
                    'equipment_supplier': 'مورد معدات',
                    'telecom_company': 'شركة اتصالات'
                }
                supplier_type = supplier_types.get(supplier['supplier_type'], supplier['supplier_type'] or "غير محدد")
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier_type))
                
                # الهاتف
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['phone'] or ""))
                
                # البريد الإلكتروني
                self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier['email'] or ""))
                
                # العنوان
                address = supplier['address'] or ""
                if len(address) > 30:
                    address = address[:30] + "..."
                self.suppliers_table.setItem(row, 6, QTableWidgetItem(address))
                
                # الحالة
                status = "نشط" if supplier['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if supplier['is_active']:
                    status_item.setBackground(Qt.green)
                    status_item.setForeground(Qt.white)
                else:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.suppliers_table.setItem(row, 7, status_item)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الموردين: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص
            # في حالة الخطأ، أظهر جدول فارغ
            self.suppliers_table.setRowCount(0)
            
    def search_suppliers(self):
        """البحث في الموردين"""
        search_text = self.search_edit.text().strip()
        supplier_type = self.type_combo.currentData()
        
        try:
            query = """
                SELECT id, name, company_name, supplier_type, phone, email, 
                       address, is_active, created_at 
                FROM suppliers 
                WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND (name LIKE ? OR company_name LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])
                
            if supplier_type:
                query += " AND supplier_type = ?"
                params.append(supplier_type)
                
            query += " ORDER BY created_at DESC"
            
            suppliers = self.db_manager.fetch_all(query, params)
            
            # تحديث الجدول
            self.suppliers_table.setRowCount(len(suppliers))
            
            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier['id'])))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['name'] or ""))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['company_name'] or ""))
                
                supplier_types = {
                    'router_supplier': 'مورد راوترات',
                    'cable_supplier': 'مورد كابلات',
                    'equipment_supplier': 'مورد معدات',
                    'telecom_company': 'شركة اتصالات'
                }
                supplier_type_display = supplier_types.get(supplier['supplier_type'], supplier['supplier_type'] or "غير محدد")
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier_type_display))
                
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['phone'] or ""))
                self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier['email'] or ""))
                
                address = supplier['address'] or ""
                if len(address) > 30:
                    address = address[:30] + "..."
                self.suppliers_table.setItem(row, 6, QTableWidgetItem(address))
                
                status = "نشط" if supplier['is_active'] else "غير نشط"
                status_item = QTableWidgetItem(status)
                if supplier['is_active']:
                    status_item.setBackground(Qt.green)
                    status_item.setForeground(Qt.white)
                else:
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                self.suppliers_table.setItem(row, 7, status_item)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {e}")
            
    def filter_by_type(self):
        """فلترة حسب النوع"""
        self.search_suppliers()
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
            QMessageBox.information(self, "تم", "تم إضافة المورد بنجاح")
        
    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
            
        QMessageBox.information(self, "قريباً", "ميزة تعديل المورد قيد التطوير")
        
    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return
            
        supplier_id = self.suppliers_table.item(current_row, 0).text()
        supplier_name = self.suppliers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف المورد '{supplier_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المورد: {e}")
                
    def view_purchases(self):
        """عرض سجل المشتريات"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لعرض سجل مشترياته")
            return
            
        QMessageBox.information(self, "قريباً", "ميزة عرض سجل المشتريات قيد التطوير")


class SupplierDialog(QDialog):
    """حوار إضافة/تعديل مورد"""

    def __init__(self, db_manager, supplier_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.supplier_id = supplier_id
        self.is_edit_mode = supplier_id is not None

        self.setup_ui()
        if self.is_edit_mode:
            self.load_supplier_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مورد" if self.is_edit_mode else "إضافة مورد جديد"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 500, 400)
        self.setModal(True)

        apply_arabic_style(self, 10)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الحوار
        title_label = QLabel(title)
        apply_arabic_style(title_label, 14, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)

        # نموذج البيانات
        form_layout = QGridLayout()
        form_layout.setSpacing(10)

        # اسم المورد
        name_label = QLabel("اسم المورد:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)

        # اسم الشركة
        company_label = QLabel("اسم الشركة:")
        apply_arabic_style(company_label, 10)
        self.company_edit = QLineEdit()
        apply_arabic_style(self.company_edit, 10)

        # الهاتف
        phone_label = QLabel("الهاتف:")
        apply_arabic_style(phone_label, 10)
        self.phone_edit = QLineEdit()
        apply_arabic_style(self.phone_edit, 10)

        # العنوان
        address_label = QLabel("العنوان:")
        apply_arabic_style(address_label, 10)
        self.address_edit = QLineEdit()
        apply_arabic_style(self.address_edit, 10)

        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        apply_arabic_style(email_label, 10)
        self.email_edit = QLineEdit()
        apply_arabic_style(self.email_edit, 10)

        # الحالة
        self.is_active_checkbox = QCheckBox("المورد نشط")
        apply_arabic_style(self.is_active_checkbox, 10)
        self.is_active_checkbox.setChecked(True)

        # ترتيب الحقول
        form_layout.addWidget(name_label, 0, 0)
        form_layout.addWidget(self.name_edit, 0, 1)
        form_layout.addWidget(company_label, 1, 0)
        form_layout.addWidget(self.company_edit, 1, 1)
        form_layout.addWidget(phone_label, 2, 0)
        form_layout.addWidget(self.phone_edit, 2, 1)
        form_layout.addWidget(address_label, 3, 0)
        form_layout.addWidget(self.address_edit, 3, 1)
        form_layout.addWidget(email_label, 4, 0)
        form_layout.addWidget(self.email_edit, 4, 1)
        form_layout.addWidget(self.is_active_checkbox, 5, 0, 1, 2)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        layout.addWidget(title_label)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

        # ربط الأحداث
        save_button.clicked.connect(self.save_supplier)
        cancel_button.clicked.connect(self.reject)

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        try:
            supplier = self.db_manager.fetch_one("""
                SELECT name, company_name, phone, address, email, is_active
                FROM suppliers WHERE id = ?
            """, (self.supplier_id,))

            if supplier:
                self.name_edit.setText(supplier['name'])
                self.company_edit.setText(supplier['company_name'] or '')
                self.phone_edit.setText(supplier['phone'])
                self.address_edit.setText(supplier['address'] or '')
                self.email_edit.setText(supplier['email'] or '')
                self.is_active_checkbox.setChecked(supplier['is_active'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المورد: {e}")

    def save_supplier(self):
        """حفظ المورد"""
        # التحقق من البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المورد")
            return

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف")
            return

        try:
            name = self.name_edit.text().strip()
            company_name = self.company_edit.text().strip() or None
            phone = self.phone_edit.text().strip()
            address = self.address_edit.text().strip() or None
            email = self.email_edit.text().strip() or None
            is_active = self.is_active_checkbox.isChecked()

            if self.is_edit_mode:
                # تحديث المورد
                self.db_manager.execute_query("""
                    UPDATE suppliers
                    SET name = ?, company_name = ?, phone = ?, address = ?, email = ?, is_active = ?
                    WHERE id = ?
                """, (name, company_name, phone, address, email, is_active, self.supplier_id))
            else:
                # إضافة مورد جديد
                self.db_manager.execute_query("""
                    INSERT INTO suppliers (name, company_name, phone, address, email, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
                """, (name, company_name, phone, address, email, is_active))

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المورد: {e}")

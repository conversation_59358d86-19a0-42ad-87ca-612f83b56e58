#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام المُحدث
"""

import sys
import os
sys.path.append('src')

def test_treasury_transfer_interface():
    """اختبار واجهة نقل الخزينة"""
    
    print("🧪 اختبار واجهة نقل الخزينة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = EnhancedTreasuryTransferWindow(db, current_user)
        
        # فحص وجود دالة التحديث القوي
        if hasattr(window, 'force_refresh_interface'):
            print("✅ دالة التحديث القوي موجودة في واجهة نقل الخزينة")
            
            # اختبار الدالة
            window.force_refresh_interface()
            print("✅ دالة التحديث القوي تعمل")
            
            return True
        else:
            print("❌ دالة التحديث القوي غير موجودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة نقل الخزينة: {e}")
        return False

def test_currency_exchange_interface():
    """اختبار واجهة شراء الدولار"""
    
    print("\n🧪 اختبار واجهة شراء الدولار...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.currency_exchange_window import CurrencyExchangeWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = CurrencyExchangeWindow(db, current_user)
        
        # فحص وجود دالة التحديث القوي
        if hasattr(window, 'force_refresh_interface'):
            print("✅ دالة التحديث القوي موجودة في واجهة شراء الدولار")
            
            # اختبار الدالة
            window.force_refresh_interface()
            print("✅ دالة التحديث القوي تعمل")
            
            return True
        else:
            print("❌ دالة التحديث القوي غير موجودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة شراء الدولار: {e}")
        return False

def test_financial_reports_interface():
    """اختبار واجهة التقارير المالية"""
    
    print("\n🧪 اختبار واجهة التقارير المالية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.financial_reports_window import FinancialReportsWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = FinancialReportsWindow(db, current_user)
        
        # فحص التبويبات
        tabs_count = window.tabs.count()
        print(f"✅ واجهة التقارير المالية تحتوي على {tabs_count} تبويبات")
        
        # فحص أسماء التبويبات
        tab_names = []
        for i in range(tabs_count):
            tab_names.append(window.tabs.tabText(i))
        
        print(f"✅ التبويبات: {', '.join(tab_names)}")
        
        # فحص وجود الجداول
        if hasattr(window, 'cash_boxes_table') and hasattr(window, 'treasury_table') and hasattr(window, 'currency_table'):
            print("✅ جميع الجداول موجودة")
            return True
        else:
            print("❌ بعض الجداول مفقودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التقارير المالية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """اختبار تكامل الواجهة الرئيسية"""
    
    print("\n🧪 اختبار تكامل الواجهة الرئيسية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.main_window import MainWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة الرئيسية
        window = MainWindow(db, current_user)
        
        # فحص وجود دالة التقارير المالية
        if hasattr(window, 'open_financial_reports'):
            print("✅ دالة فتح التقارير المالية موجودة في الواجهة الرئيسية")
            return True
        else:
            print("❌ دالة فتح التقارير المالية غير موجودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        return False

def test_treasury_operations():
    """اختبار العمليات المالية"""
    
    print("\n🧪 اختبار العمليات المالية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة
        daily_syp = treasury_manager.get_total_daily_balance('SYP')
        main_syp = treasury_manager.get_main_balance('SYP')
        
        print(f"✅ الخزينة اليومية: {daily_syp:,} ل.س")
        print(f"✅ الخزينة الرئيسية: {main_syp:,} ل.س")
        
        # اختبار نقل صغير
        if daily_syp > 1000:
            test_amount = 1000
            print(f"🔄 اختبار نقل {test_amount:,} ل.س...")
            
            transfer_success = treasury_manager.transfer_from_total_daily_to_main(
                currency_type='SYP',
                amount=test_amount
            )
            
            if transfer_success:
                print("✅ عملية النقل تعمل بشكل صحيح")
                
                # فحص الأرصدة بعد النقل
                new_daily = treasury_manager.get_total_daily_balance('SYP')
                new_main = treasury_manager.get_main_balance('SYP')
                
                daily_change = daily_syp - new_daily
                main_change = new_main - main_syp
                
                if abs(daily_change - test_amount) < 1 and abs(main_change - test_amount) < 1:
                    print("✅ النقل دقيق 100%")
                    return True
                else:
                    print(f"⚠️ النقل غير دقيق: خصم {daily_change:,} إضافة {main_change:,}")
                    return False
            else:
                print("❌ فشل في عملية النقل")
                return False
        else:
            print("⚠️ لا يوجد رصيد كافي للاختبار")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العمليات المالية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار شامل للنظام المُحدث")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    tests = [
        ("واجهة نقل الخزينة", test_treasury_transfer_interface),
        ("واجهة شراء الدولار", test_currency_exchange_interface),
        ("واجهة التقارير المالية", test_financial_reports_interface),
        ("تكامل الواجهة الرئيسية", test_main_window_integration),
        ("العمليات المالية", test_treasury_operations),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  • {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! النظام يعمل بالكامل!")
        
        print("\n📋 المميزات المتاحة:")
        print("  ✅ نقل الخزينة مع تحديث فوري للواجهة")
        print("  ✅ شراء الدولار مع تحديث فوري للواجهة")
        print("  ✅ واجهة تقارير مالية شاملة")
        print("  ✅ تقارير إغلاق الصناديق ونقل الخزينة وشراء الدولار")
        print("  ✅ ملخص مالي شامل للأرصدة والعمليات")
        print("  ✅ فلترة التقارير بالتاريخ والمستخدم")
        print("  ✅ عمليات مالية دقيقة 100%")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. سجل دخول: admin / admin123")
        print("  3. استخدم 'نقل الخزينة' و 'شراء الدولار' - ستجد الواجهات تتحدث فوراً")
        print("  4. استخدم 'التقارير المالية' لرؤية جميع العمليات والأرصدة")
        
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("💡 قد تحتاج لفحص إضافي")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح قاعدة البيانات وإنشاء جدول المستخدمين
"""

import sys
import os
import sqlite3
import hashlib

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("=== إصلاح قاعدة البيانات ===")
    
    db_path = "data/company_system.db"
    
    # التأكد من وجود مجلد data
    if not os.path.exists("data"):
        os.makedirs("data")
        print("تم إنشاء مجلد data")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 1. التحقق من الجداول الموجودة
    print("\n1. الجداول الموجودة:")
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    table_names = [table[0] for table in tables]
    print(f"الجداول الموجودة: {table_names}")
    
    # 2. إنشاء جدول المستخدمين إذا لم يكن موجوداً
    if 'users' not in table_names:
        print("\n2. إنشاء جدول المستخدمين:")
        cursor.execute("""
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'employee', 'accountant')),
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول المستخدمين")
    else:
        print("\n2. جدول المستخدمين موجود بالفعل")
        
        # التحقق من بنية الجدول
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"أعمدة جدول المستخدمين: {column_names}")
        
        # إضافة عمود password_hash إذا لم يكن موجوداً
        if 'password_hash' not in column_names and 'password' in column_names:
            print("تحديث عمود password إلى password_hash...")
            
            # إنشاء جدول مؤقت
            cursor.execute("""
                CREATE TABLE users_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'employee', 'accountant')),
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """)
            
            # نسخ البيانات
            cursor.execute("""
                INSERT INTO users_temp (id, username, password_hash, full_name, email, role, is_active, created_at, last_login)
                SELECT id, username, password, full_name, email, role, is_active, created_at, last_login
                FROM users
            """)
            
            # حذف الجدول القديم وإعادة تسمية الجديد
            cursor.execute("DROP TABLE users")
            cursor.execute("ALTER TABLE users_temp RENAME TO users")
            print("✅ تم تحديث بنية جدول المستخدمين")
    
    # 3. التحقق من وجود المستخدم الافتراضي
    print("\n3. التحقق من المستخدم الافتراضي:")
    cursor.execute("SELECT * FROM users WHERE username = 'admin'")
    admin_user = cursor.fetchone()
    
    if not admin_user:
        print("إنشاء المستخدم الافتراضي admin...")
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute("""
            INSERT INTO users (username, password_hash, full_name, email, role, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        """, ("admin", admin_password, "المدير العام", "<EMAIL>", "admin", 1))
        print("✅ تم إنشاء المستخدم الافتراضي admin")
    else:
        print("✅ المستخدم الافتراضي admin موجود")
    
    # 4. عرض جميع المستخدمين
    print("\n4. جميع المستخدمين:")
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    
    # الحصول على أسماء الأعمدة
    cursor.execute("PRAGMA table_info(users)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]
    
    print(f"إجمالي المستخدمين: {len(users)}")
    for user in users:
        user_dict = dict(zip(column_names, user))
        print(f"   - ID: {user_dict['id']} | {user_dict['username']} | {user_dict['full_name']} | {user_dict['role']} | نشط: {user_dict['is_active']}")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("\n✅ تم إصلاح قاعدة البيانات بنجاح!")
    print("يمكنك الآن تسجيل الدخول باستخدام:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")

if __name__ == "__main__":
    fix_database()

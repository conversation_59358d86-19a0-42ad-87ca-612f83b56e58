# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QAction, QStatusBar, QLabel,
                            QToolBar, QPushButton, QFrame, QGridLayout,
                            QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor

# استيراد الوحدات الأساسية
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from utils.arabic_support import simple_arabic_text, create_arabic_font, apply_arabic_style
except ImportError:
    # في حالة عدم وجود الملف، استخدم دوال بديلة
    def simple_arabic_text(text):
        return text
    def create_arabic_font(size, bold=False):
        from PyQt5.QtGui import QFont
        font = QFont("Arial", size)
        font.setBold(bold)
        return font
    def apply_arabic_style(widget, size, bold=False):
        widget.setFont(create_arabic_font(size, bold))

# سيتم استيراد الواجهات عند الحاجة لتجنب مشاكل الاستيراد الدائري

class MainWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد وفتح الصندوق
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)
        self.treasury_manager.open_cash_box(user_id=current_user['id'])

        # إنشاء مدير المخزون الموحد
        from utils.unified_inventory_manager import UnifiedInventoryManager
        self.inventory_manager = UnifiedInventoryManager(db_manager)

        # إنشاء مدير التوافق وضمان التوافق بين الجداول
        from utils.system_compatibility_manager import SystemCompatibilityManager
        self.compatibility_manager = SystemCompatibilityManager(db_manager)
        self.compatibility_manager.ensure_system_compatibility()
        self.compatibility_manager.sync_categories_across_system()

        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        self.setup_notifications_timer()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget

            # الحصول على معلومات الشاشة
            screen = QDesktopWidget().screenGeometry()
            window = self.geometry()

            # حساب الموضع المركزي
            x = (screen.width() - window.width()) // 2
            y = (screen.height() - window.height()) // 2

            # تحريك النافذة للمركز
            self.move(x, y)

        except Exception as e:
            print(f"❌ خطأ في توسيط النافذة: {e}")
            # في حالة الخطأ، استخدم موضع افتراضي
            self.move(100, 100)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"برنامج أسامة لادارة شركات الإنترنت - {self.current_user['full_name']}")

        # تحديد حجم النافذة وتوسيطها
        self.resize(1200, 800)
        self.center_window()

        # تطبيق الخط العربي على النافذة الرئيسية
        apply_arabic_style(self, 10)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط الترحيب
        welcome_frame = self.create_welcome_frame()
        main_layout.addWidget(welcome_frame)

        # شريط الإشعارات
        notifications_bar = self.create_notifications_bar()
        main_layout.addWidget(notifications_bar)

        # الأزرار الرئيسية
        buttons_frame = self.create_main_buttons()
        main_layout.addWidget(buttons_frame)
        
        # إحصائيات سريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
    def create_welcome_frame(self):
        """إنشاء إطار الترحيب"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #3498db;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # رسالة الترحيب
        welcome_label = QLabel(f"مرحباً {self.current_user['full_name']}")
        apply_arabic_style(welcome_label, 14, bold=True)
        welcome_label.setStyleSheet("color: white;")
        
        # التاريخ والوقت
        self.datetime_label = QLabel()
        apply_arabic_style(self.datetime_label, 10)
        self.datetime_label.setStyleSheet("color: white;")
        self.datetime_label.setAlignment(Qt.AlignLeft)
        
        # تحديث التاريخ والوقت
        self.update_datetime()
        
        # مؤقت لتحديث الوقت
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # تحديث كل ثانية
        
        layout.addWidget(welcome_label)
        layout.addStretch()
        layout.addWidget(self.datetime_label)
        
        return frame

    def create_notifications_bar(self):
        """إنشاء شريط الإشعارات"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        layout = QHBoxLayout(frame)

        # أيقونة الإشعارات
        notifications_icon = QLabel("🔔")
        apply_arabic_style(notifications_icon, 16)

        # عداد الإشعارات
        self.notifications_count_label = QLabel("0")
        apply_arabic_style(self.notifications_count_label, 12, bold=True)
        self.notifications_count_label.setStyleSheet("color: #e74c3c; font-weight: bold;")

        # نص الإشعارات
        notifications_text = QLabel("إشعارات جديدة")
        apply_arabic_style(notifications_text, 10)

        # زر عرض الإشعارات
        view_notifications_button = QPushButton("عرض الإشعارات")
        apply_arabic_style(view_notifications_button, 9)
        view_notifications_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        # زر تحديث الإشعارات
        refresh_notifications_button = QPushButton("تحديث")
        apply_arabic_style(refresh_notifications_button, 9)
        refresh_notifications_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        layout.addWidget(notifications_icon)
        layout.addWidget(self.notifications_count_label)
        layout.addWidget(notifications_text)
        layout.addStretch()
        layout.addWidget(refresh_notifications_button)
        layout.addWidget(view_notifications_button)

        # ربط الأحداث
        view_notifications_button.clicked.connect(self.open_notifications)
        refresh_notifications_button.clicked.connect(self.update_notifications_count)

        # تحديث عداد الإشعارات
        self.update_notifications_count()

        return frame

    def create_main_buttons(self):
        """إنشاء الأزرار الرئيسية"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        # تعريف الأزرار
        buttons_data = [
            ("شحن رصيد", "#16a085", self.open_balance_charge),
            ("شراء دولار", "#e74c3c", self.open_currency_exchange),
            ("المصاريف", "#e74c3c", self.open_expenses),
            ("إغلاق الصندوق", "#f39c12", self.open_cash_box_closure),
            ("أمر صيانة", "#34495e", self.open_maintenance_order),
            ("تسليم للعمال", "#1abc9c", self.open_worker_delivery),
            ("التقارير", "#d35400", self.open_reports),
            ("التقارير المالية", "#8e44ad", self.open_financial_reports),
            ("الإعدادات", "#7f8c8d", self.open_settings),
        ]
        
        # إنشاء الأزرار
        row, col = 0, 0
        for text, color, callback in buttons_data:
            button = self.create_main_button(text, color, callback)
            layout.addWidget(button, row, col)
            
            col += 1
            if col >= 3:  # 3 أزرار في كل صف
                col = 0
                row += 1
                
        return frame
        
    def create_main_button(self, text, color, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton(text)
        apply_arabic_style(button, 11, bold=True)
        button.setMinimumSize(200, 80)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        
        button.clicked.connect(callback)
        return button
        
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # إحصائيات اليوم المحدثة
        today_stats = self.get_today_stats()

        stats_data = [
            ("عدد المشتركين الجدد", today_stats.get('new_subscribers', 0), "#3498db"),
            ("إجمالي المبيعات", f"{today_stats.get('total_sales', 0):,.0f} ل.س", "#27ae60"),
            ("إجمالي المصاريف", f"{today_stats.get('total_expenses', 0):,.0f} ل.س", "#e74c3c"),
            ("عدد المعاملات", today_stats.get('total_transactions', 0), "#17a2b8"),
            ("منتهية الصلاحية", today_stats.get('expired_subscriptions', 0), "#f39c12"),
        ]
        
        for title, value, color in stats_data:
            stat_widget = self.create_stat_widget(title, str(value), color)
            layout.addWidget(stat_widget)
            
        return frame
        
    def create_stat_widget(self, title, value, color):
        """إنشاء ويدجت إحصائية"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout(widget)

        title_label = QLabel(title)
        apply_arabic_style(title_label, 9)
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)

        value_label = QLabel(str(value))
        apply_arabic_style(value_label, 14, bold=True)
        value_label.setStyleSheet("color: white;")
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return widget
        
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")

        # قائمة العمليات
        operations_menu = menubar.addMenu("العمليات")

        # إضافة العمليات إلى القائمة
        operations_actions = [
            ("اشتراك جديد", self.open_new_subscription),
            ("تجديد باقة", self.open_package_renewal),
            ("تسليم راوتر", self.open_router_delivery),
            ("سند قبض", self.open_receipt),
            ("سند دفع", self.open_voucher),
            ("نقل الخزينة", self.open_enhanced_treasury_transfer),
            ("المشتريات", self.open_purchases),
        ]

        for text, callback in operations_actions:
            action = QAction(text, self)
            action.triggered.connect(callback)
            operations_menu.addAction(action)

        # قائمة الإدارة
        management_menu = menubar.addMenu("الإدارة")

        # إضافة واجهات الإدارة إلى القائمة
        management_actions = [
            ("إدارة المستخدمين", self.open_users_management),
            ("إدارة الموردين", self.open_suppliers_management),
            ("إدارة الموزعين", self.open_distributors_management),
            ("إدارة التصنيفات", self.open_categories_management),
            ("إدارة المنتجات", self.open_products_management),
            ("إدارة الوحدات", self.open_units_management),
            ("إدارة الباقات", self.open_packages_management),
            ("المخزون الموحد", self.open_unified_inventory),
            ("الجرد الشامل", self.open_inventory_audit),
            ("إدارة العمال", self.open_workers_management),
            ("إدارة المشتركين", self.open_subscribers_management),
        ]

        for text, callback in management_actions:
            action = QAction(text, self)
            action.triggered.connect(callback)
            management_menu.addAction(action)

        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        
        # أزرار شريط الأدوات
        toolbar_actions = [
            ("اشتراك جديد", self.open_new_subscription),
            ("تسليم راوتر", self.open_router_delivery),
            ("تجديد باقة", self.open_package_renewal),
        ]
        
        for text, callback in toolbar_actions:
            action = QAction(text, self)
            action.triggered.connect(callback)
            toolbar.addAction(action)
            
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        user_role = self.current_user.get('role', 'مستخدم')
        user_label = QLabel(f"المستخدم: {self.current_user['full_name']} ({user_role})")
        user_label.setFont(create_arabic_font(9))
        status_bar.addPermanentWidget(user_label)
        
    def setup_connections(self):
        """إعداد الاتصالات والمؤقتات"""
        # مؤقت تحديث التاريخ والوقت
        self.datetime_timer = QTimer()
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)  # تحديث كل ثانية

        # مؤقت تحديث الإحصائيات
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(30000)  # تحديث كل 30 ثانية

        # مؤقت تحديث الإشعارات
        self.notifications_timer = QTimer()
        self.notifications_timer.timeout.connect(self.update_notifications_count)
        self.notifications_timer.start(60000)  # تحديث كل دقيقة
        
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        current_datetime = QDateTime.currentDateTime()
        datetime_text = current_datetime.toString("yyyy-MM-dd hh:mm:ss")
        self.datetime_label.setText(datetime_text)
        
    def get_today_stats(self):
        """الحصول على إحصائيات اليوم من النظام الموحد"""
        try:
            # عدد المشتركين الجدد اليوم للمستخدم الحالي
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            new_subscribers = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM subscribers WHERE DATE(created_at) = ? AND created_by = ?",
                (today, self.current_user['id'])
            )

            # إجمالي المبيعات من النظام الموحد (اشتراك جديد + تسليم راوتر + تجديد باقة)
            print(f"=== حساب إجمالي المبيعات من النظام الموحد للمستخدم: {self.current_user['username']} ===")

            # قراءة المبيعات من الخزينة اليومية الموحدة
            daily_syp_balance = self.treasury_manager.get_daily_balance(
                user_id=self.current_user['id'],
                currency_type='SYP'
            )

            # المبيعات = الرصيد اليومي (يتضمن جميع العمليات بعد فتح الصندوق)
            total_sales_amount = max(0, daily_syp_balance)
            print(f"إجمالي المبيعات من الخزينة الموحدة: {total_sales_amount:,} ل.س")

            # تفصيل المبيعات حسب النوع (للعرض)
            sales_breakdown = self.get_sales_breakdown(today)

            # حساب المصاريف (افتراضياً صفر في النظام الموحد)
            total_expenses_amount = 0

            # عدد العمليات اليوم للمستخدم الحالي
            total_transactions = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM transactions WHERE DATE(created_at) = ? AND user_name = ?",
                (today, self.current_user['username'])
            )
            
            # عدد المعاملات بدلاً من الخزينة اليومية
            total_transactions_count = total_transactions['count'] if total_transactions else 0

            # حساب إجمالي المصاريف للمستخدم الحالي اليوم (بدون الرواتب)
            expenses_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE DATE(created_at) = ? AND user_name = ? AND expense_type != 'رواتب'
            """, (today, self.current_user['username']))

            total_expenses_amount = expenses_result['total'] if expenses_result else 0
            print(f"إجمالي المصاريف (بدون رواتب): {total_expenses_amount}")

            # الاشتراكات المنتهية
            expired_subscriptions = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM subscribers WHERE subscription_end_date < date('now')"
            )

            return {
                'new_subscribers': new_subscribers['count'] if new_subscribers else 0,
                'total_sales': total_sales_amount,
                'total_expenses': total_expenses_amount,
                'total_transactions': total_transactions_count,
                'expired_subscriptions': expired_subscriptions['count'] if expired_subscriptions else 0,
            }
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات: {e}")
            return {}

    def get_sales_breakdown(self, today):
        """الحصول على تفصيل المبيعات حسب النوع"""
        try:
            breakdown = {
                'new_subscriptions': 0,
                'router_deliveries': 0,
                'package_renewals': 0
            }

            # اشتراكات جديدة من جدول المعاملات
            new_subs = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'اشتراك جديد'
            """, (today, self.current_user.get('username', '')))

            if new_subs:
                breakdown['new_subscriptions'] = new_subs['total']

            # تسليم راوترات من جدول المعاملات
            router_deliveries = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'تسليم راوتر'
            """, (today, self.current_user.get('username', '')))

            if router_deliveries:
                breakdown['router_deliveries'] = router_deliveries['total']

            # تجديد باقات من جدول المعاملات
            renewals = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'تجديد باقة'
            """, (today, self.current_user.get('username', '')))

            if renewals:
                breakdown['package_renewals'] = renewals['total']

            print(f"📊 تفصيل المبيعات:")
            print(f"  • اشتراكات جديدة: {breakdown['new_subscriptions']:,} ل.س")
            print(f"  • تسليم راوترات: {breakdown['router_deliveries']:,} ل.س")
            print(f"  • تجديد باقات: {breakdown['package_renewals']:,} ل.س")

            return breakdown

        except Exception as e:
            print(f"❌ خطأ في تفصيل المبيعات: {e}")
            return {'new_subscriptions': 0, 'router_deliveries': 0, 'package_renewals': 0}

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل اللون إلى RGB وتغميقه
        if color.startswith('#'):
            color = color[1:]
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        
        return f"#{r:02x}{g:02x}{b:02x}"
        
    # معالجات الأزرار
    def open_new_subscription(self):
        """فتح نافذة الاشتراك الجديد"""
        try:
            from ui.new_subscription_window import NewSubscriptionWindow

            # إنشاء مدير الإعدادات بالمسار الصحيح
            from utils.config_manager import ConfigManager
            config_manager = ConfigManager(".")

            dialog = NewSubscriptionWindow(self.db_manager, self.inventory_manager, config_manager, self.current_user, self)
            dialog.subscription_added.connect(self.on_subscription_added)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة الاشتراك: {e}")
            import traceback
            traceback.print_exc()

    def on_subscription_added(self, subscriber_data):
        """معالجة إضافة اشتراك جديد"""
        # تحديث الإحصائيات
        self.update_stats()

        # إظهار رسالة نجح
        QMessageBox.information(self, "تم", f"تم إضافة المشترك {subscriber_data['name']} بنجاح")

    def update_stats(self):
        """تحديث الإحصائيات"""
        # إعادة تحميل الإحصائيات وتحديث العرض
        pass
        
    def open_router_delivery(self):
        """فتح نافذة تسليم الراوتر"""
        try:
            from ui.router_delivery_window import RouterDeliveryWindow
            dialog = RouterDeliveryWindow(self.db_manager, self.inventory_manager, self.treasury_manager, self.current_user, self)
            dialog.delivery_saved.connect(self.on_delivery_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة تسليم الراوتر: {e}")
            import traceback
            traceback.print_exc()

    def on_delivery_completed(self, delivery_data):
        """معالجة إكمال التسليم"""
        # تحديث الإحصائيات
        self.update_stats()

        # إظهار رسالة نجح
        QMessageBox.information(self, "تم", f"تم تسليم الراوتر للمشترك {delivery_data['subscriber_name']} بنجاح")
        
    def open_package_renewal(self):
        """فتح نافذة تجديد الباقة"""
        try:
            from ui.package_renewal_window import PackageRenewalWindow
            dialog = PackageRenewalWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.renewal_completed.connect(self.on_renewal_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة تجديد الباقة: {e}")

    def on_renewal_completed(self, renewal_data):
        """معالجة إكمال التجديد"""
        # تحديث الإحصائيات
        self.update_stats()

        # إظهار رسالة نجح
        QMessageBox.information(self, "تم", f"تم تجديد باقة المشترك {renewal_data['subscriber_name']} بنجاح")
        
    # تم إزالة دالة إغلاق الصندوق القديمة - استخدم open_cash_box_closure بدلاً منها
        
    def open_purchases(self):
        """فتح نافذة المشتريات"""
        try:
            from ui.purchases_window import PurchasesWindow
            dialog = PurchasesWindow(self.db_manager, self.inventory_manager, self.config_manager, self.current_user, self)
            dialog.purchase_completed.connect(self.on_purchase_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المشتريات: {e}")
            import traceback
            traceback.print_exc()

    def on_purchase_completed(self, purchase_data):
        """معالجة إكمال المشترى"""
        self.update_stats()
        QMessageBox.information(self, "تم", f"تم حفظ المشترى بقيمة {purchase_data.get('total', 0)} ل.س")

    def open_balance_charge(self):
        """فتح نافذة شحن الرصيد"""
        try:
            from ui.balance_charge_window import BalanceChargeWindow
            dialog = BalanceChargeWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.charge_completed.connect(self.on_charge_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة شحن الرصيد: {e}")

    def on_charge_completed(self, charge_data):
        """معالجة إكمال الشحن"""
        self.update_stats()
        QMessageBox.information(self, "تم", f"تم شحن {charge_data.get('charge_amount', 0)} ل.س لرصيد {charge_data.get('distributor_name', '')}")

    def open_users_management(self):
        """فتح نافذة إدارة المستخدمين"""
        # التحقق من الصلاحيات
        if self.current_user['role'] != 'admin':
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية لإدارة المستخدمين")
            return

        try:
            from ui.users_management_window import UsersManagementWindow
            dialog = UsersManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة المستخدمين: {e}")

    def open_currency_exchange(self):
        """فتح نافذة شراء الدولار - تحويل العملة"""
        try:
            from ui.currency_exchange_window import CurrencyExchangeWindow
            dialog = CurrencyExchangeWindow(self.db_manager, self.treasury_manager, self.current_user, self)
            dialog.exchange_completed.connect(self.on_exchange_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة شراء الدولار: {e}")
            import traceback
            traceback.print_exc()

    def open_treasury_transfer(self):
        """فتح نافذة نقل الخزينة"""
        try:
            from ui.treasury_transfer_window import TreasuryTransferWindow
            dialog = TreasuryTransferWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.transfer_completed.connect(self.on_transfer_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة نقل الخزينة: {e}")
            import traceback
            traceback.print_exc()

    def on_exchange_completed(self, exchange_data):
        """معالجة إكمال صرف العملة"""
        self.update_stats()

        # تحديث واجهات الخزينة المفتوحة
        self.refresh_treasury_windows()

        QMessageBox.information(self, "تم", f"تم صرف {exchange_data.get('syp_amount', 0):,} ل.س إلى ${exchange_data.get('usd_amount', 0):.2f}")

    def on_transfer_completed(self, transfer_data):
        """معالجة اكتمال عملية النقل"""
        print(f"تم نقل {transfer_data['amount']} {transfer_data['currency']} إلى الخزينة الرئيسية")
        self.update_stats()

    def refresh_treasury_windows(self):
        """تحديث واجهات الخزينة المفتوحة"""
        try:
            # البحث عن واجهات الخزينة المفتوحة وتحديثها
            for widget in QApplication.allWidgets():
                if hasattr(widget, 'refresh_treasury_data'):
                    widget.refresh_treasury_data()
                    print(f"🔄 تم تحديث واجهة: {widget.__class__.__name__}")
        except Exception as e:
            print(f"❌ خطأ في تحديث واجهات الخزينة: {e}")

    def open_enhanced_treasury_transfer(self):
        """فتح نافذة نقل الخزينة المحسنة - دعم العملات المتعددة"""
        try:
            from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
            dialog = EnhancedTreasuryTransferWindow(self.db_manager, self.current_user, self)
            dialog.transfer_completed.connect(self.on_transfer_completed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة نقل الخزينة المحسنة: {e}")
            import traceback
            traceback.print_exc()

    def on_transfer_completed(self, transfer_data):
        """معالجة إكمال نقل الخزينة"""
        self.update_stats()
        currency_symbol = "$" if transfer_data.get('currency') == 'USD' else "ل.س"
        QMessageBox.information(self, "تم", f"تم نقل {transfer_data.get('amount', 0):,.2f} {currency_symbol} إلى الخزينة الرئيسية")

    def open_notifications(self):
        """فتح نافذة الإشعارات"""
        try:
            from ui.notifications_window import NotificationsWindow
            dialog = NotificationsWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
            # تحديث عداد الإشعارات بعد إغلاق النافذة
            self.update_notifications_count()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة الإشعارات: {e}")

    def update_notifications_count(self):
        """تحديث عداد الإشعارات"""
        try:
            # حساب عدد الإشعارات العاجلة
            urgent_count = 0

            # الاشتراكات المنتهية
            expired_subscriptions = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count
                FROM subscribers
                WHERE subscription_end_date < date('now')
            """)
            if expired_subscriptions:
                urgent_count += expired_subscriptions['count']

            # المخزون المنخفض
            low_stock = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count
                FROM inventory i
                JOIN products p ON i.product_id = p.id
                WHERE i.quantity < 10
            """)
            if low_stock:
                urgent_count += low_stock['count']

            # تحديث العداد
            self.notifications_count_label.setText(str(urgent_count))

            # تغيير لون العداد حسب العدد
            if urgent_count > 0:
                self.notifications_count_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
            else:
                self.notifications_count_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 14px;")

        except Exception as e:
            print(f"خطأ في تحديث عداد الإشعارات: {e}")
            self.notifications_count_label.setText("!")
            self.notifications_count_label.setStyleSheet("color: #f39c12; font-weight: bold;")

    def update_stats(self):
        """تحديث الإحصائيات في الواجهة"""
        try:
            # إعادة إنشاء إطار الإحصائيات
            old_stats_frame = self.centralWidget().layout().itemAt(3).widget()
            new_stats_frame = self.create_stats_frame()

            # استبدال الإطار القديم بالجديد
            self.centralWidget().layout().replaceWidget(old_stats_frame, new_stats_frame)
            old_stats_frame.deleteLater()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def setup_notifications_timer(self):
        """إعداد مؤقت تحديث الإشعارات"""
        self.notifications_timer = QTimer()
        self.notifications_timer.timeout.connect(self.update_notifications_count)
        # تحديث كل 5 دقائق
        self.notifications_timer.start(300000)  # 300000 ميلي ثانية = 5 دقائق

    def open_worker_delivery(self):
        """فتح نافذة التسليم للعمال"""
        try:
            # محاولة استيراد مختلفة
            try:
                from .worker_delivery_window import WorkerDeliveryWindow
            except ImportError:
                from worker_delivery_window import WorkerDeliveryWindow

            dialog = WorkerDeliveryWindow(self.db_manager, self.inventory_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة تسليم للعمال: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص

    def open_inventory(self):
        """فتح نافذة الجرد"""
        QMessageBox.information(self, "قريباً", "ميزة جرد مخزون العمال قيد التطوير")

    def open_reports(self):
        """فتح نافذة التقارير"""
        try:
            # محاولة استيراد مختلفة
            try:
                from .reports_window import ReportsWindow
            except ImportError:
                from reports_window import ReportsWindow

            dialog = ReportsWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التقارير: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص

    def open_financial_reports(self):
        """فتح نافذة التقارير المالية"""
        try:
            from .financial_reports_window import FinancialReportsWindow

            dialog = FinancialReportsWindow(self.db_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التقارير المالية: {e}")
            print(f"تفاصيل الخطأ: {e}")
            import traceback
            traceback.print_exc()

    def open_workers_management(self):
        """فتح نافذة إدارة العمال"""
        try:
            from ui.workers_management_window import WorkersManagementWindow
            dialog = WorkersManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة العمال: {e}")

    def open_suppliers_management(self):
        """فتح نافذة إدارة الموردين"""
        try:
            from ui.suppliers_management_window import SuppliersManagementWindow
            dialog = SuppliersManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة الموردين: {e}")

    def open_distributors_management(self):
        """فتح نافذة إدارة الموزعين"""
        try:
            # محاولة استيراد مختلفة
            try:
                from .distributors_management_window import DistributorsManagementWindow
            except ImportError:
                from distributors_management_window import DistributorsManagementWindow

            dialog = DistributorsManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة الموزعين: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص
        
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            from ui.settings_window import SettingsWindow
            dialog = SettingsWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.settings_updated.connect(self.on_settings_updated)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة الإعدادات: {e}")

    def on_settings_updated(self):
        """معالجة تحديث الإعدادات"""
        QMessageBox.information(self, "تم", "تم تحديث الإعدادات بنجاح")

    def open_subscribers_management(self):
        """فتح نافذة إدارة المشتركين"""
        try:
            from ui.subscribers_management_window import SubscribersManagementWindow
            dialog = SubscribersManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة المشتركين: {e}")

    def open_products_management(self):
        """فتح نافذة إدارة المنتجات"""
        try:
            from ui.products_management_window import ProductsManagementWindow
            dialog = ProductsManagementWindow(self.db_manager, self.inventory_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة المنتجات: {e}")
            import traceback
            traceback.print_exc()

    def open_categories_management(self):
        """فتح نافذة إدارة التصنيفات"""
        try:
            from ui.categories_management_window import CategoriesManagementWindow
            dialog = CategoriesManagementWindow(self.db_manager, self.current_user, self)
            dialog.category_updated.connect(self.on_category_updated)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة التصنيفات: {e}")
            import traceback
            traceback.print_exc()

    def on_category_updated(self):
        """عند تحديث التصنيفات"""
        print("تم تحديث التصنيفات")

    def open_cash_box_closure(self):
        """فتح نافذة إغلاق الصندوق"""
        try:
            from ui.cash_box_closure_window import CashBoxClosureWindow
            dialog = CashBoxClosureWindow(self.db_manager, self.treasury_manager, self.current_user, self)
            dialog.cash_box_closed.connect(self.update_stats)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إغلاق الصندوق: {e}")
            import traceback
            traceback.print_exc()

    def open_unified_inventory(self):
        """فتح نافذة المخزون الموحد"""
        try:
            from ui.unified_inventory_window import UnifiedInventoryWindow
            dialog = UnifiedInventoryWindow(self.db_manager, self.current_user, self)
            dialog.inventory_updated.connect(self.update_stats)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المخزون الموحد: {e}")
            import traceback
            traceback.print_exc()

    def open_inventory_audit(self):
        """فتح نافذة الجرد الشامل"""
        try:
            from ui.inventory_audit_window import InventoryAuditWindow
            dialog = InventoryAuditWindow(self.db_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة الجرد الشامل: {e}")
            import traceback
            traceback.print_exc()

    def open_units_management(self):
        """فتح نافذة إدارة الوحدات"""
        try:
            from ui.units_management_window import UnitsManagementWindow
            dialog = UnitsManagementWindow(self.db_manager, self.inventory_manager, self.current_user, self)
            dialog.units_updated.connect(self.update_stats)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إدارة الوحدات: {e}")
            import traceback
            traceback.print_exc()
        
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل تريد إغلاق النظام؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق اتصال قاعدة البيانات
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
        else:
            event.ignore()

    # ==================== دوال الواجهات الجديدة ====================

    def open_expenses(self):
        """فتح واجهة المصاريف"""
        try:
            from ui.expenses_window import ExpensesWindow
            dialog = ExpensesWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح واجهة المصاريف: {e}")

    def open_receipt(self):
        """فتح واجهة سند قبض من الموزعين"""
        try:
            from ui.receipt_window import ReceiptWindow
            dialog = ReceiptWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح واجهة سند القبض: {e}")

    def open_voucher(self):
        """فتح واجهة سند دفع للموردين"""
        try:
            from ui.voucher_window import VoucherWindow
            dialog = VoucherWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح واجهة سند الدفع: {e}")

    def open_maintenance_order(self):
        """فتح واجهة أمر صيانة"""
        try:
            from ui.maintenance_order_window import MaintenanceOrderWindow
            dialog = MaintenanceOrderWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح واجهة أمر الصيانة: {e}")



    def open_packages_management(self):
        """فتح واجهة إدارة الباقات"""
        try:
            from ui.packages_management_window import PackagesManagementWindow
            dialog = PackagesManagementWindow(self.db_manager, self.config_manager, self.current_user, self)
            dialog.package_updated.connect(self.refresh_dashboard)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح واجهة إدارة الباقات: {e}")

    def refresh_dashboard(self):
        """تحديث لوحة المعلومات"""
        try:
            # يمكن إضافة تحديث للإحصائيات هنا
            print("تم تحديث لوحة المعلومات")
        except Exception as e:
            print(f"خطأ في تحديث لوحة المعلومات: {e}")

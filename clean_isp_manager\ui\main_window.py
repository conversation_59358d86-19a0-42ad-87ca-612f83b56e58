#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية الحديثة
"""

import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QScrollArea, QStatusBar, QMenuBar, QAction,
                             QMessageBox, QSplashScreen, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPainter, QColor

from config.app_config import AppConfig
from ui.interface_manager import InterfaceManager
from database.database_manager import DatabaseManager
from utils.window_utils import center_window, apply_modern_style

class MainWindow(QMainWindow):
    """الواجهة الرئيسية الحديثة"""
    
    # إشارات مخصصة
    interface_requested = pyqtSignal(str, str)  # category, module_name
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict):
        """
        تهيئة الواجهة الرئيسية
        
        Args:
            db_manager: مدير قاعدة البيانات
            current_user: المستخدم الحالي
        """
        super().__init__()
        
        self.db_manager = db_manager
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        self.interface_manager = InterfaceManager()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_connections()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        # بدء المؤقتات
        self.start_timers()
        
        self.logger.info(f"تم تشغيل الواجهة الرئيسية للمستخدم: {current_user['full_name']}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إعداد النافذة الرئيسية
        self.setWindowTitle(f"{AppConfig.APP_NAME} - {self.current_user['full_name']}")
        self.setMinimumSize(1000, 700)
        self.resize(*AppConfig.UI_CONFIG['window_size'])
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء منطقة المحتوى
        content_area = self.create_content_area()
        main_layout.addWidget(content_area, 1)
    
    def create_header(self) -> QFrame:
        """إنشاء منطقة الهيدر"""
        header = QFrame()
        header.setFixedHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-bottom: 2px solid #2c3e50;
            }
        """)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # اللوغو ومعلومات الشركة
        logo_section = self.create_logo_section()
        layout.addWidget(logo_section)
        
        layout.addStretch()
        
        # معلومات المستخدم
        user_section = self.create_user_section()
        layout.addWidget(user_section)
        
        return header
    
    def create_logo_section(self) -> QWidget:
        """إنشاء قسم اللوغو"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # اللوغو
        logo_label = QLabel()
        logo_pixmap = self.create_logo()
        logo_label.setPixmap(logo_pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        layout.addWidget(logo_label)
        
        # معلومات الشركة
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(10, 0, 0, 0)
        
        # اسم التطبيق
        app_name = QLabel(AppConfig.APP_NAME)
        app_name.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        info_layout.addWidget(app_name)
        
        # إصدار التطبيق
        version_label = QLabel(f"الإصدار {AppConfig.APP_VERSION}")
        version_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        info_layout.addWidget(version_label)
        
        layout.addLayout(info_layout)
        
        return widget
    
    def create_logo(self) -> QPixmap:
        """إنشاء لوغو التطبيق"""
        # إنشاء لوغو بسيط إذا لم يكن متوفراً
        logo_path = AppConfig.get_assets_path('logo.png')
        
        if os.path.exists(logo_path):
            return QPixmap(logo_path)
        else:
            # إنشاء لوغو افتراضي
            pixmap = QPixmap(60, 60)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # رسم دائرة خلفية
            painter.setBrush(QColor(255, 255, 255, 200))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(5, 5, 50, 50)
            
            # رسم رمز الشبكة
            painter.setPen(QColor(52, 152, 219, 255))
            painter.setBrush(QColor(52, 152, 219, 255))
            
            # رسم نقاط الشبكة
            points = [(15, 15), (45, 15), (30, 30), (15, 45), (45, 45)]
            for x, y in points:
                painter.drawEllipse(x-3, y-3, 6, 6)
            
            # رسم خطوط الاتصال
            painter.setPen(QColor(52, 152, 219, 150))
            painter.drawLine(15, 15, 45, 15)
            painter.drawLine(15, 15, 30, 30)
            painter.drawLine(45, 15, 30, 30)
            painter.drawLine(30, 30, 15, 45)
            painter.drawLine(30, 30, 45, 45)
            
            painter.end()
            
            return pixmap
    
    def create_user_section(self) -> QWidget:
        """إنشاء قسم معلومات المستخدم"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # اسم المستخدم
        user_name = QLabel(f"مرحباً، {self.current_user['full_name']}")
        user_name.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        layout.addWidget(user_name)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.time_label)
        
        return widget
    
    def create_content_area(self) -> QWidget:
        """إنشاء منطقة المحتوى"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)
        
        # تخطيط المحتوى
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # إنشاء بطاقات الواجهات
        self.create_interface_cards(content_layout)
        
        return scroll_area
    
    def create_interface_cards(self, layout: QVBoxLayout):
        """إنشاء بطاقات الواجهات"""
        
        categories = self.interface_manager.get_interface_categories()
        
        for category, category_info in categories.items():
            # إنشاء بطاقة الفئة
            category_card = self.create_category_card(category, category_info)
            layout.addWidget(category_card)
        
        # إضافة مساحة مرنة في النهاية
        layout.addStretch()
    
    def create_category_card(self, category: str, category_info: dict) -> QFrame:
        """إنشاء بطاقة فئة واجهة"""
        
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 10px;
                margin: 5px;
            }}
            QFrame:hover {{
                border-color: {category_info['color']};
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        # هيدر البطاقة
        header_layout = QHBoxLayout()
        
        # أيقونة الفئة
        icon_label = QLabel("🔧")  # يمكن استبدالها بأيقونة حقيقية
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {category_info['color']};
                min-width: 40px;
                max-width: 40px;
            }}
        """)
        header_layout.addWidget(icon_label)
        
        # اسم الفئة
        category_name = QLabel(category_info['name'])
        category_name.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        header_layout.addWidget(category_name)
        
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # أزرار الوحدات
        modules_layout = QGridLayout()
        modules_layout.setSpacing(10)
        
        modules = self.interface_manager.get_category_modules(category)
        
        for i, module in enumerate(modules):
            row = i // 3
            col = i % 3
            
            module_btn = self.create_module_button(category, module, category_info['color'])
            modules_layout.addWidget(module_btn, row, col)
        
        layout.addLayout(modules_layout)
        
        return card
    
    def create_module_button(self, category: str, module: dict, color: str) -> QPushButton:
        """إنشاء زر وحدة"""
        
        btn = QPushButton(module['name'])
        btn.setMinimumHeight(50)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        """)
        
        # ربط الزر بالإشارة
        btn.clicked.connect(lambda: self.open_interface(category, module['name']))
        
        return btn
    
    def darken_color(self, color: str, factor: float = 0.2) -> str:
        """تغميق لون"""
        color = QColor(color)
        h, s, v, a = color.getHsv()
        v = max(0, int(v * (1 - factor)))
        color.setHsv(h, s, v, a)
        return color.name()
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # نسخة احتياطية
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # تسجيل الخروج
        logout_action = QAction('تسجيل الخروج', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        # إغلاق
        exit_action = QAction('إغلاق', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        # حول البرنامج
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # رسالة الترحيب
        self.status_bar.showMessage(f"مرحباً بك في {AppConfig.APP_NAME}")
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.interface_requested.connect(self.handle_interface_request)
    
    def start_timers(self):
        """بدء المؤقتات"""
        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # كل ثانية
        
        # تحديث الوقت الأولي
        self.update_time()
    
    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.setText(current_time)
    
    def open_interface(self, category: str, module_name: str):
        """فتح واجهة معينة"""
        try:
            self.status_bar.showMessage(f"جاري تحميل {module_name}...")
            
            # تحميل الواجهة
            interface = self.interface_manager.load_interface_module(
                category, module_name, self.db_manager, self.current_user, self
            )
            
            if interface:
                # عرض الواجهة
                if isinstance(interface, QMainWindow):
                    interface.show()
                else:
                    interface.exec_()
                
                self.status_bar.showMessage(f"تم تحميل {module_name} بنجاح")
            else:
                self.status_bar.showMessage(f"فشل في تحميل {module_name}")
                
        except Exception as e:
            self.logger.error(f"خطأ في فتح الواجهة {module_name}: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح الواجهة: {e}")
            self.status_bar.showMessage("جاهز")
    
    def handle_interface_request(self, category: str, module_name: str):
        """معالجة طلب فتح واجهة"""
        self.open_interface(category, module_name)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.db"
            backup_path = AppConfig.get_backup_path(backup_filename)
            
            if self.db_manager.backup_database(backup_path):
                QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            # إعادة تشغيل نافذة تسجيل الدخول
            from ui.login_window import LoginWindow
            login_window = LoginWindow(self.db_manager)
            login_window.show()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
        <h2>{AppConfig.APP_NAME}</h2>
        <p><b>الإصدار:</b> {AppConfig.APP_VERSION}</p>
        <p><b>المطور:</b> {AppConfig.APP_AUTHOR}</p>
        <p><b>الوصف:</b> {AppConfig.APP_DESCRIPTION}</p>
        <br>
        <p>نظام شامل ومتطور لإدارة شركات الإنترنت</p>
        <p>مع واجهة مستخدم حديثة وقابلية توسع عالية</p>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "تأكيد الإغلاق", "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إيقاف المؤقتات
            if hasattr(self, 'time_timer'):
                self.time_timer.stop()
            
            # إغلاق قاعدة البيانات
            if self.db_manager:
                self.db_manager.close()
            
            event.accept()
        else:
            event.ignore()

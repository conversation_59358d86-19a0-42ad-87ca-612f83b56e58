#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جدول المستخدمين
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def check_users_table():
    """فحص جدول المستخدمين"""
    
    print("🔍 فحص جدول المستخدمين...")
    
    # البحث عن قاعدة البيانات
    possible_paths = [
        "data/company_system.db",
        "company_system.db",
        "src/data/company_system.db"
    ]
    
    db_path = None
    for path in possible_paths:
        if Path(path).exists():
            db_path = path
            break
    
    if not db_path:
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    print(f"📂 قاعدة البيانات: {db_path}")
    
    try:
        db_manager = DatabaseManager(db_path)
        
        # فحص الجداول الموجودة
        tables = db_manager.fetch_all("""
            SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
        """)
        
        print(f"\n📋 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  • {table['name']}")
        
        # فحص جدول المستخدمين
        if any(table['name'] == 'users' for table in tables):
            print(f"\n✅ جدول المستخدمين موجود")
            
            # بنية الجدول
            table_info = db_manager.fetch_all("PRAGMA table_info(users)")
            print(f"\n📊 بنية جدول المستخدمين:")
            for col in table_info:
                pk = " (PRIMARY KEY)" if col[5] else ""
                print(f"  • {col[1]} - {col[2]}{pk}")
            
            # عدد المستخدمين
            count = db_manager.fetch_one("SELECT COUNT(*) as count FROM users")
            print(f"\n👥 عدد المستخدمين: {count['count'] if count else 0}")
            
            # عينة من البيانات
            if count and count['count'] > 0:
                users = db_manager.fetch_all("SELECT id, username, full_name, role, is_active FROM users LIMIT 3")
                print(f"\n📝 عينة من المستخدمين:")
                for user in users:
                    status = "نشط" if user['is_active'] else "معطل"
                    print(f"  • ID: {user['id']} | {user['username']} ({user['full_name']}) - {user['role']} - {status}")
            
        else:
            print(f"\n❌ جدول المستخدمين غير موجود")
            
            # محاولة إنشاء الجدول
            print("🔧 محاولة إنشاء جدول المستخدمين...")
            db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    role TEXT DEFAULT 'employee',
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """)
            
            # إضافة مستخدم admin إذا لم يكن موجود
            admin_exists = db_manager.fetch_one("SELECT id FROM users WHERE username = 'admin'")
            if not admin_exists:
                import hashlib
                password_hash = hashlib.sha256('123'.encode()).hexdigest()
                db_manager.execute_query("""
                    INSERT INTO users (username, password_hash, full_name, role, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, ('admin', password_hash, 'المدير العام', 'admin', 1))
                print("✅ تم إنشاء مستخدم admin")
            
            print("✅ تم إنشاء جدول المستخدمين")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    check_users_table()

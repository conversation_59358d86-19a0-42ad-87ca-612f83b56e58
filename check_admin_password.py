#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وإصلاح كلمة مرور المدير
"""

import sqlite3
import hashlib

def check_and_fix_admin_password():
    """فحص وإصلاح كلمة مرور المدير"""
    print("=== فحص كلمة مرور المدير ===")
    
    db_path = "data/company_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. البحث عن المستخدم admin
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"✅ تم العثور على المستخدم admin:")
            print(f"   ID: {admin_user['id']}")
            print(f"   اسم المستخدم: {admin_user['username']}")
            print(f"   الاسم الكامل: {admin_user['full_name']}")
            print(f"   الدور: {admin_user['role']}")
            print(f"   نشط: {admin_user['is_active']}")
            print(f"   كلمة المرور المشفرة: {admin_user['password_hash'][:20]}...")
            
            # 2. اختبار كلمات المرور المختلفة
            test_passwords = ["admin123", "admin", "123456", "password"]
            
            print("\n🔐 اختبار كلمات المرور:")
            for password in test_passwords:
                hashed = hashlib.sha256(password.encode()).hexdigest()
                if hashed == admin_user['password_hash']:
                    print(f"✅ كلمة المرور الصحيحة: {password}")
                    return password
                else:
                    print(f"❌ كلمة المرور خاطئة: {password}")
            
            # 3. إعادة تعيين كلمة المرور إلى admin123
            print("\n🔧 إعادة تعيين كلمة المرور إلى admin123...")
            new_password = "admin123"
            new_hash = hashlib.sha256(new_password.encode()).hexdigest()
            
            cursor.execute("""
                UPDATE users 
                SET password_hash = ? 
                WHERE username = 'admin'
            """, (new_hash,))
            
            conn.commit()
            print("✅ تم تحديث كلمة المرور بنجاح!")
            
            # 4. التحقق من التحديث
            cursor.execute("SELECT password_hash FROM users WHERE username = 'admin'")
            updated_user = cursor.fetchone()
            
            if updated_user['password_hash'] == new_hash:
                print("✅ تم التحقق من التحديث")
                print(f"📋 بيانات الدخول الجديدة:")
                print(f"   اسم المستخدم: admin")
                print(f"   كلمة المرور: {new_password}")
                return new_password
            else:
                print("❌ فشل في التحقق من التحديث")
                
        else:
            print("❌ لم يتم العثور على المستخدم admin")
            
            # إنشاء المستخدم admin
            print("🔧 إنشاء المستخدم admin...")
            password = "admin123"
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, ("admin", password_hash, "المدير العام", "<EMAIL>", "admin", 1))
            
            conn.commit()
            print("✅ تم إنشاء المستخدم admin")
            print(f"📋 بيانات الدخول:")
            print(f"   اسم المستخدم: admin")
            print(f"   كلمة المرور: {password}")
            return password
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

if __name__ == "__main__":
    check_and_fix_admin_password()

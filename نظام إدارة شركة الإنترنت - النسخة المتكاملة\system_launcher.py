# -*- coding: utf-8 -*-
"""
نظام إدارة شركة الإنترنت - النسخة المتكاملة
Internet Company Management System - Integrated Version

مطور بواسطة: Augment Agent
تاريخ الإنشاء: 2025-07-14
النسخة: 2.0.0 - النسخة المتكاملة

المميزات الجديدة:
- إصلاح مشاكل إغلاق الصندوق وعرض المقبوضات والمصاريف
- ضمان عدم اختلاط الصناديق
- إصلاح واجهة تسليم الراوتر
- إصلاح مشكلة عدم ظهور المستخدمين الجدد
- إدارة مخزون متكاملة (تسليم راوتر يخرج من المخزون، كبل من مخزون العامل)
- رأس فاتورة قابل للتخصيص
- عرض اسم المستخدم في أسفل الفاتورة
- تنظيم القوائم (الإدارة، العمليات)
"""

import sys
import os
import traceback
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_components():
    """اختبار مكونات النظام"""
    print("🧪 اختبار مكونات النظام...")
    
    try:
        # اختبار الوحدات الأساسية
        print("✅ الوحدات الأساسية")
        
        # اختبار قاعدة البيانات
        from database.database_manager import DatabaseManager
        print("✅ قاعدة البيانات")
        
        # اختبار الدعم العربي
        from utils.arabic_support import simple_arabic_text, create_arabic_font
        print("✅ الدعم العربي")
        
        # اختبار إدارة التكوين
        from config.config_manager import ConfigManager
        print("✅ إدارة التكوين")
        
        # اختبار الواجهات
        from ui.main_window import MainWindow
        from ui.login_window import LoginWindow
        print("✅ الواجهات الرئيسية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def initialize_system():
    """تهيئة النظام"""
    print("🔧 تهيئة النظام...")
    
    try:
        # تهيئة قاعدة البيانات
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
            
        print("✅ تم تهيئة قاعدة البيانات")
        
        # تهيئة إدارة التكوين
        from config.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("✅ تم تهيئة إدارة التكوين")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التهيئة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت - النسخة المتكاملة")
    print("=" * 70)
    
    # اختبار المكونات
    if not test_components():
        print("❌ فشل في اختبار المكونات")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ نجح اختبار جميع المكونات")
    
    # تهيئة النظام
    if not initialize_system():
        print("❌ فشل في تهيئة النظام")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ تم تهيئة النظام بنجاح")
    
    try:
        # تشغيل التطبيق
        from PyQt5.QtWidgets import QApplication
        from ui.login_window import LoginWindow
        from database.database_manager import DatabaseManager
        from config.config_manager import ConfigManager
        
        print("🚀 بدء تشغيل التطبيق...")
        
        app = QApplication(sys.argv)
        
        # إعداد الخط العربي للتطبيق
        from utils.arabic_support import setup_application_font
        setup_application_font(app)
        
        # إنشاء المدراء
        db_manager = DatabaseManager()
        config_manager = ConfigManager()
        
        # فتح نافذة تسجيل الدخول
        login_window = LoginWindow(db_manager, config_manager)
        
        if login_window.exec_() == login_window.Accepted:
            current_user = login_window.get_current_user()
            print(f"✅ تم تسجيل الدخول: {current_user['full_name']} ({current_user['role']})")
            
            # فتح النافذة الرئيسية
            from ui.main_window import MainWindow
            main_window = MainWindow(db_manager, config_manager, current_user)
            main_window.show()
            
            print("✅ تم فتح النافذة الرئيسية")
            print("🎉 النظام جاهز للاستخدام!")
            
            # تشغيل التطبيق
            sys.exit(app.exec_())
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

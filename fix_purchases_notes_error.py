#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح خطأ حفظ المشتريات - مشكلة notes
"""

import sys
import os
sys.path.append('src')

def check_purchases_table_structure():
    """فحص بنية جدول المشتريات"""
    
    print("🔍 فحص بنية جدول المشتريات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص بنية جدول purchases
        columns = db.fetch_all("PRAGMA table_info(purchases)")
        
        print(f"📊 أعمدة جدول purchases ({len(columns)}):")
        for col in columns:
            print(f"  • {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4] if col[4] else 'None'}")
        
        # فحص وجود حقل notes
        notes_column = [col for col in columns if col[1] == 'notes']
        if notes_column:
            print("✅ حقل notes موجود")
        else:
            print("❌ حقل notes مفقود")
            return False, columns
        
        # فحص بنية جدول stock_movements
        stock_columns = db.fetch_all("PRAGMA table_info(stock_movements)")
        
        print(f"\n📊 أعمدة جدول stock_movements ({len(stock_columns)}):")
        for col in stock_columns:
            print(f"  • {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4] if col[4] else 'None'}")
        
        # فحص وجود حقل notes في stock_movements
        stock_notes_column = [col for col in stock_columns if col[1] == 'notes']
        if stock_notes_column:
            print("✅ حقل notes موجود في stock_movements")
        else:
            print("❌ حقل notes مفقود في stock_movements")
        
        return True, columns
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def fix_purchases_table():
    """إصلاح جدول المشتريات"""
    
    print("\n🔧 إصلاح جدول المشتريات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # إضافة حقل notes إذا كان مفقوداً
        try:
            db.execute_query("ALTER TABLE purchases ADD COLUMN notes TEXT")
            print("✅ تم إضافة حقل notes لجدول purchases")
        except Exception as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️ حقل notes موجود بالفعل في جدول purchases")
            else:
                print(f"⚠️ خطأ في إضافة حقل notes: {e}")
        
        # إضافة حقل notes لجدول stock_movements إذا كان مفقوداً
        try:
            db.execute_query("ALTER TABLE stock_movements ADD COLUMN notes TEXT")
            print("✅ تم إضافة حقل notes لجدول stock_movements")
        except Exception as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️ حقل notes موجود بالفعل في جدول stock_movements")
            else:
                print(f"⚠️ خطأ في إضافة حقل notes لجدول stock_movements: {e}")
        
        # إضافة حقل user_id لجدول stock_movements إذا كان مفقوداً
        try:
            db.execute_query("ALTER TABLE stock_movements ADD COLUMN user_id INTEGER")
            print("✅ تم إضافة حقل user_id لجدول stock_movements")
        except Exception as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️ حقل user_id موجود بالفعل في جدول stock_movements")
            else:
                print(f"⚠️ خطأ في إضافة حقل user_id لجدول stock_movements: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول المشتريات: {e}")
        return False

def test_purchase_save():
    """اختبار حفظ المشتريات"""
    
    print("\n🧪 اختبار حفظ المشتريات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # بيانات مشترى تجريبي
        test_purchase = {
            'supplier_id': 1,
            'supplier_name': 'مورد تجريبي',
            'date': '2025-01-17',
            'invoice_number': 'TEST-001',
            'payment_method': 'نقدي',
            'subtotal': 100000,
            'tax_rate': 0,
            'tax_amount': 0,
            'total': 100000,
            'notes': 'مشترى تجريبي للاختبار'
        }
        
        print(f"📝 اختبار حفظ مشترى:")
        print(f"  • المورد: {test_purchase['supplier_name']}")
        print(f"  • رقم الفاتورة: {test_purchase['invoice_number']}")
        print(f"  • الإجمالي: {test_purchase['total']:,} ل.س")
        print(f"  • الملاحظات: {test_purchase['notes']}")
        
        # محاولة حفظ المشترى
        result = db.execute_query("""
            INSERT INTO purchases (supplier_id, supplier_name, purchase_date, invoice_number,
                                 payment_method, subtotal, tax_rate, tax_amount, total, notes, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_purchase['supplier_id'], test_purchase['supplier_name'], test_purchase['date'],
            test_purchase['invoice_number'], test_purchase['payment_method'], test_purchase['subtotal'],
            test_purchase['tax_rate'], test_purchase['tax_amount'], test_purchase['total'], test_purchase['notes'], 1
        ))
        
        if result:
            purchase_id = result.lastrowid
            print(f"✅ تم حفظ المشترى بنجاح - ID: {purchase_id}")
            
            # اختبار تسجيل حركة المخزون
            test_stock_movement = {
                'product_id': 1,
                'quantity_change': 10,
                'operation_type': 'purchase',
                'new_stock': 50,
                'notes': f"شراء من المورد - فاتورة {test_purchase['invoice_number']}",
                'user_id': 1
            }
            
            stock_result = db.execute_query("""
                INSERT INTO stock_movements (product_id, quantity_change, operation_type, new_stock, notes, user_id)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                test_stock_movement['product_id'], test_stock_movement['quantity_change'],
                test_stock_movement['operation_type'], test_stock_movement['new_stock'],
                test_stock_movement['notes'], test_stock_movement['user_id']
            ))
            
            if stock_result:
                print("✅ تم تسجيل حركة المخزون بنجاح")
            else:
                print("❌ فشل في تسجيل حركة المخزون")
                return False
            
            # حذف البيانات التجريبية
            db.execute_query("DELETE FROM purchases WHERE id = ?", (purchase_id,))
            db.execute_query("DELETE FROM stock_movements WHERE id = ?", (stock_result.lastrowid,))
            print("🗑️ تم حذف البيانات التجريبية")
            
            return True
        else:
            print("❌ فشل في حفظ المشترى")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ المشتريات: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_purchases_window_code():
    """فحص كود واجهة المشتريات"""
    
    print("\n🔍 فحص كود واجهة المشتريات...")
    
    try:
        # قراءة ملف واجهة المشتريات
        with open('src/ui/purchases_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن المشاكل المحتملة
        issues = []
        
        # فحص استعلام INSERT
        if 'INSERT INTO purchases' in content:
            print("✅ استعلام INSERT موجود")
        else:
            issues.append("استعلام INSERT مفقود")
        
        # فحص حقل notes
        if 'notes' in content:
            print("✅ حقل notes مستخدم في الكود")
        else:
            issues.append("حقل notes غير مستخدم")
        
        # فحص دالة save_purchase
        if 'def save_purchase(self):' in content:
            print("✅ دالة save_purchase موجودة")
        else:
            issues.append("دالة save_purchase مفقودة")
        
        # فحص معالجة الأخطاء
        if 'except Exception as e:' in content:
            print("✅ معالجة الأخطاء موجودة")
        else:
            issues.append("معالجة الأخطاء مفقودة")
        
        if issues:
            print("❌ مشاكل في الكود:")
            for issue in issues:
                print(f"  • {issue}")
            return False
        else:
            print("✅ الكود يبدو سليماً")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الكود: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح خطأ حفظ المشتريات - مشكلة notes")
    print("=" * 60)
    
    # فحص بنية الجدول
    table_ok, columns = check_purchases_table_structure()
    
    # إصلاح الجدول
    if not table_ok:
        fix_success = fix_purchases_table()
    else:
        fix_success = True
    
    # فحص الكود
    code_ok = check_purchases_window_code()
    
    # اختبار الحفظ
    if fix_success:
        save_test = test_purchase_save()
    else:
        save_test = False
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"  • بنية الجدول: {'✅ سليمة' if table_ok else '❌ تحتاج إصلاح'}")
    print(f"  • إصلاح الجدول: {'✅ نجح' if fix_success else '❌ فشل'}")
    print(f"  • فحص الكود: {'✅ سليم' if code_ok else '❌ يحتاج مراجعة'}")
    print(f"  • اختبار الحفظ: {'✅ نجح' if save_test else '❌ فشل'}")
    
    if all([table_ok or fix_success, code_ok, save_test]):
        print("\n🎉 تم إصلاح مشكلة حفظ المشتريات!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إضافة حقل notes لجدول purchases")
        print("  ✅ إضافة حقل notes لجدول stock_movements")
        print("  ✅ إضافة حقل user_id لجدول stock_movements")
        print("  ✅ اختبار حفظ المشتريات نجح")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. افتح واجهة المشتريات")
        print("  3. أضف مشترى جديد")
        print("  4. اضغط 'حفظ المشترى' - يجب أن يعمل الآن!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")
        
        if not (table_ok or fix_success):
            print("  🔧 مراجعة بنية جدول المشتريات")
        if not code_ok:
            print("  🔧 مراجعة كود واجهة المشتريات")
        if not save_test:
            print("  🔧 مراجعة عملية حفظ المشتريات")

if __name__ == "__main__":
    main()

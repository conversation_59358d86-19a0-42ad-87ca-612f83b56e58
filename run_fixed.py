#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام مع إصلاح مشكلة PyQt5
Run System with PyQt5 Fix
"""

import sys
import os
from pathlib import Path

def fix_qt_plugin_path():
    """إصلاح مسار Qt plugins"""
    try:
        import PyQt5
        qt_plugin_path = Path(PyQt5.__file__).parent / "Qt5" / "plugins"
        if qt_plugin_path.exists():
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qt_plugin_path)
            print(f"✅ تم تعيين مسار Qt plugins: {qt_plugin_path}")
        else:
            # جرب مسار آخر
            qt_plugin_path = Path(PyQt5.__file__).parent / "Qt" / "plugins"
            if qt_plugin_path.exists():
                os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qt_plugin_path)
                print(f"✅ تم تعيين مسار Qt plugins: {qt_plugin_path}")
    except Exception as e:
        print(f"⚠️ تحذير في إعداد Qt: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح إعدادات PyQt5...")
    
    # إصلاح مسار Qt plugins
    fix_qt_plugin_path()
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        print("📦 تحميل المكتبات...")
        from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("📁 تحميل وحدات النظام...")
        from src.database.database_manager import DatabaseManager
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        
        print("✅ تم تحميل جميع الوحدات بنجاح")
        
        # إنشاء التطبيق
        print("🚀 بدء تشغيل التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        if setup_arabic_support(app):
            print("✅ تم إعداد الدعم العربي")
        
        # اختيار مجلد المشروع
        print("📂 اختيار مجلد البيانات...")
        
        # استخدام مجلد افتراضي للاختبار
        project_dir = project_root / "data"
        project_dir.mkdir(exist_ok=True)
        
        print(f"📁 مجلد البيانات: {project_dir}")
        
        # إنشاء مدير الإعدادات
        config_manager = ConfigManager(str(project_dir))
        print("✅ تم إنشاء مدير الإعدادات")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager(str(project_dir))
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # تهيئة قاعدة البيانات
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
            
        print("✅ تم تهيئة قاعدة البيانات")
        
        # عرض نافذة تسجيل الدخول
        print("🔐 عرض نافذة تسجيل الدخول...")
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() != login_window.Accepted:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
        current_user = login_window.get_current_user()
        print(f"✅ تم تسجيل الدخول: {current_user['full_name']}")
        
        # عرض النافذة الرئيسية
        print("🏠 عرض النافذة الرئيسية...")
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        print("📋 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        # تشغيل حلقة الأحداث
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

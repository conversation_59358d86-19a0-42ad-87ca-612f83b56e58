#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة تسجيل الدخول
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
src_path = project_root / "src"

sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# تجاهل تحذيرات Qt
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

def test_login_gui():
    """اختبار واجهة تسجيل الدخول"""
    print("=== اختبار واجهة تسجيل الدخول ===")
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        print("✅ PyQt5")
        
        # استيراد وحدات النظام
        from src.database.database_manager import DatabaseManager
        from src.utils.arabic_support import setup_arabic_support
        from src.ui.login_window import LoginWindow
        print("✅ وحدات النظام")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        print("✅ الدعم العربي")
        
        # إنشاء قاعدة البيانات
        data_dir = project_root / "data"
        db_manager = DatabaseManager(str(data_dir))
        print("✅ قاعدة البيانات")
        
        # عرض نافذة تسجيل الدخول
        print("🔐 فتح نافذة تسجيل الدخول...")
        print("📋 استخدم البيانات التالية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 50)
        
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() == login_window.Accepted:
            current_user = login_window.get_current_user()
            print(f"✅ تم تسجيل الدخول بنجاح!")
            print(f"   مرحباً {current_user['full_name']}")
            
            QMessageBox.information(None, "نجح", f"مرحباً {current_user['full_name']}\nتم تسجيل الدخول بنجاح!")
            
        else:
            print("❌ تم إلغاء تسجيل الدخول")
        
        return 0
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_login_gui())

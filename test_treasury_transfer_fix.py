#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح عملية نقل الخزينة
"""

import sys
import os
sys.path.append('src')

def test_treasury_transfer_function():
    """اختبار دالة نقل الخزينة الجديدة"""
    
    print("🧪 اختبار دالة نقل الخزينة الجديدة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة قبل النقل
        print("\n💰 الأرصدة قبل النقل:")
        daily_before = treasury_manager.get_total_daily_balance('SYP')
        main_before = treasury_manager.get_main_balance('SYP')
        print(f"  • الخزينة اليومية: {daily_before:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_before:,} ل.س")
        
        if daily_before < 50000:
            print("⚠️ الرصيد اليومي أقل من 50,000 ل.س - لا يمكن اختبار النقل")
            return False
        
        # تنفيذ عملية نقل تجريبية
        print("\n🔄 تنفيذ عملية نقل تجريبية:")
        transfer_amount = 50000
        
        print(f"  • نقل: {transfer_amount:,} ل.س من اليومية للرئيسية")
        
        # تنفيذ النقل
        success = treasury_manager.transfer_from_total_daily_to_main(
            currency_type='SYP',
            amount=transfer_amount
        )
        
        if success:
            print("  ✅ تم النقل بنجاح")
            
            # فحص الأرصدة بعد النقل
            print("\n💰 الأرصدة بعد النقل:")
            daily_after = treasury_manager.get_total_daily_balance('SYP')
            main_after = treasury_manager.get_main_balance('SYP')
            print(f"  • الخزينة اليومية: {daily_after:,} ل.س")
            print(f"  • الخزينة الرئيسية: {main_after:,} ل.س")
            
            # التحقق من صحة النقل
            daily_diff = daily_before - daily_after
            main_diff = main_after - main_before
            
            print(f"\n📊 التغييرات:")
            print(f"  • خصم من اليومية: {daily_diff:,} ل.س")
            print(f"  • إضافة للرئيسية: {main_diff:,} ل.س")
            
            if abs(daily_diff - transfer_amount) < 1 and abs(main_diff - transfer_amount) < 1:
                print("  ✅ النقل صحيح")
                return True
            else:
                print("  ❌ خطأ في النقل")
                print(f"     متوقع: {transfer_amount:,} ل.س")
                print(f"     يومي: {daily_diff:,} ل.س، رئيسي: {main_diff:,} ل.س")
                return False
        else:
            print("  ❌ فشل في النقل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة النقل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_interface():
    """اختبار واجهة نقل الخزينة"""
    
    print("\n🧪 اختبار واجهة نقل الخزينة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        print("🖥️ إنشاء واجهة نقل الخزينة...")
        window = EnhancedTreasuryTransferWindow(db, current_user)
        
        # فحص الأرصدة المعروضة
        displayed_daily = window.daily_syp_label.text()
        displayed_main = window.main_syp_label.text()
        
        print(f"📺 الأرصدة المعروضة:")
        print(f"  • الخزينة اليومية: {displayed_daily}")
        print(f"  • الخزينة الرئيسية: {displayed_main}")
        
        # فحص إعدادات الواجهة
        max_amount = window.amount_spin.maximum()
        print(f"  • الحد الأقصى للنقل: {max_amount:,}")
        
        if max_amount > 0:
            print("✅ الواجهة تعرض رصيد متاح للنقل")
            
            # فحص العملة المحددة
            selected_currency = window.currency_combo.currentData()
            print(f"  • العملة المحددة: {selected_currency}")
            
            if selected_currency == "SYP":
                print("✅ العملة الافتراضية صحيحة")
                return True
            else:
                print("⚠️ العملة الافتراضية غير متوقعة")
                return False
        else:
            print("❌ الواجهة لا تعرض رصيد متاح")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_transfer_process():
    """اختبار العملية الكاملة لنقل الخزينة"""
    
    print("\n🧪 اختبار العملية الكاملة لنقل الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة الأولية
        initial_daily = treasury_manager.get_total_daily_balance('SYP')
        initial_main = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الأرصدة الأولية:")
        print(f"  • اليومية: {initial_daily:,} ل.س")
        print(f"  • الرئيسية: {initial_main:,} ل.س")
        
        if initial_daily < 75000:
            print("⚠️ الرصيد اليومي غير كافي للاختبار الكامل")
            return False
        
        # تنفيذ عملية نقل
        print(f"\n🔄 تنفيذ عملية نقل:")
        transfer_amount = 75000
        
        print(f"  • المبلغ: {transfer_amount:,} ل.س")
        print(f"  • من: الخزينة اليومية الإجمالية")
        print(f"  • إلى: الخزينة الرئيسية")
        
        # تنفيذ العملية
        success = treasury_manager.transfer_from_total_daily_to_main(
            currency_type='SYP',
            amount=transfer_amount
        )
        
        if success:
            # فحص النتائج
            final_daily = treasury_manager.get_total_daily_balance('SYP')
            final_main = treasury_manager.get_main_balance('SYP')
            
            print(f"\n💰 الأرصدة النهائية:")
            print(f"  • اليومية: {final_daily:,} ل.س")
            print(f"  • الرئيسية: {final_main:,} ل.س")
            
            daily_change = initial_daily - final_daily
            main_change = final_main - initial_main
            
            print(f"\n📊 التغييرات:")
            print(f"  • خصم من اليومية: {daily_change:,} ل.س")
            print(f"  • إضافة للرئيسية: {main_change:,} ل.س")
            
            # التحقق من دقة العملية
            if (abs(daily_change - transfer_amount) < 1 and 
                abs(main_change - transfer_amount) < 1):
                print("✅ العملية الكاملة تمت بنجاح ودقة")
                return True
            else:
                print("❌ خطأ في دقة العملية")
                return False
        else:
            print("❌ فشلت العملية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملية الكاملة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح عملية نقل الخزينة")
    print("=" * 60)
    
    # 1. اختبار الدالة الجديدة
    function_test = test_treasury_transfer_function()
    
    # 2. اختبار الواجهة
    interface_test = test_treasury_transfer_interface()
    
    # 3. اختبار العملية الكاملة
    complete_test = test_complete_transfer_process()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • اختبار الدالة الجديدة: {'✅ نجح' if function_test else '❌ فشل'}")
    print(f"  • اختبار الواجهة: {'✅ نجح' if interface_test else '❌ فشل'}")
    print(f"  • اختبار العملية الكاملة: {'✅ نجح' if complete_test else '❌ فشل'}")
    
    if all([function_test, interface_test, complete_test]):
        print("\n🎉 تم إصلاح عملية نقل الخزينة بنجاح!")
        
        print("\n📋 النتائج:")
        print("  ✅ دالة نقل الخزينة تعمل بشكل صحيح")
        print("  ✅ واجهة نقل الخزينة تعرض الأرصدة الصحيحة")
        print("  ✅ العملية الكاملة تتم بدقة")
        print("  ✅ النقل من الخزينة الإجمالية يعمل")
        print("  ✅ الخزينة اليومية تنقص والرئيسية تزيد")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. سجل دخول: admin / admin123")
        print("  3. اضغط على 'نقل الخزينة'")
        print("  4. أدخل المبلغ واضغط 'تنفيذ النقل'")
        print("  5. ستتم العملية بنجاح مع تحديث الأرصدة")
        
    else:
        print("\n❌ لا تزال هناك مشاكل:")
        
        if not function_test:
            print("  • دالة نقل الخزينة لا تعمل بشكل صحيح")
        if not interface_test:
            print("  • واجهة نقل الخزينة بها مشاكل")
        if not complete_test:
            print("  • العملية الكاملة لا تعمل بدقة")

if __name__ == "__main__":
    main()

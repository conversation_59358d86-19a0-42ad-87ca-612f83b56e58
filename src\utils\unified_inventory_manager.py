#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المخزون الموحد
Unified Inventory Manager
"""

from datetime import datetime

class UnifiedInventoryManager:
    """مدير المخزون الموحد - جدول واحد للمنتجات مع تتبع الحركات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.create_tables()
    
    def create_tables(self):
        """إنشاء جداول المخزون الموحد"""
        try:
            # جدول المنتجات الموحد (مع المخزون ووحدات القياس المختلفة)
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS unified_products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    purchase_unit TEXT NOT NULL DEFAULT 'قطعة',
                    sale_unit TEXT NOT NULL DEFAULT 'قطعة',
                    conversion_factor REAL DEFAULT 1.0,
                    purchase_price REAL DEFAULT 0,
                    sale_price REAL DEFAULT 0,
                    current_stock REAL DEFAULT 0,
                    min_stock REAL DEFAULT 0,
                    supplier_id INTEGER,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول حركات المخزون الموحد
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS unified_inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL, -- 'in' أو 'out'
                    quantity REAL NOT NULL,
                    from_location TEXT, -- 'main_stock', 'worker_123', 'supplier_456'
                    to_location TEXT,   -- 'main_stock', 'worker_123', 'customer'
                    operation_type TEXT NOT NULL, -- 'purchase', 'delivery', 'maintenance', 'router_delivery'
                    reference_id INTEGER, -- معرف العملية المرجعية
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES unified_products (id)
                )
            """)
            
            # جدول مخزون العمال
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS worker_inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    worker_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL DEFAULT 0,
                    FOREIGN KEY (worker_id) REFERENCES workers (id),
                    FOREIGN KEY (product_id) REFERENCES unified_products (id),
                    UNIQUE(worker_id, product_id)
                )
            """)
            
            # إضافة الأعمدة الجديدة للجدول الموجود إذا لم تكن موجودة
            self._add_missing_columns()

            print("✅ تم إنشاء جداول المخزون الموحد")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جداول المخزون: {e}")

    def _add_missing_columns(self):
        """إضافة الأعمدة المفقودة للجدول الموجود"""
        try:
            # التحقق من وجود الأعمدة الجديدة
            columns_info = self.db_manager.fetch_all("PRAGMA table_info(unified_products)")
            existing_columns = [col['name'] for col in columns_info] if columns_info else []

            # إضافة الأعمدة المفقودة
            if 'purchase_unit' not in existing_columns:
                self.db_manager.execute_query("ALTER TABLE unified_products ADD COLUMN purchase_unit TEXT DEFAULT 'قطعة'")
                print("✅ تم إضافة عمود purchase_unit")

            if 'sale_unit' not in existing_columns:
                self.db_manager.execute_query("ALTER TABLE unified_products ADD COLUMN sale_unit TEXT DEFAULT 'قطعة'")
                print("✅ تم إضافة عمود sale_unit")

            if 'conversion_factor' not in existing_columns:
                self.db_manager.execute_query("ALTER TABLE unified_products ADD COLUMN conversion_factor REAL DEFAULT 1.0")
                print("✅ تم إضافة عمود conversion_factor")

            # تحديث البيانات الموجودة لتتوافق مع النظام الجديد
            self._update_existing_products_units()

        except Exception as e:
            print(f"❌ خطأ في إضافة الأعمدة: {e}")

    def _update_existing_products_units(self):
        """تحديث وحدات المنتجات الموجودة"""
        try:
            # تحديث الكبلات
            self.db_manager.execute_query("""
                UPDATE unified_products
                SET purchase_unit = 'بكرة', sale_unit = 'متر', conversion_factor = 100.0
                WHERE category = 'كبل' AND (purchase_unit IS NULL OR purchase_unit = 'قطعة')
            """)

            # تحديث الراوترات
            self.db_manager.execute_query("""
                UPDATE unified_products
                SET purchase_unit = 'كرتونة', sale_unit = 'قطعة', conversion_factor = 10.0
                WHERE category = 'راوتر' AND (purchase_unit IS NULL OR purchase_unit = 'قطعة')
            """)

            # تحديث باقي المنتجات
            self.db_manager.execute_query("""
                UPDATE unified_products
                SET purchase_unit = 'قطعة', sale_unit = 'قطعة', conversion_factor = 1.0
                WHERE purchase_unit IS NULL
            """)

            print("✅ تم تحديث وحدات المنتجات الموجودة")

        except Exception as e:
            print(f"❌ خطأ في تحديث وحدات المنتجات: {e}")
    
    def migrate_existing_data(self):
        """نقل البيانات الموجودة إلى النظام الموحد"""
        try:
            print("🔄 نقل البيانات إلى النظام الموحد...")
            
            # نقل المنتجات من جدول products
            existing_products = self.db_manager.fetch_all("""
                SELECT id, name, category, unit_type, purchase_price, sale_price, 
                       stock_quantity, min_stock, supplier_id, is_active
                FROM products
            """)
            
            for product in existing_products:
                # التحقق من عدم وجود المنتج في النظام الموحد
                existing = self.db_manager.fetch_one("""
                    SELECT id FROM unified_products WHERE name = ? AND category = ?
                """, (product['name'], product['category']))
                
                if not existing:
                    # تحديد وحدات القياس والتحويل حسب الفئة
                    purchase_unit = 'قطعة'
                    sale_unit = 'قطعة'
                    conversion_factor = 1.0

                    # إعداد وحدات خاصة حسب الفئة
                    if product['category'] == 'كبل':
                        purchase_unit = 'بكرة'
                        sale_unit = 'متر'
                        conversion_factor = 100.0  # بكرة واحدة = 100 متر
                    elif product['category'] == 'راوتر':
                        purchase_unit = 'كرتونة'
                        sale_unit = 'قطعة'
                        conversion_factor = 10.0  # كرتونة واحدة = 10 قطع

                    self.db_manager.execute_query("""
                        INSERT INTO unified_products
                        (name, category, purchase_unit, sale_unit, conversion_factor,
                         purchase_price, sale_price, current_stock, min_stock, supplier_id, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        product['name'], product['category'], purchase_unit, sale_unit, conversion_factor,
                        product.get('purchase_price', 0), product.get('sale_price', 0),
                        product.get('stock_quantity', 0), product.get('min_stock', 0),
                        product.get('supplier_id'), product.get('is_active', 1)
                    ))
                    print(f"✅ تم نقل المنتج: {product['name']}")
            
            print("✅ تم نقل البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في نقل البيانات: {e}")
    
    def add_stock(self, product_id, quantity, operation_type, reference_id=None, 
                  notes="", user_id=1, from_location="supplier"):
        """إضافة مخزون (شراء من مورد)"""
        try:
            # تحديث المخزون الحالي
            self.db_manager.execute_query("""
                UPDATE unified_products 
                SET current_stock = current_stock + ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (quantity, product_id))
            
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO unified_inventory_movements 
                (product_id, movement_type, quantity, from_location, to_location, 
                 operation_type, reference_id, notes, user_id)
                VALUES (?, 'in', ?, ?, 'main_stock', ?, ?, ?, ?)
            """, (product_id, quantity, from_location, operation_type, reference_id, notes, user_id))
            
            # جلب اسم المنتج للطباعة
            product = self.db_manager.fetch_one("SELECT name FROM unified_products WHERE id = ?", (product_id,))
            product_name = product['name'] if product else f"المنتج {product_id}"
            
            print(f"✅ تم إضافة {quantity} من {product_name} للمخزون")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المخزون: {e}")
            return False
    
    def remove_stock(self, product_id, quantity, operation_type, reference_id=None,
                     notes="", user_id=1, to_location="customer"):
        """خصم مخزون (بيع أو استخدام)"""
        try:
            # التحقق من توفر المخزون
            current_stock = self.get_product_stock(product_id)
            if current_stock < quantity:
                product = self.db_manager.fetch_one("SELECT name FROM unified_products WHERE id = ?", (product_id,))
                product_name = product['name'] if product else f"المنتج {product_id}"
                print(f"❌ المخزون غير كافي: {product_name} - متاح: {current_stock}, مطلوب: {quantity}")
                return False
            
            # تحديث المخزون الحالي
            self.db_manager.execute_query("""
                UPDATE unified_products 
                SET current_stock = current_stock - ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (quantity, product_id))
            
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO unified_inventory_movements 
                (product_id, movement_type, quantity, from_location, to_location, 
                 operation_type, reference_id, notes, user_id)
                VALUES (?, 'out', ?, 'main_stock', ?, ?, ?, ?, ?)
            """, (product_id, quantity, to_location, operation_type, reference_id, notes, user_id))
            
            # جلب اسم المنتج للطباعة
            product = self.db_manager.fetch_one("SELECT name FROM unified_products WHERE id = ?", (product_id,))
            product_name = product['name'] if product else f"المنتج {product_id}"
            
            print(f"✅ تم خصم {quantity} من {product_name} من المخزون")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في خصم المخزون: {e}")
            return False
    
    def transfer_to_worker(self, product_id, worker_id, quantity, operation_type="delivery",
                          reference_id=None, notes="", user_id=1):
        """نقل مخزون للعامل"""
        try:
            # خصم من المخزون الرئيسي
            if not self.remove_stock(product_id, quantity, operation_type, reference_id, 
                                   f"نقل للعامل {worker_id} - {notes}", user_id, f"worker_{worker_id}"):
                return False
            
            # إضافة لمخزون العامل
            existing = self.db_manager.fetch_one("""
                SELECT quantity FROM worker_inventory 
                WHERE worker_id = ? AND product_id = ?
            """, (worker_id, product_id))
            
            if existing:
                # تحديث الكمية الموجودة
                self.db_manager.execute_query("""
                    UPDATE worker_inventory
                    SET quantity = quantity + ?
                    WHERE worker_id = ? AND product_id = ?
                """, (quantity, worker_id, product_id))
            else:
                # إنشاء سجل جديد
                self.db_manager.execute_query("""
                    INSERT INTO worker_inventory (worker_id, product_id, quantity)
                    VALUES (?, ?, ?)
                """, (worker_id, product_id, quantity))
            
            print(f"✅ تم نقل {quantity} للعامل {worker_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نقل المخزون للعامل: {e}")
            return False
    
    def remove_from_worker(self, product_id, worker_id, quantity, operation_type="router_delivery",
                          reference_id=None, notes="", user_id=1):
        """خصم مخزون من العامل"""
        try:
            # التحقق من توفر المخزون عند العامل
            worker_stock = self.db_manager.fetch_one("""
                SELECT quantity FROM worker_inventory 
                WHERE worker_id = ? AND product_id = ?
            """, (worker_id, product_id))
            
            if not worker_stock or worker_stock['quantity'] < quantity:
                available = worker_stock['quantity'] if worker_stock else 0
                print(f"❌ مخزون العامل غير كافي - متاح: {available}, مطلوب: {quantity}")
                return False
            
            # خصم من مخزون العامل
            self.db_manager.execute_query("""
                UPDATE worker_inventory
                SET quantity = quantity - ?
                WHERE worker_id = ? AND product_id = ?
            """, (quantity, worker_id, product_id))
            
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO unified_inventory_movements 
                (product_id, movement_type, quantity, from_location, to_location, 
                 operation_type, reference_id, notes, user_id)
                VALUES (?, 'out', ?, ?, 'customer', ?, ?, ?, ?)
            """, (product_id, quantity, f"worker_{worker_id}", operation_type, reference_id, notes, user_id))
            
            print(f"✅ تم خصم {quantity} من مخزون العامل {worker_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في خصم مخزون العامل: {e}")
            return False
    
    def get_product_stock(self, product_id):
        """جلب مخزون المنتج الحالي"""
        try:
            result = self.db_manager.fetch_one("""
                SELECT current_stock FROM unified_products WHERE id = ?
            """, (product_id,))
            return result['current_stock'] if result else 0
        except:
            return 0
    
    def get_worker_stock(self, worker_id, product_id):
        """جلب مخزون العامل لمنتج معين"""
        try:
            result = self.db_manager.fetch_one("""
                SELECT quantity FROM worker_inventory 
                WHERE worker_id = ? AND product_id = ?
            """, (worker_id, product_id))
            return result['quantity'] if result else 0
        except:
            return 0
    
    def get_all_products(self):
        """جلب جميع المنتجات مع المخزون"""
        try:
            rows = self.db_manager.fetch_all("""
                SELECT id, name, category,
                       COALESCE(purchase_unit, 'قطعة') as purchase_unit,
                       COALESCE(sale_unit, 'قطعة') as sale_unit,
                       COALESCE(conversion_factor, 1.0) as conversion_factor,
                       purchase_price, sale_price, current_stock, min_stock, is_active
                FROM unified_products
                WHERE is_active = 1
                ORDER BY category, name
            """)

            # تحويل sqlite3.Row إلى قاموس
            products = []
            for row in rows:
                product = {
                    'id': row['id'],
                    'name': row['name'],
                    'category': row['category'],
                    'purchase_unit': row['purchase_unit'],
                    'sale_unit': row['sale_unit'],
                    'conversion_factor': row['conversion_factor'],
                    'purchase_price': row['purchase_price'],
                    'sale_price': row['sale_price'],
                    'current_stock': row['current_stock'],
                    'min_stock': row['min_stock'],
                    'is_active': row['is_active'],
                    'unit_type': row['sale_unit']  # استخدام sale_unit كـ unit_type للتوافق
                }
                products.append(product)

            return products
        except Exception as e:
            print(f"❌ خطأ في جلب المنتجات: {e}")
            return []
    
    def get_products_by_category(self, category):
        """جلب المنتجات حسب الفئة"""
        try:
            rows = self.db_manager.fetch_all("""
                SELECT id, name, category,
                       COALESCE(purchase_unit, 'قطعة') as purchase_unit,
                       COALESCE(sale_unit, 'قطعة') as sale_unit,
                       COALESCE(conversion_factor, 1.0) as conversion_factor,
                       purchase_price, sale_price, current_stock, min_stock, is_active
                FROM unified_products
                WHERE category = ? AND is_active = 1 AND current_stock > 0
                ORDER BY name
            """, (category,))

            # تحويل sqlite3.Row إلى قاموس
            products = []
            for row in rows:
                product = {
                    'id': row['id'],
                    'name': row['name'],
                    'category': row['category'],
                    'purchase_unit': row['purchase_unit'],
                    'sale_unit': row['sale_unit'],
                    'conversion_factor': row['conversion_factor'],
                    'purchase_price': row['purchase_price'] if 'purchase_price' in row.keys() else 0,
                    'sale_price': row['sale_price'],
                    'current_stock': row['current_stock'],
                    'min_stock': row['min_stock'] if 'min_stock' in row.keys() else 0,
                    'is_active': row['is_active'] if 'is_active' in row.keys() else 1,
                    'unit_type': row['sale_unit']  # استخدام sale_unit كـ unit_type للتوافق
                }
                products.append(product)

            return products
        except Exception as e:
            print(f"❌ خطأ في جلب منتجات الفئة {category}: {e}")
            return []
    
    def get_worker_inventory(self, worker_id):
        """جلب مخزون العامل"""
        try:
            rows = self.db_manager.fetch_all("""
                SELECT wi.product_id, up.name, up.category,
                       COALESCE(up.sale_unit, up.unit_type, 'قطعة') as unit_type,
                       wi.quantity
                FROM worker_inventory wi
                JOIN unified_products up ON wi.product_id = up.id
                WHERE wi.worker_id = ? AND wi.quantity > 0
                ORDER BY up.category, up.name
            """, (worker_id,))

            # تحويل sqlite3.Row إلى قاموس
            inventory = []
            for row in rows:
                item = {
                    'product_id': row['product_id'],
                    'name': row['name'],
                    'category': row['category'],
                    'unit_type': row['unit_type'],
                    'quantity': row['quantity']
                }
                inventory.append(item)

            return inventory
        except Exception as e:
            print(f"❌ خطأ في جلب مخزون العامل {worker_id}: {e}")
            return []
    
    def get_inventory_movements(self, product_id=None, limit=100):
        """جلب حركات المخزون"""
        try:
            if product_id:
                return self.db_manager.fetch_all("""
                    SELECT im.*, up.name as product_name
                    FROM unified_inventory_movements im
                    JOIN unified_products up ON im.product_id = up.id
                    WHERE im.product_id = ?
                    ORDER BY im.created_at DESC
                    LIMIT ?
                """, (product_id, limit))
            else:
                return self.db_manager.fetch_all("""
                    SELECT im.*, up.name as product_name
                    FROM unified_inventory_movements im
                    JOIN unified_products up ON im.product_id = up.id
                    ORDER BY im.created_at DESC
                    LIMIT ?
                """, (limit,))
        except Exception as e:
            print(f"❌ خطأ في جلب حركات المخزون: {e}")
            return []

    def convert_to_base_unit(self, product_id, quantity, from_unit):
        """تحويل الكمية إلى الوحدة الأساسية (وحدة المخزون)"""
        try:
            product = self.db_manager.fetch_one("""
                SELECT purchase_unit, sale_unit, conversion_factor
                FROM unified_products WHERE id = ?
            """, (product_id,))

            if not product:
                return quantity

            # إذا كانت الوحدة المدخلة هي وحدة الشراء
            if from_unit == product['purchase_unit']:
                # تحويل من وحدة الشراء إلى وحدة البيع (الوحدة الأساسية)
                return quantity * product['conversion_factor']

            # إذا كانت الوحدة المدخلة هي وحدة البيع (الوحدة الأساسية)
            return quantity

        except Exception as e:
            print(f"❌ خطأ في تحويل الوحدة: {e}")
            return quantity

    def convert_from_base_unit(self, product_id, quantity, to_unit):
        """تحويل الكمية من الوحدة الأساسية إلى الوحدة المطلوبة"""
        try:
            product = self.db_manager.fetch_one("""
                SELECT purchase_unit, sale_unit, conversion_factor
                FROM unified_products WHERE id = ?
            """, (product_id,))

            if not product:
                return quantity

            # إذا كانت الوحدة المطلوبة هي وحدة الشراء
            if to_unit == product['purchase_unit']:
                # تحويل من وحدة البيع (الوحدة الأساسية) إلى وحدة الشراء
                return quantity / product['conversion_factor'] if product['conversion_factor'] > 0 else quantity

            # إذا كانت الوحدة المطلوبة هي وحدة البيع (الوحدة الأساسية)
            return quantity

        except Exception as e:
            print(f"❌ خطأ في تحويل الوحدة: {e}")
            return quantity

    def add_stock_with_unit(self, product_id, quantity, unit, operation_type, reference_id=None,
                           notes="", user_id=1, from_location="supplier"):
        """إضافة مخزون مع تحديد الوحدة"""
        try:
            # تحويل الكمية إلى الوحدة الأساسية
            base_quantity = self.convert_to_base_unit(product_id, quantity, unit)

            # إضافة للمخزون بالوحدة الأساسية
            return self.add_stock(product_id, base_quantity, operation_type, reference_id,
                                notes + f" (أصلي: {quantity} {unit})", user_id, from_location)

        except Exception as e:
            print(f"❌ خطأ في إضافة المخزون بالوحدة: {e}")
            return False

    def remove_stock_with_unit(self, product_id, quantity, unit, operation_type, reference_id=None,
                              notes="", user_id=1, to_location="customer"):
        """خصم مخزون مع تحديد الوحدة"""
        try:
            # تحويل الكمية إلى الوحدة الأساسية
            base_quantity = self.convert_to_base_unit(product_id, quantity, unit)

            # خصم من المخزون بالوحدة الأساسية
            return self.remove_stock(product_id, base_quantity, operation_type, reference_id,
                                   notes + f" (أصلي: {quantity} {unit})", user_id, to_location)

        except Exception as e:
            print(f"❌ خطأ في خصم المخزون بالوحدة: {e}")
            return False

    def get_product_info_with_units(self, product_id):
        """جلب معلومات المنتج مع وحدات القياس"""
        try:
            return self.db_manager.fetch_one("""
                SELECT id, name, category, purchase_unit, sale_unit, conversion_factor,
                       purchase_price, sale_price, current_stock, min_stock
                FROM unified_products WHERE id = ?
            """, (product_id,))
        except Exception as e:
            print(f"❌ خطأ في جلب معلومات المنتج: {e}")
            return None

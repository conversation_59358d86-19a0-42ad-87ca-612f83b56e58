# -*- coding: utf-8 -*-
"""
مدير التكوين - النسخة المتكاملة
Configuration Manager - Integrated Version

يدير جميع إعدادات النظام والفواتير
"""

import json
import os
from datetime import datetime

class ConfigManager:
    """مدير التكوين"""
    
    def __init__(self, config_path="config/settings.json"):
        """تهيئة مدير التكوين"""
        self.config_path = config_path
        self.ensure_config_directory()
        self.load_config()
    
    def ensure_config_directory(self):
        """التأكد من وجود مجلد التكوين"""
        config_dir = os.path.dirname(self.config_path)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir)
    
    def load_config(self):
        """تحميل التكوين"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except Exception as e:
                print(f"خطأ في تحميل التكوين: {e}")
                self.config = self.get_default_config()
        else:
            self.config = self.get_default_config()
            self.save_config()
    
    def get_default_config(self):
        """الحصول على التكوين الافتراضي"""
        return {
            "database": {
                "path": "data/company_system.db",
                "backup_enabled": True,
                "backup_interval_days": 7
            },
            "company": {
                "name": "شركة الإنترنت",
                "address": "سوريا - دمشق",
                "phone": "+963-11-1234567",
                "email": "<EMAIL>",
                "website": "www.company.com"
            },
            "invoice": {
                "header": "فاتورة خدمات الإنترنت",
                "footer": "شكراً لتعاملكم معنا - نتمنى لكم تجربة ممتعة",
                "show_user_name": True,
                "user_name_template": "تمت خدمتكم من قبل: {user_name}",
                "show_logo": False,
                "logo_path": "",
                "auto_print": False
            },
            "system": {
                "currency": "ل.س",
                "date_format": "%Y-%m-%d",
                "time_format": "%H:%M:%S",
                "language": "ar",
                "theme": "default"
            },
            "security": {
                "session_timeout_minutes": 480,  # 8 ساعات
                "password_min_length": 6,
                "require_password_change": False,
                "max_login_attempts": 5
            },
            "reports": {
                "default_period": "monthly",
                "auto_export": False,
                "export_format": "pdf"
            }
        }
    
    def save_config(self):
        """حفظ التكوين"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ التكوين: {e}")
            return False
    
    def get(self, key, default=None):
        """الحصول على قيمة من التكوين"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key, value):
        """تعيين قيمة في التكوين"""
        keys = key.split('.')
        config = self.config
        
        # التنقل إلى المستوى الأخير
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # تعيين القيمة
        config[keys[-1]] = value
        
        # حفظ التكوين
        return self.save_config()
    
    def get_company_info(self):
        """الحصول على معلومات الشركة"""
        return {
            'name': self.get('company.name', 'شركة الإنترنت'),
            'address': self.get('company.address', 'سوريا - دمشق'),
            'phone': self.get('company.phone', '+963-11-1234567'),
            'email': self.get('company.email', '<EMAIL>'),
            'website': self.get('company.website', 'www.company.com')
        }
    
    def get_invoice_settings(self):
        """الحصول على إعدادات الفواتير"""
        return {
            'header': self.get('invoice.header', 'فاتورة خدمات الإنترنت'),
            'footer': self.get('invoice.footer', 'شكراً لتعاملكم معنا'),
            'show_user_name': self.get('invoice.show_user_name', True),
            'user_name_template': self.get('invoice.user_name_template', 'تمت خدمتكم من قبل: {user_name}'),
            'show_logo': self.get('invoice.show_logo', False),
            'logo_path': self.get('invoice.logo_path', ''),
            'auto_print': self.get('invoice.auto_print', False)
        }
    
    def update_invoice_settings(self, settings):
        """تحديث إعدادات الفواتير"""
        for key, value in settings.items():
            self.set(f'invoice.{key}', value)
        return self.save_config()
    
    def get_system_settings(self):
        """الحصول على إعدادات النظام"""
        return {
            'currency': self.get('system.currency', 'ل.س'),
            'date_format': self.get('system.date_format', '%Y-%m-%d'),
            'time_format': self.get('system.time_format', '%H:%M:%S'),
            'language': self.get('system.language', 'ar'),
            'theme': self.get('system.theme', 'default')
        }
    
    def get_security_settings(self):
        """الحصول على إعدادات الأمان"""
        return {
            'session_timeout_minutes': self.get('security.session_timeout_minutes', 480),
            'password_min_length': self.get('security.password_min_length', 6),
            'require_password_change': self.get('security.require_password_change', False),
            'max_login_attempts': self.get('security.max_login_attempts', 5)
        }
    
    def format_currency(self, amount):
        """تنسيق العملة"""
        currency = self.get('system.currency', 'ل.س')
        return f"{amount:,.2f} {currency}"
    
    def format_date(self, date_obj):
        """تنسيق التاريخ"""
        if isinstance(date_obj, str):
            return date_obj
        
        date_format = self.get('system.date_format', '%Y-%m-%d')
        return date_obj.strftime(date_format)
    
    def format_time(self, time_obj):
        """تنسيق الوقت"""
        if isinstance(time_obj, str):
            return time_obj
        
        time_format = self.get('system.time_format', '%H:%M:%S')
        return time_obj.strftime(time_format)
    
    def format_datetime(self, datetime_obj):
        """تنسيق التاريخ والوقت"""
        if isinstance(datetime_obj, str):
            return datetime_obj
        
        date_format = self.get('system.date_format', '%Y-%m-%d')
        time_format = self.get('system.time_format', '%H:%M:%S')
        return datetime_obj.strftime(f"{date_format} {time_format}")
    
    def reset_to_defaults(self):
        """إعادة تعيين التكوين إلى الافتراضي"""
        self.config = self.get_default_config()
        return self.save_config()
    
    def export_config(self, export_path):
        """تصدير التكوين"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في تصدير التكوين: {e}")
            return False
    
    def import_config(self, import_path):
        """استيراد التكوين"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # دمج التكوين المستورد مع الحالي
            self.config.update(imported_config)
            return self.save_config()
        except Exception as e:
            print(f"خطأ في استيراد التكوين: {e}")
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لواجهة شراء الدولار
"""

import sys
import os
sys.path.append('src')

def test_currency_exchange_quick():
    """اختبار سريع لفتح واجهة شراء الدولار"""
    
    print("🧪 اختبار سريع لفتح واجهة شراء الدولار...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.currency_exchange_window import CurrencyExchangeWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير', 'role': 'مدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء واجهة شراء الدولار
        window = CurrencyExchangeWindow(db, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة شراء الدولار بنجاح")
        print(f"📋 عنوان النافذة: {window.windowTitle()}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة شراء الدولار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_currency_exchange():
    """اختبار فتح شراء الدولار من النافذة الرئيسية"""
    
    print("\n🧪 اختبار فتح شراء الدولار من النافذة الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from ui.main_window import MainWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        config_manager = ConfigManager()
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير', 'role': 'مدير'}
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(db, config_manager, current_user)
        
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # محاكاة فتح واجهة شراء الدولار
        print("🔄 محاكاة فتح واجهة شراء الدولار...")
        
        try:
            # استدعاء الدالة مباشرة بدون exec_()
            from ui.currency_exchange_window import CurrencyExchangeWindow
            dialog = CurrencyExchangeWindow(main_window.db_manager, main_window.treasury_manager, main_window.current_user, main_window)
            
            print("✅ تم إنشاء واجهة شراء الدولار من النافذة الرئيسية")
            print(f"📋 عنوان النافذة: {dialog.windowTitle()}")
            
            return True
            
        except Exception as dialog_error:
            print(f"❌ خطأ في إنشاء واجهة شراء الدولار: {dialog_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار سريع لواجهة شراء الدولار")
    print("=" * 50)
    
    # اختبار فتح الواجهة مباشرة
    direct_test = test_currency_exchange_quick()
    
    # اختبار فتح من النافذة الرئيسية
    main_window_test = test_main_window_currency_exchange()
    
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • فتح واجهة شراء الدولار مباشرة: {'✅ يعمل' if direct_test else '❌ لا يعمل'}")
    print(f"  • فتح من النافذة الرئيسية: {'✅ يعمل' if main_window_test else '❌ لا يعمل'}")
    
    if all([direct_test, main_window_test]):
        print("\n🎉 تم إصلاح مشكلة واجهة شراء الدولار!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح constructor واجهة شراء الدولار")
        print("    • تغيير من config_manager إلى treasury_manager")
        print("    • توافق مع استدعاء النافذة الرئيسية")
        
        print("  ✅ إصلاح مشكلة role في النافذة الرئيسية")
        print("    • إضافة قيمة افتراضية للـ role")
        print("    • منع خطأ KeyError")
        
        print("\n🚀 النظام الآن:")
        print("  • واجهة شراء الدولار تفتح بدون أخطاء")
        print("  • تعمل من النافذة الرئيسية")
        print("  • تعرض الأرصدة الحالية")
        print("  • جاهزة لتحويل العملة")
        
        print("\n🎯 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'شراء الدولار' من القائمة الرئيسية")
        print("  3. ستفتح الواجهة بدون أخطاء")
        print("  4. يمكنك تحويل الليرة السورية إلى دولار")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

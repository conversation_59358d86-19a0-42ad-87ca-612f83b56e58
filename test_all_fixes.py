#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع الإصلاحات
"""

import sys
import os
sys.path.append('src')

def test_cash_box_closure_window():
    """اختبار واجهة إغلاق الصندوق"""
    
    print("🧪 اختبار واجهة إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.cash_box_closure_window import CashBoxClosureWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء واجهة إغلاق الصندوق
        window = CashBoxClosureWindow(db, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة إغلاق الصندوق بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shift_management():
    """اختبار إدارة الشيفتات"""
    
    print("\n🧪 اختبار إدارة الشيفتات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # اختبار التحقق من الشيفت المفتوح
        has_open_shift = treasury_manager.is_session_active(current_user['id'])
        print(f"📊 حالة الشيفت: {'مفتوح' if has_open_shift else 'مغلق'}")
        
        if has_open_shift:
            print("✅ يوجد شيفت مفتوح - النظام يمنع فتح شيفت جديد")
        else:
            print("✅ لا يوجد شيفت مفتوح - يمكن فتح شيفت جديد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الشيفتات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_router_delivery_save():
    """اختبار حفظ تسليم الراوتر"""
    
    print("\n🧪 اختبار حفظ تسليم الراوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء واجهة تسليم الراوتر
        window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة تسليم الراوتر بنجاح")
        
        # اختبار وجود الدوال المطلوبة
        required_methods = ['save_delivery', 'validate_data', 'calculate_total_amount', 'update_treasury']
        
        for method in required_methods:
            if hasattr(window, method) and callable(getattr(window, method)):
                print(f"✅ دالة {method} موجودة")
            else:
                print(f"❌ دالة {method} غير موجودة")
                return False
        
        # اختبار وجود treasury_manager
        if hasattr(window, 'treasury_manager') and window.treasury_manager:
            print("✅ treasury_manager موجود في واجهة تسليم الراوتر")
        else:
            print("❌ treasury_manager غير موجود في واجهة تسليم الراوتر")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ تسليم الراوتر: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_window():
    """اختبار واجهة نقل الخزينة"""
    
    print("\n🧪 اختبار واجهة نقل الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.treasury_transfer_window import TreasuryTransferWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء واجهة نقل الخزينة
        window = TreasuryTransferWindow(db, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة نقل الخزينة بنجاح")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بدون خطأ total_sales")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """اختبار تكامل النافذة الرئيسية"""
    
    print("\n🧪 اختبار تكامل النافذة الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from ui.main_window import MainWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        config_manager = ConfigManager()
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(db, config_manager, current_user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود الدوال المحدثة
        required_methods = [
            'open_cash_box_closure',
            'open_treasury_transfer', 
            'open_currency_exchange'
        ]
        
        for method in required_methods:
            if hasattr(main_window, method) and callable(getattr(main_window, method)):
                print(f"✅ دالة {method} موجودة")
            else:
                print(f"❌ دالة {method} غير موجودة")
                return False
        
        # اختبار وجود treasury_manager
        if hasattr(main_window, 'treasury_manager') and main_window.treasury_manager:
            print("✅ treasury_manager موجود في النافذة الرئيسية")
        else:
            print("❌ treasury_manager غير موجود في النافذة الرئيسية")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار جميع الإصلاحات")
    print("=" * 60)
    
    # اختبار واجهة إغلاق الصندوق
    cash_box_test = test_cash_box_closure_window()
    
    # اختبار إدارة الشيفتات
    shift_test = test_shift_management()
    
    # اختبار حفظ تسليم الراوتر
    router_save_test = test_router_delivery_save()
    
    # اختبار واجهة نقل الخزينة
    transfer_test = test_treasury_transfer_window()
    
    # اختبار تكامل النافذة الرئيسية
    main_window_test = test_main_window_integration()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • واجهة إغلاق الصندوق: {'✅ مُصلحة' if cash_box_test else '❌ تحتاج إصلاح'}")
    print(f"  • إدارة الشيفتات: {'✅ تعمل' if shift_test else '❌ لا تعمل'}")
    print(f"  • حفظ تسليم الراوتر: {'✅ يعمل' if router_save_test else '❌ لا يعمل'}")
    print(f"  • واجهة نقل الخزينة: {'✅ مُصلحة' if transfer_test else '❌ تحتاج إصلاح'}")
    print(f"  • تكامل النافذة الرئيسية: {'✅ يعمل' if main_window_test else '❌ لا يعمل'}")
    
    if all([cash_box_test, shift_test, router_save_test, transfer_test, main_window_test]):
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح خطأ 'MainWindow object is not subscriptable'")
        print("    • تم تمرير treasury_manager لواجهة إغلاق الصندوق")
        
        print("  ✅ منع فتح شيفت جديد إذا كان هناك شيفت مفتوح")
        print("    • التحقق من الشيفت المفتوح عند تسجيل الدخول")
        print("    • عرض رسالة تأكيد للمستخدم")
        
        print("  ✅ إصلاح زر حفظ التسليم في واجهة تسليم الراوتر")
        print("    • تم ربط treasury_manager بالواجهة")
        print("    • تم إصلاح دالة update_treasury")
        
        print("  ✅ إصلاح خطأ 'total_sales is not defined'")
        print("    • تم إصلاح واجهة نقل الخزينة")
        print("    • تم حذف المتغيرات غير المعرفة")
        
        print("\n🔧 النظام الآن:")
        print("  • تسجيل الدخول = فتح شيفت (مع التحقق من الشيفت السابق)")
        print("  • إغلاق الصندوق = إغلاق الشيفت + نقل للخزينة اليومية")
        print("  • واجهة نقل الخزينة = من اليومية للرئيسية")
        print("  • واجهة شراء الدولار = تحويل داخلي في الخزينة اليومية")
        print("  • زر حفظ التسليم يعمل ويحفظ في الخزينة")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. إذا كان هناك شيفت مفتوح، ستظهر رسالة تأكيد")
        print("  3. جميع الواجهات تعمل بدون أخطاء")
        print("  4. زر حفظ التسليم يحفظ ويضيف للخزينة")
        print("  5. إغلاق الصندوق ينقل للخزينة الرئيسية")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")
        
        if not cash_box_test:
            print("  • راجع واجهة إغلاق الصندوق")
        if not shift_test:
            print("  • راجع إدارة الشيفتات")
        if not router_save_test:
            print("  • راجع حفظ تسليم الراوتر")
        if not transfer_test:
            print("  • راجع واجهة نقل الخزينة")
        if not main_window_test:
            print("  • راجع تكامل النافذة الرئيسية")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية جدول المصاريف
"""

import sqlite3
from datetime import date

def main():
    conn = sqlite3.connect('data/company_system.db')
    cursor = conn.cursor()

    print('=== بنية جدول expenses ===')
    cursor.execute('PRAGMA table_info(expenses)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'{col[1]}: {col[2]}')

    print('\n=== عينة من بيانات expenses ===')
    cursor.execute('SELECT * FROM expenses ORDER BY created_at DESC LIMIT 5')
    expenses = cursor.fetchall()
    for exp in expenses:
        print(f'ID: {exp[0]}, النوع: {exp[1]}, المبلغ: {exp[2]}, التاريخ: {exp[3]}')

    today = date.today().strftime('%Y-%m-%d')
    print(f'\n=== مصاريف اليوم {today} ===')
    cursor.execute('SELECT * FROM expenses WHERE DATE(expense_date) = ?', (today,))
    today_expenses = cursor.fetchall()
    print(f'عدد المصاريف اليوم: {len(today_expenses)}')
    for exp in today_expenses:
        print(f'النوع: {exp[1]}, المبلغ: {exp[2]}, المستخدم: {exp[6] if len(exp) > 6 else "غير محدد"}')

    print('\n=== اختبار استعلام إغلاق الصندوق ===')
    cursor.execute("""
        SELECT COALESCE(SUM(amount), 0) as total FROM expenses
        WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type != 'رواتب'
    """, (today, 'admin'))
    result = cursor.fetchone()
    print(f'إجمالي المصاريف لـ admin (بدون رواتب): {result[0] if result else 0}')

    conn.close()

if __name__ == "__main__":
    main()

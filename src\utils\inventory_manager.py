#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المخزون المتكامل
Integrated Inventory Manager
"""

from datetime import datetime

class InventoryManager:
    """مدير المخزون المتكامل"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.create_inventory_tables()
    
    def create_inventory_tables(self):
        """إنشاء جداول المخزون"""
        try:
            # جدول حركات المخزون
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL, -- 'in', 'out', 'transfer'
                    quantity REAL NOT NULL,
                    unit_type TEXT,
                    from_location TEXT, -- 'main', 'worker_id', 'supplier_id'
                    to_location TEXT,   -- 'main', 'worker_id', 'customer'
                    reference_type TEXT, -- 'purchase', 'delivery', 'transfer', 'sale'
                    reference_id INTEGER,
                    notes TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products(id)
                )
            """)
            
            # جدول مخزون المواقع
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS location_inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    location_type TEXT NOT NULL, -- 'main', 'worker'
                    location_id TEXT NOT NULL,   -- 'main' or worker_id
                    quantity REAL DEFAULT 0,
                    unit_type TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products(id),
                    UNIQUE(product_id, location_type, location_id)
                )
            """)
            
            print("✅ تم إنشاء جداول المخزون")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جداول المخزون: {e}")
    
    def add_stock(self, product_id, quantity, unit_type, location_type='main', 
                  location_id='main', reference_type='purchase', reference_id=None, 
                  notes='', user_id=1):
        """إضافة مخزون"""
        try:
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO inventory_movements 
                (product_id, movement_type, quantity, unit_type, to_location, 
                 reference_type, reference_id, notes, user_id)
                VALUES (?, 'in', ?, ?, ?, ?, ?, ?, ?)
            """, (product_id, quantity, unit_type, f"{location_type}:{location_id}", 
                  reference_type, reference_id, notes, user_id))
            
            # تحديث المخزون
            self._update_location_inventory(product_id, location_type, location_id, 
                                          quantity, unit_type)
            
            print(f"✅ تم إضافة {quantity} {unit_type} للمنتج {product_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المخزون: {e}")
            return False
    
    def remove_stock(self, product_id, quantity, unit_type, location_type='main', 
                     location_id='main', reference_type='sale', reference_id=None, 
                     notes='', user_id=1):
        """خصم مخزون"""
        try:
            # التحقق من توفر المخزون
            current_stock = self.get_stock(product_id, location_type, location_id)
            if current_stock < quantity:
                raise Exception(f"المخزون غير كافي. المتاح: {current_stock}, المطلوب: {quantity}")
            
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO inventory_movements 
                (product_id, movement_type, quantity, unit_type, from_location, 
                 reference_type, reference_id, notes, user_id)
                VALUES (?, 'out', ?, ?, ?, ?, ?, ?, ?)
            """, (product_id, quantity, unit_type, f"{location_type}:{location_id}", 
                  reference_type, reference_id, notes, user_id))
            
            # تحديث المخزون
            self._update_location_inventory(product_id, location_type, location_id, 
                                          -quantity, unit_type)
            
            print(f"✅ تم خصم {quantity} {unit_type} من المنتج {product_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في خصم المخزون: {e}")
            return False
    
    def transfer_stock(self, product_id, quantity, unit_type, 
                       from_location_type, from_location_id,
                       to_location_type, to_location_id,
                       reference_type='transfer', reference_id=None, 
                       notes='', user_id=1):
        """نقل مخزون بين المواقع"""
        try:
            # التحقق من توفر المخزون في الموقع المصدر
            current_stock = self.get_stock(product_id, from_location_type, from_location_id)
            if current_stock < quantity:
                raise Exception(f"المخزون غير كافي في الموقع المصدر. المتاح: {current_stock}")
            
            # تسجيل الحركة
            self.db_manager.execute_query("""
                INSERT INTO inventory_movements 
                (product_id, movement_type, quantity, unit_type, from_location, to_location,
                 reference_type, reference_id, notes, user_id)
                VALUES (?, 'transfer', ?, ?, ?, ?, ?, ?, ?, ?)
            """, (product_id, quantity, unit_type, 
                  f"{from_location_type}:{from_location_id}",
                  f"{to_location_type}:{to_location_id}",
                  reference_type, reference_id, notes, user_id))
            
            # خصم من الموقع المصدر
            self._update_location_inventory(product_id, from_location_type, from_location_id, 
                                          -quantity, unit_type)
            
            # إضافة للموقع المستهدف
            self._update_location_inventory(product_id, to_location_type, to_location_id, 
                                          quantity, unit_type)
            
            print(f"✅ تم نقل {quantity} {unit_type} من {from_location_type}:{from_location_id} إلى {to_location_type}:{to_location_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نقل المخزون: {e}")
            return False
    
    def get_stock(self, product_id, location_type='main', location_id='main'):
        """الحصول على كمية المخزون"""
        try:
            result = self.db_manager.fetch_one("""
                SELECT quantity FROM location_inventory 
                WHERE product_id = ? AND location_type = ? AND location_id = ?
            """, (product_id, location_type, location_id))
            
            return result['quantity'] if result else 0
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على المخزون: {e}")
            return 0
    
    def get_all_stock(self, product_id):
        """الحصول على جميع مخزون المنتج في جميع المواقع"""
        try:
            results = self.db_manager.fetch_all("""
                SELECT location_type, location_id, quantity, unit_type, last_updated
                FROM location_inventory 
                WHERE product_id = ? AND quantity > 0
                ORDER BY location_type, location_id
            """, (product_id,))
            
            return results
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على جميع المخزون: {e}")
            return []
    
    def get_inventory_movements(self, product_id=None, location_type=None, 
                               location_id=None, limit=100):
        """الحصول على حركات المخزون"""
        try:
            query = """
                SELECT im.*, p.name as product_name
                FROM inventory_movements im
                LEFT JOIN products p ON im.product_id = p.id
                WHERE 1=1
            """
            params = []
            
            if product_id:
                query += " AND im.product_id = ?"
                params.append(product_id)
            
            if location_type and location_id:
                query += " AND (im.from_location LIKE ? OR im.to_location LIKE ?)"
                location_pattern = f"{location_type}:{location_id}%"
                params.extend([location_pattern, location_pattern])
            
            query += " ORDER BY im.created_at DESC LIMIT ?"
            params.append(limit)
            
            return self.db_manager.fetch_all(query, params)
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على حركات المخزون: {e}")
            return []
    
    def _update_location_inventory(self, product_id, location_type, location_id, 
                                  quantity_change, unit_type):
        """تحديث مخزون الموقع"""
        try:
            # محاولة تحديث السجل الموجود
            updated = self.db_manager.execute_query("""
                UPDATE location_inventory 
                SET quantity = quantity + ?, last_updated = CURRENT_TIMESTAMP
                WHERE product_id = ? AND location_type = ? AND location_id = ?
            """, (quantity_change, product_id, location_type, location_id))
            
            # إذا لم يتم التحديث، إنشاء سجل جديد
            if updated == 0:
                self.db_manager.execute_query("""
                    INSERT INTO location_inventory 
                    (product_id, location_type, location_id, quantity, unit_type)
                    VALUES (?, ?, ?, ?, ?)
                """, (product_id, location_type, location_id, max(0, quantity_change), unit_type))
            
        except Exception as e:
            print(f"❌ خطأ في تحديث مخزون الموقع: {e}")
            raise e
    
    def sync_with_products_table(self):
        """مزامنة مع جدول المنتجات"""
        try:
            # الحصول على جميع المنتجات
            products = self.db_manager.fetch_all("""
                SELECT id, stock_quantity, sale_unit FROM products
            """)
            
            for product in products:
                product_id = product[0]
                stock_quantity = product[1] or 0
                unit_type = product[2] or 'قطعة'
                
                # التحقق من وجود المخزون في النظام الجديد
                current_stock = self.get_stock(product_id, 'main', 'main')
                
                if current_stock != stock_quantity:
                    # تحديث المخزون
                    difference = stock_quantity - current_stock
                    if difference > 0:
                        self.add_stock(product_id, difference, unit_type, 
                                     reference_type='sync', notes='مزامنة مع جدول المنتجات')
                    elif difference < 0:
                        self.remove_stock(product_id, abs(difference), unit_type,
                                        reference_type='sync', notes='مزامنة مع جدول المنتجات')
            
            print("✅ تم مزامنة المخزون مع جدول المنتجات")
            
        except Exception as e:
            print(f"❌ خطأ في المزامنة: {e}")
    
    def get_low_stock_products(self, location_type='main', location_id='main'):
        """الحصول على المنتجات ذات المخزون المنخفض"""
        try:
            results = self.db_manager.fetch_all("""
                SELECT p.id, p.name, p.min_stock_level, 
                       COALESCE(li.quantity, 0) as current_stock,
                       p.sale_unit
                FROM products p
                LEFT JOIN location_inventory li ON p.id = li.product_id 
                    AND li.location_type = ? AND li.location_id = ?
                WHERE p.min_stock_level > 0 
                    AND COALESCE(li.quantity, 0) <= p.min_stock_level
                ORDER BY (COALESCE(li.quantity, 0) / NULLIF(p.min_stock_level, 0))
            """, (location_type, location_id))
            
            return results
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على المنتجات منخفضة المخزون: {e}")
            return []

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح دالة الإحصائيات في الواجهة الرئيسية
"""

import sys
import os
sys.path.append('src')

def fix_main_window_stats():
    """إصلاح دالة الإحصائيات لتستخدم النظام الموحد"""
    
    print("🔧 إصلاح دالة الإحصائيات في الواجهة الرئيسية...")
    
    # قراءة الملف الحالي
    with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن بداية ونهاية دالة get_today_stats
    start_marker = "def get_today_stats(self):"
    end_marker = "except Exception as e:"
    
    start_index = content.find(start_marker)
    if start_index == -1:
        print("❌ لم يتم العثور على دالة get_today_stats")
        return False
    
    # البحث عن نهاية الدالة
    lines = content[start_index:].split('\n')
    end_line_index = -1
    
    for i, line in enumerate(lines):
        if "except Exception as e:" in line and "get_today_stats" not in line:
            end_line_index = i
            break
    
    if end_line_index == -1:
        print("❌ لم يتم العثور على نهاية دالة get_today_stats")
        return False
    
    # إنشاء الدالة الجديدة
    new_function = '''    def get_today_stats(self):
        """جلب إحصائيات اليوم من النظام الموحد"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # عدد المشتركين الجدد اليوم للمستخدم الحالي فقط
            new_subscribers = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM subscribers WHERE DATE(created_at) = ? AND created_by = ?",
                (today, self.current_user['id'])
            )
            
            # إجمالي المبيعات من النظام الموحد
            print(f"=== حساب الإحصائيات من النظام الموحد للمستخدم: {self.current_user['username']} ===")
            
            # قراءة الأرصدة من الخزينة الموحدة
            daily_syp_balance = self.treasury_manager.get_daily_balance(
                user_id=self.current_user['id'],
                currency_type='SYP'
            )
            
            daily_usd_balance = self.treasury_manager.get_daily_balance(
                user_id=self.current_user['id'],
                currency_type='USD'
            )
            
            # المبيعات = الرصيد الإيجابي في الخزينة اليومية
            total_sales_amount = max(0, daily_syp_balance)
            
            print(f"رصيد الخزينة اليومية: {daily_syp_balance:,} ل.س + ${daily_usd_balance:.2f}")
            print(f"إجمالي المبيعات المحسوب: {total_sales_amount:,} ل.س")

            # حساب المصاريف (افتراضياً صفر في النظام الموحد)
            total_expenses_amount = 0

            # عدد المعاملات اليوم للمستخدم الحالي
            total_transactions = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM transactions WHERE DATE(created_at) = ? AND user_name = ?",
                (today, self.current_user['username'])
            )
            
            total_transactions_count = total_transactions['count'] if total_transactions else 0

            # الاشتراكات منتهية الصلاحية
            expired_subscriptions = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM subscribers WHERE subscription_end_date < date('now')"
            )

            return {
                'new_subscribers': new_subscribers['count'] if new_subscribers else 0,
                'total_sales': total_sales_amount,
                'total_expenses': total_expenses_amount,
                'total_transactions': total_transactions_count,
                'expired_subscriptions': expired_subscriptions['count'] if expired_subscriptions else 0,
            }
        '''
    
    # استبدال الدالة القديمة بالجديدة
    before_function = content[:start_index]
    after_function_start = start_index + len('\\n'.join(lines[:end_line_index]))
    after_function = content[after_function_start:]
    
    # البحث عن نهاية الدالة الصحيحة
    except_index = content.find("except Exception as e:", start_index)
    if except_index != -1:
        # البحث عن نهاية except block
        return_index = content.find("return {}", except_index)
        if return_index == -1:
            return_index = content.find("return", except_index)
        
        if return_index != -1:
            # البحث عن نهاية السطر
            end_function_index = content.find("\\n", return_index)
            if end_function_index != -1:
                before_function = content[:start_index]
                after_function = content[end_function_index + 1:]
                
                new_content = before_function + new_function + "\\n" + after_function
                
                # كتابة الملف الجديد
                with open('src/ui/main_window.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ تم إصلاح دالة get_today_stats بنجاح")
                return True
    
    print("❌ فشل في إصلاح الدالة")
    return False

def add_treasury_manager_to_main_window():
    """إضافة مدير الخزينة للواجهة الرئيسية إذا لم يكن موجود"""
    
    print("🔧 التحقق من وجود مدير الخزينة في الواجهة الرئيسية...")
    
    with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود مدير الخزينة
    if "UnifiedTreasuryManager" in content:
        print("✅ مدير الخزينة موجود بالفعل")
        return True
    
    # البحث عن موضع الإضافة
    init_marker = "self.current_user = current_user"
    init_index = content.find(init_marker)
    
    if init_index == -1:
        print("❌ لم يتم العثور على موضع الإضافة")
        return False
    
    # إضافة مدير الخزينة
    addition = '''
        
        # إنشاء مدير الخزينة الموحد وفتح الصندوق
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)
        self.treasury_manager.open_cash_box(user_id=current_user['id'])'''
    
    # العثور على نهاية السطر
    end_line_index = content.find("\\n", init_index)
    if end_line_index != -1:
        new_content = content[:end_line_index] + addition + content[end_line_index:]
        
        with open('src/ui/main_window.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ تم إضافة مدير الخزينة للواجهة الرئيسية")
        return True
    
    print("❌ فشل في إضافة مدير الخزينة")
    return False

def test_fixes():
    """اختبار الإصلاحات"""
    
    print("🧪 اختبار الإصلاحات...")
    
    try:
        # استيراد الواجهة الرئيسية
        sys.path.append('src')
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # محاكاة مستخدم
        current_user = {'id': 1, 'username': 'admin'}
        
        # استيراد الواجهة الرئيسية
        from ui.main_window import MainWindow
        
        # إنشاء واجهة وهمية للاختبار
        from PyQt5.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        
        # إنشاء الواجهة
        main_window = MainWindow(db, None, current_user)
        
        # اختبار دالة الإحصائيات
        stats = main_window.get_today_stats()
        
        print("📊 نتائج الاختبار:")
        print(f"  • المشتركين الجدد: {stats.get('new_subscribers', 0)}")
        print(f"  • إجمالي المبيعات: {stats.get('total_sales', 0):,} ل.س")
        print(f"  • إجمالي المصاريف: {stats.get('total_expenses', 0):,} ل.س")
        print(f"  • عدد المعاملات: {stats.get('total_transactions', 0)}")
        print(f"  • منتهية الصلاحية: {stats.get('expired_subscriptions', 0)}")
        
        print("✅ الاختبار نجح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح الواجهة الرئيسية...")
    
    # إضافة مدير الخزينة
    add_treasury_manager_to_main_window()
    
    # إصلاح دالة الإحصائيات
    fix_main_window_stats()
    
    # اختبار الإصلاحات
    test_fixes()
    
    print("🎉 انتهى الإصلاح!")

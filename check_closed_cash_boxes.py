#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص الصناديق المغلقة ونقلها للنظام الموحد
"""

import sys
import os
sys.path.append('src')

def check_closed_cash_boxes():
    """فحص الصناديق المغلقة"""
    
    print("🔍 فحص الصناديق المغلقة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص الصناديق المغلقة
        closed_boxes = db.fetch_all("""
            SELECT id, user_id, opening_balance, total_sales, total_expenses, 
                   net_amount, closed_at, shift_date
            FROM cash_boxes 
            WHERE is_closed = 1 
            ORDER BY closed_at DESC
        """)
        
        print(f"📦 تم العثور على {len(closed_boxes)} صندوق مغلق:")
        
        total_net = 0
        for box in closed_boxes:
            net = box.get('net_amount', 0) or 0
            total_net += net
            print(f"  • صندوق {box['id']} - مستخدم {box['user_id']} - تاريخ: {box.get('shift_date', 'غير محدد')} - صافي: {net:,} ل.س")
        
        print(f"\n💰 إجمالي الصافي من الصناديق المغلقة: {total_net:,} ل.س")
        
        return closed_boxes, total_net
        
    except Exception as e:
        print(f"❌ خطأ في فحص الصناديق المغلقة: {e}")
        import traceback
        traceback.print_exc()
        return [], 0

def check_unified_treasury():
    """فحص النظام الموحد للخزينة"""
    
    print("\n🔍 فحص النظام الموحد للخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص جدول unified_treasury
        unified_records = db.fetch_all("""
            SELECT user_id, session_date, currency_type, daily_balance, main_balance, 
                   is_session_active, last_updated
            FROM unified_treasury 
            ORDER BY session_date DESC, user_id
        """)
        
        print(f"📊 تم العثور على {len(unified_records)} سجل في النظام الموحد:")
        
        for record in unified_records:
            status = "نشط" if record['is_session_active'] else "مغلق"
            print(f"  • مستخدم {record['user_id']} - {record['session_date']} - {record['currency_type']}: يومي {record['daily_balance']:,} - رئيسي {record['main_balance']:,} - {status}")
        
        # فحص الأرصدة الحالية
        current_user = {'id': 1}
        daily_syp = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_syp = treasury_manager.get_main_balance('SYP')
        
        print(f"\n💰 الأرصدة الحالية للمستخدم 1:")
        print(f"  • الخزينة اليومية: {daily_syp:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_syp:,} ل.س")
        
        return daily_syp, main_syp
        
    except Exception as e:
        print(f"❌ خطأ في فحص النظام الموحد: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

def migrate_closed_boxes_to_unified():
    """نقل الصناديق المغلقة للنظام الموحد"""
    
    print("\n🔄 نقل الصناديق المغلقة للنظام الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # الحصول على الصناديق المغلقة التي لم تُنقل بعد
        closed_boxes = db.fetch_all("""
            SELECT id, user_id, net_amount, shift_date, closed_at
            FROM cash_boxes 
            WHERE is_closed = 1 AND net_amount > 0
            ORDER BY closed_at
        """)
        
        print(f"📦 سيتم نقل {len(closed_boxes)} صندوق مغلق...")
        
        total_migrated = 0
        successful_migrations = 0
        
        for box in closed_boxes:
            try:
                user_id = box['user_id']
                net_amount = box.get('net_amount', 0) or 0
                shift_date = box.get('shift_date') or box.get('closed_at', '').split()[0]
                
                if net_amount > 0:
                    # إضافة المبلغ للخزينة اليومية في النظام الموحد
                    success = treasury_manager.add_to_daily_treasury(
                        user_id=user_id,
                        currency_type='SYP',
                        amount=net_amount,
                        date=shift_date
                    )
                    
                    if success:
                        total_migrated += net_amount
                        successful_migrations += 1
                        print(f"  ✅ صندوق {box['id']}: {net_amount:,} ل.س - مستخدم {user_id}")
                        
                        # تسجيل العملية
                        db.execute_query("""
                            INSERT INTO transactions (type, description, amount, user_name, created_at)
                            VALUES (?, ?, ?, ?, ?)
                        """, (
                            "نقل صندوق مغلق للنظام الموحد",
                            f"نقل صندوق {box['id']} للنظام الموحد - صافي: {net_amount:,} ل.س",
                            net_amount,
                            f"user_{user_id}",
                            shift_date + " 23:59:59"
                        ))
                    else:
                        print(f"  ❌ فشل في نقل صندوق {box['id']}")
                        
            except Exception as box_error:
                print(f"  ❌ خطأ في نقل صندوق {box['id']}: {box_error}")
        
        print(f"\n📊 نتائج النقل:")
        print(f"  • تم نقل {successful_migrations} صندوق بنجاح")
        print(f"  • إجمالي المبلغ المنقول: {total_migrated:,} ل.س")
        
        return successful_migrations, total_migrated
        
    except Exception as e:
        print(f"❌ خطأ في نقل الصناديق المغلقة: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

def verify_migration():
    """التحقق من نجاح النقل"""
    
    print("\n✅ التحقق من نجاح النقل...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1}
        
        # فحص الأرصدة بعد النقل
        daily_syp = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_syp = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الأرصدة بعد النقل:")
        print(f"  • الخزينة اليومية: {daily_syp:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_syp:,} ل.س")
        
        if daily_syp > 0:
            print("✅ النقل نجح - الآن يمكن استخدام واجهة شراء الدولار ونقل الخزينة")
            return True
        else:
            print("❌ لا يزال الرصيد صفر - قد تحتاج لفحص إضافي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من النقل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مشكلة الأرصدة الصفرية في واجهات الخزينة")
    print("=" * 70)
    
    # 1. فحص الصناديق المغلقة
    closed_boxes, total_net = check_closed_cash_boxes()
    
    # 2. فحص النظام الموحد
    daily_balance, main_balance = check_unified_treasury()
    
    # 3. نقل الصناديق المغلقة للنظام الموحد
    if total_net > 0 and daily_balance == 0:
        print(f"\n🔧 المشكلة: يوجد {total_net:,} ل.س في الصناديق المغلقة لكن الخزينة اليومية فارغة")
        print("🔄 سيتم نقل الصناديق المغلقة للنظام الموحد...")
        
        migrated_count, migrated_amount = migrate_closed_boxes_to_unified()
        
        if migrated_count > 0:
            print(f"✅ تم نقل {migrated_count} صندوق بمبلغ {migrated_amount:,} ل.س")
            
            # 4. التحقق من نجاح النقل
            verification_success = verify_migration()
            
            if verification_success:
                print("\n🎉 تم إصلاح المشكلة بنجاح!")
                print("\n📋 ما تم إنجازه:")
                print(f"  • نقل {migrated_count} صندوق مغلق للنظام الموحد")
                print(f"  • إضافة {migrated_amount:,} ل.س للخزينة اليومية")
                print("  • الآن يمكن استخدام واجهة شراء الدولار")
                print("  • الآن يمكن استخدام واجهة نقل الخزينة")
                
                print("\n🚀 للاستخدام:")
                print("  1. افتح واجهة شراء الدولار - ستجد الرصيد متوفر")
                print("  2. افتح واجهة نقل الخزينة - ستجد الرصيد متوفر")
                print("  3. جميع العمليات ستعمل بشكل طبيعي")
            else:
                print("\n❌ هناك مشكلة في التحقق - قد تحتاج فحص إضافي")
        else:
            print("❌ فشل في نقل الصناديق المغلقة")
    elif daily_balance > 0:
        print(f"\n✅ لا توجد مشكلة - الخزينة اليومية تحتوي على {daily_balance:,} ل.س")
    else:
        print(f"\n⚠️ لا توجد صناديق مغلقة بمبالغ موجبة لنقلها")

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
Settings Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTabWidget,
                            QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont, QPixmap

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class SettingsWindow(QDialog):
    """نافذة الإعدادات"""
    
    settings_updated = pyqtSignal()
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعدادات النظام")
        self.setGeometry(100, 100, 700, 600)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إعدادات النظام")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # تبويبات الإعدادات
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)
        
        # تبويب معلومات الشركة
        company_tab = self.create_company_tab()
        self.tabs.addTab(company_tab, "معلومات الشركة")
        
        # تبويب الإعدادات المالية
        financial_tab = self.create_financial_tab()
        self.tabs.addTab(financial_tab, "الإعدادات المالية")
        
        # تبويب إعدادات النظام
        system_tab = self.create_system_tab()
        self.tabs.addTab(system_tab, "إعدادات النظام")

        # تبويب إعدادات الطباعة
        printing_tab = self.create_printing_tab()
        self.tabs.addTab(printing_tab, "إعدادات الطباعة")
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_company_tab(self):
        """إنشاء تبويب معلومات الشركة"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # معلومات الشركة
        company_group = QGroupBox("معلومات الشركة")
        apply_arabic_style(company_group, 10, bold=True)
        
        company_layout = QGridLayout(company_group)
        company_layout.setSpacing(10)
        
        # اسم الشركة
        name_label = QLabel("اسم الشركة:")
        apply_arabic_style(name_label, 10)
        self.company_name_edit = QLineEdit()
        apply_arabic_style(self.company_name_edit, 10)
        
        # العنوان
        address_label = QLabel("العنوان:")
        apply_arabic_style(address_label, 10)
        self.company_address_edit = QTextEdit()
        apply_arabic_style(self.company_address_edit, 10)
        self.company_address_edit.setMaximumHeight(80)
        
        # الهاتف
        phone_label = QLabel("الهاتف:")
        apply_arabic_style(phone_label, 10)
        self.company_phone_edit = QLineEdit()
        apply_arabic_style(self.company_phone_edit, 10)
        
        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        apply_arabic_style(email_label, 10)
        self.company_email_edit = QLineEdit()
        apply_arabic_style(self.company_email_edit, 10)
        
        # الشعار
        logo_label = QLabel("شعار الشركة:")
        apply_arabic_style(logo_label, 10)
        
        logo_layout = QHBoxLayout()
        self.logo_path_edit = QLineEdit()
        apply_arabic_style(self.logo_path_edit, 10)
        self.logo_path_edit.setReadOnly(True)
        
        browse_logo_button = QPushButton("تصفح...")
        apply_arabic_style(browse_logo_button, 9)
        browse_logo_button.clicked.connect(self.browse_logo)
        
        logo_layout.addWidget(self.logo_path_edit)
        logo_layout.addWidget(browse_logo_button)
        
        company_layout.addWidget(name_label, 0, 0)
        company_layout.addWidget(self.company_name_edit, 0, 1)
        company_layout.addWidget(address_label, 1, 0)
        company_layout.addWidget(self.company_address_edit, 1, 1)
        company_layout.addWidget(phone_label, 2, 0)
        company_layout.addWidget(self.company_phone_edit, 2, 1)
        company_layout.addWidget(email_label, 3, 0)
        company_layout.addWidget(self.company_email_edit, 3, 1)
        company_layout.addWidget(logo_label, 4, 0)
        company_layout.addLayout(logo_layout, 4, 1)
        
        layout.addWidget(company_group)
        layout.addStretch()
        
        return widget
        
    def create_financial_tab(self):
        """إنشاء تبويب الإعدادات المالية"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # الإعدادات المالية
        financial_group = QGroupBox("الإعدادات المالية")
        apply_arabic_style(financial_group, 10, bold=True)
        
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(10)
        
        # رسم الاشتراك الافتراضي
        subscription_label = QLabel("رسم الاشتراك الافتراضي:")
        apply_arabic_style(subscription_label, 10)
        self.subscription_fee_spin = QDoubleSpinBox()
        apply_arabic_style(self.subscription_fee_spin, 10)
        self.subscription_fee_spin.setRange(0, *********)
        self.subscription_fee_spin.setSuffix(" ل.س")
        
        # العملة الافتراضية
        currency_label = QLabel("العملة الافتراضية:")
        apply_arabic_style(currency_label, 10)
        self.currency_combo = QComboBox()
        apply_arabic_style(self.currency_combo, 10)
        self.currency_combo.addItem("ليرة سورية (ل.س)", "SYP")
        self.currency_combo.addItem("دولار أمريكي ($)", "USD")
        self.currency_combo.addItem("يورو (€)", "EUR")
        
        # سعر صرف الدولار
        usd_rate_label = QLabel("سعر صرف الدولار:")
        apply_arabic_style(usd_rate_label, 10)
        self.usd_rate_spin = QDoubleSpinBox()
        apply_arabic_style(self.usd_rate_spin, 10)
        self.usd_rate_spin.setRange(0, 999999)
        self.usd_rate_spin.setSuffix(" ل.س")
        
        # سعر صرف اليورو
        eur_rate_label = QLabel("سعر صرف اليورو:")
        apply_arabic_style(eur_rate_label, 10)
        self.eur_rate_spin = QDoubleSpinBox()
        apply_arabic_style(self.eur_rate_spin, 10)
        self.eur_rate_spin.setRange(0, 999999)
        self.eur_rate_spin.setSuffix(" ل.س")
        
        financial_layout.addWidget(subscription_label, 0, 0)
        financial_layout.addWidget(self.subscription_fee_spin, 0, 1)
        financial_layout.addWidget(currency_label, 1, 0)
        financial_layout.addWidget(self.currency_combo, 1, 1)
        financial_layout.addWidget(usd_rate_label, 2, 0)
        financial_layout.addWidget(self.usd_rate_spin, 2, 1)
        financial_layout.addWidget(eur_rate_label, 3, 0)
        financial_layout.addWidget(self.eur_rate_spin, 3, 1)
        
        layout.addWidget(financial_group)
        layout.addStretch()
        
        return widget
        
    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي")
        apply_arabic_style(backup_group, 10, bold=True)
        
        backup_layout = QGridLayout(backup_group)
        backup_layout.setSpacing(10)
        
        # النسخ الاحتياطي التلقائي
        auto_backup_label = QLabel("النسخ الاحتياطي التلقائي:")
        apply_arabic_style(auto_backup_label, 10)
        self.auto_backup_check = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        apply_arabic_style(self.auto_backup_check, 10)
        
        # فترة النسخ الاحتياطي
        backup_interval_label = QLabel("فترة النسخ الاحتياطي (ساعات):")
        apply_arabic_style(backup_interval_label, 10)
        self.backup_interval_spin = QSpinBox()
        apply_arabic_style(self.backup_interval_spin, 10)
        self.backup_interval_spin.setRange(1, 168)  # من ساعة إلى أسبوع
        self.backup_interval_spin.setSuffix(" ساعة")
        
        # عدد النسخ الاحتياطية المحفوظة
        max_backups_label = QLabel("عدد النسخ الاحتياطية المحفوظة:")
        apply_arabic_style(max_backups_label, 10)
        self.max_backups_spin = QSpinBox()
        apply_arabic_style(self.max_backups_spin, 10)
        self.max_backups_spin.setRange(1, 100)
        self.max_backups_spin.setSuffix(" نسخة")
        
        backup_layout.addWidget(auto_backup_label, 0, 0)
        backup_layout.addWidget(self.auto_backup_check, 0, 1)
        backup_layout.addWidget(backup_interval_label, 1, 0)
        backup_layout.addWidget(self.backup_interval_spin, 1, 1)
        backup_layout.addWidget(max_backups_label, 2, 0)
        backup_layout.addWidget(self.max_backups_spin, 2, 1)

        # أزرار النسخ الاحتياطي
        backup_buttons_layout = QHBoxLayout()

        create_backup_button = QPushButton("إنشاء نسخة احتياطية")
        apply_arabic_style(create_backup_button, 10, bold=True)
        create_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        restore_backup_button = QPushButton("استعادة نسخة احتياطية")
        apply_arabic_style(restore_backup_button, 10, bold=True)
        restore_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)

        backup_buttons_layout.addWidget(create_backup_button)
        backup_buttons_layout.addWidget(restore_backup_button)

        backup_layout.addLayout(backup_buttons_layout, 3, 0, 1, 2)

        # ربط الأحداث
        create_backup_button.clicked.connect(self.create_backup)
        restore_backup_button.clicked.connect(self.restore_backup)
        
        # إعدادات الواجهة
        ui_group = QGroupBox("إعدادات الواجهة")
        apply_arabic_style(ui_group, 10, bold=True)
        
        ui_layout = QGridLayout(ui_group)
        ui_layout.setSpacing(10)
        
        # حجم الخط
        font_size_label = QLabel("حجم الخط:")
        apply_arabic_style(font_size_label, 10)
        self.font_size_spin = QSpinBox()
        apply_arabic_style(self.font_size_spin, 10)
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setSuffix(" نقطة")
        
        # نوع الخط
        font_family_label = QLabel("نوع الخط:")
        apply_arabic_style(font_family_label, 10)
        self.font_family_combo = QComboBox()
        apply_arabic_style(self.font_family_combo, 10)
        self.font_family_combo.addItems(["Tahoma", "Arial", "Segoe UI", "Calibri"])
        
        ui_layout.addWidget(font_size_label, 0, 0)
        ui_layout.addWidget(self.font_size_spin, 0, 1)
        ui_layout.addWidget(font_family_label, 1, 0)
        ui_layout.addWidget(self.font_family_combo, 1, 1)
        
        layout.addWidget(backup_group)
        layout.addWidget(ui_group)
        layout.addStretch()
        
        return widget

    def create_printing_tab(self):
        """إنشاء تبويب إعدادات الطباعة"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # مجموعة إعدادات الطباعة
        printing_group = QGroupBox("إعدادات الطباعة")
        apply_arabic_style(printing_group, 10, bold=True)

        printing_layout = QGridLayout(printing_group)
        printing_layout.setSpacing(10)

        # اسم الطابعة
        printer_label = QLabel("اسم الطابعة:")
        apply_arabic_style(printer_label, 10)
        self.printer_name_edit = QLineEdit()
        apply_arabic_style(self.printer_name_edit, 10)
        self.printer_name_edit.setPlaceholderText("اتركه فارغاً للطابعة الافتراضية")

        # زر اختيار الطابعة
        select_printer_button = QPushButton("اختيار طابعة")
        apply_arabic_style(select_printer_button, 10)
        select_printer_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # عدد النسخ
        copies_label = QLabel("عدد النسخ:")
        apply_arabic_style(copies_label, 10)
        self.copies_spin = QSpinBox()
        apply_arabic_style(self.copies_spin, 10)
        self.copies_spin.setRange(1, 10)
        self.copies_spin.setValue(1)

        # حجم الورق
        paper_size_label = QLabel("حجم الورق:")
        apply_arabic_style(paper_size_label, 10)
        self.paper_size_combo = QComboBox()
        apply_arabic_style(self.paper_size_combo, 10)
        self.paper_size_combo.addItems(["A4", "A5", "Letter", "Legal", "80mm (حراري)"])

        # اتجاه الطباعة
        orientation_label = QLabel("اتجاه الطباعة:")
        apply_arabic_style(orientation_label, 10)
        self.orientation_combo = QComboBox()
        apply_arabic_style(self.orientation_combo, 10)
        self.orientation_combo.addItems(["عمودي", "أفقي"])

        # جودة الطباعة
        quality_label = QLabel("جودة الطباعة:")
        apply_arabic_style(quality_label, 10)
        self.quality_combo = QComboBox()
        apply_arabic_style(self.quality_combo, 10)
        self.quality_combo.addItems(["عادية", "عالية", "مسودة"])

        # طباعة تلقائية
        auto_print_checkbox = QCheckBox("طباعة تلقائية للفواتير")
        apply_arabic_style(auto_print_checkbox, 10)
        self.auto_print_checkbox = auto_print_checkbox

        # طباعة الشعار
        print_logo_checkbox = QCheckBox("طباعة شعار الشركة")
        apply_arabic_style(print_logo_checkbox, 10)
        self.print_logo_checkbox = print_logo_checkbox

        printing_layout.addWidget(printer_label, 0, 0)
        printing_layout.addWidget(self.printer_name_edit, 0, 1)
        printing_layout.addWidget(select_printer_button, 0, 2)
        printing_layout.addWidget(copies_label, 1, 0)
        printing_layout.addWidget(self.copies_spin, 1, 1)
        printing_layout.addWidget(paper_size_label, 2, 0)
        printing_layout.addWidget(self.paper_size_combo, 2, 1)
        printing_layout.addWidget(orientation_label, 3, 0)
        printing_layout.addWidget(self.orientation_combo, 3, 1)
        printing_layout.addWidget(quality_label, 4, 0)
        printing_layout.addWidget(self.quality_combo, 4, 1)
        printing_layout.addWidget(auto_print_checkbox, 5, 0, 1, 3)
        printing_layout.addWidget(print_logo_checkbox, 6, 0, 1, 3)

        # مجموعة اختبار الطباعة
        test_group = QGroupBox("اختبار الطباعة")
        apply_arabic_style(test_group, 10, bold=True)

        test_layout = QVBoxLayout(test_group)
        test_layout.setSpacing(10)

        test_print_button = QPushButton("طباعة صفحة اختبار")
        apply_arabic_style(test_print_button, 10, bold=True)
        test_print_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        test_layout.addWidget(test_print_button)

        layout.addWidget(printing_group)
        layout.addWidget(test_group)
        layout.addStretch()

        # ربط الأحداث
        select_printer_button.clicked.connect(self.select_printer)
        test_print_button.clicked.connect(self.test_print)

        return widget

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر حفظ
        save_button = QPushButton("حفظ الإعدادات")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        # زر إعادة تعيين
        reset_button = QPushButton("إعادة تعيين")
        apply_arabic_style(reset_button, 10)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر إلغاء
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(reset_button)
        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(save_button)
        
        save_button.clicked.connect(self.save_settings)
        reset_button.clicked.connect(self.reset_settings)
        cancel_button.clicked.connect(self.reject)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات الحالية"""
        try:
            # معلومات الشركة
            company_info = self.config_manager.get_company_info()
            self.company_name_edit.setText(company_info.get('name', ''))
            self.company_address_edit.setPlainText(company_info.get('address', ''))
            self.company_phone_edit.setText(company_info.get('phone', ''))
            self.company_email_edit.setText(company_info.get('email', ''))
            self.logo_path_edit.setText(company_info.get('logo_path', ''))
            
            # الإعدادات المالية
            financial_settings = self.config_manager.get_financial_settings()
            self.subscription_fee_spin.setValue(financial_settings.get('subscription_fee', 50000))
            
            # العملة الافتراضية
            default_currency = financial_settings.get('default_currency', 'SYP')
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == default_currency:
                    self.currency_combo.setCurrentIndex(i)
                    break
                    
            # أسعار الصرف
            exchange_rates = financial_settings.get('exchange_rates', {})
            self.usd_rate_spin.setValue(exchange_rates.get('USD', 15000))
            self.eur_rate_spin.setValue(exchange_rates.get('EUR', 16000))
            
            # إعدادات النظام
            self.auto_backup_check.setChecked(self.config_manager.get('backup.auto_backup', True))
            self.backup_interval_spin.setValue(self.config_manager.get('backup.backup_interval', 24))
            self.max_backups_spin.setValue(self.config_manager.get('backup.max_backups', 30))
            
            # إعدادات الواجهة
            ui_settings = self.config_manager.get_ui_settings()
            self.font_size_spin.setValue(ui_settings.get('font_size', 10))
            
            font_family = ui_settings.get('font_family', 'Tahoma')
            index = self.font_family_combo.findText(font_family)
            if index >= 0:
                self.font_family_combo.setCurrentIndex(index)
                
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"خطأ في تحميل الإعدادات: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف الشعار",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            self.logo_path_edit.setText(file_path)
            
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ معلومات الشركة
            company_info = {
                'name': self.company_name_edit.text().strip(),
                'address': self.company_address_edit.toPlainText().strip(),
                'phone': self.company_phone_edit.text().strip(),
                'email': self.company_email_edit.text().strip(),
                'logo_path': self.logo_path_edit.text().strip()
            }
            self.config_manager.update_company_info(company_info)
            
            # حفظ الإعدادات المالية
            self.config_manager.set('financial.subscription_fee', self.subscription_fee_spin.value())
            self.config_manager.set('financial.default_currency', self.currency_combo.currentData())
            
            exchange_rates = {
                'USD': self.usd_rate_spin.value(),
                'EUR': self.eur_rate_spin.value()
            }
            self.config_manager.set('financial.exchange_rates', exchange_rates)
            
            # حفظ إعدادات النظام
            self.config_manager.set('backup.auto_backup', self.auto_backup_check.isChecked())
            self.config_manager.set('backup.backup_interval', self.backup_interval_spin.value())
            self.config_manager.set('backup.max_backups', self.max_backups_spin.value())
            
            # حفظ إعدادات الواجهة
            self.config_manager.set('ui.font_size', self.font_size_spin.value())
            self.config_manager.set('ui.font_family', self.font_family_combo.currentText())
            
            # إرسال إشارة التحديث
            self.settings_updated.emit()
            
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات: {e}")
            
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "تأكيد إعادة التعيين",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.config_manager.reset_to_defaults()
                self.load_data()
                QMessageBox.information(self, "نجح", "تم إعادة تعيين الإعدادات بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إعادة تعيين الإعدادات: {e}")

    def select_printer(self):
        """اختيار طابعة"""
        try:
            from PyQt5.QtPrintSupport import QPrintDialog, QPrinter

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                printer_name = printer.printerName()
                self.printer_name_edit.setText(printer_name)
                QMessageBox.information(self, "تم", f"تم اختيار الطابعة: {printer_name}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في اختيار الطابعة: {e}")

    def test_print(self):
        """طباعة صفحة اختبار"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect

            printer = QPrinter()

            # إعداد الطابعة
            if self.printer_name_edit.text().strip():
                printer.setPrinterName(self.printer_name_edit.text().strip())

            printer.setCopyCount(self.copies_spin.value())

            # إعداد حجم الورق
            paper_size = self.paper_size_combo.currentText()
            if "A4" in paper_size:
                printer.setPageSize(QPrinter.A4)
            elif "A5" in paper_size:
                printer.setPageSize(QPrinter.A5)
            elif "Letter" in paper_size:
                printer.setPageSize(QPrinter.Letter)
            elif "Legal" in paper_size:
                printer.setPageSize(QPrinter.Legal)

            # إعداد الاتجاه
            if self.orientation_combo.currentText() == "أفقي":
                printer.setOrientation(QPrinter.Landscape)
            else:
                printer.setOrientation(QPrinter.Portrait)

            # إعداد الجودة
            quality = self.quality_combo.currentText()
            if quality == "عالية":
                printer.setResolution(600)
            elif quality == "مسودة":
                printer.setResolution(150)
            else:
                printer.setResolution(300)

            # بدء الطباعة
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط
                font = QFont("Tahoma", 12)
                painter.setFont(font)

                # رسم النص
                rect = painter.viewport()

                test_text = f"""
صفحة اختبار الطباعة
{'='*30}

اسم الطابعة: {printer.printerName()}
حجم الورق: {self.paper_size_combo.currentText()}
الاتجاه: {self.orientation_combo.currentText()}
الجودة: {self.quality_combo.currentText()}
عدد النسخ: {self.copies_spin.value()}

تاريخ الطباعة: {QDate.currentDate().toString("yyyy-MM-dd")}
وقت الطباعة: {QTime.currentTime().toString("hh:mm:ss")}

هذا نص تجريبي للتأكد من عمل الطباعة بشكل صحيح.
يجب أن تظهر النصوص العربية بوضوح وبدون مشاكل.

شركة الإنترنت - نظام إدارة متكامل
"""

                painter.drawText(rect, Qt.AlignTop | Qt.AlignRight, test_text)
                painter.end()

                QMessageBox.information(self, "نجح", "تم إرسال صفحة الاختبار للطباعة")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في بدء عملية الطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة صفحة الاختبار: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import shutil
            import os
            from datetime import datetime

            # اختيار مجلد الحفظ
            backup_dir = QFileDialog.getExistingDirectory(
                self,
                "اختيار مجلد حفظ النسخة الاحتياطية",
                os.path.expanduser("~/Desktop")
            )

            if not backup_dir:
                return

            # إنشاء اسم الملف مع التاريخ والوقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_internet_company_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # نسخ قاعدة البيانات
            db_path = self.db_manager.db_path
            shutil.copy2(db_path, backup_path)

            QMessageBox.information(
                self,
                "تم",
                f"تم إنشاء النسخة الاحتياطية بنجاح\n\nالمسار: {backup_path}"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {e}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import shutil
            import os

            # تحذير المستخدم
            reply = QMessageBox.warning(
                self,
                "تحذير",
                "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية.\n"
                "هل أنت متأكد من المتابعة؟\n\n"
                "ننصح بإنشاء نسخة احتياطية من البيانات الحالية أولاً.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # اختيار ملف النسخة الاحتياطية
            backup_file, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف النسخة الاحتياطية",
                os.path.expanduser("~/Desktop"),
                "ملفات قاعدة البيانات (*.db);;جميع الملفات (*.*)"
            )

            if not backup_file:
                return

            # إغلاق الاتصال بقاعدة البيانات
            self.db_manager.close_connection()

            # استعادة النسخة الاحتياطية
            db_path = self.db_manager.db_path
            shutil.copy2(backup_file, db_path)

            # إعادة الاتصال بقاعدة البيانات
            self.db_manager.connect()

            QMessageBox.information(
                self,
                "تم",
                "تم استعادة النسخة الاحتياطية بنجاح\n\nسيتم إعادة تشغيل البرنامج"
            )

            # إعادة تشغيل التطبيق
            QApplication.quit()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في استعادة النسخة الاحتياطية: {e}")
            # إعادة الاتصال في حالة الخطأ
            try:
                self.db_manager.connect()
            except:
                pass

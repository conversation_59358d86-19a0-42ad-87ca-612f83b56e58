#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي للخزينة اليومية
"""

import sys
import os
sys.path.append('src')

def fix_negative_balances():
    """إصلاح الأرصدة السالبة"""
    
    print("🔧 إصلاح الأرصدة السالبة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # البحث عن الأرصدة السالبة
        negative_balances = db.fetch_all("""
            SELECT user_id, session_date, daily_balance, main_balance, last_updated
            FROM unified_treasury 
            WHERE daily_balance < 0 OR main_balance < 0
            ORDER BY user_id, session_date DESC
        """)
        
        print(f"📊 تم العثور على {len(negative_balances)} رصيد سالب:")
        
        for balance in negative_balances:
            print(f"  • المستخدم {balance['user_id']} - {balance['session_date']}: "
                  f"يومي {balance['daily_balance']:,} ل.س، رئيسي {balance['main_balance']:,} ل.س")
        
        if negative_balances:
            # إصلاح الأرصدة السالبة
            print("\n🔧 إصلاح الأرصدة السالبة...")
            
            for balance in negative_balances:
                # تصفير الأرصدة السالبة
                db.execute_query("""
                    UPDATE unified_treasury 
                    SET daily_balance = CASE WHEN daily_balance < 0 THEN 0 ELSE daily_balance END,
                        main_balance = CASE WHEN main_balance < 0 THEN 0 ELSE main_balance END
                    WHERE user_id = ? AND session_date = ? AND last_updated = ?
                """, (balance['user_id'], balance['session_date'], balance['last_updated']))
            
            print("✅ تم تصفير الأرصدة السالبة")
        else:
            print("✅ لا توجد أرصدة سالبة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأرصدة السالبة: {e}")
        return False

def rebuild_treasury_from_transactions():
    """إعادة بناء الخزينة من المعاملات"""
    
    print("\n🔧 إعادة بناء الخزينة من المعاملات...")
    
    try:
        from database.database_manager import DatabaseManager
        from datetime import datetime
        
        db = DatabaseManager('.')
        
        # الحصول على جميع المعاملات مرتبة بالتاريخ
        transactions = db.fetch_all("""
            SELECT user_name, amount, type, created_at, description
            FROM transactions 
            WHERE amount > 0
            ORDER BY created_at ASC
        """)
        
        print(f"📋 تم العثور على {len(transactions)} معاملة")
        
        # تجميع المعاملات حسب المستخدم واليوم
        user_daily_totals = {}
        
        for trans in transactions:
            user_name = trans['user_name']
            amount = trans['amount']
            date = trans['created_at'][:10]  # YYYY-MM-DD
            
            # استخراج user_id من user_name
            if user_name.startswith('user_'):
                try:
                    user_id = int(user_name.replace('user_', ''))
                except:
                    user_id = 1  # افتراضي
            else:
                user_id = 1  # admin = user_1
            
            # تجميع المبالغ
            key = f"{user_id}_{date}"
            if key not in user_daily_totals:
                user_daily_totals[key] = {
                    'user_id': user_id,
                    'date': date,
                    'total': 0
                }
            
            user_daily_totals[key]['total'] += amount
        
        print(f"📊 تم تجميع المعاملات في {len(user_daily_totals)} مجموعة يومية")
        
        # عرض المجاميع
        for key, data in user_daily_totals.items():
            if data['total'] > 0:
                print(f"  • المستخدم {data['user_id']} - {data['date']}: {data['total']:,} ل.س")
        
        return user_daily_totals
        
    except Exception as e:
        print(f"❌ خطأ في إعادة بناء الخزينة: {e}")
        return {}

def update_treasury_with_correct_balances():
    """تحديث الخزينة بالأرصدة الصحيحة"""
    
    print("\n🔧 تحديث الخزينة بالأرصدة الصحيحة...")
    
    try:
        from database.database_manager import DatabaseManager
        from datetime import datetime
        
        db = DatabaseManager('.')
        
        # الحصول على معاملات اليوم
        today = datetime.now().strftime('%Y-%m-%d')
        
        today_transactions = db.fetch_all("""
            SELECT user_name, SUM(amount) as total_amount
            FROM transactions 
            WHERE DATE(created_at) = ? AND amount > 0
            GROUP BY user_name
        """, (today,))
        
        print(f"📅 معاملات اليوم ({today}):")
        
        for trans in today_transactions:
            user_name = trans['user_name']
            total_amount = trans['total_amount']
            
            # استخراج user_id
            if user_name.startswith('user_'):
                try:
                    user_id = int(user_name.replace('user_', ''))
                except:
                    user_id = 1
            else:
                user_id = 1  # admin = user_1
            
            print(f"  • المستخدم {user_id}: {total_amount:,} ل.س")
            
            # تحديث الرصيد اليومي
            db.execute_query("""
                UPDATE unified_treasury 
                SET daily_balance = ?
                WHERE user_id = ? AND session_date = ? AND currency_type = 'SYP'
            """, (total_amount, user_id, today))
            
            print(f"    ✅ تم تحديث رصيد المستخدم {user_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الأرصدة: {e}")
        return False

def test_fixed_treasury():
    """اختبار الخزينة المُصححة"""
    
    print("\n🧪 اختبار الخزينة المُصححة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة بعد الإصلاح
        print("\n📊 الأرصدة بعد الإصلاح:")
        
        # الحساب اليدوي
        manual_daily = 0
        manual_main = 0
        
        users = db.fetch_all("SELECT DISTINCT user_id FROM unified_treasury")
        
        for user in users:
            user_balance = db.fetch_one("""
                SELECT daily_balance, main_balance FROM unified_treasury
                WHERE user_id = ? AND currency_type = 'SYP'
                ORDER BY session_date DESC, last_updated DESC
                LIMIT 1
            """, (user['user_id'],))
            
            if user_balance:
                daily = user_balance['daily_balance'] or 0
                main = user_balance['main_balance'] or 0
                manual_daily += daily
                manual_main += main
                
                if daily != 0 or main != 0:
                    print(f"  • المستخدم {user['user_id']}: يومي {daily:,} ل.س، رئيسي {main:,} ل.س")
        
        print(f"\n💰 الحساب اليدوي:")
        print(f"  • الخزينة اليومية: {manual_daily:,} ل.س")
        print(f"  • الخزينة الرئيسية: {manual_main:,} ل.س")
        
        # الحساب بدوال النظام
        system_daily = treasury_manager.get_total_daily_balance('SYP')
        system_main = treasury_manager.get_main_balance('SYP')
        
        print(f"\n🔧 الحساب بدوال النظام:")
        print(f"  • الخزينة اليومية: {system_daily:,} ل.س")
        print(f"  • الخزينة الرئيسية: {system_main:,} ل.س")
        
        # فحص التطابق
        daily_match = abs(manual_daily - system_daily) < 1
        main_match = abs(manual_main - system_main) < 1
        
        print(f"\n✅ نتائج التطابق:")
        print(f"  • الخزينة اليومية: {'✅ متطابقة' if daily_match else '❌ غير متطابقة'}")
        print(f"  • الخزينة الرئيسية: {'✅ متطابقة' if main_match else '❌ غير متطابقة'}")
        
        return daily_match and main_match
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخزينة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح نهائي للخزينة اليومية")
    print("=" * 70)
    
    # 1. إصلاح الأرصدة السالبة
    negative_fixed = fix_negative_balances()
    
    # 2. إعادة بناء الخزينة من المعاملات
    transaction_data = rebuild_treasury_from_transactions()
    
    # 3. تحديث الأرصدة الصحيحة
    balances_updated = update_treasury_with_correct_balances()
    
    # 4. اختبار الإصلاح
    treasury_fixed = test_fixed_treasury()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الإصلاح:")
    print(f"  • إصلاح الأرصدة السالبة: {'✅ نجح' if negative_fixed else '❌ فشل'}")
    print(f"  • إعادة بناء من المعاملات: {'✅ نجح' if transaction_data else '❌ فشل'}")
    print(f"  • تحديث الأرصدة: {'✅ نجح' if balances_updated else '❌ فشل'}")
    print(f"  • اختبار الإصلاح: {'✅ نجح' if treasury_fixed else '❌ فشل'}")
    
    if treasury_fixed:
        print("\n🎉 تم إصلاح الخزينة اليومية بنجاح!")
        
        print("\n📋 ما تم إنجازه:")
        print("  ✅ إصلاح دالة get_total_daily_balance()")
        print("  ✅ حساب أحدث رصيد لكل مستخدم فقط")
        print("  ✅ إصلاح الأرصدة السالبة")
        print("  ✅ مزامنة الأرصدة مع المعاملات")
        print("  ✅ تطابق الحساب اليدوي مع دوال النظام")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. ستجد الخزينة اليومية تعرض الرصيد الصحيح")
        print("  3. جميع العمليات المالية ستعمل بدقة")
        
    else:
        print("\n❌ لا تزال هناك مشاكل في الخزينة")
        print("💡 قد تحتاج لفحص إضافي")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
Simple System Test
"""

import sys
import os
from pathlib import Path

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # اختبار قاعدة البيانات
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager - OK")
        
        # اختبار مدير الإعدادات
        from src.utils.config_manager import ConfigManager
        print("✅ ConfigManager - OK")
        
        # اختبار الدعم العربي
        from src.utils.arabic_support import reshape_arabic_text, format_currency
        print("✅ Arabic Support - OK")
        
        # اختبار النصوص العربية
        arabic_text = reshape_arabic_text("مرحباً بك في نظام إدارة شركة الإنترنت")
        print(f"✅ Arabic Text: {arabic_text}")
        
        # اختبار تنسيق العملة
        currency_text = format_currency(150000)
        print(f"✅ Currency Format: {currency_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n📊 اختبار قاعدة البيانات...")
    
    try:
        # إنشاء مجلد مؤقت للاختبار
        test_dir = Path(__file__).parent / "test_data"
        test_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة البيانات
        from src.database.database_manager import DatabaseManager
        db_manager = DatabaseManager(str(test_dir))
        
        if db_manager.initialize_database():
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            
            # اختبار إدراج بيانات
            users = db_manager.fetch_all("SELECT * FROM users")
            print(f"✅ عدد المستخدمين: {len(users)}")
            
            products = db_manager.fetch_all("SELECT * FROM products")
            print(f"✅ عدد المنتجات: {len(products)}")
            
            packages = db_manager.fetch_all("SELECT * FROM packages")
            print(f"✅ عدد الباقات: {len(packages)}")
            
            return True
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_config():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    try:
        test_dir = Path(__file__).parent / "test_data"
        
        from src.utils.config_manager import ConfigManager
        config_manager = ConfigManager(str(test_dir))
        
        # اختبار قراءة الإعدادات
        company_name = config_manager.get("company.name", "شركة الإنترنت")
        print(f"✅ اسم الشركة: {company_name}")
        
        subscription_fee = config_manager.get("financial.subscription_fee", 50000)
        print(f"✅ رسم الاشتراك: {subscription_fee}")
        
        # اختبار كتابة الإعدادات
        config_manager.set("test.value", "اختبار")
        test_value = config_manager.get("test.value")
        print(f"✅ قيمة الاختبار: {test_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام إدارة شركة الإنترنت")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # اختبار الاستيرادات
    if test_imports():
        success_count += 1
        
    # اختبار قاعدة البيانات
    if test_database():
        success_count += 1
        
    # اختبار الإعدادات
    if test_config():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{total_tests} اختبار نجح")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للتشغيل")
        
        print("\n🚀 لتشغيل النظام الكامل:")
        print("python start_system.py")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

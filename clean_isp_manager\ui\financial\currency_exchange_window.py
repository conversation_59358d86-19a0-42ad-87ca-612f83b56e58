#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة شراء الدولار وصرف العملة المتجاوبة
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QGridLayout, QPushButton, QLabel, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QGroupBox, QFrame, QMessageBox, QScrollArea,
                             QButtonGroup, QRadioButton, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager
from utils.window_utils import center_window, apply_modern_style
from config.app_config import AppConfig

class CurrencyExchangeWindow(QDialog):
    """واجهة شراء الدولار وصرف العملة المتجاوبة"""
    
    # إشارات مخصصة
    exchange_completed = pyqtSignal(dict)
    rates_updated = pyqtSignal()
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """تهيئة واجهة صرف العملة"""
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.current_user = current_user
        self.treasury_manager = TreasuryManager(db_manager)
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الحالة
        self.current_rates = {}
        self.current_balances = {}
        self.is_mobile_view = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.setup_responsive_design()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        # بدء المؤقتات
        self.start_timers()
        
        self.logger.info("تم فتح واجهة صرف العملة")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتجاوبة"""
        
        # إعداد النافذة
        self.setWindowTitle("شراء الدولار وصرف العملة")
        self.setModal(True)
        self.setMinimumSize(700, 600)
        self.resize(1000, 800)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الرئيسي
        main_widget = QFrame()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الأقسام
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        rates_section = self.create_rates_section()
        main_layout.addWidget(rates_section)
        
        balances_section = self.create_balances_section()
        main_layout.addWidget(balances_section)
        
        exchange_section = self.create_exchange_section()
        main_layout.addWidget(exchange_section)
        
        calculator_section = self.create_calculator_section()
        main_layout.addWidget(calculator_section)
        
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        # إعداد التخطيط النهائي
        dialog_layout = QVBoxLayout()
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(scroll_area)
        self.setLayout(dialog_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # أيقونة صرف العملة
        icon_label = QLabel("💱")
        icon_label.setStyleSheet("font-size: 28px; margin-right: 10px;")
        layout.addWidget(icon_label)
        
        # معلومات الهيدر
        info_layout = QVBoxLayout()
        
        title_label = QLabel("شراء الدولار وصرف العملة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        info_layout.addWidget(title_label)
        
        subtitle_label = QLabel("تحويل العملات وإدارة أسعار الصرف")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #fdeaa7;
                margin: 0;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # معلومات المستخدم والوقت
        user_info_layout = QVBoxLayout()
        
        user_label = QLabel(f"المستخدم: {self.current_user['full_name']}")
        user_label.setStyleSheet("font-size: 12px; color: #fdeaa7;")
        user_info_layout.addWidget(user_label)
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 11px; color: #fdcb6e;")
        user_info_layout.addWidget(self.time_label)
        
        layout.addLayout(user_info_layout)
        
        return frame
    
    def create_rates_section(self) -> QGroupBox:
        """إنشاء قسم أسعار الصرف"""
        
        group = QGroupBox("أسعار الصرف الحالية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # سعر الشراء
        buy_frame = self.create_rate_card("سعر الشراء", "USD → SYP", "#27ae60")
        self.buy_rate_label = buy_frame.findChild(QLabel, "rate_label")
        layout.addWidget(buy_frame, 0, 0)
        
        # سعر البيع
        sell_frame = self.create_rate_card("سعر البيع", "SYP → USD", "#e74c3c")
        self.sell_rate_label = sell_frame.findChild(QLabel, "rate_label")
        layout.addWidget(sell_frame, 0, 1)
        
        # تحديث أسعار الصرف
        rates_update_layout = QHBoxLayout()
        
        self.buy_rate_edit = QDoubleSpinBox()
        self.buy_rate_edit.setRange(1, 999999)
        self.buy_rate_edit.setValue(15000)
        self.buy_rate_edit.setSuffix(" ل.س")
        self.buy_rate_edit.setMinimumHeight(35)
        rates_update_layout.addWidget(QLabel("سعر الشراء:"))
        rates_update_layout.addWidget(self.buy_rate_edit)
        
        self.sell_rate_edit = QDoubleSpinBox()
        self.sell_rate_edit.setRange(1, 999999)
        self.sell_rate_edit.setValue(15100)
        self.sell_rate_edit.setSuffix(" ل.س")
        self.sell_rate_edit.setMinimumHeight(35)
        rates_update_layout.addWidget(QLabel("سعر البيع:"))
        rates_update_layout.addWidget(self.sell_rate_edit)
        
        update_rates_btn = QPushButton("تحديث الأسعار")
        update_rates_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        update_rates_btn.clicked.connect(self.update_exchange_rates)
        rates_update_layout.addWidget(update_rates_btn)
        
        layout.addLayout(rates_update_layout, 1, 0, 1, 2)
        
        return group
    
    def create_rate_card(self, title: str, direction: str, color: str) -> QFrame:
        """إنشاء بطاقة سعر صرف"""
        
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                color: white;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 80px;
            }}
            QFrame:hover {{
                background-color: {self.darken_color(color)};
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # عنوان البطاقة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # اتجاه التحويل
        direction_label = QLabel(direction)
        direction_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(direction_label)
        
        # قيمة السعر
        rate_label = QLabel("15,000")
        rate_label.setObjectName("rate_label")
        rate_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                margin: 5px 0;
            }
        """)
        layout.addWidget(rate_label)
        
        # رمز العملة
        symbol_label = QLabel("ل.س / $")
        symbol_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(symbol_label)
        
        return frame
    
    def create_balances_section(self) -> QGroupBox:
        """إنشاء قسم الأرصدة"""
        
        group = QGroupBox("الأرصدة المتاحة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #8e44ad;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #8e44ad;
                background-color: white;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رصيد الليرة السورية
        syp_frame = self.create_balance_card("الليرة السورية", "SYP", "#3498db")
        self.syp_balance_label = syp_frame.findChild(QLabel, "balance_label")
        layout.addWidget(syp_frame, 0, 0)
        
        # رصيد الدولار
        usd_frame = self.create_balance_card("الدولار الأمريكي", "USD", "#f39c12")
        self.usd_balance_label = usd_frame.findChild(QLabel, "balance_label")
        layout.addWidget(usd_frame, 0, 1)
        
        return group
    
    def create_balance_card(self, title: str, currency: str, color: str) -> QFrame:
        """إنشاء بطاقة رصيد"""
        
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                color: white;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 70px;
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # عنوان البطاقة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # قيمة الرصيد
        balance_label = QLabel("0")
        balance_label.setObjectName("balance_label")
        balance_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                margin: 5px 0;
            }
        """)
        layout.addWidget(balance_label)
        
        # رمز العملة
        symbol = "ل.س" if currency == "SYP" else "$"
        symbol_label = QLabel(symbol)
        symbol_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(symbol_label)
        
        return frame
    
    def create_exchange_section(self) -> QGroupBox:
        """إنشاء قسم الصرف"""
        
        group = QGroupBox("عملية الصرف")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #27ae60;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # نوع العملية
        operation_layout = QHBoxLayout()
        self.operation_group = QButtonGroup()
        
        self.buy_usd_radio = QRadioButton("شراء دولار (ل.س → $)")
        self.buy_usd_radio.setChecked(True)
        self.operation_group.addButton(self.buy_usd_radio, 0)
        operation_layout.addWidget(self.buy_usd_radio)
        
        self.sell_usd_radio = QRadioButton("بيع دولار ($ → ل.س)")
        self.operation_group.addButton(self.sell_usd_radio, 1)
        operation_layout.addWidget(self.sell_usd_radio)
        
        layout.addRow("نوع العملية:", operation_layout)
        
        # المبلغ المدفوع
        self.input_amount_spin = QDoubleSpinBox()
        self.input_amount_spin.setRange(0, 999999999)
        self.input_amount_spin.setDecimals(0)
        self.input_amount_spin.setSuffix(" ل.س")
        self.input_amount_spin.setMinimumHeight(40)
        self.input_amount_spin.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addRow("المبلغ المدفوع:", self.input_amount_spin)
        
        # المبلغ المستلم
        self.output_amount_label = QLabel("0 $")
        self.output_amount_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                background-color: #d5f4e6;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #27ae60;
            }
        """)
        layout.addRow("المبلغ المستلم:", self.output_amount_label)
        
        # العمولة
        self.commission_spin = QDoubleSpinBox()
        self.commission_spin.setRange(0, 100)
        self.commission_spin.setValue(0)
        self.commission_spin.setSuffix(" %")
        self.commission_spin.setMinimumHeight(35)
        layout.addRow("العمولة:", self.commission_spin)
        
        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات...")
        self.notes_edit.setMaximumHeight(60)
        layout.addRow("الملاحظات:", self.notes_edit)
        
        return group
    
    def create_calculator_section(self) -> QGroupBox:
        """إنشاء قسم الحاسبة"""
        
        group = QGroupBox("حاسبة الصرف")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8f5ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #9b59b6;
                background-color: #f8f5ff;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        self.calculator_label = QLabel("أدخل المبلغ لحساب قيمة الصرف")
        self.calculator_label.setStyleSheet("""
            QLabel {
                color: #8e44ad;
                font-size: 13px;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)
        self.calculator_label.setWordWrap(True)
        layout.addWidget(self.calculator_label)
        
        return group
    
    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار"""
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # زر تنفيذ الصرف
        self.exchange_button = QPushButton("💱 تنفيذ الصرف")
        self.exchange_button.setMinimumHeight(45)
        self.exchange_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.exchange_button)
        
        # زر تحديث الأرصدة
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setMinimumHeight(45)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_button)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setMinimumHeight(45)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)
        
        return frame
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        
        # ربط الأزرار
        self.exchange_button.clicked.connect(self.execute_exchange)
        
        # ربط تغيير نوع العملية
        self.operation_group.buttonClicked.connect(self.on_operation_changed)
        
        # ربط تغيير المبلغ
        self.input_amount_spin.valueChanged.connect(self.calculate_exchange)
        self.commission_spin.valueChanged.connect(self.calculate_exchange)
        
        # ربط تغيير أسعار الصرف
        self.buy_rate_edit.valueChanged.connect(self.calculate_exchange)
        self.sell_rate_edit.valueChanged.connect(self.calculate_exchange)
        
        # ربط الإشارات المخصصة
        self.exchange_completed.connect(self.on_exchange_completed)
    
    def setup_responsive_design(self):
        """إعداد التصميم المتجاوب"""
        
        screen_size = self.size()
        
        if screen_size.width() < 900 or screen_size.height() < 700:
            self.is_mobile_view = True
            self.apply_mobile_styles()
        else:
            self.is_mobile_view = False
            self.apply_desktop_styles()
    
    def apply_mobile_styles(self):
        """تطبيق أنماط الجوال"""
        
        mobile_style = """
            QLabel { font-size: 11px; }
            QPushButton { font-size: 11px; padding: 6px 12px; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { 
                font-size: 11px; 
                min-height: 30px; 
            }
        """
        self.setStyleSheet(self.styleSheet() + mobile_style)
    
    def apply_desktop_styles(self):
        """تطبيق أنماط سطح المكتب"""
        
        desktop_style = """
            QLabel { font-size: 10pt; }
            QPushButton { font-size: 10pt; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { 
                font-size: 10pt; 
            }
        """
        self.setStyleSheet(self.styleSheet() + desktop_style)
    
    def darken_color(self, color: str, factor: float = 0.2) -> str:
        """تغميق لون"""
        color_obj = QColor(color)
        h, s, v, a = color_obj.getHsv()
        v = max(0, int(v * (1 - factor)))
        color_obj.setHsv(h, s, v, a)
        return color_obj.name()
    
    def start_timers(self):
        """بدء المؤقتات"""
        
        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        
        # تحديث أولي
        self.update_time()
    
    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime('%H:%M:%S')
        self.time_label.setText(f"الوقت: {current_time}")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.load_exchange_rates()
            self.refresh_balances()
            self.calculate_exchange()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
    
    def load_exchange_rates(self):
        """تحميل أسعار الصرف"""
        try:
            # الحصول على أسعار الصرف من الإعدادات
            buy_rate_setting = self.db_manager.fetch_one("""
                SELECT setting_value FROM financial_settings 
                WHERE setting_name = 'usd_buy_rate'
            """)
            
            sell_rate_setting = self.db_manager.fetch_one("""
                SELECT setting_value FROM financial_settings 
                WHERE setting_name = 'usd_sell_rate'
            """)
            
            # استخدام القيم الافتراضية إذا لم توجد
            buy_rate = float(buy_rate_setting['setting_value']) if buy_rate_setting else 15000
            sell_rate = float(sell_rate_setting['setting_value']) if sell_rate_setting else 15100
            
            # تحديث الواجهة
            self.buy_rate_edit.setValue(buy_rate)
            self.sell_rate_edit.setValue(sell_rate)
            
            self.buy_rate_label.setText(f"{buy_rate:,.0f}")
            self.sell_rate_label.setText(f"{sell_rate:,.0f}")
            
            # حفظ الأسعار الحالية
            self.current_rates = {
                'buy_rate': buy_rate,
                'sell_rate': sell_rate
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل أسعار الصرف: {e}")
    
    def refresh_balances(self):
        """تحديث الأرصدة"""
        try:
            # الحصول على الأرصدة
            syp_balance = self.treasury_manager.get_user_daily_balance(self.current_user['id'], 'SYP')
            usd_balance = self.treasury_manager.get_user_daily_balance(self.current_user['id'], 'USD')
            
            # تحديث العرض
            self.syp_balance_label.setText(f"{syp_balance:,.0f}")
            self.usd_balance_label.setText(f"{usd_balance:.2f}")
            
            # حفظ الأرصدة الحالية
            self.current_balances = {
                'syp': syp_balance,
                'usd': usd_balance
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الأرصدة: {e}")
    
    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_exchange_rates()
        self.refresh_balances()
        self.calculate_exchange()
    
    def update_exchange_rates(self):
        """تحديث أسعار الصرف في قاعدة البيانات"""
        try:
            buy_rate = self.buy_rate_edit.value()
            sell_rate = self.sell_rate_edit.value()
            
            # التحقق من صحة الأسعار
            if buy_rate >= sell_rate:
                QMessageBox.warning(
                    self, "تحذير", 
                    "سعر البيع يجب أن يكون أكبر من سعر الشراء"
                )
                return
            
            # تحديث في قاعدة البيانات
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO financial_settings (setting_name, setting_value, description)
                VALUES ('usd_buy_rate', ?, 'سعر شراء الدولار')
            """, (str(buy_rate),))
            
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO financial_settings (setting_name, setting_value, description)
                VALUES ('usd_sell_rate', ?, 'سعر بيع الدولار')
            """, (str(sell_rate),))
            
            # تحديث العرض
            self.buy_rate_label.setText(f"{buy_rate:,.0f}")
            self.sell_rate_label.setText(f"{sell_rate:,.0f}")
            
            # حفظ الأسعار الحالية
            self.current_rates = {
                'buy_rate': buy_rate,
                'sell_rate': sell_rate
            }
            
            # إعادة حساب الصرف
            self.calculate_exchange()
            
            QMessageBox.information(self, "نجح", "تم تحديث أسعار الصرف بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث أسعار الصرف: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث أسعار الصرف: {e}")
    
    def on_operation_changed(self):
        """معالجة تغيير نوع العملية"""
        
        if self.buy_usd_radio.isChecked():
            # شراء دولار
            self.input_amount_spin.setSuffix(" ل.س")
            self.input_amount_spin.setDecimals(0)
        else:
            # بيع دولار
            self.input_amount_spin.setSuffix(" $")
            self.input_amount_spin.setDecimals(2)
        
        self.calculate_exchange()
    
    def calculate_exchange(self):
        """حساب قيمة الصرف"""
        try:
            input_amount = self.input_amount_spin.value()
            commission_rate = self.commission_spin.value() / 100
            
            if input_amount <= 0:
                self.output_amount_label.setText("0")
                self.calculator_label.setText("أدخل المبلغ لحساب قيمة الصرف")
                return
            
            if self.buy_usd_radio.isChecked():
                # شراء دولار (ل.س → $)
                rate = self.current_rates.get('buy_rate', 15000)
                output_amount = input_amount / rate
                commission = output_amount * commission_rate
                final_amount = output_amount - commission
                
                self.output_amount_label.setText(f"{final_amount:.2f} $")
                
                calculator_text = f"""
💰 حساب شراء الدولار:

📥 المبلغ المدفوع: {input_amount:,.0f} ل.س
💱 سعر الشراء: {rate:,.0f} ل.س/$
💵 المبلغ قبل العمولة: {output_amount:.2f} $
💸 العمولة ({self.commission_spin.value()}%): {commission:.2f} $
✅ المبلغ النهائي: {final_amount:.2f} $
                """
                
            else:
                # بيع دولار ($ → ل.س)
                rate = self.current_rates.get('sell_rate', 15100)
                output_amount = input_amount * rate
                commission = output_amount * commission_rate
                final_amount = output_amount - commission
                
                self.output_amount_label.setText(f"{final_amount:,.0f} ل.س")
                
                calculator_text = f"""
💰 حساب بيع الدولار:

📥 المبلغ المدفوع: {input_amount:.2f} $
💱 سعر البيع: {rate:,.0f} ل.س/$
💵 المبلغ قبل العمولة: {output_amount:,.0f} ل.س
💸 العمولة ({self.commission_spin.value()}%): {commission:,.0f} ل.س
✅ المبلغ النهائي: {final_amount:,.0f} ل.س
                """
            
            self.calculator_label.setText(calculator_text.strip())
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الصرف: {e}")
    
    def execute_exchange(self):
        """تنفيذ عملية الصرف"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_exchange_data():
                return
            
            # جمع بيانات الصرف
            input_amount = self.input_amount_spin.value()
            commission_rate = self.commission_spin.value() / 100
            notes = self.notes_edit.toPlainText().strip()
            
            if self.buy_usd_radio.isChecked():
                # شراء دولار
                from_currency = 'SYP'
                to_currency = 'USD'
                rate = self.current_rates.get('buy_rate', 15000)
                operation_type = "شراء دولار"
            else:
                # بيع دولار
                from_currency = 'USD'
                to_currency = 'SYP'
                rate = self.current_rates.get('sell_rate', 15100)
                operation_type = "بيع دولار"
            
            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الصرف",
                f"هل أنت متأكد من {operation_type}؟\n"
                f"المبلغ: {input_amount:,.2f if from_currency == 'USD' else input_amount:,.0f} "
                f"{'$' if from_currency == 'USD' else 'ل.س'}",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تعطيل الزر أثناء المعالجة
            self.exchange_button.setEnabled(False)
            self.exchange_button.setText("جاري الصرف...")
            
            # تنفيذ الصرف
            success = self.treasury_manager.exchange_currency(
                user_id=self.current_user['id'],
                from_currency=from_currency,
                to_currency=to_currency,
                from_amount=input_amount,
                exchange_rate=rate,
                notes=f"{operation_type} - {notes}" if notes else operation_type
            )
            
            if success:
                # إرسال إشارة النجاح
                exchange_data = {
                    'operation_type': operation_type,
                    'input_amount': input_amount,
                    'from_currency': from_currency,
                    'to_currency': to_currency,
                    'rate': rate,
                    'commission': commission_rate,
                    'notes': notes
                }
                self.exchange_completed.emit(exchange_data)
                
                QMessageBox.information(
                    self, "نجح الصرف",
                    f"تم {operation_type} بنجاح"
                )
                
                # مسح النموذج
                self.clear_form()
                
            else:
                QMessageBox.critical(
                    self, "فشل الصرف",
                    "فشل في تنفيذ عملية الصرف. يرجى التحقق من الرصيد المتاح."
                )
            
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الصرف: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ الصرف: {e}")
            
        finally:
            # إعادة تفعيل الزر
            self.exchange_button.setEnabled(True)
            self.exchange_button.setText("💱 تنفيذ الصرف")
    
    def validate_exchange_data(self) -> bool:
        """التحقق من صحة بيانات الصرف"""
        
        input_amount = self.input_amount_spin.value()
        
        # التحقق من المبلغ
        if input_amount <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.input_amount_spin.setFocus()
            return False
        
        # التحقق من توفر الرصيد
        if self.buy_usd_radio.isChecked():
            # شراء دولار - التحقق من رصيد الليرة
            available_balance = self.current_balances.get('syp', 0)
            if input_amount > available_balance:
                QMessageBox.warning(
                    self, "تحذير", 
                    f"الرصيد غير كافي\n"
                    f"الرصيد المتاح: {available_balance:,.0f} ل.س"
                )
                return False
        else:
            # بيع دولار - التحقق من رصيد الدولار
            available_balance = self.current_balances.get('usd', 0)
            if input_amount > available_balance:
                QMessageBox.warning(
                    self, "تحذير", 
                    f"الرصيد غير كافي\n"
                    f"الرصيد المتاح: ${available_balance:.2f}"
                )
                return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.input_amount_spin.setValue(0)
        self.commission_spin.setValue(0)
        self.notes_edit.clear()
        
        # تحديث البيانات
        self.refresh_data()
    
    def on_exchange_completed(self, exchange_data: dict):
        """معالجة اكتمال الصرف"""
        self.logger.info(f"تم {exchange_data['operation_type']} بنجاح")
        
        # تحديث الأرصدة
        self.refresh_balances()
    
    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للتجاوب"""
        super().resizeEvent(event)
        self.setup_responsive_design()
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        # إيقاف المؤقتات
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()
        
        event.accept()

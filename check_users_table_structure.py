#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية جدول المستخدمين والتأكد من التوافق
"""

import sys
import os
import hashlib
sys.path.append('src')

def check_table_structure():
    """فحص بنية جدول المستخدمين"""
    
    print("🔍 فحص بنية جدول المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # فحص بنية الجدول
        columns = db.fetch_all("PRAGMA table_info(users)")
        
        print(f"📊 بنية جدول المستخدمين ({len(columns)} عمود):")
        for col in columns:
            print(f"  • {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4] if col[4] else 'None'}")
        
        # فحص الفهارس
        indexes = db.fetch_all("PRAGMA index_list(users)")
        print(f"\n📋 الفهارس ({len(indexes)}):")
        for idx in indexes:
            print(f"  • {idx[1]} - Unique: {idx[2]}")
        
        # فحص المستخدمين الموجودين
        users = db.fetch_all("SELECT id, username, full_name, email, role, is_active FROM users")
        print(f"\n👥 المستخدمين الموجودين ({len(users)}):")
        
        for user in users:
            print(f"  • ID: {user[0]}")
            print(f"    اسم المستخدم: '{user[1]}'")
            print(f"    الاسم الكامل: '{user[2]}'")
            print(f"    البريد الإلكتروني: '{user[3] if user[3] else 'غير محدد'}'")
            print(f"    الدور: '{user[4]}'")
            print(f"    نشط: {user[5]}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_management_compatibility():
    """اختبار التوافق مع واجهة إدارة المستخدمين"""
    
    print("🧪 اختبار التوافق مع واجهة إدارة المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # محاكاة إضافة مستخدم من واجهة إدارة المستخدمين
        test_username = 'test_user_mgmt'
        test_password = 'test123'
        test_full_name = 'مستخدم تجريبي'
        test_email = '<EMAIL>'
        test_role = 'employee'
        test_is_active = 1
        
        # تشفير كلمة المرور (نفس الطريقة في واجهة إدارة المستخدمين)
        password_hash = hashlib.sha256(test_password.encode()).hexdigest()
        
        print(f"📝 إضافة مستخدم تجريبي:")
        print(f"  • اسم المستخدم: {test_username}")
        print(f"  • كلمة المرور: {test_password}")
        print(f"  • الاسم الكامل: {test_full_name}")
        print(f"  • البريد الإلكتروني: {test_email}")
        print(f"  • الدور: {test_role}")
        print(f"  • نشط: {test_is_active}")
        
        # حذف المستخدم إذا كان موجوداً
        db.execute_query("DELETE FROM users WHERE username = ?", (test_username,))
        
        # إضافة المستخدم (نفس الاستعلام في واجهة إدارة المستخدمين)
        result = db.execute_query("""
            INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (test_username, password_hash, test_full_name, test_email, test_role, test_is_active))
        
        if result:
            print("✅ تم إضافة المستخدم بنجاح")
            
            # التحقق من المستخدم
            added_user = db.fetch_one("SELECT * FROM users WHERE username = ?", (test_username,))
            
            if added_user:
                print("✅ تم العثور على المستخدم في قاعدة البيانات")
                print(f"  • ID: {added_user['id']}")
                print(f"  • اسم المستخدم: {added_user['username']}")
                print(f"  • كلمة المرور المشفرة: {added_user['password_hash'][:20]}...")
                print(f"  • الاسم الكامل: {added_user['full_name']}")
                print(f"  • البريد الإلكتروني: {added_user['email']}")
                print(f"  • الدور: {added_user['role']}")
                print(f"  • نشط: {added_user['is_active']}")
                
                # اختبار تسجيل الدخول (نفس الطريقة في واجهة تسجيل الدخول)
                login_test = db.fetch_one("""
                    SELECT * FROM users 
                    WHERE username = ? AND password_hash = ? AND is_active = 1
                """, (test_username, password_hash))
                
                if login_test:
                    print("✅ اختبار تسجيل الدخول نجح")
                    
                    # تنظيف - حذف المستخدم التجريبي
                    db.execute_query("DELETE FROM users WHERE username = ?", (test_username,))
                    print("🗑️ تم حذف المستخدم التجريبي")
                    
                    return True
                else:
                    print("❌ اختبار تسجيل الدخول فشل")
                    return False
            else:
                print("❌ لم يتم العثور على المستخدم بعد الإضافة")
                return False
        else:
            print("❌ فشل في إضافة المستخدم")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوافق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_users_login():
    """اختبار تسجيل الدخول للمستخدمين الموجودين"""
    
    print("\n🧪 اختبار تسجيل الدخول للمستخدمين الموجودين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # الحصول على جميع المستخدمين النشطين
        users = db.fetch_all("SELECT username FROM users WHERE is_active = 1")
        
        print(f"👥 المستخدمين النشطين ({len(users)}):")
        
        success_count = 0
        
        for user in users:
            username = user['username']
            
            # محاولة تسجيل الدخول بكلمة المرور الافتراضية
            password = '123'
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # اختبار تسجيل الدخول
            login_user = db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, hashed_password))
            
            if login_user:
                print(f"  ✅ {username} / {password} - نجح")
                success_count += 1
            else:
                print(f"  ❌ {username} / {password} - فشل")
                
                # فحص السبب
                user_check = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
                if user_check:
                    if user_check['password_hash'] != hashed_password:
                        print(f"    السبب: كلمة المرور خاطئة")
                        print(f"    المتوقع: {user_check['password_hash'][:20]}...")
                        print(f"    المدخل: {hashed_password[:20]}...")
                    if user_check['is_active'] != 1:
                        print(f"    السبب: المستخدم غير نشط ({user_check['is_active']})")
        
        print(f"\n📊 النتيجة: {success_count}/{len(users)} مستخدمين يمكنهم تسجيل الدخول")
        
        return success_count == len(users)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 فحص التوافق بين واجهة تسجيل الدخول وإدارة المستخدمين")
    print("=" * 70)
    
    # فحص بنية الجدول
    structure_check = check_table_structure()
    
    # اختبار التوافق مع واجهة إدارة المستخدمين
    compatibility_test = test_user_management_compatibility()
    
    # اختبار تسجيل الدخول للمستخدمين الموجودين
    existing_users_test = test_existing_users_login()
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"  • فحص بنية الجدول: {'✅ نجح' if structure_check else '❌ فشل'}")
    print(f"  • اختبار التوافق: {'✅ نجح' if compatibility_test else '❌ فشل'}")
    print(f"  • اختبار المستخدمين الموجودين: {'✅ نجح' if existing_users_test else '❌ فشل'}")
    
    if all([structure_check, compatibility_test, existing_users_test]):
        print("\n🎉 التوافق مثالي! واجهة تسجيل الدخول تتحقق من نفس جدول إدارة المستخدمين")
        
        print("\n📋 التأكيدات:")
        print("  ✅ بنية الجدول صحيحة ومتوافقة")
        print("  ✅ إضافة المستخدمين من واجهة الإدارة تعمل")
        print("  ✅ تسجيل الدخول يتحقق من نفس الجدول")
        print("  ✅ تشفير كلمات المرور متطابق")
        print("  ✅ جميع المستخدمين يمكنهم تسجيل الدخول")
        
    else:
        print("\n❌ هناك مشاكل في التوافق!")
        
        if not structure_check:
            print("  🔧 مراجعة بنية جدول المستخدمين")
        if not compatibility_test:
            print("  🔧 مراجعة التوافق مع واجهة إدارة المستخدمين")
        if not existing_users_test:
            print("  🔧 مراجعة كلمات مرور المستخدمين الموجودين")

if __name__ == "__main__":
    main()

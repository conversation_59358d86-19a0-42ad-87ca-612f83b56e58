#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الخزينة المتكامل
Integrated Treasury Manager
"""

from datetime import datetime

class TreasuryManager:
    """مدير الخزينة المتكامل"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.create_treasury_tables()
    
    def create_treasury_tables(self):
        """إنشاء جداول الخزينة"""
        try:
            # جدول الخزينة الرئيسي (للنقل إليه)
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS main_treasury (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    currency_type TEXT UNIQUE NOT NULL DEFAULT 'SYP',
                    balance REAL DEFAULT 0,
                    exchange_rate REAL DEFAULT 1.0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الخزينة اليومية (من الشيفتات)
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS daily_treasury (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shift_date DATE NOT NULL,
                    currency_type TEXT NOT NULL DEFAULT 'SYP',
                    opening_balance REAL DEFAULT 0,
                    sales_amount REAL DEFAULT 0,
                    expenses_amount REAL DEFAULT 0,
                    currency_exchanges REAL DEFAULT 0,
                    closing_balance REAL DEFAULT 0,
                    is_closed INTEGER DEFAULT 0,
                    closed_at TIMESTAMP,
                    user_id INTEGER,
                    UNIQUE(shift_date, currency_type)
                )
            """)
            
            # جدول نقل الخزينة
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS treasury_transfers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transfer_date DATE NOT NULL,
                    currency_type TEXT DEFAULT 'SYP',
                    amount REAL NOT NULL,
                    exchange_rate_used REAL DEFAULT 1.0,
                    amount_in_syp REAL,
                    from_treasury TEXT DEFAULT 'daily',
                    to_treasury TEXT DEFAULT 'main',
                    receiver TEXT,
                    notes TEXT,
                    transferred_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            print("✅ تم إنشاء جداول الخزينة المتكاملة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جداول الخزينة: {e}")
    
    def get_daily_treasury_balance(self, currency_type='SYP', date=None):
        """الحصول على رصيد الخزينة اليومية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # الحصول على رصيد اليوم
            daily_data = self.db_manager.fetch_one("""
                SELECT closing_balance, is_closed FROM daily_treasury 
                WHERE shift_date = ? AND currency_type = ?
            """, (date, currency_type))
            
            if daily_data and daily_data['is_closed'] == 0:
                # الشيفت مفتوح، إرجاع الرصيد الحالي
                return daily_data['closing_balance']
            else:
                # لا يوجد شيفت مفتوح، الرصيد صفر
                return 0.0
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على رصيد الخزينة اليومية: {e}")
            return 0.0
    
    def update_daily_treasury(self, currency_type='SYP', sales_amount=0, 
                             expenses_amount=0, currency_exchanges=0, 
                             user_id=1, date=None):
        """تحديث الخزينة اليومية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # الحصول على السجل الحالي أو إنشاؤه
            existing = self.db_manager.fetch_one("""
                SELECT * FROM daily_treasury 
                WHERE shift_date = ? AND currency_type = ?
            """, (date, currency_type))
            
            if existing:
                # تحديث السجل الموجود
                new_sales = existing['sales_amount'] + sales_amount
                new_expenses = existing['expenses_amount'] + expenses_amount
                new_exchanges = existing['currency_exchanges'] + currency_exchanges
                new_closing = existing['opening_balance'] + new_sales - new_expenses - new_exchanges
                
                self.db_manager.execute_query("""
                    UPDATE daily_treasury 
                    SET sales_amount = ?, expenses_amount = ?, currency_exchanges = ?,
                        closing_balance = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE shift_date = ? AND currency_type = ?
                """, (new_sales, new_expenses, new_exchanges, new_closing, date, currency_type))
            else:
                # إنشاء سجل جديد
                closing_balance = sales_amount - expenses_amount - currency_exchanges
                
                self.db_manager.execute_query("""
                    INSERT INTO daily_treasury 
                    (shift_date, currency_type, opening_balance, sales_amount, 
                     expenses_amount, currency_exchanges, closing_balance, user_id)
                    VALUES (?, ?, 0, ?, ?, ?, ?, ?)
                """, (date, currency_type, sales_amount, expenses_amount, 
                      currency_exchanges, closing_balance, user_id))
            
            print(f"✅ تم تحديث الخزينة اليومية: {currency_type}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الخزينة اليومية: {e}")
            return False
    
    def close_daily_treasury(self, user_id=1, date=None):
        """إغلاق الخزينة اليومية (إغلاق الصندوق)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # الحصول على جميع العملات لليوم
            daily_records = self.db_manager.fetch_all("""
                SELECT * FROM daily_treasury 
                WHERE shift_date = ? AND is_closed = 0
            """, (date,))
            
            for record in daily_records:
                currency_type = record['currency_type']
                closing_balance = record['closing_balance']
                
                # إغلاق السجل
                self.db_manager.execute_query("""
                    UPDATE daily_treasury 
                    SET is_closed = 1, closed_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (record['id'],))
                
                # نقل الرصيد للخزينة الرئيسية
                if closing_balance > 0:
                    self.transfer_to_main_treasury(
                        currency_type=currency_type,
                        amount=closing_balance,
                        notes=f"إغلاق صندوق {date}",
                        user_id=user_id
                    )
            
            print(f"✅ تم إغلاق الخزينة اليومية لتاريخ {date}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إغلاق الخزينة اليومية: {e}")
            return False
    
    def transfer_to_main_treasury(self, currency_type='SYP', amount=0, 
                                 notes='', user_id=1):
        """نقل مبلغ للخزينة الرئيسية"""
        try:
            # تسجيل النقل
            self.db_manager.execute_query("""
                INSERT INTO treasury_transfers 
                (transfer_date, currency_type, amount, from_treasury, to_treasury,
                 notes, transferred_by, created_at)
                VALUES (?, ?, ?, 'daily', 'main', ?, ?, CURRENT_TIMESTAMP)
            """, (datetime.now().strftime('%Y-%m-%d'), currency_type, amount, 
                  notes, f"user_{user_id}"))
            
            # تحديث الخزينة الرئيسية
            main_updated = self.db_manager.execute_query("""
                UPDATE main_treasury 
                SET balance = balance + ?, last_updated = CURRENT_TIMESTAMP
                WHERE currency_type = ?
            """, (amount, currency_type))
            
            if main_updated == 0:
                # إنشاء سجل جديد في الخزينة الرئيسية
                exchange_rate = 1.0 if currency_type == 'SYP' else 15000.0
                self.db_manager.execute_query("""
                    INSERT INTO main_treasury (currency_type, balance, exchange_rate)
                    VALUES (?, ?, ?)
                """, (currency_type, amount, exchange_rate))
            
            print(f"✅ تم نقل {amount} {currency_type} للخزينة الرئيسية")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في النقل للخزينة الرئيسية: {e}")
            return False
    
    def process_currency_exchange(self, from_currency='SYP', to_currency='USD',
                                 amount_from=0, exchange_rate=15000, user_id=1):
        """معالجة عملية صرف العملة"""
        try:
            amount_to = amount_from / exchange_rate
            date = datetime.now().strftime('%Y-%m-%d')
            
            # تحديث الخزينة اليومية - خصم العملة المصدر
            self.update_daily_treasury(
                currency_type=from_currency,
                currency_exchanges=amount_from,
                user_id=user_id,
                date=date
            )
            
            # تحديث الخزينة اليومية - إضافة العملة المستهدفة
            self.update_daily_treasury(
                currency_type=to_currency,
                sales_amount=amount_to,  # نعتبرها مبيعات بالعملة الجديدة
                user_id=user_id,
                date=date
            )
            
            print(f"✅ تم معالجة صرف العملة: {amount_from} {from_currency} → {amount_to} {to_currency}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في معالجة صرف العملة: {e}")
            return False
    
    def get_main_treasury_balance(self, currency_type='SYP'):
        """الحصول على رصيد الخزينة الرئيسية"""
        try:
            result = self.db_manager.fetch_one("""
                SELECT balance FROM main_treasury WHERE currency_type = ?
            """, (currency_type,))
            
            return result['balance'] if result else 0.0
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على رصيد الخزينة الرئيسية: {e}")
            return 0.0
    
    def reset_daily_treasury(self, date=None):
        """إعادة تعيين الخزينة اليومية (بداية شيفت جديد)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # حذف السجلات المفتوحة لليوم
            self.db_manager.execute_query("""
                DELETE FROM daily_treasury 
                WHERE shift_date = ? AND is_closed = 0
            """, (date,))
            
            print(f"✅ تم إعادة تعيين الخزينة اليومية لتاريخ {date}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعادة تعيين الخزينة اليومية: {e}")
            return False

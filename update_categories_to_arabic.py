#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث تصنيفات المنتجات للعربية
"""

import sqlite3

def update_categories_to_arabic():
    """تحديث تصنيفات المنتجات للعربية"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        print('🔄 تحديث تصنيفات المنتجات للعربية...')
        
        # تحديث منتجات الراوتر
        cursor.execute('UPDATE products SET category = ? WHERE category = ?', ('راوتر', 'router'))
        router_count = cursor.rowcount
        print(f'✅ تم تحديث {router_count} منتج راوتر')
        
        # تحديث منتجات الكبل
        cursor.execute('UPDATE products SET category = ? WHERE category = ?', ('كبل', 'cable'))
        cable_count = cursor.rowcount
        print(f'✅ تم تحديث {cable_count} منتج كبل')
        
        conn.commit()
        
        # عرض التصنيفات الحالية
        categories = cursor.execute('SELECT DISTINCT category FROM products WHERE category IS NOT NULL ORDER BY category').fetchall()
        print('\n📂 التصنيفات الحالية:')
        for cat in categories:
            count = cursor.execute('SELECT COUNT(*) FROM products WHERE category = ?', (cat[0],)).fetchone()[0]
            print(f'  • {cat[0]} ({count} منتج)')
        
        conn.close()
        print('\n✅ تم تحديث التصنيفات بنجاح!')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == "__main__":
    update_categories_to_arabic()

# -*- coding: utf-8 -*-
"""
وحدة الدعم العربي - النسخة المتكاملة
Arabic Support Module - Integrated Version

توفر دعم شامل للغة العربية في الواجهات
"""

from PyQt5.QtGui import QFont, QFontDatabase
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QApplication
import os

# قائمة الخطوط العربية المفضلة
ARABIC_FONTS = [
    "Tahoma",
    "Arial Unicode MS", 
    "Segoe UI",
    "Microsoft Sans Serif",
    "Arial",
    "Times New Roman"
]

def get_best_arabic_font():
    """الحصول على أفضل خط عربي متاح"""
    font_db = QFontDatabase()
    available_fonts = font_db.families()
    
    # البحث عن أفضل خط متاح
    for font_name in ARABIC_FONTS:
        if font_name in available_fonts:
            return font_name
    
    # إذا لم يوجد خط مناسب، استخدم الخط الافتراضي
    return "Arial"

def create_arabic_font(size=10, bold=False, italic=False):
    """إنشاء خط عربي"""
    font_name = get_best_arabic_font()
    font = QFont(font_name, size)
    font.setBold(bold)
    font.setItalic(italic)
    
    # تحسين عرض النص العربي
    font.setStyleHint(QFont.SansSerif)
    font.setStyleStrategy(QFont.PreferAntialias)
    
    return font

def apply_arabic_style(widget, size=10, bold=False, italic=False):
    """تطبيق النمط العربي على عنصر واجهة"""
    if widget is None:
        return
    
    font = create_arabic_font(size, bold, italic)
    widget.setFont(font)
    
    # تعيين اتجاه النص للعربية
    widget.setLayoutDirection(Qt.RightToLeft)
    
    # تحسين عرض النص
    if hasattr(widget, 'setAlignment'):
        widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

def simple_arabic_text(text):
    """معالجة بسيطة للنص العربي"""
    if not text:
        return ""
    
    # إزالة المسافات الزائدة
    text = text.strip()
    
    # استبدال الأرقام الإنجليزية بالعربية إذا لزم الأمر
    # (يمكن تفعيل هذا حسب الحاجة)
    # arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    # english_digits = "0123456789"
    # for i, digit in enumerate(english_digits):
    #     text = text.replace(digit, arabic_digits[i])
    
    return text

def setup_application_font(app):
    """إعداد الخط العربي للتطبيق كاملاً"""
    if not isinstance(app, QApplication):
        return
    
    # إنشاء خط افتراضي للتطبيق
    default_font = create_arabic_font(10)
    app.setFont(default_font)
    
    # تعيين اتجاه التطبيق للعربية
    app.setLayoutDirection(Qt.RightToLeft)

def apply_rtl_layout(widget):
    """تطبيق تخطيط من اليمين لليسار"""
    if widget is None:
        return
    
    widget.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق على العناصر الفرعية
    for child in widget.findChildren(QWidget):
        child.setLayoutDirection(Qt.RightToLeft)

def format_arabic_number(number, decimal_places=2):
    """تنسيق الأرقام للعرض العربي"""
    if number is None:
        return "0"
    
    try:
        # تنسيق الرقم مع الفواصل
        if isinstance(number, (int, float)):
            if decimal_places == 0:
                formatted = f"{int(number):,}"
            else:
                formatted = f"{float(number):,.{decimal_places}f}"
        else:
            formatted = str(number)
        
        return formatted
    except:
        return str(number)

def format_arabic_currency(amount, currency="ل.س"):
    """تنسيق العملة للعرض العربي"""
    formatted_amount = format_arabic_number(amount, 2)
    return f"{formatted_amount} {currency}"

def create_title_font(size=14):
    """إنشاء خط للعناوين"""
    return create_arabic_font(size, bold=True)

def create_header_font(size=12):
    """إنشاء خط للرؤوس"""
    return create_arabic_font(size, bold=True)

def create_label_font(size=10):
    """إنشاء خط للتسميات"""
    return create_arabic_font(size)

def create_input_font(size=10):
    """إنشاء خط لحقول الإدخال"""
    return create_arabic_font(size)

def create_button_font(size=10):
    """إنشاء خط للأزرار"""
    return create_arabic_font(size, bold=True)

def apply_table_style(table_widget):
    """تطبيق النمط العربي على الجداول"""
    if table_widget is None:
        return
    
    # تطبيق الخط العربي
    table_widget.setFont(create_arabic_font(9))
    
    # تعيين اتجاه الجدول
    table_widget.setLayoutDirection(Qt.RightToLeft)
    
    # تحسين عرض الجدول
    table_widget.setAlternatingRowColors(True)
    table_widget.setSelectionBehavior(table_widget.SelectRows)
    
    # تطبيق النمط على الرأس
    header = table_widget.horizontalHeader()
    if header:
        header.setFont(create_arabic_font(9, bold=True))
        header.setLayoutDirection(Qt.RightToLeft)

def apply_menu_style(menu_widget):
    """تطبيق النمط العربي على القوائم"""
    if menu_widget is None:
        return
    
    # تطبيق الخط العربي
    menu_widget.setFont(create_arabic_font(10))
    
    # تعيين اتجاه القائمة
    menu_widget.setLayoutDirection(Qt.RightToLeft)

def apply_dialog_style(dialog_widget):
    """تطبيق النمط العربي على النوافذ المنبثقة"""
    if dialog_widget is None:
        return
    
    # تطبيق الخط العربي
    dialog_widget.setFont(create_arabic_font(10))
    
    # تعيين اتجاه النافذة
    dialog_widget.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق على جميع العناصر الفرعية
    apply_rtl_layout(dialog_widget)

def get_arabic_month_name(month_number):
    """الحصول على اسم الشهر بالعربية"""
    months = {
        1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
        5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
        9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
    }
    return months.get(month_number, "")

def get_arabic_day_name(day_number):
    """الحصول على اسم اليوم بالعربية"""
    days = {
        0: "الاثنين", 1: "الثلاثاء", 2: "الأربعاء", 3: "الخميس",
        4: "الجمعة", 5: "السبت", 6: "الأحد"
    }
    return days.get(day_number, "")

def format_arabic_date(date_obj):
    """تنسيق التاريخ بالعربية"""
    if date_obj is None:
        return ""
    
    try:
        day = date_obj.day
        month = get_arabic_month_name(date_obj.month)
        year = date_obj.year
        
        return f"{day} {month} {year}"
    except:
        return str(date_obj)

def validate_arabic_input(text):
    """التحقق من صحة الإدخال العربي"""
    if not text:
        return True
    
    # السماح بالأحرف العربية والأرقام والمسافات وعلامات الترقيم الأساسية
    allowed_chars = set("أبتثجحخدذرزسشصضطظعغفقكلمنهوي" + 
                        "ابتثجحخدذرزسشصضطظعغفقكلمنهوي" +
                        "ءآإأؤئة" + 
                        "0123456789" + 
                        " .,;:!?()-+*/=@#$%^&")
    
    return all(char in allowed_chars for char in text)

# دوال مساعدة للتوافق مع الإصدارات السابقة
def get_arabic_font(size=10, bold=False):
    """دالة للتوافق مع الإصدارات السابقة"""
    return create_arabic_font(size, bold)

def set_arabic_font(widget, size=10, bold=False):
    """دالة للتوافق مع الإصدارات السابقة"""
    apply_arabic_style(widget, size, bold)

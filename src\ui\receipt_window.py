# -*- coding: utf-8 -*-
"""
نافذة سند قبض من الموزعين
Receipt from Distributors Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QDoubleSpinBox, QGroupBox, 
                            QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency


class ReceiptWindow(QDialog):
    """نافذة سند قبض من الموزعين"""
    
    receipt_added = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)

        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سند قبض من الموزعين")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("سند قبض من الموزعين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #d5f4e6;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # نموذج إضافة سند قبض
        receipt_form = self.create_receipt_form()
        
        # جدول سندات القبض
        self.receipts_table = self.create_receipts_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addWidget(receipt_form)
        layout.addWidget(self.receipts_table)
        layout.addLayout(buttons_layout)
        
    def create_receipt_form(self):
        """إنشاء نموذج إضافة سند قبض"""
        group = QGroupBox("إضافة سند قبض جديد")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # رقم السند
        receipt_number_label = QLabel("رقم السند:")
        apply_arabic_style(receipt_number_label, 10)
        self.receipt_number_edit = QLineEdit()
        apply_arabic_style(self.receipt_number_edit, 10)
        self.receipt_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        self.receipt_number_edit.setReadOnly(True)
        
        # الموزع
        distributor_label = QLabel("الموزع:")
        apply_arabic_style(distributor_label, 10)
        self.distributor_combo = QComboBox()
        apply_arabic_style(self.distributor_combo, 10)
        
        # المبلغ
        amount_label = QLabel("المبلغ:")
        apply_arabic_style(amount_label, 10)
        self.amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.amount_spin, 10)
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setSuffix(" ل.س")
        
        # التاريخ
        date_label = QLabel("التاريخ:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # الوصف
        description_label = QLabel("الوصف:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QLineEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setPlaceholderText("وصف سند القبض...")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        
        # زر الإضافة
        add_button = QPushButton("إضافة سند قبض")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(receipt_number_label, 0, 0)
        layout.addWidget(self.receipt_number_edit, 0, 1)
        layout.addWidget(distributor_label, 0, 2)
        layout.addWidget(self.distributor_combo, 0, 3)
        layout.addWidget(amount_label, 1, 0)
        layout.addWidget(self.amount_spin, 1, 1)
        layout.addWidget(date_label, 1, 2)
        layout.addWidget(self.date_edit, 1, 3)
        layout.addWidget(description_label, 2, 0)
        layout.addWidget(self.description_edit, 2, 1, 1, 2)
        layout.addWidget(notes_label, 3, 0)
        layout.addWidget(self.notes_edit, 3, 1, 1, 2)
        layout.addWidget(add_button, 3, 3)
        
        add_button.clicked.connect(self.add_receipt)
        self.distributor_combo.currentTextChanged.connect(self.update_distributor_info)
        
        return group
        
    def create_receipts_table(self):
        """إنشاء جدول سندات القبض"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "الرقم", "رقم السند", "الموزع", "المبلغ", "التاريخ", 
            "الوصف", "المستخدم", "الملاحظات"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(5, header.Stretch)  # عمود الوصف
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر حذف سند
        delete_button = QPushButton("حذف سند")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر طباعة
        print_button = QPushButton("طباعة سند")
        apply_arabic_style(print_button, 10)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(delete_button)
        layout.addWidget(print_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        delete_button.clicked.connect(self.delete_receipt)
        print_button.clicked.connect(self.print_receipt)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_distributors()
        self.load_receipts_data()
        self.generate_receipt_number()
        
    def load_distributors(self):
        """تحميل قائمة الموزعين"""
        try:
            distributors = self.db_manager.fetch_all("""
                SELECT id, name, balance FROM distributors 
                WHERE is_active = 1 ORDER BY name
            """)
            
            self.distributor_combo.clear()
            for distributor in distributors:
                balance_text = f" (الرصيد: {format_currency(distributor['balance'])})"
                self.distributor_combo.addItem(
                    distributor['name'] + balance_text, 
                    distributor
                )
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموزعين: {e}")
            
    def load_receipts_data(self):
        """تحميل بيانات سندات القبض"""
        try:
            receipts = self.db_manager.fetch_all("""
                SELECT id, receipt_number, distributor_name, amount, 
                       receipt_date, description, user_name, notes, created_at
                FROM receipts 
                ORDER BY receipt_date DESC, created_at DESC
            """)
            
            self.receipts_table.setRowCount(len(receipts))
            
            for row, receipt in enumerate(receipts):
                self.receipts_table.setItem(row, 0, QTableWidgetItem(str(receipt['id'])))
                self.receipts_table.setItem(row, 1, QTableWidgetItem(receipt['receipt_number']))
                self.receipts_table.setItem(row, 2, QTableWidgetItem(receipt['distributor_name']))
                self.receipts_table.setItem(row, 3, QTableWidgetItem(format_currency(receipt['amount'])))
                self.receipts_table.setItem(row, 4, QTableWidgetItem(receipt['receipt_date']))
                self.receipts_table.setItem(row, 5, QTableWidgetItem(receipt['description'] or ''))
                self.receipts_table.setItem(row, 6, QTableWidgetItem(receipt['user_name'] or ''))
                self.receipts_table.setItem(row, 7, QTableWidgetItem(receipt['notes'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات سندات القبض: {e}")
            
    def generate_receipt_number(self):
        """إنشاء رقم سند جديد"""
        try:
            # جلب آخر رقم سند
            last_receipt = self.db_manager.fetch_one("""
                SELECT receipt_number FROM receipts 
                ORDER BY id DESC LIMIT 1
            """)
            
            if last_receipt and last_receipt['receipt_number']:
                # استخراج الرقم من آخر سند
                last_number = int(last_receipt['receipt_number'].replace('REC-', ''))
                new_number = last_number + 1
            else:
                new_number = 1
                
            receipt_number = f"REC-{new_number:06d}"
            self.receipt_number_edit.setText(receipt_number)
            
        except Exception as e:
            # في حالة الخطأ، استخدم رقم افتراضي
            self.receipt_number_edit.setText("REC-000001")
            
    def update_distributor_info(self):
        """تحديث معلومات الموزع المختار"""
        distributor_data = self.distributor_combo.currentData()
        if distributor_data:
            # يمكن إضافة معلومات إضافية هنا
            pass
            
    def add_receipt(self):
        """إضافة سند قبض جديد"""
        try:
            # التحقق من البيانات
            if self.amount_spin.value() <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if not self.description_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف لسند القبض")
                return
                
            distributor_data = self.distributor_combo.currentData()
            if not distributor_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع")
                return
            
            receipt_data = {
                'receipt_number': self.receipt_number_edit.text(),
                'distributor_id': distributor_data['id'],
                'distributor_name': distributor_data['name'],
                'amount': self.amount_spin.value(),
                'receipt_date': self.date_edit.date().toString("yyyy-MM-dd"),
                'description': self.description_edit.text().strip(),
                'notes': self.notes_edit.toPlainText().strip(),
                'user_name': self.current_user['username']
            }
            
            # حفظ سند القبض
            self.db_manager.execute_query("""
                INSERT INTO receipts (receipt_number, distributor_id, distributor_name, 
                                    amount, receipt_date, description, notes, user_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                receipt_data['receipt_number'], receipt_data['distributor_id'], 
                receipt_data['distributor_name'], receipt_data['amount'], 
                receipt_data['receipt_date'], receipt_data['description'], 
                receipt_data['notes'], receipt_data['user_name']
            ))
            
            # إضافة للصندوق باستخدام النظام الموحد (ليظهر في إغلاق الصندوق)
            cash_box_success = self.treasury_manager.add_to_cash_box(
                user_id=self.current_user['id'],
                amount=receipt_data['amount'],
                description=f"سند قبض من {receipt_data['distributor_name']}: {receipt_data['description']}",
                transaction_type="receipt"
            )

            if not cash_box_success:
                raise Exception("فشل في إضافة المبلغ للصندوق")
            
            # تقليل رصيد الموزع
            self.db_manager.execute_query("""
                UPDATE distributors SET balance = balance - ? WHERE id = ?
            """, (receipt_data['amount'], receipt_data['distributor_id']))
            
            QMessageBox.information(self, "تم", "تم إضافة سند القبض وإضافة المبلغ للصندوق بنجاح")
            
            # إرسال إشارة
            self.receipt_added.emit(receipt_data)
            
            # إعادة تعيين النموذج
            self.reset_form()
            
            # تحديث البيانات
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة سند القبض: {e}")
            
    def delete_receipt(self):
        """حذف سند قبض"""
        current_row = self.receipts_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سند للحذف")
            return
            
        receipt_id = self.receipts_table.item(current_row, 0).text()
        receipt_number = self.receipts_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف سند القبض '{receipt_number}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM receipts WHERE id = ?", (receipt_id,))
                QMessageBox.information(self, "تم", "تم حذف سند القبض بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف سند القبض: {e}")
                
    def print_receipt(self):
        """طباعة سند القبض"""
        QMessageBox.information(self, "قريباً", "ميزة طباعة سند القبض قيد التطوير")
        
    def reset_form(self):
        """إعادة تعيين النموذج"""
        self.amount_spin.setValue(0)
        self.date_edit.setDate(QDate.currentDate())
        self.description_edit.clear()
        self.notes_edit.clear()
        self.distributor_combo.setCurrentIndex(0)
        self.generate_receipt_number()

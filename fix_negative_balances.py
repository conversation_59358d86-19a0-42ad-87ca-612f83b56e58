#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة الأرصدة السالبة في النظام الموحد
"""

import sys
import os
sys.path.append('src')

def fix_negative_balances():
    """إصلاح الأرصدة السالبة"""
    
    print("🔧 إصلاح الأرصدة السالبة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص الأرصدة السالبة
        negative_balances = db.fetch_all("""
            SELECT user_id, session_date, currency_type, daily_balance, main_balance
            FROM unified_treasury 
            WHERE daily_balance < 0
            ORDER BY user_id, session_date
        """)
        
        print(f"📊 تم العثور على {len(negative_balances)} رصيد سالب:")
        for balance in negative_balances:
            print(f"  • مستخدم {balance['user_id']} - {balance['session_date']}: يومي {balance['daily_balance']:,} ل.س")
        
        if negative_balances:
            print("\n🔧 إصلاح الأرصدة السالبة...")
            
            # تصفير الأرصدة السالبة
            db.execute_query("""
                UPDATE unified_treasury
                SET daily_balance = 0
                WHERE daily_balance < 0
            """)
            
            print("✅ تم تصفير الأرصدة السالبة")
            
            # فحص النتائج
            remaining_negative = db.fetch_all("""
                SELECT COUNT(*) as count FROM unified_treasury WHERE daily_balance < 0
            """)
            
            if remaining_negative[0]['count'] == 0:
                print("✅ لا توجد أرصدة سالبة متبقية")
                return True
            else:
                print(f"⚠️ لا تزال هناك {remaining_negative[0]['count']} أرصدة سالبة")
                return False
        else:
            print("✅ لا توجد أرصدة سالبة")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأرصدة السالبة: {e}")
        return False

def test_fixed_calculations():
    """اختبار الحسابات بعد الإصلاح"""
    
    print("\n🧪 اختبار الحسابات بعد الإصلاح...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة الجديدة
        print("\n💰 الأرصدة بعد الإصلاح:")
        
        # الحساب اليدوي
        manual_daily = db.fetch_one("""
            SELECT COALESCE(SUM(daily_balance), 0) as total
            FROM unified_treasury
            WHERE currency_type = 'SYP' AND is_session_active = 1
        """)
        
        manual_main = db.fetch_all("""
            SELECT user_id, MAX(main_balance) as max_main
            FROM unified_treasury
            WHERE currency_type = 'SYP'
            GROUP BY user_id
        """)
        
        total_main = sum(user['max_main'] for user in manual_main)
        
        print(f"  • الحساب اليدوي - يومي: {manual_daily['total']:,} ل.س")
        print(f"  • الحساب اليدوي - رئيسي: {total_main:,} ل.س")
        
        # الحساب بالدوال
        calculated_daily = treasury_manager.get_total_daily_balance('SYP')
        calculated_main = treasury_manager.get_main_balance('SYP')
        
        print(f"  • الحساب بالدوال - يومي: {calculated_daily:,} ل.س")
        print(f"  • الحساب بالدوال - رئيسي: {calculated_main:,} ل.س")
        
        # مقارنة النتائج
        daily_match = abs(calculated_daily - manual_daily['total']) < 1
        main_match = abs(calculated_main - total_main) < 1
        
        print(f"\n📊 مقارنة النتائج:")
        print(f"  • تطابق الخزينة اليومية: {'✅' if daily_match else '❌'}")
        print(f"  • تطابق الخزينة الرئيسية: {'✅' if main_match else '❌'}")
        
        return daily_match and main_match
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحسابات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transfer_after_fix():
    """اختبار النقل بعد الإصلاح"""
    
    print("\n🧪 اختبار النقل بعد الإصلاح...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الأرصدة قبل النقل
        daily_before = treasury_manager.get_total_daily_balance('SYP')
        main_before = treasury_manager.get_main_balance('SYP')
        
        print(f"الأرصدة قبل النقل:")
        print(f"  • يومي: {daily_before:,} ل.س")
        print(f"  • رئيسي: {main_before:,} ل.س")
        
        if daily_before <= 0:
            print("⚠️ لا يوجد رصيد يومي للنقل")
            return False
        
        # تنفيذ نقل صغير
        transfer_amount = min(10000, daily_before)
        print(f"\nتنفيذ نقل {transfer_amount:,} ل.س...")
        
        success = treasury_manager.transfer_from_total_daily_to_main(
            currency_type='SYP',
            amount=transfer_amount
        )
        
        if success:
            # فحص الأرصدة بعد النقل
            daily_after = treasury_manager.get_total_daily_balance('SYP')
            main_after = treasury_manager.get_main_balance('SYP')
            
            print(f"\nالأرصدة بعد النقل:")
            print(f"  • يومي: {daily_after:,} ل.س")
            print(f"  • رئيسي: {main_after:,} ل.س")
            
            daily_change = daily_before - daily_after
            main_change = main_after - main_before
            
            print(f"\nالتغييرات:")
            print(f"  • خصم من اليومي: {daily_change:,} ل.س")
            print(f"  • إضافة للرئيسي: {main_change:,} ل.س")
            
            if abs(daily_change - transfer_amount) < 1 and abs(main_change - transfer_amount) < 1:
                print("✅ النقل يعمل بشكل صحيح بعد الإصلاح")
                return True
            else:
                print("❌ النقل لا يزال لا يعمل بشكل صحيح")
                return False
        else:
            print("❌ فشل النقل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النقل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مشكلة الأرصدة السالبة والحسابات")
    print("=" * 60)
    
    # 1. إصلاح الأرصدة السالبة
    fix_success = fix_negative_balances()
    
    # 2. اختبار الحسابات بعد الإصلاح
    calc_success = test_fixed_calculations()
    
    # 3. اختبار النقل بعد الإصلاح
    transfer_success = test_transfer_after_fix()
    
    print("\n" + "=" * 60)
    print("📊 ملخص الإصلاح:")
    print(f"  • إصلاح الأرصدة السالبة: {'✅ نجح' if fix_success else '❌ فشل'}")
    print(f"  • إصلاح الحسابات: {'✅ نجح' if calc_success else '❌ فشل'}")
    print(f"  • اختبار النقل: {'✅ نجح' if transfer_success else '❌ فشل'}")
    
    if all([fix_success, calc_success, transfer_success]):
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        
        print("\n📋 النتائج:")
        print("  ✅ تم تصفير الأرصدة السالبة")
        print("  ✅ دوال الحساب تعمل بشكل صحيح")
        print("  ✅ عملية النقل تعمل بدقة")
        print("  ✅ الخزينة اليومية تنقص والرئيسية تزيد")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. سجل دخول: admin / admin123")
        print("  3. اضغط على 'نقل الخزينة'")
        print("  4. ستجد الأرصدة الصحيحة والنقل يعمل")
        
    else:
        print("\n❌ لا تزال هناك مشاكل:")
        
        if not fix_success:
            print("  • فشل في إصلاح الأرصدة السالبة")
        if not calc_success:
            print("  • دوال الحساب لا تزال لا تعمل بشكل صحيح")
        if not transfer_success:
            print("  • عملية النقل لا تزال لا تعمل")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QPushButton, QLabel, QFrame, 
                             QMessageBox, QCheckBox, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont

from config.app_config import AppConfig
from database.database_manager import DatabaseManager
from utils.window_utils import center_window, apply_modern_style

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    # إشارة تسجيل الدخول الناجح
    login_successful = pyqtSignal(dict)
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة نافذة تسجيل الدخول
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        super().__init__()
        
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.login_attempts = 0
        self.max_attempts = AppConfig.SECURITY_CONFIG['max_login_attempts']
        
        # إعداد النافذة
        self.setup_ui()
        self.setup_connections()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق
        apply_modern_style(self)
        
        self.logger.info("تم تشغيل نافذة تسجيل الدخول")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # منطقة اللوغو والعنوان
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        # منطقة تسجيل الدخول
        login_section = self.create_login_section()
        main_layout.addWidget(login_section)
        
        # منطقة الأزرار
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        # شريط التقدم (مخفي في البداية)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # معلومات إضافية
        info_section = self.create_info_section()
        main_layout.addWidget(info_section)
        
        self.setLayout(main_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر"""
        
        frame = QFrame()
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(15)
        
        # اللوغو
        logo_label = QLabel()
        logo_pixmap = self.create_login_logo()
        logo_label.setPixmap(logo_pixmap)
        logo_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(logo_label)
        
        # عنوان التطبيق
        title_label = QLabel(AppConfig.APP_NAME)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px 0;
            }
        """)
        layout.addWidget(title_label)
        
        # وصف التطبيق
        desc_label = QLabel(AppConfig.APP_DESCRIPTION)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(desc_label)
        
        return frame
    
    def create_login_logo(self) -> QPixmap:
        """إنشاء لوغو تسجيل الدخول"""
        
        pixmap = QPixmap(80, 80)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم دائرة خلفية متدرجة
        painter.setBrush(QColor(52, 152, 219, 200))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(5, 5, 70, 70)
        
        # رسم دائرة داخلية
        painter.setBrush(QColor(255, 255, 255, 150))
        painter.drawEllipse(15, 15, 50, 50)
        
        # رسم رمز المستخدم
        painter.setPen(QColor(52, 152, 219, 255))
        painter.setBrush(QColor(52, 152, 219, 255))
        
        # رأس المستخدم
        painter.drawEllipse(32, 25, 16, 16)
        
        # جسم المستخدم
        painter.drawEllipse(25, 45, 30, 20)
        
        painter.end()
        
        return pixmap
    
    def create_login_section(self) -> QFrame:
        """إنشاء قسم تسجيل الدخول"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QFormLayout(frame)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # حقل اسم المستخدم
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setText("admin")  # قيمة افتراضية للتطوير
        layout.addRow("اسم المستخدم:", self.username_edit)
        
        # حقل كلمة المرور
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setText("admin123")  # قيمة افتراضية للتطوير
        layout.addRow("كلمة المرور:", self.password_edit)
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكر بياناتي")
        layout.addRow("", self.remember_checkbox)
        
        return frame
    
    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار"""
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setMinimumHeight(40)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.login_button)
        
        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.close)
        layout.addWidget(cancel_button)
        
        return frame
    
    def create_info_section(self) -> QFrame:
        """إنشاء قسم المعلومات"""
        
        frame = QFrame()
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        # معلومات الإصدار
        version_label = QLabel(f"الإصدار {AppConfig.APP_VERSION}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #95a5a6;
            }
        """)
        layout.addWidget(version_label)
        
        # معلومات المطور
        author_label = QLabel(f"© 2024 {AppConfig.APP_AUTHOR}")
        author_label.setAlignment(Qt.AlignCenter)
        author_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #95a5a6;
            }
        """)
        layout.addWidget(author_label)
        
        return frame
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        
        # ربط الأزرار
        self.login_button.clicked.connect(self.handle_login)
        
        # ربط مفتاح Enter
        self.username_edit.returnPressed.connect(self.handle_login)
        self.password_edit.returnPressed.connect(self.handle_login)
        
        # ربط إشارة تسجيل الدخول الناجح
        self.login_successful.connect(self.open_main_window)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        # التحقق من صحة البيانات
        if not username or not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تعطيل الأزرار وإظهار شريط التقدم
        self.login_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        
        # محاولة تسجيل الدخول
        try:
            user = self.authenticate_user(username, password)
            
            if user:
                # تسجيل الدخول ناجح
                self.logger.info(f"تسجيل دخول ناجح للمستخدم: {username}")
                
                # تحديث آخر تسجيل دخول
                self.update_last_login(user['id'])
                
                # إرسال إشارة النجاح
                self.login_successful.emit(user)
                
            else:
                # تسجيل الدخول فاشل
                self.login_attempts += 1
                remaining_attempts = self.max_attempts - self.login_attempts
                
                if remaining_attempts > 0:
                    QMessageBox.warning(
                        self, "خطأ في تسجيل الدخول",
                        f"اسم المستخدم أو كلمة المرور غير صحيحة\n"
                        f"المحاولات المتبقية: {remaining_attempts}"
                    )
                else:
                    QMessageBox.critical(
                        self, "تم حظر الدخول",
                        "تم استنفاد عدد المحاولات المسموحة\n"
                        "يرجى المحاولة لاحقاً"
                    )
                    self.close()
                
                self.logger.warning(f"محاولة دخول فاشلة للمستخدم: {username}")
        
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الدخول: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تسجيل الدخول: {e}")
        
        finally:
            # إعادة تفعيل الأزرار وإخفاء شريط التقدم
            self.login_button.setEnabled(True)
            self.progress_bar.setVisible(False)
    
    def authenticate_user(self, username: str, password: str) -> dict:
        """التحقق من صحة بيانات المستخدم"""
        
        user = self.db_manager.fetch_one("""
            SELECT id, username, password, full_name, role, is_active, permissions, settings
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        """, (username, password))
        
        if user:
            return dict(user)
        
        return None
    
    def update_last_login(self, user_id: int):
        """تحديث آخر تسجيل دخول"""
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self.db_manager.execute_query("""
            UPDATE users 
            SET last_login = ? 
            WHERE id = ?
        """, (current_time, user_id))
        
        # تسجيل النشاط
        self.db_manager.execute_query("""
            INSERT INTO activity_log (user_id, action, details, created_at)
            VALUES (?, 'login', 'تسجيل دخول ناجح', ?)
        """, (user_id, current_time))
    
    def open_main_window(self, user: dict):
        """فتح النافذة الرئيسية"""
        
        try:
            from ui.main_window import MainWindow
            
            # إنشاء النافذة الرئيسية
            main_window = MainWindow(self.db_manager, user)
            main_window.show()
            
            # إغلاق نافذة تسجيل الدخول
            self.accept()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح النافذة الرئيسية: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح النافذة الرئيسية: {e}")
    
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        
        # إغلاق النافذة بمفتاح Escape
        if event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

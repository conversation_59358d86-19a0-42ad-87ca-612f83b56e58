#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات واجهة المستخدم
"""

import sys
import os
sys.path.append('src')

def test_window_centering():
    """اختبار توسيط النوافذ"""
    
    print("🧪 اختبار توسيط النوافذ...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QDialog
        from utils.window_utils import center_window, setup_dialog_window
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # اختبار دالة التوسيط العامة
        test_dialog = QDialog()
        test_dialog.resize(400, 300)
        
        # اختبار التوسيط
        center_window(test_dialog)
        
        # فحص الموضع
        pos = test_dialog.pos()
        print(f"✅ موضع النافذة بعد التوسيط: ({pos.x()}, {pos.y()})")
        
        # اختبار إعداد النافذة الكامل
        test_dialog2 = QDialog()
        setup_dialog_window(
            test_dialog2, 
            "اختبار التوسيط", 
            size=(500, 400), 
            center=True, 
            modal=False
        )
        
        pos2 = test_dialog2.pos()
        print(f"✅ موضع النافذة الثانية بعد الإعداد: ({pos2.x()}, {pos2.y()})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار توسيط النوافذ: {e}")
        return False

def test_login_window_centering():
    """اختبار توسيط نافذة تسجيل الدخول"""
    
    print("\n🧪 اختبار توسيط نافذة تسجيل الدخول...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow(db)
        
        # فحص وجود دالة التوسيط
        if hasattr(login_window, 'center_window'):
            print("✅ دالة التوسيط موجودة في نافذة تسجيل الدخول")
            
            # فحص الموضع
            pos = login_window.pos()
            print(f"✅ موضع نافذة تسجيل الدخول: ({pos.x()}, {pos.y()})")
            
            return True
        else:
            print("❌ دالة التوسيط غير موجودة في نافذة تسجيل الدخول")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تسجيل الدخول: {e}")
        return False

def test_main_window_centering():
    """اختبار توسيط النافذة الرئيسية"""
    
    print("\n🧪 اختبار توسيط النافذة الرئيسية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.main_window import MainWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(db, None, current_user)
        
        # فحص وجود دالة التوسيط
        if hasattr(main_window, 'center_window'):
            print("✅ دالة التوسيط موجودة في النافذة الرئيسية")
            
            # فحص الموضع
            pos = main_window.pos()
            size = main_window.size()
            print(f"✅ موضع النافذة الرئيسية: ({pos.x()}, {pos.y()})")
            print(f"✅ حجم النافذة الرئيسية: {size.width()} × {size.height()}")
            
            return True
        else:
            print("❌ دالة التوسيط غير موجودة في النافذة الرئيسية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة الرئيسية: {e}")
        return False

def test_shift_auto_open():
    """اختبار الفتح التلقائي للشيفت"""
    
    print("\n🧪 اختبار الفتح التلقائي للشيفت...")
    
    try:
        # فحص كود system_launcher
        with open('system_launcher.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص إزالة سؤال الشيفت
        if "QMessageBox.question" not in content or "شيفت مفتوح سابق" not in content:
            print("✅ تم إزالة سؤال الشيفت من system_launcher")
            
            # فحص وجود الفتح التلقائي
            if "المتابعة مع الشيفت المفتوح" in content:
                print("✅ تم إضافة الفتح التلقائي للشيفت")
                return True
            else:
                print("⚠️ لم يتم العثور على كود الفتح التلقائي")
                return False
        else:
            print("❌ لا يزال سؤال الشيفت موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفتح التلقائي للشيفت: {e}")
        return False

def test_system_launch():
    """اختبار تشغيل النظام"""
    
    print("\n🧪 اختبار تشغيل النظام...")
    
    try:
        # محاولة استيراد المكونات الأساسية
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        from utils.window_utils import center_window
        
        print("✅ تم استيراد جميع المكونات بنجاح")
        
        # اختبار إنشاء قاعدة البيانات
        db = DatabaseManager('.')
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار تحسينات واجهة المستخدم")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    tests = [
        ("توسيط النوافذ العام", test_window_centering),
        ("توسيط نافذة تسجيل الدخول", test_login_window_centering),
        ("توسيط النافذة الرئيسية", test_main_window_centering),
        ("الفتح التلقائي للشيفت", test_shift_auto_open),
        ("تشغيل النظام", test_system_launch),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  • {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع التحسينات تعمل بشكل مثالي!")
        
        print("\n📋 التحسينات المطبقة:")
        print("  ✅ توسيط التطبيق في الشاشة عند الفتح")
        print("  ✅ توسيط نافذة تسجيل الدخول")
        print("  ✅ توسيط النافذة الرئيسية")
        print("  ✅ إلغاء سؤال الشيفت والفتح التلقائي")
        print("  ✅ إنشاء أدوات مساعدة للنوافذ")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. ستجد نافذة تسجيل الدخول في وسط الشاشة")
        print("  3. سجل دخول: admin / admin123")
        print("  4. ستفتح النافذة الرئيسية في وسط الشاشة مباشرة")
        print("  5. لن يظهر سؤال الشيفت - سيفتح تلقائياً")
        
        print("\n💡 المميزات الجديدة:")
        print("  • جميع النوافذ تظهر في وسط الشاشة")
        print("  • فتح تلقائي للشيفت بدون أسئلة")
        print("  • تجربة مستخدم أكثر سلاسة")
        print("  • أدوات مساعدة للنوافذ قابلة للاستخدام في المستقبل")
        
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("💡 قد تحتاج لفحص إضافي")

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نافذة إنشاء الفاتورة
Invoice Window
"""
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                            QSplitter, QWidget, QFrame, QGroupBox, QDateEdit, QTimeEdit)
from PyQt5.QtCore import Qt, QDate, QTime
from PyQt5.QtGui import QFont, QIcon

try:
    from utils.arabic_support import apply_arabic_style
except ImportError:
    from utils.arabic_support import apply_arabic_style

class InvoiceWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("إنشاء فاتورة")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)

        # Apply Arabic style
        apply_arabic_style(self, 10)

        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Header
        self.create_header(main_layout)

        # Invoice Details
        self.create_invoice_details(main_layout)

        # Footer
        self.create_footer(main_layout)

        self.setLayout(main_layout)

    def create_header(self, layout):
        # Create a frame for the header
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #3498db;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)

        # Company Name
        company_name = QLabel("شركة الإنترنت")
        apply_arabic_style(company_name, 16, bold=True)
        company_name.setStyleSheet("color: white;")
        header_layout.addWidget(company_name)

        # Address and Contact
        address = QLabel("العنوان: 123 شارع الرئيسية، الرياض")
        phone = QLabel("الهاتف: 0123456789")
        apply_arabic_style(address, 12)
        apply_arabic_style(phone, 12)
        address.setStyleSheet("color: white;")
        phone.setStyleSheet("color: white;")
        header_layout.addWidget(address)
        header_layout.addWidget(phone)

        layout.addWidget(header_frame)

    def create_invoice_details(self, layout):
        # Invoice Number and Date
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        details_layout = QVBoxLayout(details_frame)

        # Invoice Number
        invoice_label = QLabel("رقم الفاتورة:")
        apply_arabic_style(invoice_label, 12, bold=True)
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        apply_arabic_style(self.invoice_number_edit, 12)
        details_layout.addWidget(invoice_label)
        details_layout.addWidget(self.invoice_number_edit)

        # Date
        date_label = QLabel("التاريخ:")
        apply_arabic_style(date_label, 12, bold=True)
        self.date_edit = QLineEdit()
        self.date_edit.setReadOnly(True)
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d")
        self.date_edit.setText(current_date)
        apply_arabic_style(self.date_edit, 12)
        details_layout.addWidget(date_label)
        details_layout.addWidget(self.date_edit)

        # Table for items
        table_label = QLabel("تفاصيل الفاتورة")
        apply_arabic_style(table_label, 14, bold=True)
        details_layout.addWidget(table_label)

        table_widget = QTableWidget()
        table_widget.setRowCount(5)
        table_widget.setColumnCount(4)
        table_widget.setHorizontalHeaderLabels(["الوصف", "الكمية", "السعر", "المبلغ"])
        table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        apply_arabic_style(table_widget, 12)

        # Set headers
        table_widget.setItem(0, 0, QTableWidgetItem("منتج"))
        table_widget.setItem(0, 1, QTableWidgetItem("عدد"))
        table_widget.setItem(0, 2, QTableWidgetItem("سعر الوحدة"))
        table_widget.setItem(0, 3, QTableWidgetItem("المبلغ"))

        details_layout.addWidget(table_widget)

        # Buttons
        button_layout = QHBoxLayout()
        add_button = QPushButton("إضافة سلعة")
        apply_arabic_style(add_button, 12)
        add_button.clicked.connect(self.add_item)
        button_layout.addWidget(add_button)

        calculate_button = QPushButton("حساب المبلغ")
        apply_arabic_style(calculate_button, 12)
        calculate_button.clicked.connect(self.calculate_total)
        button_layout.addWidget(calculate_button)

        details_layout.addLayout(button_layout)

        layout.addWidget(details_frame)

    def create_footer(self, layout):
        footer_frame = QFrame()
        footer_frame.setFrameStyle(QFrame.StyledPanel)
        footer_frame.setStyleSheet("""
            QFrame {
                background-color: #3498db;
                border-radius: 10px;
                padding: 15px;
                text-align: center;
            }
        """)
        footer_layout = QVBoxLayout(footer_frame)

        total_label = QLabel("المبلغ الإجمالي:")
        apply_arabic_style(total_label, 14, bold=True)
        total_label.setStyleSheet("color: white;")
        footer_layout.addWidget(total_label)

        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        apply_arabic_style(self.total_edit, 14)
        self.total_edit.setStyleSheet("background-color: white;")
        footer_layout.addWidget(self.total_edit)

        thank_you_label = QLabel("شكراً لكم")
        apply_arabic_style(thank_you_label, 14)
        thank_you_label.setStyleSheet("color: white;")
        footer_layout.addWidget(thank_you_label)

        layout.addWidget(footer_frame)

    def add_item(self):
        # This function will be implemented to add a new row to the table
        pass

    def calculate_total(self):
        # This function will calculate the total based on the table
        pass

    def generate_invoice_number(self):
        # Generate a unique invoice number with date and sequential number
        today = datetime.now().strftime("%Y%m%d")
        counter_file = f"invoice_counter_{today}.txt"
        
        if os.path.exists(counter_file):
            with open(counter_file, "r") as f:
                count = int(f.read()) + 1
            with open(counter_file, "w") as f:
                f.write(str(count))
            return f"INV-{today}-{count:03d}"
        else:
            with open(counter_file, "w") as f:
                f.write("1")
            return f"INV-{today}-001"

    def save_invoice(self):
        # Save the invoice to a file
        invoice_data = {
            "number": self.invoice_number_edit.text(),
            "date": self.date_edit.text(),
            "items": [],
            "total": self.total_edit.text()
        }
        
        # Get items from table
        for row in range(self.tableWidget.rowCount()):
            item_data = {
                "description": self.tableWidget.item(row, 0).text(),
                "quantity": self.tableWidget.item(row, 1).text(),
                "unit_price": self.tableWidget.item(row, 2).text(),
                "amount": self.tableWidget.item(row, 3).text()
            }
            invoice_data["items"].append(item_data)
        
        # Save to file
        with open(f"invoice_{self.invoice_number_edit.text()}.json", "w") as f:
            json.dump(invoice_data, f, indent=4)
        
        QMessageBox.information(self, "تم", "تم حفظ الفاتورة بنجاح")
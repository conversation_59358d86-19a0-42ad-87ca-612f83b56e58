#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشخيص مشكلة زر حفظ وتسليم
"""

import sys
import os
sys.path.append('src')

def test_router_delivery_save():
    """اختبار تشخيص مشكلة الحفظ"""
    
    print("🔍 تشخيص مشكلة زر حفظ وتسليم...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # التحقق من وجود البيانات المطلوبة
        print("\n🔍 فحص البيانات المطلوبة:")
        
        # 1. فحص المشتركين
        subscribers = db.fetch_all("""
            SELECT id, name FROM subscribers 
            WHERE delivered = 0 OR delivered IS NULL
            LIMIT 5
        """)
        print(f"📊 عدد المشتركين غير المُسلمين: {len(subscribers)}")
        
        if len(subscribers) == 0:
            print("⚠️ لا توجد مشتركين - سأضيف مشترك تجريبي")
            
            # إضافة مشترك تجريبي
            result = db.execute_query("""
                INSERT INTO subscribers (name, phone, address, package_id, router_type, delivered)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ("مشترك تجريبي", "123456789", "عنوان تجريبي", 1, "راوتر تجريبي", 0))
            
            if result:
                print("✅ تم إضافة مشترك تجريبي")
                subscribers = db.fetch_all("""
                    SELECT id, name FROM subscribers 
                    WHERE delivered = 0 OR delivered IS NULL
                    LIMIT 5
                """)
                print(f"📊 عدد المشتركين بعد الإضافة: {len(subscribers)}")
        
        # 2. فحص الراوترات
        routers = inventory_manager.get_products_by_category('راوتر')
        available_routers = [r for r in routers if r.get('current_stock', 0) > 0]
        print(f"📊 عدد الراوترات المتوفرة: {len(available_routers)}")
        
        # 3. فحص العمال
        workers = db.fetch_all("SELECT id, name FROM workers WHERE is_active = 1")
        print(f"📊 عدد العمال النشطين: {len(workers)}")
        
        # 4. فحص الباقات
        packages = db.fetch_all("SELECT id, name FROM packages LIMIT 5")
        print(f"📊 عدد الباقات: {len(packages)}")
        
        # إنشاء واجهة تسليم الراوتر للاختبار
        print("\n🧪 إنشاء واجهة تسليم الراوتر...")
        
        from ui.router_delivery_window import RouterDeliveryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء الواجهة
        window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة تسليم الراوتر")
        
        # اختبار البيانات في الواجهة
        print("\n🔍 فحص البيانات في الواجهة:")
        
        # فحص المشتركين في القائمة
        subscriber_count = window.subscriber_combo.count()
        print(f"📊 عدد المشتركين في القائمة: {subscriber_count - 1}")  # -1 للعنصر الأول
        
        # فحص الراوترات في القائمة
        router_count = window.router_combo.count()
        print(f"📊 عدد الراوترات في القائمة: {router_count - 1}")
        
        # فحص العمال في القائمة
        worker_count = window.worker_combo.count()
        print(f"📊 عدد العمال في القائمة: {worker_count - 1}")
        
        # فحص الباقات في القائمة
        package_count = window.package_combo.count()
        print(f"📊 عدد الباقات في القائمة: {package_count - 1}")
        
        # محاولة محاكاة عملية الحفظ
        print("\n🧪 محاكاة عملية الحفظ...")
        
        if subscriber_count > 1 and router_count > 1 and worker_count > 1:
            print("✅ البيانات الأساسية متوفرة")
            
            # اختيار البيانات
            window.subscriber_combo.setCurrentIndex(1)  # أول مشترك
            window.router_combo.setCurrentIndex(1)      # أول راوتر
            window.worker_combo.setCurrentIndex(1)      # أول عامل
            
            print("✅ تم اختيار البيانات الأساسية")
            
            # اختبار دالة التحقق
            print("🔍 اختبار دالة التحقق...")
            validation_result = window.validate_data()
            print(f"📊 نتيجة التحقق: {'✅ نجح' if validation_result else '❌ فشل'}")
            
            if validation_result:
                print("✅ البيانات صحيحة - يمكن الحفظ")
                
                # اختبار دالة حساب المبلغ
                try:
                    total_amount = window.calculate_total_amount()
                    print(f"💰 المبلغ الإجمالي: {total_amount:,} ل.س")
                    print("✅ دالة حساب المبلغ تعمل")
                except Exception as calc_error:
                    print(f"❌ خطأ في حساب المبلغ: {calc_error}")
                    return False
                
                print("🎉 جميع الاختبارات نجحت - الزر يجب أن يعمل!")
                return True
            else:
                print("❌ البيانات غير صحيحة")
                return False
        else:
            print("❌ البيانات الأساسية غير متوفرة:")
            if subscriber_count <= 1:
                print("  • لا توجد مشتركين")
            if router_count <= 1:
                print("  • لا توجد راوترات")
            if worker_count <= 1:
                print("  • لا توجد عمال")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_tables():
    """اختبار وجود الجداول المطلوبة"""
    
    print("\n🔍 فحص الجداول المطلوبة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # قائمة الجداول المطلوبة
        required_tables = [
            'subscribers',
            'unified_products', 
            'workers',
            'packages',
            'router_deliveries',
            'worker_inventory',
            'unified_inventory_movements'
        ]
        
        print("📋 فحص الجداول:")
        all_tables_exist = True
        
        for table in required_tables:
            try:
                result = db.fetch_one(f"SELECT COUNT(*) as count FROM {table}")
                count = result['count'] if result else 0
                print(f"  ✅ {table}: {count} سجل")
            except Exception as table_error:
                print(f"  ❌ {table}: غير موجود - {table_error}")
                all_tables_exist = False
        
        if all_tables_exist:
            print("✅ جميع الجداول موجودة")
            return True
        else:
            print("❌ بعض الجداول مفقودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص مشكلة زر حفظ وتسليم")
    print("=" * 50)
    
    # فحص الجداول
    tables_test = test_database_tables()
    
    # فحص عملية الحفظ
    save_test = test_router_delivery_save()
    
    print("\n" + "=" * 50)
    print("📊 ملخص التشخيص:")
    print(f"  • الجداول: {'✅ موجودة' if tables_test else '❌ مشكلة'}")
    print(f"  • عملية الحفظ: {'✅ جاهزة' if save_test else '❌ مشكلة'}")
    
    if all([tables_test, save_test]):
        print("\n🎉 النظام جاهز - زر حفظ وتسليم يجب أن يعمل!")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'تسليم راوتر'")
        print("  3. اختر مشترك، راوتر، وعامل")
        print("  4. اضغط 'حفظ وتسليم'")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح:")
        if not tables_test:
            print("  • تحقق من وجود الجداول في قاعدة البيانات")
        if not save_test:
            print("  • تحقق من وجود البيانات المطلوبة (مشتركين، راوترات، عمال)")

if __name__ == "__main__":
    main()

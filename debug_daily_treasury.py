#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة الخزينة اليومية
"""

import sys
import os
sys.path.append('src')

def check_daily_treasury_issue():
    """فحص مشكلة الخزينة اليومية"""
    
    print("🔍 فحص مشكلة الخزينة اليومية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        print("\n📊 فحص بيانات الخزينة الموحدة:")
        
        # فحص جميع البيانات في unified_treasury
        all_records = db.fetch_all("""
            SELECT user_id, session_date, daily_balance, main_balance, 
                   is_session_active, last_updated
            FROM unified_treasury 
            ORDER BY user_id, session_date DESC, last_updated DESC
        """)
        
        print(f"📋 إجمالي السجلات: {len(all_records)}")
        
        # تجميع البيانات حسب المستخدم
        users_data = {}
        for record in all_records:
            user_id = record['user_id']
            if user_id not in users_data:
                users_data[user_id] = []
            users_data[user_id].append(record)
        
        print(f"\n👥 عدد المستخدمين: {len(users_data)}")
        
        # عرض تفاصيل كل مستخدم
        total_daily_manual = 0
        total_main_manual = 0
        
        for user_id, records in users_data.items():
            print(f"\n👤 المستخدم {user_id}:")
            print(f"   📝 عدد السجلات: {len(records)}")
            
            # أحدث سجل
            latest_record = records[0]
            print(f"   📅 آخر تاريخ: {latest_record['session_date']}")
            print(f"   💰 الرصيد اليومي: {latest_record['daily_balance']:,} ل.س")
            print(f"   🏦 الرصيد الرئيسي: {latest_record['main_balance']:,} ل.س")
            print(f"   🔄 الجلسة نشطة: {'نعم' if latest_record['is_session_active'] else 'لا'}")
            
            # جمع الأرصدة للحساب اليدوي
            total_daily_manual += latest_record['daily_balance'] or 0
            total_main_manual += latest_record['main_balance'] or 0
            
            # عرض جميع السجلات للمستخدم
            if len(records) > 1:
                print(f"   📋 السجلات السابقة:")
                for i, record in enumerate(records[1:6]):  # أول 5 سجلات سابقة
                    print(f"      {i+1}. {record['session_date']}: يومي={record['daily_balance']:,}, رئيسي={record['main_balance']:,}")
        
        print(f"\n📊 الحساب اليدوي:")
        print(f"   💰 إجمالي الخزينة اليومية: {total_daily_manual:,} ل.س")
        print(f"   🏦 إجمالي الخزينة الرئيسية: {total_main_manual:,} ل.س")
        
        # مقارنة مع دوال النظام
        print(f"\n🔧 الحساب بدوال النظام:")
        system_daily = treasury_manager.get_total_daily_balance('SYP')
        system_main = treasury_manager.get_main_balance('SYP')
        
        print(f"   💰 الخزينة اليومية: {system_daily:,} ل.س")
        print(f"   🏦 الخزينة الرئيسية: {system_main:,} ل.س")
        
        # فحص التطابق
        daily_match = abs(total_daily_manual - system_daily) < 1
        main_match = abs(total_main_manual - system_main) < 1
        
        print(f"\n✅ نتائج المقارنة:")
        print(f"   💰 تطابق الخزينة اليومية: {'✅ نعم' if daily_match else '❌ لا'}")
        print(f"   🏦 تطابق الخزينة الرئيسية: {'✅ نعم' if main_match else '❌ لا'}")
        
        if not daily_match:
            diff = system_daily - total_daily_manual
            print(f"   ⚠️ الفرق في الخزينة اليومية: {diff:,} ل.س")
        
        if not main_match:
            diff = system_main - total_main_manual
            print(f"   ⚠️ الفرق في الخزينة الرئيسية: {diff:,} ل.س")
        
        return daily_match and main_match
        
    except Exception as e:
        print(f"❌ خطأ في فحص الخزينة اليومية: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_treasury_calculation_method():
    """فحص طريقة حساب الخزينة"""
    
    print("\n🔍 فحص طريقة حساب الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص كيف تحسب دالة get_total_daily_balance
        print("\n📊 تفصيل حساب الخزينة اليومية:")
        
        # الحصول على جميع المستخدمين
        users = db.fetch_all("""
            SELECT DISTINCT user_id FROM unified_treasury WHERE currency_type = 'SYP'
        """)
        
        total_calculated = 0
        
        for user in users:
            # أحدث رصيد يومي لكل مستخدم
            user_balance = db.fetch_one("""
                SELECT daily_balance FROM unified_treasury
                WHERE user_id = ? AND currency_type = 'SYP'
                ORDER BY session_date DESC, last_updated DESC
                LIMIT 1
            """, (user['user_id'],))
            
            if user_balance:
                balance = user_balance['daily_balance'] or 0
                total_calculated += balance
                print(f"   👤 المستخدم {user['user_id']}: {balance:,} ل.س")
        
        print(f"\n💰 إجمالي محسوب: {total_calculated:,} ل.س")
        
        # مقارنة مع دالة النظام
        system_total = treasury_manager.get_total_daily_balance('SYP')
        print(f"💰 إجمالي النظام: {system_total:,} ل.س")
        
        match = abs(total_calculated - system_total) < 1
        print(f"✅ التطابق: {'نعم' if match else 'لا'}")
        
        return match
        
    except Exception as e:
        print(f"❌ خطأ في فحص طريقة الحساب: {e}")
        return False

def check_recent_transactions():
    """فحص المعاملات الأخيرة"""
    
    print("\n🔍 فحص المعاملات الأخيرة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص آخر المعاملات
        recent_transactions = db.fetch_all("""
            SELECT type, description, amount, user_name, created_at
            FROM transactions 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        print(f"📋 آخر {len(recent_transactions)} معاملات:")
        
        for i, trans in enumerate(recent_transactions, 1):
            print(f"   {i}. {trans['type']} - {trans['amount']:,} ل.س")
            print(f"      👤 {trans['user_name']} - {trans['created_at']}")
            print(f"      📝 {trans['description'][:50]}...")
            print()
        
        # فحص معاملات اليوم
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')
        
        today_transactions = db.fetch_all("""
            SELECT type, SUM(amount) as total_amount, COUNT(*) as count
            FROM transactions 
            WHERE DATE(created_at) = ?
            GROUP BY type
            ORDER BY total_amount DESC
        """, (today,))
        
        print(f"📅 معاملات اليوم ({today}):")
        
        total_today = 0
        for trans in today_transactions:
            print(f"   📊 {trans['type']}: {trans['total_amount']:,} ل.س ({trans['count']} معاملة)")
            total_today += trans['total_amount'] or 0
        
        print(f"\n💰 إجمالي معاملات اليوم: {total_today:,} ل.س")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المعاملات: {e}")
        return False

def suggest_treasury_fix():
    """اقتراح إصلاح للخزينة"""
    
    print("\n💡 اقتراحات الإصلاح:")
    
    suggestions = [
        "1. إعادة حساب الأرصدة من المعاملات الفعلية",
        "2. تنظيف السجلات المكررة أو الخاطئة",
        "3. إعادة بناء الخزينة من الصفر",
        "4. مزامنة الأرصدة مع المعاملات",
        "5. فحص وإصلاح الأرصدة السالبة"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print(f"\n🔧 هل تريد تطبيق إصلاح تلقائي؟")
    return True

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص مشكلة الخزينة اليومية")
    print("=" * 70)
    
    # فحص المشكلة
    treasury_ok = check_daily_treasury_issue()
    
    # فحص طريقة الحساب
    calculation_ok = check_treasury_calculation_method()
    
    # فحص المعاملات الأخيرة
    transactions_ok = check_recent_transactions()
    
    # اقتراح الإصلاح
    suggest_treasury_fix()
    
    print("\n" + "=" * 70)
    print("📊 ملخص التشخيص:")
    print(f"   💰 الخزينة اليومية: {'✅ صحيحة' if treasury_ok else '❌ خاطئة'}")
    print(f"   🔧 طريقة الحساب: {'✅ صحيحة' if calculation_ok else '❌ خاطئة'}")
    print(f"   📋 المعاملات: {'✅ تم فحصها' if transactions_ok else '❌ خطأ في الفحص'}")
    
    if not treasury_ok:
        print("\n⚠️ تم اكتشاف مشكلة في الخزينة اليومية!")
        print("💡 يُنصح بتشغيل إصلاح الخزينة")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لاختبار النظام
"""

import sqlite3
from datetime import date

def main():
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('data/company_system.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    today = date.today().strftime('%Y-%m-%d')
    print(f'=== إنشاء بيانات تجريبية لتاريخ: {today} ===')

    try:
        # إضافة مقبوضات تجريبية
        print('\n--- إضافة مقبوضات تجريبية ---')
        cursor.execute("""
            INSERT INTO receipts (receipt_number, distributor_name, amount, receipt_date, description, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, ('R001', 'موزع تجريبي', 100000, today, 'مقبوضات تجريبية', 'admin'))
        
        cursor.execute("""
            INSERT INTO receipts (receipt_number, distributor_name, amount, receipt_date, description, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, ('R002', 'موزع آخر', 75000, today, 'مقبوضات تجريبية 2', 'admin'))
        
        print('✅ تم إضافة مقبوضات تجريبية')

        # إضافة مصاريف تجريبية
        print('\n--- إضافة مصاريف تجريبية ---')
        cursor.execute("""
            INSERT INTO expenses (expense_type, amount, expense_date, description, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        """, ('صيانة', 25000, today, 'صيانة تجريبية', 'admin'))
        
        cursor.execute("""
            INSERT INTO expenses (expense_type, amount, expense_date, description, user_name, created_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        """, ('مواد', 15000, today, 'شراء مواد', 'admin'))
        
        print('✅ تم إضافة مصاريف تجريبية')

        # إضافة معاملات تجريبية
        print('\n--- إضافة معاملات تجريبية ---')
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, user_name, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))
        """, ('اشتراك جديد', 'اشتراك تجريبي', 50000, 'admin'))
        
        cursor.execute("""
            INSERT INTO transactions (type, description, amount, user_name, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))
        """, ('تسليم راوتر', 'تسليم راوتر تجريبي', 80000, 'admin'))
        
        print('✅ تم إضافة معاملات تجريبية')

        # إضافة تسليم راوتر تجريبي
        print('\n--- إضافة تسليم راوتر تجريبي ---')
        cursor.execute("""
            INSERT INTO router_deliveries (
                subscriber_id, subscriber_name, router_type, subscription_fee, package_type,
                cable_type, cable_meters, worker_name, total_amount,
                delivery_date, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), ?)
        """, (
            1, 'مشترك تجريبي', 'TP-Link', 50000, 'باقة 10 ميجا',
            'كابل عادي', 50, 'عامل تجريبي', 120000, 1
        ))
        
        print('✅ تم إضافة تسليم راوتر تجريبي')

        # حفظ التغييرات
        conn.commit()
        print('\n✅ تم حفظ جميع البيانات التجريبية')

        # التحقق من البيانات
        print('\n=== التحقق من البيانات المضافة ===')
        
        cursor.execute('SELECT COUNT(*) as count FROM receipts WHERE DATE(receipt_date) = ?', (today,))
        receipts_count = cursor.fetchone()[0]
        print(f'عدد المقبوضات اليوم: {receipts_count}')
        
        cursor.execute('SELECT COUNT(*) as count FROM expenses WHERE DATE(expense_date) = ?', (today,))
        expenses_count = cursor.fetchone()[0]
        print(f'عدد المصاريف اليوم: {expenses_count}')
        
        cursor.execute('SELECT COUNT(*) as count FROM transactions WHERE DATE(created_at) = ?', (today,))
        transactions_count = cursor.fetchone()[0]
        print(f'عدد المعاملات اليوم: {transactions_count}')
        
        cursor.execute('SELECT COUNT(*) as count FROM router_deliveries WHERE DATE(delivery_date) = ?', (today,))
        deliveries_count = cursor.fetchone()[0]
        print(f'عدد تسليمات الراوترات اليوم: {deliveries_count}')

    except Exception as e:
        print(f'❌ خطأ في إنشاء البيانات: {e}')
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()

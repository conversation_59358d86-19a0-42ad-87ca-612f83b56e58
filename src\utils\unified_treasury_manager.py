#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الخزينة الموحد
Unified Treasury Manager
"""

import os
import shutil
from datetime import datetime

class UnifiedTreasuryManager:
    """مدير الخزينة الموحد - جدول واحد لجميع العمليات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.create_unified_treasury_table()

    def format_currency(self, amount, currency_type):
        """تنسيق العملة مع الرمز المناسب"""
        if currency_type == "USD":
            return f"${amount:,.2f}"
        elif currency_type == "EUR":
            return f"€{amount:,.2f}"
        else:  # SYP
            return f"{amount:,.0f} ل.س"
    
    def create_unified_treasury_table(self):
        """إنشاء جدول الخزينة الموحد"""
        try:
            # حذف الجداول القديمة
            old_tables = ['daily_treasury', 'main_treasury', 'treasury']
            for table in old_tables:
                try:
                    self.db_manager.execute_query(f"DROP TABLE IF EXISTS {table}")
                    print(f"🗑️ تم حذف الجدول القديم: {table}")
                except:
                    pass
            
            # إنشاء الجدول الموحد
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS unified_treasury (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_date DATE NOT NULL,
                    currency_type TEXT NOT NULL DEFAULT 'SYP',
                    daily_balance REAL DEFAULT 0,
                    main_balance REAL DEFAULT 0,
                    closing_balance REAL DEFAULT 0,
                    exchange_rate REAL DEFAULT 1.0,
                    is_session_active INTEGER DEFAULT 1,
                    session_opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_closed_at TIMESTAMP,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, session_date, currency_type)
                )
            """)

            # إضافة عمود closing_balance إذا لم يكن موجوداً
            try:
                self.db_manager.execute_query("ALTER TABLE unified_treasury ADD COLUMN closing_balance REAL DEFAULT 0")
                print("✅ تم إضافة عمود closing_balance")
            except:
                pass  # العمود موجود بالفعل

            # إنشاء جدول معاملات الصندوق
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS cash_box_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cash_box_id INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    description TEXT,
                    transaction_type TEXT DEFAULT 'general',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes (id)
                )
            """)
            
            print("✅ تم إنشاء جدول الخزينة الموحد")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الخزينة الموحد: {e}")
    
    def open_cash_box(self, user_id, date=None):
        """فتح الصندوق (بداية الجلسة)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # التحقق من وجود جلسة مفتوحة
            existing_session = self.db_manager.fetch_one("""
                SELECT id FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND is_session_active = 1
            """, (user_id, date))

            if existing_session:
                print(f"⚠️ يوجد صندوق مفتوح بالفعل للمستخدم {user_id} في {date}")
                return True

            # البحث عن جلسات مغلقة لنفس التاريخ
            closed_sessions = self.db_manager.fetch_all("""
                SELECT currency_type, main_balance FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND is_session_active = 0
            """, (user_id, date))

            if closed_sessions:
                # إعادة فتح الجلسات المغلقة
                print(f"🔄 إعادة فتح الجلسات المغلقة للمستخدم {user_id} في {date}")

                self.db_manager.execute_query("""
                    UPDATE unified_treasury
                    SET is_session_active = 1,
                        daily_balance = 0,
                        session_opened_at = CURRENT_TIMESTAMP,
                        session_closed_at = NULL
                    WHERE user_id = ? AND session_date = ? AND is_session_active = 0
                """, (user_id, date))

                print(f"✅ تم إعادة فتح الصندوق للمستخدم {user_id} في {date}")

                # فتح صندوق فعلي في جدول cash_boxes
                self.create_cash_box_record(user_id)

                return True

            # إنشاء جلسة جديدة للعملات الأساسية
            currencies = [
                ('SYP', 1.0),
                ('USD', 15000.0),
                ('EUR', 16000.0)
            ]

            for currency_type, exchange_rate in currencies:
                self.db_manager.execute_query("""
                    INSERT INTO unified_treasury
                    (user_id, session_date, currency_type, daily_balance, main_balance,
                     exchange_rate, is_session_active)
                    VALUES (?, ?, ?, 0, 0, ?, 1)
                """, (user_id, date, currency_type, exchange_rate))

            print(f"✅ تم فتح الصندوق للمستخدم {user_id} في {date}")

            # فتح صندوق فعلي في جدول cash_boxes
            self.create_cash_box_record(user_id)

            return True

        except Exception as e:
            print(f"❌ خطأ في فتح الصندوق: {e}")
            return False
    
    def close_cash_box(self, user_id, actual_cash=None, notes=None, date=None):
        """إغلاق الصندوق (نقل الأموال + نسخ احتياطي)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            print(f"🔒 محاولة إغلاق الصندوق للمستخدم {user_id} في {date}")

            # الحصول على الجلسة المفتوحة
            sessions = self.db_manager.fetch_all("""
                SELECT id, currency_type, daily_balance, main_balance
                FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND is_session_active = 1
            """, (user_id, date))

            print(f"🔍 عدد الجلسات المفتوحة: {len(sessions)}")

            if not sessions:
                print(f"⚠️ لا يوجد صندوق مفتوح للمستخدم {user_id} في {date}")

                # محاولة البحث عن أي جلسة للمستخدم
                all_sessions = self.db_manager.fetch_all("""
                    SELECT id, currency_type, daily_balance, main_balance, is_session_active, session_date
                    FROM unified_treasury
                    WHERE user_id = ?
                    ORDER BY session_date DESC
                """, (user_id,))

                print(f"🔍 جميع جلسات المستخدم ({len(all_sessions)}):")
                for session in all_sessions:
                    print(f"  • ID: {session[0]}, العملة: {session[1]}, التاريخ: {session[5]}, مفتوحة: {session[4]}")

                return False
            
            # نقل الأموال من اليومية للرئيسية
            for session in sessions:
                session_id = session[0]
                currency_type = session[1]
                daily_balance = session[2]
                main_balance = session[3]
                
                new_main_balance = main_balance + daily_balance
                
                # تحديث الجلسة
                self.db_manager.execute_query("""
                    UPDATE unified_treasury 
                    SET daily_balance = 0, 
                        main_balance = ?, 
                        is_session_active = 0,
                        session_closed_at = CURRENT_TIMESTAMP,
                        last_updated = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (new_main_balance, session_id))
                
                print(f"💰 تم نقل {daily_balance:,.2f} {currency_type} للخزينة الرئيسية")
            
            # إنشاء نسخة احتياطية
            backup_success = self.create_backup()
            if backup_success:
                print("💾 تم إنشاء نسخة احتياطية بنجاح")
            else:
                print("⚠️ فشل في إنشاء النسخة الاحتياطية")
            
            print(f"✅ تم إغلاق الصندوق للمستخدم {user_id} في {date}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إغلاق الصندوق: {e}")
            return False
    
    def add_to_daily_treasury(self, user_id, currency_type, amount, date=None):
        """إضافة مبلغ للخزينة اليومية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # محاولة التحديث
            self.db_manager.execute_query("""
                UPDATE unified_treasury
                SET daily_balance = daily_balance + ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 1
            """, (amount, user_id, date, currency_type))

            # التحقق من نجاح التحديث
            new_balance = self.get_daily_balance(user_id, currency_type, date)
            if new_balance == 0 and amount > 0:
                # إنشاء جلسة جديدة إذا لم تكن موجودة
                print(f"🔄 إنشاء جلسة جديدة للمستخدم {user_id}")
                self.open_cash_box(user_id, date)
                self.db_manager.execute_query("""
                    UPDATE unified_treasury
                    SET daily_balance = daily_balance + ?,
                        last_updated = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 1
                """, (amount, user_id, date, currency_type))
            
            print(f"✅ تم إضافة {amount:,.2f} {currency_type} للخزينة اليومية")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المبلغ للخزينة اليومية: {e}")
            return False
    
    def subtract_from_daily_treasury(self, user_id, currency_type, amount, date=None):
        """خصم مبلغ من الخزينة اليومية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # التحقق من توفر الرصيد
            current_balance = self.get_daily_balance(user_id, currency_type, date)
            if current_balance < amount:
                print(f"❌ الرصيد غير كافي: متاح {current_balance:,.2f} {currency_type}")
                return False
            
            self.db_manager.execute_query("""
                UPDATE unified_treasury
                SET daily_balance = daily_balance - ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 1
            """, (amount, user_id, date, currency_type))

            # التحقق من نجاح العملية
            new_balance = self.get_daily_balance(user_id, currency_type, date)
            if new_balance >= 0:
                print(f"✅ تم خصم {amount:,.2f} {currency_type} من الخزينة اليومية")
                return True
            else:
                print(f"❌ فشل في خصم المبلغ")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في خصم المبلغ من الخزينة اليومية: {e}")
            return False
    
    def exchange_currency(self, user_id, from_currency, to_currency, amount_from, exchange_rate, date=None):
        """صرف العملة (تحويل في الخزينة اليومية)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            amount_to = amount_from / exchange_rate
            
            # خصم العملة المصدر
            if not self.subtract_from_daily_treasury(user_id, from_currency, amount_from, date):
                return False
            
            # إضافة العملة المستهدفة
            if not self.add_to_daily_treasury(user_id, to_currency, amount_to, date):
                # إرجاع العملة المصدر في حالة الفشل
                self.add_to_daily_treasury(user_id, from_currency, amount_from, date)
                return False
            
            print(f"✅ تم صرف العملة: {amount_from:,.2f} {from_currency} → {amount_to:.2f} {to_currency}")
            return True

        except Exception as e:
            print(f"❌ خطأ في صرف العملة: {e}")
            return False

    def exchange_currency_from_total_daily(self, from_currency, to_currency, amount_from, exchange_rate, date=None):
        """صرف العملة من الخزينة اليومية الإجمالية (جميع المستخدمين)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            amount_to = amount_from / exchange_rate

            # التحقق من توفر الرصيد الإجمالي
            total_balance = self.get_total_daily_balance(from_currency)
            if total_balance < amount_from:
                print(f"❌ الرصيد الإجمالي غير كافي: متاح {total_balance:,.2f} {from_currency}")
                return False

            # الحصول على المستخدمين الذين لديهم رصيد من الصناديق المغلقة
            users = self.db_manager.fetch_all("""
                SELECT DISTINCT user_id FROM unified_treasury
                WHERE currency_type = ? AND session_date = ?
            """, (from_currency, date))

            remaining_amount = amount_from
            deducted_users = []

            for user in users:
                if remaining_amount <= 0:
                    break

                # الحصول على الرصيد المتاح من الصناديق المغلقة
                user_balance = self.get_daily_balance(user['user_id'], from_currency, date)

                if user_balance <= 0:
                    continue

                deduct_amount = min(remaining_amount, user_balance)

                # خصم من هذا المستخدم باستخدام دالة الخصم من الخزينة اليومية
                if self.deduct_from_daily_treasury(user['user_id'], deduct_amount, from_currency, f"صرف عملة - {deduct_amount:,.2f} {from_currency} إلى {amount_to:,.2f} {to_currency}"):
                    deducted_users.append({'user_id': user['user_id'], 'amount': deduct_amount})
                    remaining_amount -= deduct_amount
                    print(f"  ✅ خصم {self.format_currency(deduct_amount, from_currency)} من المستخدم {user['user_id']}")

            if remaining_amount > 0:
                # إرجاع المبالغ المخصومة
                for deduction in deducted_users:
                    self.add_to_daily_treasury(deduction['user_id'], from_currency, deduction['amount'], date)
                print(f"❌ فشل في خصم المبلغ الكامل")
                return False

            # إضافة العملة المستهدفة للمستخدم الأول (أو يمكن توزيعها)
            if users:
                first_user = users[0]['user_id']
                if self.add_to_daily_treasury(first_user, to_currency, amount_to, date):
                    print(f"✅ تم صرف العملة من الخزينة الإجمالية: {amount_from:,.2f} {from_currency} → {amount_to:.2f} {to_currency}")
                    return True
                else:
                    # إرجاع المبالغ المخصومة
                    for deduction in deducted_users:
                        self.add_to_daily_treasury(deduction['user_id'], from_currency, deduction['amount'], date)
                    return False

            return False

        except Exception as e:
            print(f"❌ خطأ في صرف العملة من الخزينة الإجمالية: {e}")
            return False

    def exchange_currency_in_main_treasury(self, user_id, from_currency, to_currency, amount_from, exchange_rate, date=None):
        """صرف العملة في الخزينة الرئيسية (للصناديق المغلقة)"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            amount_to = amount_from / exchange_rate

            # التحقق من توفر الرصيد في الخزينة الرئيسية
            current_balance = self.get_main_balance(user_id, from_currency, date)
            if current_balance < amount_from:
                print(f"❌ الرصيد غير كافي في الخزينة الرئيسية: متاح {current_balance:,.2f} {from_currency}")
                return False

            # خصم العملة المصدر من الخزينة الرئيسية
            self.db_manager.execute_query("""
                UPDATE unified_treasury
                SET main_balance = main_balance - ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency_type = ?
            """, (amount_from, user_id, date, from_currency))

            # إضافة العملة المستهدفة للخزينة الرئيسية
            # التحقق من وجود سجل للعملة المستهدفة
            existing_to = self.db_manager.fetch_one("""
                SELECT id FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND currency_type = ?
            """, (user_id, date, to_currency))

            if existing_to:
                # تحديث الرصيد الموجود
                self.db_manager.execute_query("""
                    UPDATE unified_treasury
                    SET main_balance = main_balance + ?,
                        last_updated = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND session_date = ? AND currency_type = ?
                """, (amount_to, user_id, date, to_currency))
            else:
                # إنشاء سجل جديد
                self.db_manager.execute_query("""
                    INSERT INTO unified_treasury
                    (user_id, session_date, currency_type, daily_balance, main_balance, exchange_rate, is_session_active)
                    VALUES (?, ?, ?, 0, ?, ?, 0)
                """, (user_id, date, to_currency, amount_to, exchange_rate))

            print(f"✅ تم صرف العملة في الخزينة الرئيسية: {amount_from:,.2f} {from_currency} → {amount_to:.2f} {to_currency}")
            return True

        except Exception as e:
            print(f"❌ خطأ في صرف العملة في الخزينة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def transfer_to_main_treasury(self, user_id, currency_type, amount, date=None):
        """نقل مبلغ من الخزينة اليومية للرئيسية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # التحقق من توفر الرصيد
            current_balance = self.get_daily_balance(user_id, currency_type, date)
            if current_balance < amount:
                print(f"❌ الرصيد غير كافي للنقل: متاح {current_balance:,.2f} {currency_type}")
                return False
            
            # تنفيذ النقل
            self.db_manager.execute_query("""
                UPDATE unified_treasury
                SET daily_balance = daily_balance - ?,
                    main_balance = main_balance + ?,
                    last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 1
            """, (amount, amount, user_id, date, currency_type))

            # التحقق من نجاح العملية
            new_daily_balance = self.get_daily_balance(user_id, currency_type, date)
            if new_daily_balance >= 0:
                print(f"✅ تم نقل {amount:,.2f} {currency_type} للخزينة الرئيسية")
                return True
            else:
                print(f"❌ فشل في النقل")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في النقل للخزينة الرئيسية: {e}")
            return False

    def transfer_from_total_daily_to_main(self, currency_type, amount, date=None):
        """نقل مبلغ من الخزينة اليومية الإجمالية للرئيسية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # التحقق من توفر الرصيد الإجمالي
            total_balance = self.get_total_daily_balance(currency_type)
            if total_balance < amount:
                print(f"❌ الرصيد الإجمالي غير كافي للنقل: متاح {total_balance:,.2f} {currency_type}")
                return False

            print(f"🔄 نقل {amount:,.2f} {currency_type} من الخزينة اليومية الإجمالية للرئيسية...")

            # الحصول على المستخدمين الذين لديهم رصيد يومي من الصناديق المغلقة
            users = self.db_manager.fetch_all("""
                SELECT DISTINCT user_id FROM unified_treasury
                WHERE currency_type = ? AND session_date = ?
            """, (currency_type, date))

            remaining_amount = amount
            transferred_users = []

            # خصم من المستخدمين حسب أرصدتهم من الصناديق المغلقة
            for user in users:
                if remaining_amount <= 0:
                    break

                # الحصول على الرصيد المتاح من الصناديق المغلقة
                user_balance = self.get_daily_balance(user['user_id'], currency_type, date)

                if user_balance <= 0:
                    continue

                transfer_amount = min(remaining_amount, user_balance)

                # تحديد رمز العملة
                currency_symbol = "$" if currency_type == "USD" else "ل.س"
                print(f"  📦 نقل {transfer_amount:,.2f if currency_type == 'USD' else transfer_amount:,.0f} {currency_symbol} من المستخدم {user['user_id']} (من الصناديق المغلقة)")

                # تسجيل النقل كمصروف من الخزينة اليومية
                # تسجيل النقل كمصروف من الخزينة اليومية
                transfer_success = self.deduct_from_daily_treasury(
                    user['user_id'],
                    transfer_amount,
                    currency_type,
                    f"نقل للخزينة الرئيسية - {transfer_amount:,.2f} {currency_type}"
                )

                if transfer_success:
                    # تسجيل النقل في جدول التحويلات
                    self.db_manager.execute_query("""
                        INSERT INTO treasury_transfers
                        (user_id, amount, currency_type, transfer_type, description, created_at)
                        VALUES (?, ?, ?, 'daily_to_main', ?, CURRENT_TIMESTAMP)
                    """, (user['user_id'], transfer_amount, currency_type,
                          f"نقل من الخزينة اليومية للرئيسية"))

                    # إضافة للخزينة الرئيسية
                    self.db_manager.execute_query("""
                        UPDATE unified_treasury
                        SET main_balance = main_balance + ?,
                            last_updated = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND session_date = ? AND currency_type = ?
                        ORDER BY last_updated DESC LIMIT 1
                    """, (transfer_amount, user['user_id'], date, currency_type))

                    transferred_users.append({
                        'user_id': user['user_id'],
                        'amount': transfer_amount
                    })
                    remaining_amount -= transfer_amount

                    currency_symbol = "$" if currency_type == "USD" else "ل.س"
                    print(f"    ✅ تم نقل {transfer_amount:,.2f if currency_type == 'USD' else transfer_amount:,.0f} {currency_symbol} من المستخدم {user['user_id']}")
                else:
                    currency_symbol = "$" if currency_type == "USD" else "ل.س"
                    print(f"    ❌ فشل في نقل {transfer_amount:,.2f if currency_type == 'USD' else transfer_amount:,.0f} {currency_symbol} من المستخدم {user['user_id']}")

            if remaining_amount > 0:
                currency_symbol = "$" if currency_type == "USD" else "ل.س"
                print(f"❌ لم يتم نقل المبلغ الكامل - متبقي: {remaining_amount:,.2f if currency_type == 'USD' else remaining_amount:,.0f} {currency_symbol}")
                return False

            currency_symbol = "$" if currency_type == "USD" else "ل.س"
            print(f"✅ تم نقل {amount:,.2f if currency_type == 'USD' else amount:,.0f} {currency_symbol} من الخزينة اليومية الإجمالية للرئيسية")
            return True

        except Exception as e:
            print(f"❌ خطأ في النقل من الخزينة الإجمالية: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_daily_balance(self, user_id, currency_type, date=None):
        """الحصول على رصيد الخزينة اليومية من الصناديق المغلقة"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # الحصول على مجموع الصناديق المغلقة لهذا المستخدم في هذا التاريخ
            closed_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(closing_balance), 0) as total
                FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND currency_type = ?
                AND is_session_active = 0 AND closing_balance > 0
            """, (user_id, date, currency_type))

            closed_balance = closed_result['total'] if closed_result else 0.0

            # طرح المبالغ المنقولة للخزينة الرئيسية
            transferred_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM treasury_transfers
                WHERE user_id = ? AND DATE(created_at) = ? AND currency_type = ?
                AND transfer_type = 'daily_to_main'
            """, (user_id, date, currency_type))

            transferred_amount = transferred_result['total'] if transferred_result else 0.0

            # طرح المصروفات المخصومة من الخزينة اليومية
            expenses_result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE user_id = ? AND DATE(created_at) = ? AND currency = ?
                AND deducted_from_daily = 1
            """, (user_id, date, currency_type))

            expenses_amount = expenses_result['total'] if expenses_result else 0.0

            # الرصيد النهائي = الصناديق المغلقة - المنقول - المصروفات
            final_balance = closed_balance - transferred_amount - expenses_amount
            final_balance = max(0, final_balance)  # لا يمكن أن يكون سالب

            print(f"💰 رصيد {currency_type} اليومي للمستخدم {user_id}:")
            print(f"   📦 الصناديق المغلقة: {self.format_currency(closed_balance, currency_type)}")
            print(f"   📤 المنقول للرئيسية: {self.format_currency(transferred_amount, currency_type)}")
            print(f"   💸 المصروفات: {self.format_currency(expenses_amount, currency_type)}")
            print(f"   💵 الرصيد النهائي: {self.format_currency(final_balance, currency_type)}")

            return final_balance

        except Exception as e:
            print(f"❌ خطأ في الحصول على الرصيد اليومي: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

    def get_total_daily_balance(self, currency_type='SYP', date=None):
        """الحصول على إجمالي الخزينة اليومية من جميع الصناديق المغلقة"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # الحصول على جميع المستخدمين النشطين
            users = self.db_manager.fetch_all("""
                SELECT DISTINCT user_id FROM unified_treasury WHERE currency_type = ?
            """, (currency_type,))

            total_balance = 0.0

            print(f"🔍 حساب إجمالي الخزينة اليومية {currency_type} من الصناديق المغلقة:")

            for user in users:
                # الحصول على رصيد الخزينة اليومية لكل مستخدم من الصناديق المغلقة
                user_balance = self.get_daily_balance(user['user_id'], currency_type, date)
                total_balance += user_balance
                print(f"  • المستخدم {user['user_id']}: {self.format_currency(user_balance, currency_type)}")

            print(f"💰 إجمالي الخزينة اليومية {currency_type} (جميع المستخدمين): {self.format_currency(total_balance, currency_type)}")
            return total_balance

        except Exception as e:
            print(f"❌ خطأ في الحصول على إجمالي الخزينة اليومية: {e}")
            return 0.0

    def get_main_balance(self, user_id, currency_type, date=None):
        """الحصول على رصيد الخزينة الرئيسية"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # البحث في الجلسات المفتوحة أولاً
            result = self.db_manager.fetch_one("""
                SELECT main_balance FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 1
            """, (user_id, date, currency_type))

            if result:
                balance = result['main_balance']
                print(f"💰 رصيد {currency_type} الرئيسي للمستخدم {user_id} (جلسة مفتوحة): {balance}")
                return balance

            # البحث في الجلسات المغلقة
            result = self.db_manager.fetch_one("""
                SELECT main_balance FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND currency_type = ? AND is_session_active = 0
            """, (user_id, date, currency_type))

            balance = result['main_balance'] if result else 0.0
            print(f"💰 رصيد {currency_type} الرئيسي للمستخدم {user_id} (جلسة مغلقة): {balance}")

            return balance

        except Exception as e:
            print(f"❌ خطأ في الحصول على الرصيد الرئيسي: {e}")
            return 0.0
    
    def is_session_active(self, user_id, date=None):
        """التحقق من وجود جلسة مفتوحة"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            result = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count FROM unified_treasury
                WHERE user_id = ? AND session_date = ? AND is_session_active = 1
            """, (user_id, date))

            count = result['count'] if result else 0
            print(f"🔍 فحص الجلسة: المستخدم {user_id}, التاريخ {date}, عدد الجلسات المفتوحة: {count}")

            return count > 0

        except Exception as e:
            print(f"❌ خطأ في التحقق من الجلسة: {e}")
            return False
    
    def create_backup(self):
        """إنشاء نسخة احتياطية للبرنامج"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = f"backups/backup_{timestamp}"
            
            # إنشاء مجلد النسخ الاحتياطية
            os.makedirs(backup_dir, exist_ok=True)
            
            # نسخ قاعدة البيانات
            if os.path.exists('data/company_system.db'):
                shutil.copy2('data/company_system.db', f"{backup_dir}/company_system.db")
                print(f"📁 تم نسخ قاعدة البيانات")
            
            # نسخ ملفات الإعدادات
            config_files = ['config.json', 'settings.ini']
            for config_file in config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, f"{backup_dir}/{config_file}")
                    print(f"📁 تم نسخ {config_file}")
            
            # نسخ مجلد البيانات كاملاً
            if os.path.exists('data'):
                shutil.copytree('data', f"{backup_dir}/data", dirs_exist_ok=True)
                print(f"📁 تم نسخ مجلد البيانات")
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            with open(f"{backup_dir}/backup_info.txt", 'w', encoding='utf-8') as f:
                f.write(f"نسخة احتياطية تلقائية\n")
                f.write(f"التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"سبب النسخ: إغلاق الصندوق\n")
                f.write(f"حجم قاعدة البيانات: {os.path.getsize('data/company_system.db') if os.path.exists('data/company_system.db') else 0} بايت\n")
            
            print(f"✅ تم إنشاء نسخة احتياطية في: {backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

    def get_main_balance(self, currency_type='SYP'):
        """الحصول على رصيد الخزينة الرئيسية (أحدث رصيد لكل مستخدم)"""
        try:
            # جمع أحدث رصيد رئيسي لكل مستخدم (الرصيد الحالي)
            users = self.db_manager.fetch_all("""
                SELECT DISTINCT user_id FROM unified_treasury WHERE currency_type = ?
            """, (currency_type,))

            total_balance = 0.0
            for user in users:
                # أحدث رصيد رئيسي لهذا المستخدم
                user_balance = self.db_manager.fetch_one("""
                    SELECT main_balance FROM unified_treasury
                    WHERE user_id = ? AND currency_type = ?
                    ORDER BY session_date DESC, last_updated DESC
                    LIMIT 1
                """, (user['user_id'], currency_type))

                if user_balance:
                    total_balance += user_balance['main_balance']

            print(f"💰 رصيد الخزينة الرئيسية {currency_type}: {total_balance}")
            return total_balance

        except Exception as e:
            print(f"❌ خطأ في الحصول على رصيد الخزينة الرئيسية: {e}")
            # طريقة احتياطية أبسط
            try:
                # جمع أحدث main_balance لكل مستخدم
                users = self.db_manager.fetch_all("""
                    SELECT DISTINCT user_id FROM unified_treasury WHERE currency_type = ?
                """, (currency_type,))

                total_balance = 0.0
                for user in users:
                    user_balance = self.db_manager.fetch_one("""
                        SELECT main_balance FROM unified_treasury
                        WHERE user_id = ? AND currency_type = ?
                        ORDER BY session_date DESC, last_updated DESC
                        LIMIT 1
                    """, (user['user_id'], currency_type))

                    if user_balance:
                        total_balance += user_balance['main_balance']

                print(f"💰 رصيد الخزينة الرئيسية {currency_type} (طريقة احتياطية): {total_balance}")
                return total_balance

            except Exception as fallback_error:
                print(f"❌ خطأ في الطريقة الاحتياطية: {fallback_error}")
                return 0.0

    def get_cash_box_balance(self, user_id):
        """الحصول على رصيد الصندوق للمستخدم"""
        try:
            # البحث عن الصندوق المفتوح
            cash_box = self.db_manager.fetch_one("""
                SELECT id, opening_balance FROM cash_boxes
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC LIMIT 1
            """, (user_id,))

            if not cash_box:
                print(f"⚠️ لا يوجد صندوق مفتوح للمستخدم {user_id}")
                return 0.0

            # حساب إجمالي المعاملات
            transactions_sum = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM cash_box_transactions
                WHERE cash_box_id = ?
            """, (cash_box['id'],))

            total_transactions = transactions_sum['total'] if transactions_sum else 0.0
            balance = cash_box['opening_balance'] + total_transactions

            print(f"💰 رصيد الصندوق للمستخدم {user_id}: {balance}")
            return balance

        except Exception as e:
            print(f"❌ خطأ في الحصول على رصيد الصندوق: {e}")
            return 0.0

    def add_to_cash_box(self, user_id, amount, description, transaction_type='sale'):
        """إضافة مبلغ للصندوق"""
        try:
            # البحث عن الصندوق المفتوح
            cash_box = self.db_manager.fetch_one("""
                SELECT id FROM cash_boxes
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC LIMIT 1
            """, (user_id,))

            if not cash_box:
                print(f"❌ لا يوجد صندوق مفتوح للمستخدم {user_id}")
                return False

            # إضافة المعاملة
            result = self.db_manager.execute_query("""
                INSERT INTO cash_box_transactions
                (cash_box_id, amount, description, transaction_type, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (cash_box['id'], amount, description, transaction_type))

            if result:
                print(f"✅ تم إضافة {amount:,} ل.س للصندوق - {description}")
                return True
            else:
                print(f"❌ فشل في إضافة المبلغ للصندوق")
                return False

        except Exception as e:
            print(f"❌ خطأ في إضافة المبلغ للصندوق: {e}")
            return False

    def subtract_from_cash_box(self, user_id, amount, description, transaction_type="expense"):
        """خصم مبلغ من الصندوق"""
        try:
            # البحث عن الصندوق المفتوح
            cash_box = self.db_manager.fetch_one("""
                SELECT id FROM cash_boxes
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC LIMIT 1
            """, (user_id,))

            if not cash_box:
                print(f"❌ لا يوجد صندوق مفتوح للمستخدم {user_id}")
                return False

            # إضافة معاملة سالبة (خصم)
            result = self.db_manager.execute_query("""
                INSERT INTO cash_box_transactions
                (cash_box_id, amount, description, transaction_type, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (cash_box['id'], -amount, description, transaction_type))

            if result:
                print(f"✅ تم خصم {amount:,} ل.س من الصندوق - {description}")
                return True
            else:
                print(f"❌ فشل في خصم المبلغ من الصندوق")
                return False

        except Exception as e:
            print(f"❌ خطأ في خصم المبلغ من الصندوق: {e}")
            return False

    def deduct_from_daily_treasury(self, user_id, amount, currency_type, description):
        """خصم مبلغ من الخزينة اليومية (تسجيل كمصروف)"""
        try:
            # التحقق من توفر الرصيد
            available_balance = self.get_daily_balance(user_id, currency_type)

            if available_balance < amount:
                print(f"❌ الرصيد غير كافي. المتاح: {available_balance}, المطلوب: {amount}")
                return False

            # تسجيل المصروف مع تحديد أنه مخصوم من الخزينة اليومية
            result = self.db_manager.execute_query("""
                INSERT INTO expenses
                (user_id, amount, currency, description, deducted_from_daily, created_at)
                VALUES (?, ?, ?, ?, 1, CURRENT_TIMESTAMP)
            """, (user_id, amount, currency_type, description))

            if result:
                print(f"✅ تم خصم {amount:,} {currency_type} من الخزينة اليومية - {description}")
                return True
            else:
                print(f"❌ فشل في خصم المبلغ من الخزينة اليومية")
                return False

        except Exception as e:
            print(f"❌ خطأ في خصم المبلغ من الخزينة اليومية: {e}")
            import traceback
            traceback.print_exc()
            return False

    def close_cash_box_to_daily_treasury(self, user_id, actual_cash=None, notes=None):
        """إغلاق الصندوق ونقل الرصيد للخزينة اليومية (التدفق الصحيح)"""
        try:
            print(f"🔒 إغلاق الصندوق للمستخدم {user_id} - نقل للخزينة اليومية")

            # الحصول على رصيد الصندوق المفتوح
            cash_box_balance = self.get_cash_box_balance(user_id)

            if cash_box_balance <= 0:
                print("⚠️ لا يوجد رصيد في الصندوق للنقل")
                # إغلاق الصندوق حتى لو كان الرصيد صفر
                self.close_cash_box_record(user_id, actual_cash or 0, notes or "إغلاق صندوق فارغ")
                # إغلاق الجلسة أيضاً
                self.close_session(user_id)
                return True

            print(f"💰 رصيد الصندوق: {cash_box_balance:,} ل.س")

            # إضافة رصيد الصندوق للخزينة اليومية
            success = self.add_to_daily_treasury(
                user_id=user_id,
                currency_type='SYP',
                amount=cash_box_balance,
                description=f"إغلاق الصندوق - نقل {cash_box_balance:,} ل.س للخزينة اليومية"
            )

            if success:
                print(f"✅ تم نقل {cash_box_balance:,} ل.س من الصندوق للخزينة اليومية")

                # إغلاق الصندوق في جدول cash_boxes
                self.close_cash_box_record(user_id, actual_cash or cash_box_balance, notes)

                # إغلاق الجلسة في unified_treasury مع الرصيد الختامي
                self.close_session(user_id, closing_balance=actual_cash or cash_box_balance)

                # إنشاء نسخة احتياطية
                backup_success = self.create_backup()
                if backup_success:
                    print("💾 تم إنشاء نسخة احتياطية بنجاح")

                return True
            else:
                print("❌ فشل في نقل رصيد الصندوق للخزينة اليومية")
                return False

        except Exception as e:
            print(f"❌ خطأ في إغلاق الصندوق: {e}")
            return False

    def close_cash_box_record(self, user_id, actual_cash, notes):
        """إغلاق سجل الصندوق في جدول cash_boxes"""
        try:
            # البحث عن الصندوق المفتوح
            cash_box = self.db_manager.fetch_one("""
                SELECT id FROM cash_boxes
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC LIMIT 1
            """, (user_id,))

            if cash_box:
                # إغلاق الصندوق
                result = self.db_manager.execute_query("""
                    UPDATE cash_boxes
                    SET status = 'closed',
                        closing_balance = ?,
                        notes = ?,
                        closed_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (actual_cash, notes, cash_box['id']))

                if result:
                    print(f"✅ تم إغلاق سجل الصندوق - ID: {cash_box['id']}")
                    return True
                else:
                    print(f"❌ فشل في إغلاق سجل الصندوق")
                    return False
            else:
                print("⚠️ لا يوجد صندوق مفتوح للإغلاق")
                return True  # لا نعتبرها خطأ

        except Exception as e:
            print(f"❌ خطأ في إغلاق سجل الصندوق: {e}")
            return False

    def close_session(self, user_id, date=None, closing_balance=None):
        """إغلاق الجلسة في unified_treasury مع حفظ الرصيد الختامي"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # الحصول على الرصيد الحالي إذا لم يتم تمريره
            if closing_balance is None:
                current_session = self.db_manager.fetch_one("""
                    SELECT daily_balance FROM unified_treasury
                    WHERE user_id = ? AND session_date = ? AND is_session_active = 1
                    ORDER BY last_updated DESC LIMIT 1
                """, (user_id, date))
                closing_balance = current_session['daily_balance'] if current_session else 0

            # إغلاق جميع الجلسات المفتوحة للمستخدم في هذا التاريخ
            result = self.db_manager.execute_query("""
                UPDATE unified_treasury
                SET is_session_active = 0,
                    closing_balance = ?,
                    session_closed_at = CURRENT_TIMESTAMP,
                    last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND is_session_active = 1
            """, (closing_balance, user_id, date))

            if result:
                print(f"✅ تم إغلاق الجلسة للمستخدم {user_id} في {date} برصيد ختامي: {closing_balance:,}")
                return True
            else:
                print(f"⚠️ لا توجد جلسة مفتوحة للمستخدم {user_id} في {date}")
                return True  # لا نعتبرها خطأ

        except Exception as e:
            print(f"❌ خطأ في إغلاق الجلسة: {e}")
            return False

    def create_cash_box_record(self, user_id):
        """إنشاء سجل صندوق في جدول cash_boxes"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # التحقق من عدم وجود صندوق مفتوح لنفس المستخدم واليوم
            existing_box = self.db_manager.fetch_one("""
                SELECT id FROM cash_boxes
                WHERE user_id = ? AND date = ? AND status = 'open'
            """, (user_id, today))

            if existing_box:
                print(f"⚠️ يوجد صندوق مفتوح بالفعل للمستخدم {user_id} في {today}")
                return True

            # إنشاء صندوق جديد أو إعادة فتح صندوق مغلق
            try:
                result = self.db_manager.execute_query("""
                    INSERT INTO cash_boxes
                    (user_id, date, opening_balance, status, created_at)
                    VALUES (?, ?, 0, 'open', CURRENT_TIMESTAMP)
                """, (user_id, today))

                if result:
                    print(f"✅ تم إنشاء صندوق جديد للمستخدم {user_id}")
                    return True
                else:
                    print(f"❌ فشل في إنشاء صندوق للمستخدم {user_id}")
                    return False

            except Exception as insert_error:
                # إذا فشل الإدراج بسبب UNIQUE constraint، حاول إعادة فتح صندوق مغلق
                if "UNIQUE constraint failed" in str(insert_error):
                    print(f"🔄 محاولة إعادة فتح صندوق مغلق للمستخدم {user_id}")

                    update_result = self.db_manager.execute_query("""
                        UPDATE cash_boxes
                        SET status = 'open',
                            closing_balance = NULL,
                            notes = NULL,
                            closed_at = NULL
                        WHERE user_id = ? AND date = ? AND status = 'closed'
                    """, (user_id, today))

                    if update_result:
                        print(f"✅ تم إعادة فتح صندوق للمستخدم {user_id}")
                        return True
                    else:
                        print(f"❌ فشل في إعادة فتح الصندوق للمستخدم {user_id}")
                        return False
                else:
                    raise insert_error

        except Exception as e:
            print(f"❌ خطأ في إنشاء سجل الصندوق: {e}")
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتسليم الراوتر
"""

import sys
import os
sys.path.append('src')

def test_router_delivery_import():
    """اختبار استيراد واجهة تسليم الراوتر"""
    
    print("🧪 اختبار استيراد واجهة تسليم الراوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        print("✅ تم استيراد مدير قاعدة البيانات")
        print("✅ تم استيراد مدير الخزينة الموحد")
        
        # اختبار قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        print("✅ تم إنشاء اتصال قاعدة البيانات")
        
        # اختبار مدير الخزينة
        treasury_manager = UnifiedTreasuryManager(db)
        print("✅ تم إنشاء مدير الخزينة الموحد")
        
        # اختبار استيراد واجهة تسليم الراوتر
        from ui.router_delivery_window import RouterDeliveryWindow
        print("✅ تم استيراد واجهة تسليم الراوتر")
        
        # اختبار البيانات المطلوبة
        routers = db.fetch_all("SELECT COUNT(*) as count FROM products WHERE category = 'router'")
        router_count = routers[0]['count'] if routers else 0
        print(f"📦 عدد الراوترات في قاعدة البيانات: {router_count}")
        
        packages = db.fetch_all("SELECT COUNT(*) as count FROM packages")
        package_count = packages[0]['count'] if packages else 0
        print(f"📋 عدد الباقات في قاعدة البيانات: {package_count}")
        
        subscribers = db.fetch_all("SELECT COUNT(*) as count FROM subscribers WHERE delivered = 0")
        subscriber_count = subscribers[0]['count'] if subscribers else 0
        print(f"👥 عدد المشتركين غير المسلمين: {subscriber_count}")
        
        # اختبار الخزينة الموحدة
        user_id = 1
        treasury_manager.open_cash_box(user_id=user_id)
        
        balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد الحالي في الخزينة: {balance:,} ل.س")
        
        print(f"\n🎉 جميع المكونات تعمل بشكل صحيح!")
        print(f"\n📋 ملخص الاختبار:")
        print(f"  ✅ استيراد الواجهة: نجح")
        print(f"  ✅ قاعدة البيانات: متصلة")
        print(f"  ✅ الخزينة الموحدة: تعمل")
        print(f"  ✅ البيانات: متوفرة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_update():
    """اختبار تحديث الخزينة"""
    
    print("\n" + "="*50)
    print("🧪 اختبار تحديث الخزينة عند تسليم الراوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        # فتح الصندوق
        treasury_manager.open_cash_box(user_id=user_id)
        
        # الرصيد قبل العملية
        balance_before = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد قبل العملية: {balance_before:,} ل.س")
        
        # محاكاة عملية تسليم راوتر
        delivery_amount = 200000  # مبلغ تسليم الراوتر
        
        success = treasury_manager.add_to_daily_treasury(
            user_id=user_id,
            currency_type='SYP',
            amount=delivery_amount
        )
        
        if success:
            print(f"✅ تم إضافة {delivery_amount:,} ل.س للخزينة")
        else:
            print(f"❌ فشل في إضافة المبلغ للخزينة")
            return False
        
        # الرصيد بعد العملية
        balance_after = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد بعد العملية: {balance_after:,} ل.س")
        
        # التحقق من صحة العملية
        expected_balance = balance_before + delivery_amount
        if abs(balance_after - expected_balance) < 0.01:
            print(f"✅ تحديث الخزينة يعمل بشكل صحيح")
            print(f"  • المبلغ المضاف: {delivery_amount:,} ل.س")
            print(f"  • الزيادة الفعلية: {balance_after - balance_before:,.0f} ل.س")
            return True
        else:
            print(f"❌ خطأ في تحديث الخزينة")
            print(f"  • المتوقع: {expected_balance:,} ل.س")
            print(f"  • الفعلي: {balance_after:,} ل.س")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحديث الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار تسليم الراوتر...")
    
    # اختبار الاستيراد
    import_success = test_router_delivery_import()
    
    # اختبار تحديث الخزينة
    treasury_success = test_treasury_update()
    
    print(f"\n📊 نتائج الاختبار النهائية:")
    print(f"  • استيراد الواجهة: {'✅ نجح' if import_success else '❌ فشل'}")
    print(f"  • تحديث الخزينة: {'✅ نجح' if treasury_success else '❌ فشل'}")
    
    if import_success and treasury_success:
        print(f"\n🎉 تسليم الراوتر جاهز للاستخدام!")
        print(f"\n📋 الإصلاحات المطبقة:")
        print(f"  ✅ إصلاح مشكلة inventory_manager المفقود")
        print(f"  ✅ إضافة النظام الموحد للخزينة")
        print(f"  ✅ تحديث الخزينة عند حفظ التسليم")
        print(f"  ✅ معالجة الأخطاء المحسنة")
        print(f"  ✅ استخدام الطريقة التقليدية كبديل")
    else:
        print(f"\n❌ هناك مشاكل تحتاج إصلاح")
    
    print(f"\n🔧 للاستخدام:")
    print(f"  1. شغل التطبيق: python system_launcher.py")
    print(f"  2. سجل دخول: admin / 123")
    print(f"  3. افتح 'تسليم راوتر'")
    print(f"  4. املأ البيانات واضغط 'حفظ'")
    print(f"  5. التطبيق لن يغلق والخزينة ستتحدث!")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة تسليم الراوتر - نسخة جديدة ونظيفة
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QComboBox, QPushButton, QMessageBox,
                            QCheckBox, QSpinBox, QDoubleSpinBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    # محاولة استيراد بديل
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
        from database.database_manager import DatabaseManager
    except ImportError as e2:
        print(f"خطأ في الاستيراد البديل: {e2}")
        # لا نغلق التطبيق، بل نرفع استثناء يمكن التعامل معه
        raise ImportError(f"فشل في استيراد المكتبات المطلوبة: {e2}")

# استيراد دالة الشيفت بشكل منفصل
def get_current_shift(db_manager, user_id, user_name):
    """الحصول على الشيفت الحالي للمستخدم أو إنشاء واحد جديد"""
    from datetime import datetime

    today = datetime.now().strftime('%Y-%m-%d')

    # البحث عن شيفت مفتوح لليوم
    current_shift = db_manager.fetch_one("""
        SELECT id, user_id, user_name, shift_date, start_time, status
        FROM shifts
        WHERE user_id = ? AND shift_date = ? AND status = 'open'
    """, (user_id, today))

    if current_shift:
        print(f"📂 شيفت مفتوح موجود: ID {current_shift['id']} للمستخدم {current_shift['user_name']}")
        return current_shift['id']

    # إنشاء شيفت جديد
    try:
        cursor = db_manager.execute_query("""
            INSERT INTO shifts (user_id, user_name, shift_date, start_time, status)
            VALUES (?, ?, ?, ?, 'open')
        """, (user_id, user_name, today, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

        shift_id = cursor.lastrowid if hasattr(cursor, 'lastrowid') else 1
        print(f"✅ تم إنشاء شيفت جديد: ID {shift_id} للمستخدم {user_name}")
        return shift_id

    except Exception as e:
        print(f"❌ خطأ في إنشاء الشيفت: {e}")
        return None

class RouterDeliveryWindow(QDialog):
    """واجهة تسليم الراوتر - نسخة جديدة ونظيفة"""
    
    delivery_saved = pyqtSignal()
    
    def __init__(self, db_manager, inventory_manager=None, treasury_manager=None, current_user=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user

        # استخدام مدير الخزينة الممرر أو إنشاء واحد جديد
        if treasury_manager:
            self.treasury_manager = treasury_manager
        else:
            try:
                from utils.unified_treasury_manager import UnifiedTreasuryManager
                self.treasury_manager = UnifiedTreasuryManager(db_manager)
            except Exception as e:
                print(f"⚠️ تحذير: لم يتم تحميل مدير الخزينة الموحد: {e}")
                self.treasury_manager = None

        # استخدام مدير المخزون الممرر أو إنشاء واحد جديد
        if inventory_manager:
            self.inventory_manager = inventory_manager
        else:
            try:
                from utils.unified_inventory_manager import UnifiedInventoryManager
                self.inventory_manager = UnifiedInventoryManager(db_manager)
            except Exception as e:
                print(f"⚠️ تحذير: لم يتم تحميل مدير المخزون الموحد: {e}")
                self.inventory_manager = None
        self.selected_subscriber = None
        
        self.setWindowTitle(reshape_arabic_text("تسليم راوتر"))
        self.setModal(True)
        self.resize(800, 600)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
        print("✅ تم إنشاء واجهة تسليم الراوتر الجديدة")

    def safe_format_currency(self, amount):
        """تنسيق آمن للعملة"""
        try:
            if amount is None:
                return "0 ل.س"
            return format_currency(amount)
        except Exception as e:
            print(f"⚠️ خطأ في تنسيق العملة {amount}: {e}")
            try:
                return f"{float(amount):,.0f} ل.س"
            except:
                return "0 ل.س"

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("🚚 تسليم راوتر للمشترك"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # قسم بيانات المشترك
        subscriber_frame = self.create_subscriber_section()
        layout.addWidget(subscriber_frame)
        
        # قسم بيانات التسليم
        delivery_frame = self.create_delivery_section()
        layout.addWidget(delivery_frame)
        
        # قسم الإجمالي
        total_frame = self.create_total_section()
        layout.addWidget(total_frame)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_subscriber_section(self):
        """إنشاء قسم بيانات المشترك"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(10)
        
        # عنوان القسم
        section_title = QLabel(reshape_arabic_text("👤 بيانات المشترك"))
        section_title.setFont(create_arabic_font(13, bold=True))
        section_title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(section_title, 0, 0, 1, 4)
        
        # اسم المشترك (قابل للبحث والكتابة)
        layout.addWidget(QLabel(reshape_arabic_text("اسم المشترك:")), 1, 0)
        self.subscriber_combo = QComboBox()
        self.subscriber_combo.setEditable(True)
        self.subscriber_combo.setFont(create_arabic_font(11))
        self.subscriber_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
            QComboBox:focus {
                border-color: #007bff;
            }
        """)
        layout.addWidget(self.subscriber_combo, 1, 1, 1, 3)
        
        # معلومات المشترك (تظهر بعد الاختيار)
        self.subscriber_info_label = QLabel(reshape_arabic_text("اختر مشتركاً لعرض معلوماته"))
        self.subscriber_info_label.setFont(create_arabic_font(10))
        self.subscriber_info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background-color: #e9ecef;
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }
        """)
        layout.addWidget(self.subscriber_info_label, 2, 0, 1, 4)
        
        return frame

    def create_delivery_section(self):
        """إنشاء قسم بيانات التسليم"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(10)
        
        # عنوان القسم
        section_title = QLabel(reshape_arabic_text("📦 بيانات التسليم"))
        section_title.setFont(create_arabic_font(13, bold=True))
        section_title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        layout.addWidget(section_title, 0, 0, 1, 4)
        
        # رسم الاشتراك الثابت
        layout.addWidget(QLabel(reshape_arabic_text("رسم الاشتراك:")), 1, 0)
        self.subscription_fee_label = QLabel("50,000 ل.س")
        self.subscription_fee_label.setFont(create_arabic_font(11, bold=True))
        self.subscription_fee_label.setStyleSheet("color: #28a745; padding: 5px;")
        layout.addWidget(self.subscription_fee_label, 1, 1)
        
        self.subscription_paid_check = QCheckBox(reshape_arabic_text("تم التسديد"))
        self.subscription_paid_check.setFont(create_arabic_font(10))
        layout.addWidget(self.subscription_paid_check, 1, 2)
        
        # نوع الراوتر
        layout.addWidget(QLabel(reshape_arabic_text("نوع الراوتر:")), 2, 0)
        self.router_combo = QComboBox()
        self.router_combo.setFont(create_arabic_font(11))
        self.router_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
        """)
        layout.addWidget(self.router_combo, 2, 1)
        
        # سعر الراوتر
        self.router_price_label = QLabel("0 ل.س")
        self.router_price_label.setFont(create_arabic_font(11, bold=True))
        self.router_price_label.setStyleSheet("color: #dc3545; padding: 5px;")
        layout.addWidget(self.router_price_label, 2, 2)
        
        self.router_paid_check = QCheckBox(reshape_arabic_text("تم التسديد"))
        self.router_paid_check.setFont(create_arabic_font(10))
        layout.addWidget(self.router_paid_check, 2, 3)
        
        # نوع الباقة
        layout.addWidget(QLabel(reshape_arabic_text("نوع الباقة:")), 3, 0)
        self.package_combo = QComboBox()
        self.package_combo.setFont(create_arabic_font(11))
        self.package_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
        """)
        layout.addWidget(self.package_combo, 3, 1, 1, 3)
        
        # عامل التركيب
        layout.addWidget(QLabel(reshape_arabic_text("عامل التركيب:")), 4, 0)
        self.worker_combo = QComboBox()
        self.worker_combo.setFont(create_arabic_font(11))
        self.worker_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
        """)
        layout.addWidget(self.worker_combo, 4, 1, 1, 3)
        
        # نوع الكبل
        layout.addWidget(QLabel(reshape_arabic_text("نوع الكبل:")), 5, 0)
        self.cable_combo = QComboBox()
        self.cable_combo.setFont(create_arabic_font(11))
        self.cable_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
        """)
        layout.addWidget(self.cable_combo, 5, 1)
        
        # عدد الأمتار
        layout.addWidget(QLabel(reshape_arabic_text("عدد الأمتار:")), 5, 2)
        self.cable_meters_spin = QSpinBox()
        self.cable_meters_spin.setRange(0, 1000)
        self.cable_meters_spin.setValue(0)
        self.cable_meters_spin.setFont(create_arabic_font(11))
        self.cable_meters_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 5px;
                background-color: white;
                min-height: 25px;
            }
        """)
        layout.addWidget(self.cable_meters_spin, 5, 3)
        
        # سعر المتر وتكلفة الكبل
        layout.addWidget(QLabel(reshape_arabic_text("سعر المتر:")), 6, 0)
        self.cable_price_label = QLabel("0 ل.س")
        self.cable_price_label.setFont(create_arabic_font(11))
        self.cable_price_label.setStyleSheet("color: #6c757d; padding: 5px;")
        layout.addWidget(self.cable_price_label, 6, 1)
        
        layout.addWidget(QLabel(reshape_arabic_text("تكلفة الكبل:")), 6, 2)
        self.cable_cost_label = QLabel("0 ل.س")
        self.cable_cost_label.setFont(create_arabic_font(11, bold=True))
        self.cable_cost_label.setStyleSheet("color: #fd7e14; padding: 5px;")
        layout.addWidget(self.cable_cost_label, 6, 3)
        
        return frame

    def create_total_section(self):
        """إنشاء قسم الإجمالي"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #28a745;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # عنوان الإجمالي
        total_title = QLabel(reshape_arabic_text("💰 الإجمالي المطلوب:"))
        total_title.setFont(create_arabic_font(14, bold=True))
        total_title.setStyleSheet("color: #155724;")
        layout.addWidget(total_title)
        
        layout.addStretch()
        
        # قيمة الإجمالي
        self.total_label = QLabel("0 ل.س")
        self.total_label.setFont(create_arabic_font(18, bold=True))
        self.total_label.setStyleSheet("""
            QLabel {
                color: #155724;
                background-color: white;
                padding: 10px 20px;
                border: 2px solid #28a745;
                border-radius: 8px;
            }
        """)
        layout.addWidget(self.total_label)
        
        return frame

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر الحفظ
        self.save_btn = QPushButton(reshape_arabic_text("💾 حفظ وتسليم"))
        self.save_btn.setFont(create_arabic_font(12, bold=True))
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        layout.addWidget(self.save_btn)
        
        # زر الطباعة
        self.print_btn = QPushButton(reshape_arabic_text("🖨️ طباعة"))
        self.print_btn.setFont(create_arabic_font(12))
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.print_btn.setEnabled(False)  # يتم تفعيله بعد الحفظ
        layout.addWidget(self.print_btn)
        
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = QPushButton(reshape_arabic_text("❌ إلغاء"))
        cancel_btn.setFont(create_arabic_font(12))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        return layout

    def load_data(self):
        """تحميل البيانات الأساسية"""
        try:
            print("🔄 تحميل البيانات...")

            # تحميل المشتركين الذين لم يستلموا راوترهم
            self.load_undelivered_subscribers()

            # تحميل أنواع الراوترات
            self.load_routers()

            # تحميل الباقات
            self.load_packages()

            # تحميل العمال
            self.load_workers()

            print("✅ تم تحميل جميع البيانات")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل البيانات: {e}"))

    def load_undelivered_subscribers(self):
        """تحميل المشتركين الذين لم يستلموا راوترهم"""
        try:
            # محاولة الاستعلام مع عمود delivered
            try:
                subscribers = self.db_manager.fetch_all("""
                    SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid,
                           s.router_type_id, s.router_type, s.delivered
                    FROM subscribers s
                    LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
                    WHERE rd.id IS NULL AND (s.delivered = 0 OR s.delivered IS NULL)
                    ORDER BY s.name
                """)
                print(f"✅ تم تحميل {len(subscribers)} مشترك غير مسلم (مع عمود delivered)")
            except:
                # استعلام احتياطي بدون عمود delivered
                subscribers = self.db_manager.fetch_all("""
                    SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid,
                           s.router_type_id, s.router_type
                    FROM subscribers s
                    LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
                    WHERE rd.id IS NULL
                    ORDER BY s.name
                """)
                print(f"✅ تم تحميل {len(subscribers)} مشترك غير مسلم (بدون عمود delivered)")

            self.subscriber_combo.clear()
            self.subscriber_combo.addItem(reshape_arabic_text("-- اختر مشتركاً --"), None)

            if subscribers:
                for subscriber in subscribers:
                    # تحويل البيانات إلى dict آمن
                    safe_data = {}
                    if hasattr(subscriber, 'keys'):
                        for key in subscriber.keys():
                            safe_data[key] = subscriber[key]
                    else:
                        safe_data = dict(subscriber)

                    name = safe_data.get('name', 'غير محدد')
                    phone = safe_data.get('phone', '')

                    display_text = name
                    if phone:
                        display_text += f" - {phone}"

                    self.subscriber_combo.addItem(display_text, safe_data)

                print(f"✅ تم تحميل {len(subscribers)} مشترك لم يستلم راوتر")
            else:
                print("⚠️ لا يوجد مشتركون لم يستلموا راوترهم")

        except Exception as e:
            print(f"❌ خطأ في تحميل المشتركين: {e}")

    def load_routers(self):
        """تحميل أنواع الراوترات من النظام الموحد"""
        try:
            if self.inventory_manager:
                # تحميل الراوترات من النظام الموحد
                routers = self.inventory_manager.get_products_by_category('راوتر')
            else:
                # الطريقة القديمة كبديل
                routers = self.db_manager.fetch_all("""
                    SELECT id, name, sale_price as unit_price, current_stock
                    FROM unified_products
                    WHERE category = 'راوتر' AND is_active = 1 AND current_stock > 0
                    ORDER BY name
                """)

            self.router_combo.clear()
            self.router_combo.addItem(reshape_arabic_text("-- اختر نوع الراوتر --"), None)

            for router in routers:
                router_data = {
                    'id': router['id'],
                    'name': router['name'],
                    'unit_price': router.get('sale_price', router.get('unit_price', 0)),
                    'current_stock': router.get('current_stock', 0)
                }
                stock_info = f" (متوفر: {router_data['current_stock']:.0f})"
                display_text = f"{router['name']} - {self.safe_format_currency(router_data['unit_price'])}{stock_info}"
                self.router_combo.addItem(display_text, router_data)

            print(f"✅ تم تحميل {len(routers)} نوع راوتر من النظام الموحد")

        except Exception as e:
            print(f"❌ خطأ في تحميل الراوترات: {e}")

    def load_packages(self):
        """تحميل الباقات"""
        try:
            packages = self.db_manager.fetch_all("""
                SELECT id, name, price FROM packages
                ORDER BY name
            """)

            self.package_combo.clear()
            self.package_combo.addItem(reshape_arabic_text("-- اختر نوع الباقة --"), None)

            for package in packages:
                package_data = {
                    'id': package['id'],
                    'name': package['name'],
                    'price': package['price']
                }
                self.package_combo.addItem(f"{package['name']} - {self.safe_format_currency(package['price'])}", package_data)

            print(f"✅ تم تحميل {len(packages)} باقة")

        except Exception as e:
            print(f"❌ خطأ في تحميل الباقات: {e}")

    def load_workers(self):
        """تحميل العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT id, name FROM workers
                WHERE is_active = 1
                ORDER BY name
            """)

            self.worker_combo.clear()
            self.worker_combo.addItem(reshape_arabic_text("-- اختر عامل التركيب --"), None)

            for worker in workers:
                worker_data = {
                    'id': worker['id'],
                    'name': worker['name']
                }
                self.worker_combo.addItem(worker['name'], worker_data)

            print(f"✅ تم تحميل {len(workers)} عامل")

        except Exception as e:
            print(f"❌ خطأ في تحميل العمال: {e}")

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        # اختيار المشترك
        self.subscriber_combo.currentIndexChanged.connect(self.on_subscriber_selected)
        # إزالة البحث التلقائي لتجنب المشاكل
        # self.subscriber_combo.lineEdit().textChanged.connect(self.search_subscribers)

        # تحديث الأسعار
        self.router_combo.currentIndexChanged.connect(self.update_router_price)
        self.package_combo.currentIndexChanged.connect(self.update_total)  # تحديث الإجمالي عند تغيير الباقة
        self.worker_combo.currentIndexChanged.connect(self.load_worker_cables)
        self.cable_combo.currentIndexChanged.connect(self.update_cable_price)
        self.cable_meters_spin.valueChanged.connect(self.calculate_cable_cost)

        # تحديث الإجمالي
        self.subscription_paid_check.toggled.connect(self.update_total)
        self.router_paid_check.toggled.connect(self.update_total)

        # أزرار التحكم
        self.save_btn.clicked.connect(self.save_delivery)
        self.print_btn.clicked.connect(self.print_delivery)

    def search_subscribers(self, text=None):
        """البحث الذكي في المشتركين"""
        try:
            search_text = (text or self.subscriber_combo.lineEdit().text()).strip().lower()

            if len(search_text) < 1:
                # إعادة تحميل القائمة الأساسية بدون استدعاء متكرر
                return

            # البحث في المشتركين
            subscribers = self.db_manager.fetch_all("""
                SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid,
                       s.router_type_id, s.router_type
                FROM subscribers s
                LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
                WHERE rd.id IS NULL AND (
                    LOWER(s.name) LIKE ? OR
                    LOWER(s.phone) LIKE ?
                )
                ORDER BY s.name
            """, (f'%{search_text}%', f'%{search_text}%'))

            self.subscriber_combo.clear()
            self.subscriber_combo.addItem(reshape_arabic_text("-- اختر مشتركاً --"), None)

            if subscribers:
                for subscriber in subscribers:
                    # تحويل البيانات إلى dict آمن
                    safe_data = {}
                    if hasattr(subscriber, 'keys'):
                        for key in subscriber.keys():
                            safe_data[key] = subscriber[key]
                    else:
                        safe_data = dict(subscriber)

                    name = safe_data.get('name', 'غير محدد')
                    phone = safe_data.get('phone', '')

                    display_text = name
                    if phone:
                        display_text += f" - {phone}"

                    self.subscriber_combo.addItem(display_text, safe_data)
            else:
                # إضافة خيار لإضافة مشترك جديد
                self.subscriber_combo.addItem(
                    reshape_arabic_text(f"➕ إضافة مشترك جديد: {search_text}"),
                    {'new_subscriber': True, 'name': search_text}
                )

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")

    def on_subscriber_selected(self, index):
        """عند اختيار مشترك"""
        try:
            if index <= 0:
                self.selected_subscriber = None
                self.subscriber_info_label.setText(reshape_arabic_text("اختر مشتركاً لعرض معلوماته"))
                return

            subscriber_data = self.subscriber_combo.currentData()
            if not subscriber_data:
                return

            # التحقق من المشترك الجديد
            if subscriber_data.get('new_subscriber'):
                self.handle_new_subscriber(subscriber_data['name'])
                return

            self.selected_subscriber = subscriber_data

            # عرض معلومات المشترك
            name = subscriber_data.get('name', 'غير محدد')
            phone = subscriber_data.get('phone', 'غير محدد')
            sub_paid = "✅ مدفوع" if subscriber_data.get('subscription_paid') else "❌ غير مدفوع"
            router_paid = "✅ مدفوع" if subscriber_data.get('router_paid') else "❌ غير مدفوع"

            info_text = f"📱 {phone} | 💰 الاشتراك: {sub_paid} | 🔌 الراوتر: {router_paid}"
            self.subscriber_info_label.setText(reshape_arabic_text(info_text))

            # تعيين حالة التسديد
            self.subscription_paid_check.setChecked(bool(subscriber_data.get('subscription_paid')))
            self.router_paid_check.setChecked(bool(subscriber_data.get('router_paid')))

            # تعيين نوع الراوتر إذا كان محفوظاً
            router_type_id = subscriber_data.get('router_type_id')
            print(f"🔍 البحث عن نوع الراوتر المحفوظ: ID = {router_type_id}")

            if router_type_id:
                found_router = False
                for i in range(self.router_combo.count()):
                    router_data = self.router_combo.itemData(i)
                    if router_data and router_data.get('id') == router_type_id:
                        self.router_combo.setCurrentIndex(i)
                        found_router = True
                        print(f"✅ تم العثور على نوع الراوتر وتعيينه: {router_data.get('name')}")
                        break

                if not found_router:
                    print(f"⚠️ لم يتم العثور على نوع الراوتر بالـ ID: {router_type_id}")
            else:
                print("ℹ️ لا يوجد نوع راوتر محفوظ لهذا المشترك")

            self.update_total()

        except Exception as e:
            print(f"❌ خطأ في اختيار المشترك: {e}")

    def handle_new_subscriber(self, name):
        """التعامل مع مشترك جديد"""
        reply = QMessageBox.question(
            self,
            reshape_arabic_text("مشترك جديد"),
            reshape_arabic_text(f"هل تريد إضافة مشترك جديد باسم: {name}؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # فتح واجهة اشتراك جديد
            try:
                from ui.new_subscription_window import NewSubscriptionWindow
                dialog = NewSubscriptionWindow(self.db_manager, self.current_user, self)
                dialog.name_edit.setText(name)  # تعبئة الاسم مسبقاً
                if dialog.exec_() == QDialog.Accepted:
                    # إعادة تحميل القائمة بدون استدعاء متكرر
                    pass
            except Exception as e:
                print(f"❌ خطأ في فتح واجهة اشتراك جديد: {e}")

    def update_router_price(self):
        """تحديث سعر الراوتر"""
        try:
            router_data = self.router_combo.currentData()
            if router_data:
                price = router_data.get('unit_price', 0)
                self.router_price_label.setText(self.safe_format_currency(price))
            else:
                self.router_price_label.setText("0 ل.س")

            self.update_total()

        except Exception as e:
            print(f"❌ خطأ في تحديث سعر الراوتر: {e}")

    def load_worker_cables(self):
        """تحميل كبلات العامل المختار"""
        try:
            worker_data = self.worker_combo.currentData()
            if not worker_data:
                self.cable_combo.clear()
                self.cable_combo.addItem(reshape_arabic_text("-- اختر عاملاً أولاً --"), None)
                self.cable_price_label.setText("0 ل.س")
                self.cable_cost_label.setText("0 ل.س")
                self.cable_meters_spin.setMaximum(0)
                return

            print(f"🔍 تحميل كبلات العامل: {worker_data['name']} (ID: {worker_data['id']})")

            # تحميل كبلات العامل من النظام الموحد
            if self.inventory_manager:
                # استخدام مدير المخزون الموحد
                worker_inventory = self.inventory_manager.get_worker_inventory(worker_data['id'])
                cables = [item for item in worker_inventory if item['category'] == 'كبل' and item['quantity'] > 0]
            else:
                # الطريقة القديمة كبديل
                cables = self.db_manager.fetch_all("""
                    SELECT wi.product_id, up.name, up.sale_price as unit_price, wi.quantity
                    FROM worker_inventory wi
                    JOIN unified_products up ON wi.product_id = up.id
                    WHERE wi.worker_id = ? AND up.category = 'كبل' AND wi.quantity > 0
                    ORDER BY up.name
                """, (worker_data['id'],))

            self.cable_combo.clear()
            self.cable_combo.addItem(reshape_arabic_text("-- اختر نوع الكبل --"), None)

            if cables:
                for cable in cables:
                    # التعامل مع البيانات من النظام الموحد أو القديم
                    cable_data = {
                        'id': cable.get('product_id', cable.get('id')),
                        'name': cable['name'],
                        'unit_price': cable.get('unit_price', 0),
                        'available_quantity': cable['quantity']
                    }

                    # تنسيق العرض
                    price_text = format_currency(cable_data['unit_price']) if cable_data['unit_price'] else "0 ل.س"
                    display_text = f"{cable['name']} - {price_text}/م (متوفر: {cable['quantity']:.1f}م)"

                    self.cable_combo.addItem(display_text, cable_data)
                    print(f"  • {cable['name']}: {cable['quantity']:.1f}م متوفر بسعر {cable_data['unit_price']} ل.س/م")

                print(f"✅ تم تحميل {len(cables)} نوع كبل للعامل {worker_data['name']}")
            else:
                print(f"⚠️ لا توجد كبلات متوفرة للعامل {worker_data['name']}")
                self.cable_combo.addItem(reshape_arabic_text("-- لا توجد كبلات متوفرة --"), None)

        except Exception as e:
            print(f"❌ خطأ في تحميل كبلات العامل: {e}")
            import traceback
            traceback.print_exc()

    def update_cable_price(self):
        """تحديث سعر المتر للكبل"""
        try:
            cable_data = self.cable_combo.currentData()
            if cable_data:
                price = cable_data.get('unit_price', 0)
                self.cable_price_label.setText(format_currency(price))

                # تحديث الحد الأقصى للأمتار
                max_quantity = cable_data.get('available_quantity', 0)
                self.cable_meters_spin.setMaximum(max_quantity)
            else:
                self.cable_price_label.setText("0 ل.س")
                self.cable_meters_spin.setMaximum(1000)

            self.calculate_cable_cost()

        except Exception as e:
            print(f"❌ خطأ في تحديث سعر الكبل: {e}")

    def calculate_cable_cost(self):
        """حساب تكلفة الكبل"""
        try:
            cable_data = self.cable_combo.currentData()
            meters = self.cable_meters_spin.value()

            if cable_data and meters > 0:
                unit_price = cable_data.get('unit_price', 0)
                total_cost = unit_price * meters
                self.cable_cost_label.setText(format_currency(total_cost))
            else:
                self.cable_cost_label.setText("0 ل.س")

            self.update_total()

        except Exception as e:
            print(f"❌ خطأ في حساب تكلفة الكبل: {e}")

    def update_total(self):
        """تحديث الإجمالي"""
        try:
            total = 0

            # رسم الاشتراك (إذا لم يكن مدفوعاً)
            if not self.subscription_paid_check.isChecked():
                total += 50000  # رسم الاشتراك الثابت
                print("💰 إضافة رسم الاشتراك: 50,000 ل.س")

            # سعر الراوتر (إذا لم يكن مدفوعاً)
            if not self.router_paid_check.isChecked():
                router_data = self.router_combo.currentData()
                if router_data:
                    router_price = router_data.get('unit_price', 0)
                    total += router_price
                    print(f"🔌 إضافة سعر الراوتر: {format_currency(router_price)}")

            # سعر الباقة
            package_data = self.package_combo.currentData()
            if package_data:
                package_price = package_data.get('price', 0)
                total += package_price
                print(f"📦 إضافة سعر الباقة: {format_currency(package_price)}")

            # تكلفة الكبل
            cable_data = self.cable_combo.currentData()
            meters = self.cable_meters_spin.value()
            if cable_data and meters > 0:
                unit_price = cable_data.get('unit_price', 0)
                cable_cost = unit_price * meters
                total += cable_cost
                print(f"🔗 إضافة تكلفة الكبل: {format_currency(cable_cost)} ({meters}م × {format_currency(unit_price)})")

            try:
                formatted_total = format_currency(total)
                print(f"💵 الإجمالي النهائي: {formatted_total}")
                self.total_label.setText(formatted_total)
            except Exception as format_error:
                print(f"⚠️ خطأ في تنسيق العملة: {format_error}")
                self.total_label.setText(f"{total:,} ل.س")

        except Exception as e:
            print(f"❌ خطأ في تحديث الإجمالي: {e}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        try:
            # التحقق من المشترك
            if not hasattr(self, 'selected_subscriber') or not self.selected_subscriber:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى اختيار مشترك"))
                return False

            # التحقق من الراوتر
            if self.router_combo.currentIndex() <= 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى اختيار نوع الراوتر"))
                return False

            # التحقق من العامل
            if self.worker_combo.currentIndex() <= 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى اختيار عامل التركيب"))
                return False

            # التحقق من الكبل (اختياري)
            if self.cable_combo.currentIndex() > 0:
                cable_meters = self.cable_meters_spin.value()
                if cable_meters <= 0:
                    QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                      reshape_arabic_text("يرجى تحديد عدد أمتار الكبل"))
                    return False

            print("✅ تم التحقق من البيانات بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في التحقق من البيانات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في التحقق من البيانات: {e}"))
            return False

        # التحقق من الكبل إذا تم تحديد أمتار
        if self.cable_meters_spin.value() > 0:
            if self.cable_combo.currentIndex() <= 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى اختيار نوع الكبل"))
                return False

            # التحقق من توفر الكمية
            cable_data = self.cable_combo.currentData()
            if cable_data:
                available = cable_data.get('available_quantity', 0)
                requested = self.cable_meters_spin.value()
                if requested > available:
                    QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                      reshape_arabic_text(f"الكمية المطلوبة ({requested}م) أكبر من المتوفر ({available}م)"))
                    return False

        return True

    def save_delivery(self):
        """حفظ تسليم الراوتر مع النظام الموحد"""
        print("🚀 بدء حفظ تسليم الراوتر...")

        try:
            # التحقق من البيانات
            print("🔍 التحقق من البيانات...")
            if not self.validate_data():
                print("❌ فشل في التحقق من البيانات")
                return

            print("✅ تم التحقق من البيانات بنجاح")

            # جمع البيانات
            print("📊 جمع البيانات...")
            subscriber_name = self.selected_subscriber.get('name', '') if self.selected_subscriber else ''
            router_data = self.router_combo.currentData()
            cable_data = self.cable_combo.currentData()
            worker_data = self.worker_combo.currentData()
            package_data = self.package_combo.currentData()

            cable_meters = self.cable_meters_spin.value()

            print("💰 حساب المبلغ الإجمالي...")
            total_amount = self.calculate_total_amount()

            print(f"📋 بيانات التسليم:")
            print(f"  • المشترك: {subscriber_name}")
            print(f"  • الراوتر: {router_data['name'] if router_data else 'غير محدد'}")
            print(f"  • الكبل: {cable_data['name'] if cable_data else 'غير محدد'} - {cable_meters}م")
            print(f"  • العامل: {worker_data['name'] if worker_data else 'غير محدد'}")
            print(f"  • الإجمالي: {total_amount:,} ل.س")

            # حفظ في قاعدة البيانات
            delivery_id = self.save_delivery_record(subscriber_name, router_data, cable_data,
                                                  worker_data, package_data, cable_meters, total_amount)

            if delivery_id:
                # تحديث المخزون
                inventory_success = self.update_inventory(router_data, cable_data, worker_data,
                                                        cable_meters, delivery_id)

                if inventory_success:
                    # تحديث الخزينة
                    treasury_success = self.update_treasury(total_amount)

                    if treasury_success:
                        from PyQt5.QtWidgets import QMessageBox
                        QMessageBox.information(self, "نجح",
                                              f"تم حفظ تسليم الراوتر بنجاح!\n"
                                              f"المشترك: {subscriber_name}\n"
                                              f"الإجمالي: {total_amount:,} ل.س")

                        # إعادة تحميل البيانات
                        self.load_data()

                        print("🎉 تم حفظ تسليم الراوتر بنجاح!")
                        return True

            return False

        except Exception as e:
            print(f"❌ خطأ في حفظ تسليم الراوتر: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التسليم: {e}")
            return False

    def save_delivery_record(self, subscriber_name, router_data, cable_data, worker_data,
                           package_data, cable_meters, total_amount):
        """حفظ سجل التسليم في قاعدة البيانات"""
        try:
            result = self.db_manager.execute_query("""
                INSERT INTO router_deliveries
                (subscriber_name, router_id, router_name, router_price,
                 cable_id, cable_name, cable_price_per_meter, cable_meters, cable_cost,
                 worker_id, worker_name, package_id, package_name, package_price,
                 total_amount, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                subscriber_name,
                router_data['id'] if router_data else None,
                router_data['name'] if router_data else '',
                router_data['unit_price'] if router_data else 0,
                cable_data['id'] if cable_data else None,
                cable_data['name'] if cable_data else '',
                cable_data['unit_price'] if cable_data else 0,
                cable_meters,
                cable_meters * (cable_data['unit_price'] if cable_data else 0),
                worker_data['id'] if worker_data else None,
                worker_data['name'] if worker_data else '',
                package_data['id'] if package_data else None,
                package_data['name'] if package_data else '',
                package_data['price'] if package_data else 0,
                total_amount,
                self.current_user['id']
            ))

            if result:
                delivery_id = result.lastrowid
                print(f"✅ تم حفظ سجل التسليم - ID: {delivery_id}")

                # تحديث حالة التسليم للمشترك
                self.update_subscriber_delivery_status(subscriber_name)

                # إضافة سجل في transactions للتوافق مع إغلاق الصندوق
                self.add_transaction_record(subscriber_name, total_amount)

                return delivery_id

            return None

        except Exception as e:
            print(f"❌ خطأ في حفظ سجل التسليم: {e}")
            return None

    def update_subscriber_delivery_status(self, subscriber_name):
        """تحديث حالة التسليم للمشترك"""
        try:
            # تحديث حالة التسليم إلى 1 (مسلم)
            result = self.db_manager.execute_query("""
                UPDATE subscribers
                SET delivered = 1
                WHERE name = ?
            """, (subscriber_name,))

            if result:
                print(f"✅ تم تحديث حالة التسليم للمشترك: {subscriber_name}")
                return True
            else:
                print(f"⚠️ لم يتم تحديث حالة التسليم للمشترك: {subscriber_name}")
                return False

        except Exception as e:
            print(f"❌ خطأ في تحديث حالة التسليم: {e}")
            return False

    def add_transaction_record(self, subscriber_name, total_amount):
        """إضافة سجل في transactions للتوافق مع إغلاق الصندوق"""
        try:
            # إضافة سجل في جدول transactions
            result = self.db_manager.execute_query("""
                INSERT INTO transactions
                (type, amount, description, user_name, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                'تسليم راوتر',
                total_amount,
                f'تسليم راوتر للمشترك: {subscriber_name}',
                self.current_user['username']
            ))

            if result:
                print(f"✅ تم إضافة سجل المعاملة - تسليم راوتر: {total_amount:,} ل.س")
                return True
            else:
                print(f"⚠️ لم يتم إضافة سجل المعاملة")
                return False

        except Exception as e:
            print(f"❌ خطأ في إضافة سجل المعاملة: {e}")
            return False

    def update_inventory(self, router_data, cable_data, worker_data, cable_meters, delivery_id):
        """تحديث المخزون - خصم الراوتر من المخزون الرئيسي والكبل من مخزون العامل"""
        try:
            if not self.inventory_manager:
                print("⚠️ مدير المخزون غير متوفر")
                return False

            # خصم الراوتر من المخزون الرئيسي
            if router_data:
                router_success = self.inventory_manager.remove_stock(
                    product_id=router_data['id'],
                    quantity=1,
                    operation_type="router_delivery",
                    reference_id=delivery_id,
                    notes=f"تسليم راوتر للمشترك - {self.selected_subscriber.get('name', '')}",
                    user_id=self.current_user['id'],
                    to_location="customer"
                )

                if not router_success:
                    print("❌ فشل في خصم الراوتر من المخزون")
                    return False

                print(f"✅ تم خصم راوتر {router_data['name']} من المخزون الرئيسي")

            # خصم الكبل من مخزون العامل
            if cable_data and worker_data and cable_meters > 0:
                cable_success = self.inventory_manager.remove_from_worker(
                    product_id=cable_data['id'],
                    worker_id=worker_data['id'],
                    quantity=cable_meters,
                    operation_type="router_delivery",
                    reference_id=delivery_id,
                    notes=f"تسليم كبل للمشترك - {self.selected_subscriber.get('name', '')}",
                    user_id=self.current_user['id']
                )

                if not cable_success:
                    print("❌ فشل في خصم الكبل من مخزون العامل")
                    return False

                print(f"✅ تم خصم {cable_meters}م من {cable_data['name']} من مخزون العامل {worker_data['name']}")

            return True

        except Exception as e:
            print(f"❌ خطأ في تحديث المخزون: {e}")
            return False

    def update_treasury(self, total_amount):
        """تحديث الخزينة"""
        try:
            if hasattr(self, 'treasury_manager') and self.treasury_manager:
                success = self.treasury_manager.add_to_daily_treasury(
                    user_id=self.current_user['id'],
                    currency_type='SYP',
                    amount=total_amount
                )

                if success:
                    print(f"✅ تم إضافة {total_amount:,} ل.س للخزينة")
                    return True
                else:
                    print("❌ فشل في تحديث الخزينة")
                    return False
            else:
                print("⚠️ مدير الخزينة غير متوفر")
                return True  # نعتبرها نجحت لعدم إيقاف العملية

        except Exception as e:
            print(f"❌ خطأ في تحديث الخزينة: {e}")
            return False

    def calculate_total_amount(self):
        """حساب المبلغ الإجمالي"""
        total = 0

        try:
            # رسم الاشتراك
            if not self.subscription_paid_check.isChecked():
                total += 50000  # رسم الاشتراك الثابت
                print(f"💰 رسم الاشتراك: 50,000 ل.س")

            # سعر الراوتر
            if not self.router_paid_check.isChecked():
                router_data = self.router_combo.currentData()
                if router_data:
                    # استخدام sale_price أو unit_price
                    router_price = router_data.get('sale_price', router_data.get('unit_price', 0))
                    total += router_price
                    print(f"💰 سعر الراوتر: {router_price:,} ل.س")

            # سعر الباقة (اختياري)
            package_data = self.package_combo.currentData()
            if package_data and package_data.get('price', 0) > 0:
                package_price = package_data.get('price', 0)
                total += package_price
                print(f"💰 سعر الباقة: {package_price:,} ل.س")

            # تكلفة الكبل
            cable_data = self.cable_combo.currentData()
            if cable_data:
                meters = self.cable_meters_spin.value()
                # استخدام sale_price أو unit_price
                cable_price_per_meter = cable_data.get('sale_price', cable_data.get('unit_price', 0))
                cable_cost = meters * cable_price_per_meter
                total += cable_cost
                print(f"💰 تكلفة الكبل: {meters}م × {cable_price_per_meter:,} = {cable_cost:,} ل.س")

            print(f"💵 الإجمالي النهائي: {total:,} ل.س")
            return total

        except Exception as e:
            print(f"❌ خطأ في حساب المبلغ الإجمالي: {e}")
            return 0

    def print_delivery(self):
        """طباعة فاتورة التسليم"""
        try:
            # هنا يمكن إضافة كود الطباعة
            QMessageBox.information(self, reshape_arabic_text("طباعة"),
                                  reshape_arabic_text("سيتم إضافة وظيفة الطباعة قريباً"))
        except Exception as e:
            print(f"❌ خطأ في الطباعة: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في الطباعة: {e}"))

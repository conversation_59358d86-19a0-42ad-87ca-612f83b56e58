#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة شركة الإنترنت
Internet Company Management System
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "src"))

# تجاهل تحذيرات Qt
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت")
    print("=" * 50)
    
    try:
        # استيراد المكتبات
        from PyQt5.QtWidgets import QApplication, QMessageBox
        
        # استيراد وحدات النظام
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from utils.arabic_support import setup_arabic_support
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        
        print("✅ تم تحميل جميع الوحدات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        
        # إنشاء مجلد البيانات
        data_dir = current_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # نافذة تسجيل الدخول
        print("🔐 عرض نافذة تسجيل الدخول...")
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() != login_window.Accepted:
            print("تم إلغاء تسجيل الدخول")
            return 0
        
        current_user = login_window.get_current_user()
        print(f"✅ مرحباً {current_user['full_name']}")
        
        # النافذة الرئيسية
        print("🏠 عرض النافذة الرئيسية...")
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        print("📋 بيانات الدخول: admin / admin123")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

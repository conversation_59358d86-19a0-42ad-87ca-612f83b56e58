#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقل الصناديق المغلقة للنظام الموحد
"""

import sys
import os
sys.path.append('src')

def migrate_all_closed_boxes():
    """نقل جميع الصناديق المغلقة للنظام الموحد"""
    
    print("🔄 نقل الصناديق المغلقة للنظام الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص الصناديق المغلقة
        closed_boxes = db.fetch_all("""
            SELECT id, user_id, net_amount, shift_date, closed_at, total_sales, total_expenses
            FROM cash_boxes 
            WHERE is_closed = 1
            ORDER BY closed_at
        """)
        
        print(f"📦 تم العثور على {len(closed_boxes)} صندوق مغلق")
        
        total_migrated = 0
        successful_migrations = 0
        
        for box in closed_boxes:
            try:
                user_id = box['user_id']
                net_amount = box.get('net_amount', 0) or 0
                shift_date = box.get('shift_date')
                
                # استخدام تاريخ الإغلاق إذا لم يكن هناك تاريخ شيفت
                if not shift_date and box.get('closed_at'):
                    shift_date = box['closed_at'].split()[0]
                elif not shift_date:
                    shift_date = '2025-01-20'  # تاريخ افتراضي
                
                print(f"  📦 صندوق {box['id']}: مستخدم {user_id}, صافي: {net_amount:,} ل.س, تاريخ: {shift_date}")
                
                if net_amount > 0:
                    # إضافة المبلغ للخزينة اليومية
                    success = treasury_manager.add_to_daily_treasury(
                        user_id=user_id,
                        currency_type='SYP',
                        amount=net_amount,
                        date=shift_date
                    )
                    
                    if success:
                        total_migrated += net_amount
                        successful_migrations += 1
                        print(f"    ✅ تم نقل {net_amount:,} ل.س للنظام الموحد")
                    else:
                        print(f"    ❌ فشل في النقل")
                elif net_amount < 0:
                    print(f"    ⚠️ صافي سالب - تم تجاهله")
                else:
                    print(f"    ⚠️ صافي صفر - تم تجاهله")
                        
            except Exception as box_error:
                print(f"    ❌ خطأ في نقل صندوق {box['id']}: {box_error}")
        
        print(f"\n📊 نتائج النقل:")
        print(f"  • تم نقل {successful_migrations} صندوق بنجاح")
        print(f"  • إجمالي المبلغ المنقول: {total_migrated:,} ل.س")
        
        # فحص الأرصدة بعد النقل
        current_user = {'id': 1}
        daily_syp = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_syp = treasury_manager.get_main_balance('SYP')
        
        print(f"\n💰 الأرصدة بعد النقل:")
        print(f"  • الخزينة اليومية للمستخدم 1: {daily_syp:,} ل.س")
        print(f"  • الخزينة الرئيسية: {main_syp:,} ل.س")
        
        if daily_syp > 0:
            print("\n🎉 تم النقل بنجاح!")
            print("✅ الآن يمكن استخدام واجهة شراء الدولار")
            print("✅ الآن يمكن استخدام واجهة نقل الخزينة")
            return True
        else:
            print("\n⚠️ لا يزال الرصيد صفر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في نقل الصناديق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interfaces_after_migration():
    """اختبار الواجهات بعد النقل"""
    
    print("\n🧪 اختبار الواجهات بعد النقل...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # اختبار واجهة شراء الدولار
        print("\n💵 اختبار واجهة شراء الدولار:")
        syp_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        usd_balance = treasury_manager.get_daily_balance(current_user['id'], 'USD')
        
        print(f"  • رصيد الليرة السورية: {syp_balance:,} ل.س")
        print(f"  • رصيد الدولار: ${usd_balance:.2f}")
        
        if syp_balance > 0:
            print("  ✅ واجهة شراء الدولار ستعمل بشكل طبيعي")
        else:
            print("  ❌ واجهة شراء الدولار لا تزال تظهر رصيد صفر")
        
        # اختبار واجهة نقل الخزينة
        print("\n🔄 اختبار واجهة نقل الخزينة:")
        main_syp = treasury_manager.get_main_balance('SYP')
        main_usd = treasury_manager.get_main_balance('USD')
        
        print(f"  • الخزينة اليومية - ليرة: {syp_balance:,} ل.س")
        print(f"  • الخزينة الرئيسية - ليرة: {main_syp:,} ل.س")
        print(f"  • الخزينة الرئيسية - دولار: ${main_usd:.2f}")
        
        if syp_balance > 0:
            print("  ✅ واجهة نقل الخزينة ستعمل بشكل طبيعي")
        else:
            print("  ❌ واجهة نقل الخزينة لا تزال تظهر رصيد صفر")
        
        return syp_balance > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مشكلة الأرصدة الصفرية")
    print("=" * 50)
    
    # نقل الصناديق المغلقة
    migration_success = migrate_all_closed_boxes()
    
    if migration_success:
        # اختبار الواجهات
        test_success = test_interfaces_after_migration()
        
        if test_success:
            print("\n🎉 تم إصلاح المشكلة بالكامل!")
            print("\n📋 ما تم إنجازه:")
            print("  ✅ نقل جميع الصناديق المغلقة للنظام الموحد")
            print("  ✅ إضافة الأرصدة للخزينة اليومية")
            print("  ✅ واجهة شراء الدولار تعمل الآن")
            print("  ✅ واجهة نقل الخزينة تعمل الآن")
            
            print("\n🚀 للاستخدام:")
            print("  1. افتح واجهة شراء الدولار")
            print("  2. ستجد الرصيد متوفر للتحويل")
            print("  3. افتح واجهة نقل الخزينة")
            print("  4. ستجد الرصيد متوفر للنقل")
        else:
            print("\n⚠️ تم النقل لكن هناك مشكلة في الاختبار")
    else:
        print("\n❌ فشل في نقل الصناديق المغلقة")

if __name__ == "__main__":
    main()

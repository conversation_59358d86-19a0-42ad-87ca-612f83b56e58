# 📖 دليل إضافة الواجهات الجديدة

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية إضافة واجهات جديدة للتطبيق بسهولة ودون الحاجة لفهم عميق في البرمجة.

## 🚀 الخطوات السريعة

### 1. نسخ القالب
```bash
# انسخ ملف القالب
cp templates/interface_template.py ui/[category]/your_interface.py
```

### 2. تعديل الكود
- غير اسم الفئة من `TemplateWindow` إلى اسم واجهتك
- عدل العنوان والحقول حسب احتياجاتك

### 3. تسجيل الواجهة
أضف الواجهة في ملف `config/app_config.py`

### 4. إعادة التشغيل
أعد تشغيل التطبيق لرؤية الواجهة الجديدة

## 📋 خطوات مفصلة

### الخطوة 1: اختيار الفئة المناسبة

الفئات المتاحة:
- `financial`: الإدارة المالية
- `subscribers`: إدارة المشتركين  
- `inventory`: إدارة المخزون
- `reports`: التقارير والإحصائيات
- `system`: إدارة النظام

### الخطوة 2: إنشاء ملف الواجهة

```python
# مثال: ui/financial/my_financial_window.py

from templates.interface_template import TemplateWindow

class MyFinancialWindow(TemplateWindow):
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(db_manager, current_user, parent)
        
        # تخصيص الواجهة
        self.setWindowTitle("واجهتي المالية الجديدة")
```

### الخطوة 3: تسجيل الواجهة في الإعدادات

```python
# في ملف config/app_config.py
AVAILABLE_INTERFACES = {
    'financial': {
        'name': 'الإدارة المالية',
        'icon': 'money.png',
        'color': '#2ecc71',
        'modules': [
            # الواجهات الموجودة...
            {'name': 'واجهتي الجديدة', 'class': 'MyFinancialWindow'},  # أضف هذا السطر
        ]
    },
    # باقي الفئات...
}
```

## 🎨 تخصيص الواجهة

### تغيير التخطيط

```python
def create_content_section(self):
    # للنموذج البسيط
    return self.create_simple_form()
    
    # للتبويبات
    return self.create_tabbed_content()
    
    # للجدول مع نموذج
    return self.create_table_with_form()
```

### إضافة حقول جديدة

```python
def create_basic_data_tab(self):
    widget = QWidget()
    layout = QFormLayout(widget)
    
    # حقل نص
    self.my_field = QLineEdit()
    layout.addRow("حقلي الجديد:", self.my_field)
    
    # قائمة منسدلة
    self.my_combo = QComboBox()
    self.my_combo.addItems(["خيار 1", "خيار 2"])
    layout.addRow("قائمتي:", self.my_combo)
    
    # حقل رقمي
    self.my_number = QSpinBox()
    self.my_number.setRange(0, 1000)
    layout.addRow("رقمي:", self.my_number)
    
    return widget
```

### تخصيص قاعدة البيانات

```python
def save_data(self):
    try:
        # جمع البيانات
        data = {
            'field1': self.my_field.text(),
            'field2': self.my_combo.currentText(),
            'field3': self.my_number.value()
        }
        
        # حفظ في قاعدة البيانات
        self.db_manager.execute_query("""
            INSERT INTO my_table (field1, field2, field3, created_by, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, (data['field1'], data['field2'], data['field3'], 
              self.current_user['id'], datetime.now()))
        
        QMessageBox.information(self, "نجح", "تم الحفظ بنجاح")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"خطأ في الحفظ: {e}")
```

## 🗃️ إنشاء جداول قاعدة البيانات

### إضافة جدول جديد

```python
# في ملف database/database_manager.py
# أضف في دالة create_tables()

self.execute_query("""
    CREATE TABLE IF NOT EXISTS my_new_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        amount REAL DEFAULT 0,
        created_by INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users (id)
    )
""")
```

## 🎨 تخصيص التصميم

### تغيير الألوان

```python
def create_header_section(self):
    frame = QFrame()
    frame.setStyleSheet("""
        QFrame {
            background-color: #e74c3c;  /* أحمر */
            color: white;
            border-radius: 8px;
            padding: 15px;
        }
    """)
    # باقي الكود...
```

### إضافة أيقونات

```python
# في قسم الهيدر
icon_label = QLabel("💰")  # أيقونة المال
icon_label = QLabel("👥")  # أيقونة المستخدمين
icon_label = QLabel("📦")  # أيقونة المخزون
icon_label = QLabel("📊")  # أيقونة التقارير
icon_label = QLabel("⚙️")   # أيقونة الإعدادات
```

## 📊 أمثلة عملية

### مثال 1: واجهة إدارة العملاء

```python
class CustomersWindow(TemplateWindow):
    def setup_ui(self):
        super().setup_ui()
        self.setWindowTitle("إدارة العملاء")
    
    def create_basic_data_tab(self):
        widget = QWidget()
        layout = QFormLayout(widget)
        
        self.customer_name = QLineEdit()
        layout.addRow("اسم العميل:", self.customer_name)
        
        self.customer_phone = QLineEdit()
        layout.addRow("الهاتف:", self.customer_phone)
        
        self.customer_address = QTextEdit()
        layout.addRow("العنوان:", self.customer_address)
        
        return widget
```

### مثال 2: واجهة التقارير المالية

```python
class FinancialReportsWindow(TemplateWindow):
    def create_content_section(self):
        return self.create_table_with_form()
    
    def load_initial_data(self):
        # تحميل التقارير من قاعدة البيانات
        reports = self.db_manager.fetch_all("""
            SELECT * FROM financial_reports 
            ORDER BY created_at DESC
        """)
        
        # ملء الجدول
        self.populate_table(reports)
```

## 🔧 نصائح متقدمة

### 1. إضافة التحقق من البيانات

```python
def validate_data(self):
    if not self.name_edit.text().strip():
        QMessageBox.warning(self, "تحذير", "الاسم مطلوب")
        return False
    
    if len(self.phone_edit.text()) < 10:
        QMessageBox.warning(self, "تحذير", "رقم الهاتف قصير")
        return False
    
    return True
```

### 2. إضافة البحث والفلترة

```python
def create_search_section(self):
    search_layout = QHBoxLayout()
    
    self.search_edit = QLineEdit()
    self.search_edit.setPlaceholderText("البحث...")
    self.search_edit.textChanged.connect(self.filter_data)
    
    search_button = QPushButton("بحث")
    search_button.clicked.connect(self.search_data)
    
    search_layout.addWidget(self.search_edit)
    search_layout.addWidget(search_button)
    
    return search_layout
```

### 3. إضافة التصدير والطباعة

```python
def create_buttons_section(self):
    frame = super().create_buttons_section()
    layout = frame.layout()
    
    # زر التصدير
    export_button = QPushButton("تصدير Excel")
    export_button.clicked.connect(self.export_to_excel)
    layout.insertWidget(0, export_button)
    
    # زر الطباعة
    print_button = QPushButton("طباعة")
    print_button.clicked.connect(self.print_data)
    layout.insertWidget(1, print_button)
    
    return frame
```

## 🚨 أخطاء شائعة وحلولها

### خطأ: الواجهة لا تظهر
**الحل:** تأكد من إضافة الواجهة في `app_config.py`

### خطأ: لا يمكن العثور على الوحدة
**الحل:** تأكد من وضع الملف في المجلد الصحيح

### خطأ: قاعدة البيانات
**الحل:** تأكد من إنشاء الجداول المطلوبة

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. راجع ملف `templates/interface_template.py`
2. انظر إلى الواجهات الموجودة كأمثلة
3. تحقق من ملفات السجلات في مجلد `logs/`
4. اتصل بفريق الدعم التقني

## 🎉 مبروك!

الآن يمكنك إضافة واجهات جديدة بسهولة ودون الحاجة لخبرة برمجية عميقة!

---

**تم إعداد هذا الدليل بواسطة فريق تطوير أنظمة الإنترنت** 🚀

# -*- coding: utf-8 -*-
"""
نافذة إدارة المستخدمين والصلاحيات
Users and Permissions Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton,
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
import hashlib

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class UsersManagementWindow(QDialog):
    """نافذة إدارة المستخدمين والصلاحيات"""

    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        self.setup_ui()
        self.load_data()
        self.setup_connections()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المستخدمين والصلاحيات")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)

        apply_arabic_style(self, 10)

        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("إدارة المستخدمين والصلاحيات")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)

        # تبويبات
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)

        # تبويب المستخدمين
        users_tab = self.create_users_tab()
        self.tabs.addTab(users_tab, "إدارة المستخدمين")

        # تبويب الصلاحيات
        permissions_tab = self.create_permissions_tab()
        self.tabs.addTab(permissions_tab, "إدارة الصلاحيات")

        # أزرار التحكم
        buttons_layout = self.create_buttons()

        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # أدوات التحكم
        controls_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)

        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث بالاسم أو اسم المستخدم...")

        # فلتر الدور
        role_label = QLabel("الدور:")
        apply_arabic_style(role_label, 10)

        self.role_filter_combo = QComboBox()
        apply_arabic_style(self.role_filter_combo, 10)
        self.role_filter_combo.addItem("جميع الأدوار", "")
        self.role_filter_combo.addItem("مدير عام", "admin")
        self.role_filter_combo.addItem("مدير", "manager")
        self.role_filter_combo.addItem("موظف", "employee")
        self.role_filter_combo.addItem("محاسب", "accountant")

        # زر البحث
        search_button = QPushButton("بحث")
        apply_arabic_style(search_button, 10)
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        controls_layout.addWidget(search_label)
        controls_layout.addWidget(self.search_edit)
        controls_layout.addWidget(role_label)
        controls_layout.addWidget(self.role_filter_combo)
        controls_layout.addWidget(search_button)
        controls_layout.addStretch()

        # جدول المستخدمين
        self.users_table = self.create_users_table()

        # أزرار إدارة المستخدمين
        users_buttons_layout = self.create_users_buttons()

        layout.addLayout(controls_layout)
        layout.addWidget(self.users_table)
        layout.addLayout(users_buttons_layout)

        # ربط الأحداث
        search_button.clicked.connect(self.search_users)
        self.role_filter_combo.currentTextChanged.connect(self.filter_users)
        self.search_edit.returnPressed.connect(self.search_users)

        return widget

    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        table = QTableWidget()
        apply_arabic_style(table, 9)

        columns = ["الرقم", "اسم المستخدم", "الاسم الكامل", "الدور", "البريد الإلكتروني", "الحالة", "آخر دخول", "تاريخ الإنشاء"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)

        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)

        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)

        # تنسيق الأعمدة
        table.setColumnWidth(0, 60)   # الرقم
        table.setColumnWidth(1, 120)  # اسم المستخدم
        table.setColumnWidth(2, 150)  # الاسم الكامل
        table.setColumnWidth(3, 100)  # الدور
        table.setColumnWidth(4, 180)  # البريد الإلكتروني
        table.setColumnWidth(5, 80)   # الحالة
        table.setColumnWidth(6, 120)  # آخر دخول

        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        return table

    def create_users_buttons(self):
        """إنشاء أزرار إدارة المستخدمين"""
        layout = QHBoxLayout()

        add_user_button = QPushButton("إضافة مستخدم جديد")
        apply_arabic_style(add_user_button, 10, bold=True)
        add_user_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        edit_user_button = QPushButton("تعديل المستخدم")
        apply_arabic_style(edit_user_button, 10)
        edit_user_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        delete_user_button = QPushButton("حذف المستخدم")
        apply_arabic_style(delete_user_button, 10)
        delete_user_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        reset_password_button = QPushButton("إعادة تعيين كلمة المرور")
        apply_arabic_style(reset_password_button, 10)
        reset_password_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)

        layout.addWidget(add_user_button)
        layout.addWidget(edit_user_button)
        layout.addWidget(delete_user_button)
        layout.addWidget(reset_password_button)
        layout.addStretch()

        # ربط الأحداث
        add_user_button.clicked.connect(self.add_user)
        edit_user_button.clicked.connect(self.edit_user)
        delete_user_button.clicked.connect(self.delete_user)
        reset_password_button.clicked.connect(self.reset_password)

        return layout

    def create_permissions_tab(self):
        """إنشاء تبويب الصلاحيات"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # اختيار المستخدم
        user_selection_layout = QHBoxLayout()

        user_label = QLabel("اختر المستخدم:")
        apply_arabic_style(user_label, 10, bold=True)

        self.permissions_user_combo = QComboBox()
        apply_arabic_style(self.permissions_user_combo, 10)

        user_selection_layout.addWidget(user_label)
        user_selection_layout.addWidget(self.permissions_user_combo)
        user_selection_layout.addStretch()

        # مجموعات الصلاحيات
        permissions_groups_layout = QGridLayout()

        # صلاحيات العمليات
        operations_group = QGroupBox("صلاحيات العمليات")
        apply_arabic_style(operations_group, 10, bold=True)
        operations_layout = QVBoxLayout(operations_group)

        self.can_add_subscription = QCheckBox("إضافة اشتراك جديد")
        self.can_deliver_router = QCheckBox("تسليم راوتر")
        self.can_renew_package = QCheckBox("تجديد باقة")
        self.can_close_cash = QCheckBox("إغلاق الصندوق")

        apply_arabic_style(self.can_add_subscription, 9)
        apply_arabic_style(self.can_deliver_router, 9)
        apply_arabic_style(self.can_renew_package, 9)
        apply_arabic_style(self.can_close_cash, 9)

        operations_layout.addWidget(self.can_add_subscription)
        operations_layout.addWidget(self.can_deliver_router)
        operations_layout.addWidget(self.can_renew_package)
        operations_layout.addWidget(self.can_close_cash)

        # صلاحيات الإدارة
        management_group = QGroupBox("صلاحيات الإدارة")
        apply_arabic_style(management_group, 10, bold=True)
        management_layout = QVBoxLayout(management_group)

        self.can_manage_subscribers = QCheckBox("إدارة المشتركين")
        self.can_manage_products = QCheckBox("إدارة المنتجات")
        self.can_manage_workers = QCheckBox("إدارة العمال")
        self.can_manage_suppliers = QCheckBox("إدارة الموردين")
        self.can_manage_users = QCheckBox("إدارة المستخدمين")

        apply_arabic_style(self.can_manage_subscribers, 9)
        apply_arabic_style(self.can_manage_products, 9)
        apply_arabic_style(self.can_manage_workers, 9)
        apply_arabic_style(self.can_manage_suppliers, 9)
        apply_arabic_style(self.can_manage_users, 9)

        management_layout.addWidget(self.can_manage_subscribers)
        management_layout.addWidget(self.can_manage_products)
        management_layout.addWidget(self.can_manage_workers)
        management_layout.addWidget(self.can_manage_suppliers)
        management_layout.addWidget(self.can_manage_users)

        # صلاحيات المالية
        financial_group = QGroupBox("الصلاحيات المالية")
        apply_arabic_style(financial_group, 10, bold=True)
        financial_layout = QVBoxLayout(financial_group)

        self.can_make_purchases = QCheckBox("المشتريات من الموردين")
        self.can_charge_balance = QCheckBox("شحن رصيد الموزعين")
        self.can_view_reports = QCheckBox("عرض التقارير")
        self.can_edit_settings = QCheckBox("تعديل الإعدادات")

        apply_arabic_style(self.can_make_purchases, 9)
        apply_arabic_style(self.can_charge_balance, 9)
        apply_arabic_style(self.can_view_reports, 9)
        apply_arabic_style(self.can_edit_settings, 9)

        financial_layout.addWidget(self.can_make_purchases)
        financial_layout.addWidget(self.can_charge_balance)
        financial_layout.addWidget(self.can_view_reports)
        financial_layout.addWidget(self.can_edit_settings)

        permissions_groups_layout.addWidget(operations_group, 0, 0)
        permissions_groups_layout.addWidget(management_group, 0, 1)
        permissions_groups_layout.addWidget(financial_group, 1, 0, 1, 2)

        # أزرار حفظ الصلاحيات
        permissions_buttons_layout = QHBoxLayout()

        save_permissions_button = QPushButton("حفظ الصلاحيات")
        apply_arabic_style(save_permissions_button, 10, bold=True)
        save_permissions_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        reset_permissions_button = QPushButton("إعادة تعيين")
        apply_arabic_style(reset_permissions_button, 10)
        reset_permissions_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        permissions_buttons_layout.addStretch()
        permissions_buttons_layout.addWidget(reset_permissions_button)
        permissions_buttons_layout.addWidget(save_permissions_button)

        layout.addLayout(user_selection_layout)
        layout.addLayout(permissions_groups_layout)
        layout.addLayout(permissions_buttons_layout)

        # ربط الأحداث
        self.permissions_user_combo.currentIndexChanged.connect(self.load_user_permissions)
        save_permissions_button.clicked.connect(self.save_permissions)
        reset_permissions_button.clicked.connect(self.reset_permissions)

        return widget

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)

        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)

        return layout

    def load_data(self):
        """تحميل البيانات"""
        print("=== تحميل جميع البيانات ===")
        self.load_users()
        self.load_permissions_users()
        print("=== انتهاء تحميل البيانات ===")

    def force_refresh_table(self):
        """إعادة تحديث الجدول بقوة"""
        print("=== إعادة تحديث الجدول بقوة ===")

        try:
            # مسح الجدول تماماً
            self.users_table.clear()
            self.users_table.setRowCount(0)
            self.users_table.setColumnCount(0)

            # إعادة تعيين العناوين
            columns = ["الرقم", "اسم المستخدم", "الاسم الكامل", "الدور", "البريد الإلكتروني", "الحالة", "آخر دخول", "تاريخ الإنشاء"]
            self.users_table.setColumnCount(len(columns))
            self.users_table.setHorizontalHeaderLabels(columns)

            # إعادة تحميل البيانات
            self.load_users()

            # إعادة تعيين الفلاتر
            self.role_filter_combo.setCurrentIndex(0)

            # إظهار جميع الصفوف
            for row in range(self.users_table.rowCount()):
                self.users_table.setRowHidden(row, False)

            # تحديث الواجهة
            self.users_table.resizeColumnsToContents()
            self.users_table.update()
            self.users_table.repaint()

            # إجبار إعادة الرسم
            self.users_table.viewport().update()

            print(f"تم تحديث الجدول - عدد الصفوف الحالي: {self.users_table.rowCount()}")
            print("=== انتهاء إعادة التحديث بقوة ===")

        except Exception as e:
            print(f"خطأ في إعادة تحديث الجدول: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث جدول المستخدمين: {e}")

    def load_users(self):
        """تحميل المستخدمين"""
        try:
            print("=== بدء تحميل المستخدمين ===")

            # إعداد الجدول
            headers = ["الرقم", "اسم المستخدم", "الاسم الكامل", "الدور", "البريد الإلكتروني", "الحالة"]
            self.users_table.setColumnCount(len(headers))
            self.users_table.setHorizontalHeaderLabels(headers)

            # جلب المستخدمين
            users = self.db_manager.fetch_all("SELECT * FROM users ORDER BY id")
            print(f"📊 تم جلب {len(users)} مستخدم")

            # إعداد الجدول
            self.users_table.setRowCount(len(users))

            # ترجمة الأدوار
            role_map = {
                'admin': 'مدير عام',
                'manager': 'مدير',
                'employee': 'موظف',
                'accountant': 'محاسب'
            }

            # إضافة البيانات للجدول
            for row, user in enumerate(users):
                try:
                    print(f"📝 معالجة المستخدم {row}: {user}")

                    # البيانات تأتي كـ tuple: (id, username, password_hash, full_name, email, role, is_active, created_at, last_login)
                    user_id = str(user[0])
                    username = str(user[1])
                    full_name = str(user[3])  # تخطي password_hash في الفهرس 2
                    email = str(user[4]) if user[4] else ''
                    role = str(user[5])
                    is_active = user[6]

                    # العمود 0: الرقم
                    id_item = QTableWidgetItem(user_id)
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(create_arabic_font(11))
                    self.users_table.setItem(row, 0, id_item)

                    # العمود 1: اسم المستخدم
                    username_item = QTableWidgetItem(username)
                    username_item.setFont(create_arabic_font(11))
                    self.users_table.setItem(row, 1, username_item)

                    # العمود 2: الاسم الكامل
                    fullname_item = QTableWidgetItem(full_name)
                    fullname_item.setFont(create_arabic_font(11))
                    self.users_table.setItem(row, 2, fullname_item)

                    # العمود 3: الدور
                    role_text = role_map.get(role, role)
                    role_item = QTableWidgetItem(role_text)
                    role_item.setTextAlignment(Qt.AlignCenter)
                    role_item.setFont(create_arabic_font(11))
                    self.users_table.setItem(row, 3, role_item)

                    # العمود 4: البريد الإلكتروني
                    email_item = QTableWidgetItem(email)
                    email_item.setFont(create_arabic_font(11))
                    self.users_table.setItem(row, 4, email_item)

                    # العمود 5: الحالة
                    status_text = "نشط" if is_active else "معطل"
                    status_item = QTableWidgetItem(status_text)
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(create_arabic_font(11))
                    if is_active:
                        status_item.setBackground(Qt.green)
                        status_item.setForeground(Qt.black)
                    else:
                        status_item.setBackground(Qt.red)
                        status_item.setForeground(Qt.black)
                    self.users_table.setItem(row, 5, status_item)

                    print(f"✅ تم إضافة المستخدم: {username} ({full_name}) - {role_text}")

                except Exception as e:
                    print(f"❌ خطأ في إضافة المستخدم {row}: {e}")
                    continue

            # تحديث الجدول
            self.users_table.resizeColumnsToContents()

            # تحديد عرض الأعمدة
            header = self.users_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
            header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المستخدم
            header.setSectionResizeMode(2, QHeaderView.Stretch)           # الاسم الكامل
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الدور
            header.setSectionResizeMode(4, QHeaderView.Stretch)           # البريد
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة

            print(f"✅ تم تحميل {len(users)} مستخدم في الجدول بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المستخدمين: {e}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص
            # في حالة الخطأ، أظهر جدول فارغ
            self.users_table.setRowCount(0)

    def load_permissions_users(self):
        """تحميل المستخدمين في قائمة الصلاحيات"""
        try:
            users = self.db_manager.fetch_all("""
                SELECT id, username, full_name FROM users
                WHERE is_active = 1
                ORDER BY full_name
            """)

            self.permissions_user_combo.clear()
            for user in users:
                display_name = f"{user['full_name']} ({user['username']})"
                self.permissions_user_combo.addItem(display_name, user['id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المستخدمين: {e}")

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def search_users(self):
        """البحث في المستخدمين"""
        search_text = self.search_edit.text().strip().lower()

        for row in range(self.users_table.rowCount()):
            show_row = True

            if search_text:
                username = self.users_table.item(row, 1).text().lower()
                full_name = self.users_table.item(row, 2).text().lower()

                if search_text not in username and search_text not in full_name:
                    show_row = False

            self.users_table.setRowHidden(row, not show_row)

    def filter_users(self):
        """فلترة المستخدمين حسب الدور"""
        selected_role = self.role_filter_combo.currentData()

        for row in range(self.users_table.rowCount()):
            show_row = True

            if selected_role:
                role_text = self.users_table.item(row, 3).text()
                role_translations = {
                    'مدير عام': 'admin',
                    'مدير': 'manager',
                    'موظف': 'employee',
                    'محاسب': 'accountant'
                }

                actual_role = None
                for arabic, english in role_translations.items():
                    if role_text == arabic:
                        actual_role = english
                        break

                if actual_role != selected_role:
                    show_row = False

            self.users_table.setRowHidden(row, not show_row)

    def add_user(self):
        """إضافة مستخدم جديد"""
        print("=== فتح نافذة إضافة مستخدم ===")
        dialog = UserDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            print("=== تم قبول إضافة المستخدم - بدء التحديث ===")

            try:
                # عدد المستخدمين قبل التحديث
                old_count = self.users_table.rowCount()
                print(f"عدد المستخدمين قبل التحديث: {old_count}")

                # انتظار قصير للتأكد من حفظ البيانات
                import time
                time.sleep(0.1)

                # إعادة تحميل البيانات بقوة
                print("إعادة تحميل البيانات بقوة...")
                self.force_refresh_table()  # تحديث قوي للجدول

                # عدد المستخدمين بعد التحديث
                new_count = self.users_table.rowCount()
                print(f"عدد المستخدمين بعد التحديث: {new_count}")

                # طباعة محتويات الجدول للتشخيص
                print("=== محتويات الجدول بعد التحديث ===")
                for row in range(new_count):
                    id_item = self.users_table.item(row, 0)
                    username_item = self.users_table.item(row, 1)
                    fullname_item = self.users_table.item(row, 2)

                    id_text = id_item.text() if id_item else "فارغ"
                    username_text = username_item.text() if username_item else "فارغ"
                    fullname_text = fullname_item.text() if fullname_item else "فارغ"

                    print(f"الصف {row}: ID={id_text}, اسم المستخدم={username_text}, الاسم الكامل={fullname_text}")

                # التحقق من نجاح الإضافة
                if new_count > old_count:
                    print(f"✅ تم إضافة {new_count - old_count} مستخدم جديد بنجاح!")
                else:
                    print("⚠️ لم يتم اكتشاف زيادة في عدد المستخدمين - محاولة إعادة التحميل...")
                    # محاولة إعادة التحميل مرة أخرى
                    self.load_users()
                    final_count = self.users_table.rowCount()
                    print(f"العدد بعد إعادة التحميل: {final_count}")

                # تحديث الواجهة
                print("تحديث الواجهة...")
                self.users_table.clearSelection()
                self.users_table.scrollToTop()

                # إعادة تعيين الفلاتر أولاً
                print("إعادة تعيين الفلاتر...")
                self.role_filter_combo.setCurrentIndex(0)  # "جميع الأدوار"

                # إظهار جميع الصفوف
                for row in range(self.users_table.rowCount()):
                    self.users_table.setRowHidden(row, False)

                print("تم إظهار جميع المستخدمين")

                # تحديث عدد المستخدمين وإعادة تنسيق الجدول
                print("تحديث الجدول...")
                self.users_table.resizeColumnsToContents()

                # إجبار إعادة الرسم
                self.users_table.viewport().repaint()

                print("=== انتهاء تحديث واجهة المستخدمين ===")

            except Exception as e:
                print(f"خطأ في تحديث واجهة المستخدمين: {e}")
                QMessageBox.warning(self, "تحذير", f"تم إضافة المستخدم ولكن حدث خطأ في تحديث الواجهة: {e}")
            self.users_table.update()
            self.users_table.repaint()

            # التأكد من أن الجدول مرئي ومحدث
            self.users_table.viewport().update()

            # تحديث قائمة المستخدمين في الصلاحيات
            print("تحديث قائمة الصلاحيات...")
            self.load_permissions_users()

            # عدد المستخدمين النهائي
            final_count = self.users_table.rowCount()
            print(f"عدد المستخدمين النهائي: {final_count}")

            # التحقق من أن المستخدم الجديد ظاهر في الجدول
            if final_count > old_count:
                print("✅ تم إضافة المستخدم وهو ظاهر في الجدول")
                # التمرير للمستخدم الجديد (أول صف)
                self.users_table.selectRow(0)
                self.users_table.scrollToTop()
            else:
                print("⚠️ تحذير: المستخدم قد لا يكون ظاهراً في الجدول")
                # محاولة إعادة التحميل مرة أخرى
                print("محاولة إعادة التحميل...")
                self.load_users()

            QMessageBox.information(self, "تم", "تم إضافة المستخدم بنجاح")
            print(f"=== انتهاء عملية إضافة المستخدم - إجمالي المستخدمين: {final_count} ===")
        else:
            print("تم إلغاء إضافة المستخدم")

    def edit_user(self):
        """تعديل مستخدم"""
        try:
            current_row = self.users_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
                return

            # التحقق من وجود العنصر في الجدول
            item = self.users_table.item(current_row, 0)
            if not item or not item.text():
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف المستخدم")
                return

            user_id = int(item.text())
            print(f"تعديل المستخدم ID: {user_id}")

            dialog = UserDialog(self.db_manager, user_id=user_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_users()
                self.load_permissions_users()
                QMessageBox.information(self, "تم", "تم تحديث المستخدم بنجاح")

        except Exception as e:
            print(f"خطأ في تعديل المستخدم: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تعديل المستخدم: {str(e)}")

    def delete_user(self):
        """حذف مستخدم"""
        try:
            current_row = self.users_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للحذف")
                return

            # التحقق من وجود العناصر في الجدول
            id_item = self.users_table.item(current_row, 0)
            username_item = self.users_table.item(current_row, 1)

            if not id_item or not id_item.text() or not username_item or not username_item.text():
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المستخدم")
                return

            user_id = int(id_item.text())
            username = username_item.text()

            if user_id == self.current_user['id']:
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف المستخدم الحالي")
                return

            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل تريد حذف المستخدم '{username}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    self.db_manager.execute_query(
                        "DELETE FROM users WHERE id = ?",
                        (user_id,)
                    )
                    self.load_users()
                    self.load_permissions_users()
                    QMessageBox.information(self, "تم", "تم حذف المستخدم بنجاح")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستخدم: {e}")

        except Exception as e:
            print(f"خطأ في حذف المستخدم: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستخدم: {str(e)}")

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        try:
            current_row = self.users_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لإعادة تعيين كلمة المرور")
                return

            # التحقق من وجود العناصر في الجدول
            id_item = self.users_table.item(current_row, 0)
            username_item = self.users_table.item(current_row, 1)

            if not id_item or not id_item.text() or not username_item or not username_item.text():
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المستخدم")
                return

            user_id = int(id_item.text())
            username = username_item.text()

            new_password = "123456"  # كلمة مرور افتراضية

            reply = QMessageBox.question(
                self,
                "تأكيد إعادة التعيين",
                f"هل تريد إعادة تعيين كلمة مرور المستخدم '{username}'؟\n"
                f"كلمة المرور الجديدة ستكون: {new_password}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    password_hash = hashlib.sha256(new_password.encode()).hexdigest()
                    self.db_manager.execute_query(
                        "UPDATE users SET password_hash = ? WHERE id = ?",
                        (password_hash, user_id)
                    )
                    QMessageBox.information(
                        self,
                        "تم",
                        f"تم إعادة تعيين كلمة مرور المستخدم '{username}'\n"
                        f"كلمة المرور الجديدة: {new_password}"
                    )
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إعادة تعيين كلمة المرور: {e}")

        except Exception as e:
            print(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")

    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم"""
        if self.permissions_user_combo.currentIndex() < 0:
            return

        user_id = self.permissions_user_combo.currentData()
        if not user_id:
            return

        try:
            # إعادة تعيين جميع الصلاحيات
            checkboxes = [
                self.can_add_subscription, self.can_deliver_router, self.can_renew_package,
                self.can_close_cash, self.can_manage_subscribers, self.can_manage_products,
                self.can_manage_workers, self.can_manage_suppliers, self.can_manage_users,
                self.can_make_purchases, self.can_charge_balance, self.can_view_reports,
                self.can_edit_settings
            ]

            for checkbox in checkboxes:
                checkbox.setChecked(False)

            # تحميل الصلاحيات من قاعدة البيانات
            permissions = self.db_manager.fetch_all("""
                SELECT permission_name FROM user_permissions
                WHERE user_id = ?
            """, (user_id,))

            permission_mapping = {
                'add_subscription': self.can_add_subscription,
                'deliver_router': self.can_deliver_router,
                'renew_package': self.can_renew_package,
                'close_cash': self.can_close_cash,
                'manage_subscribers': self.can_manage_subscribers,
                'manage_products': self.can_manage_products,
                'manage_workers': self.can_manage_workers,
                'manage_suppliers': self.can_manage_suppliers,
                'manage_users': self.can_manage_users,
                'make_purchases': self.can_make_purchases,
                'charge_balance': self.can_charge_balance,
                'view_reports': self.can_view_reports,
                'edit_settings': self.can_edit_settings
            }

            for permission in permissions:
                checkbox = permission_mapping.get(permission['permission_name'])
                if checkbox:
                    checkbox.setChecked(True)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الصلاحيات: {e}")

    def save_permissions(self):
        """حفظ الصلاحيات"""
        if self.permissions_user_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم")
            return

        user_id = self.permissions_user_combo.currentData()
        if not user_id:
            return

        try:
            # حذف الصلاحيات الحالية
            self.db_manager.execute_query(
                "DELETE FROM user_permissions WHERE user_id = ?",
                (user_id,)
            )

            # إضافة الصلاحيات الجديدة
            permissions_to_save = []

            permission_mapping = {
                'add_subscription': self.can_add_subscription,
                'deliver_router': self.can_deliver_router,
                'renew_package': self.can_renew_package,
                'close_cash': self.can_close_cash,
                'manage_subscribers': self.can_manage_subscribers,
                'manage_products': self.can_manage_products,
                'manage_workers': self.can_manage_workers,
                'manage_suppliers': self.can_manage_suppliers,
                'manage_users': self.can_manage_users,
                'make_purchases': self.can_make_purchases,
                'charge_balance': self.can_charge_balance,
                'view_reports': self.can_view_reports,
                'edit_settings': self.can_edit_settings
            }

            for permission_name, checkbox in permission_mapping.items():
                if checkbox.isChecked():
                    permissions_to_save.append((user_id, permission_name))

            if permissions_to_save:
                self.db_manager.execute_many(
                    "INSERT INTO user_permissions (user_id, permission_name) VALUES (?, ?)",
                    permissions_to_save
                )

            QMessageBox.information(self, "تم", "تم حفظ الصلاحيات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الصلاحيات: {e}")

    def reset_permissions(self):
        """إعادة تعيين الصلاحيات"""
        checkboxes = [
            self.can_add_subscription, self.can_deliver_router, self.can_renew_package,
            self.can_close_cash, self.can_manage_subscribers, self.can_manage_products,
            self.can_manage_workers, self.can_manage_suppliers, self.can_manage_users,
            self.can_make_purchases, self.can_charge_balance, self.can_view_reports,
            self.can_edit_settings
        ]

        for checkbox in checkboxes:
            checkbox.setChecked(False)


class UserDialog(QDialog):
    """حوار إضافة/تعديل مستخدم"""

    def __init__(self, db_manager, user_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.is_edit_mode = user_id is not None

        self.setup_ui()
        if self.is_edit_mode:
            self.load_user_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 500, 400)
        self.setModal(True)

        apply_arabic_style(self, 10)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الحوار
        title_label = QLabel(title)
        apply_arabic_style(title_label, 14, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)

        # نموذج البيانات
        form_layout = QGridLayout()
        form_layout.setSpacing(10)

        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        apply_arabic_style(username_label, 10)
        self.username_edit = QLineEdit()
        apply_arabic_style(self.username_edit, 10)

        # الاسم الكامل
        full_name_label = QLabel("الاسم الكامل:")
        apply_arabic_style(full_name_label, 10)
        self.full_name_edit = QLineEdit()
        apply_arabic_style(self.full_name_edit, 10)

        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        apply_arabic_style(email_label, 10)
        self.email_edit = QLineEdit()
        apply_arabic_style(self.email_edit, 10)

        # الدور
        role_label = QLabel("الدور:")
        apply_arabic_style(role_label, 10)
        self.role_combo = QComboBox()
        apply_arabic_style(self.role_combo, 10)
        self.role_combo.addItem("مدير عام", "admin")
        self.role_combo.addItem("مدير", "manager")
        self.role_combo.addItem("موظف", "employee")
        self.role_combo.addItem("محاسب", "accountant")

        # كلمة المرور (فقط في وضع الإضافة)
        if not self.is_edit_mode:
            password_label = QLabel("كلمة المرور:")
            apply_arabic_style(password_label, 10)
            self.password_edit = QLineEdit()
            apply_arabic_style(self.password_edit, 10)
            self.password_edit.setEchoMode(QLineEdit.Password)

            confirm_password_label = QLabel("تأكيد كلمة المرور:")
            apply_arabic_style(confirm_password_label, 10)
            self.confirm_password_edit = QLineEdit()
            apply_arabic_style(self.confirm_password_edit, 10)
            self.confirm_password_edit.setEchoMode(QLineEdit.Password)

        # الحالة
        self.is_active_checkbox = QCheckBox("المستخدم نشط")
        apply_arabic_style(self.is_active_checkbox, 10)
        self.is_active_checkbox.setChecked(True)

        # ترتيب الحقول
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_edit, 0, 1)
        form_layout.addWidget(full_name_label, 1, 0)
        form_layout.addWidget(self.full_name_edit, 1, 1)
        form_layout.addWidget(email_label, 2, 0)
        form_layout.addWidget(self.email_edit, 2, 1)
        form_layout.addWidget(role_label, 3, 0)
        form_layout.addWidget(self.role_combo, 3, 1)

        if not self.is_edit_mode:
            form_layout.addWidget(password_label, 4, 0)
            form_layout.addWidget(self.password_edit, 4, 1)
            form_layout.addWidget(confirm_password_label, 5, 0)
            form_layout.addWidget(self.confirm_password_edit, 5, 1)
            form_layout.addWidget(self.is_active_checkbox, 6, 0, 1, 2)
        else:
            form_layout.addWidget(self.is_active_checkbox, 4, 0, 1, 2)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        layout.addWidget(title_label)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

        # ربط الأحداث
        save_button.clicked.connect(self.save_user)
        cancel_button.clicked.connect(self.reject)

    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        try:
            user = self.db_manager.fetch_one("""
                SELECT username, full_name, email, role, is_active
                FROM users WHERE id = ?
            """, (self.user_id,))

            if user:
                self.username_edit.setText(user['username'])
                self.full_name_edit.setText(user['full_name'])
                self.email_edit.setText(user['email'] or '')

                # تعيين الدور
                for i in range(self.role_combo.count()):
                    if self.role_combo.itemData(i) == user['role']:
                        self.role_combo.setCurrentIndex(i)
                        break

                self.is_active_checkbox.setChecked(user['is_active'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المستخدم: {e}")

    def save_user(self):
        """حفظ المستخدم"""
        # التحقق من البيانات
        if not self.username_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم")
            return

        if not self.full_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم الكامل")
            return

        if not self.is_edit_mode:
            if not self.password_edit.text():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور")
                return

            if self.password_edit.text() != self.confirm_password_edit.text():
                QMessageBox.warning(self, "تحذير", "كلمة المرور وتأكيدها غير متطابقين")
                return

            if len(self.password_edit.text()) < 6:
                QMessageBox.warning(self, "تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return

        try:
            username = self.username_edit.text().strip()
            full_name = self.full_name_edit.text().strip()
            email = self.email_edit.text().strip() or None
            role = self.role_combo.currentData()
            is_active = self.is_active_checkbox.isChecked()

            if self.is_edit_mode:
                # تحديث المستخدم
                self.db_manager.execute_query("""
                    UPDATE users
                    SET username = ?, full_name = ?, email = ?, role = ?, is_active = ?
                    WHERE id = ?
                """, (username, full_name, email, role, is_active, self.user_id))
            else:
                # التحقق من عدم تكرار اسم المستخدم
                existing_user = self.db_manager.fetch_one(
                    "SELECT id FROM users WHERE username = ?",
                    (username,)
                )

                if existing_user:
                    QMessageBox.warning(self, "تحذير", "اسم المستخدم موجود مسبقاً")
                    return

                # إضافة مستخدم جديد
                print(f"=== إضافة مستخدم جديد ===")
                print(f"اسم المستخدم: {username}")
                print(f"الاسم الكامل: {full_name}")
                print(f"البريد الإلكتروني: {email}")
                print(f"الدور: {role}")
                print(f"نشط: {is_active}")

                password_hash = hashlib.sha256(self.password_edit.text().encode()).hexdigest()
                print(f"تم تشفير كلمة المرور")

                try:
                    print("محاولة إضافة المستخدم إلى قاعدة البيانات...")
                    result = self.db_manager.execute_query("""
                        INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
                    """, (username, password_hash, full_name, email, role, is_active))

                    if result:
                        print("✅ تم تنفيذ استعلام الإضافة بنجاح")

                        # انتظار قصير للتأكد من حفظ البيانات
                        import time
                        time.sleep(0.1)

                        # التحقق من الإضافة
                        print("التحقق من إضافة المستخدم...")
                        verification = self.db_manager.fetch_one(
                            "SELECT id, username, full_name, email, role, is_active FROM users WHERE username = ?",
                            (username,)
                        )

                        if verification:
                            print(f"✅ تم التحقق من إضافة المستخدم:")
                            print(f"   ID: {verification['id']}")
                            print(f"   اسم المستخدم: {verification['username']}")
                            print(f"   الاسم الكامل: {verification['full_name']}")
                            print(f"   البريد: {verification['email']}")
                            print(f"   الدور: {verification['role']}")
                            print(f"   نشط: {verification['is_active']}")
                            QMessageBox.information(self, "تم", f"تم إضافة المستخدم '{full_name}' بنجاح!")
                        else:
                            print("❌ خطأ: لم يتم العثور على المستخدم بعد الإضافة!")
                            QMessageBox.warning(self, "تحذير", "تم حفظ المستخدم ولكن لم يتم التحقق من الإضافة")
                    else:
                        print("❌ فشل في تنفيذ استعلام الإضافة")
                        QMessageBox.critical(self, "خطأ", "فشل في إضافة المستخدم إلى قاعدة البيانات")

                    print(f"=== انتهاء إضافة المستخدم في قاعدة البيانات ===")

                except Exception as db_error:
                    print(f"خطأ في قاعدة البيانات: {db_error}")
                    QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المستخدم في قاعدة البيانات: {db_error}")
                    return

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المستخدم: {e}")
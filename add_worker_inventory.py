#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لمخزون العامل
"""

import sqlite3
import sys
import os

def add_worker_inventory():
    """إضافة بيانات تجريبية لمخزون العامل"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        # الحصول على معرف العامل الأول
        worker = cursor.execute('SELECT id, name FROM workers WHERE is_active = 1 LIMIT 1').fetchone()
        if not worker:
            print('❌ لا يوجد عمال نشطون')
            return
            
        worker_id, worker_name = worker
        print(f'🔧 إضافة مخزون للعامل: {worker_name} (ID: {worker_id})')
        
        # الحصول على منتجات الكبل
        cables = cursor.execute('SELECT id, name, unit_price FROM products WHERE category = "cable" LIMIT 5').fetchall()
        
        if not cables:
            print('❌ لا توجد منتجات كبل في قاعدة البيانات')
            return
        
        for cable in cables:
            cable_id, cable_name, cable_price = cable
            
            # إضافة كمية للعامل
            cursor.execute('''
                INSERT OR REPLACE INTO worker_inventory (worker_id, product_id, quantity)
                VALUES (?, ?, ?)
            ''', (worker_id, cable_id, 100))  # 100 متر لكل نوع كبل
            
            print(f'✅ تم إضافة 100م من {cable_name} (سعر: {cable_price} ل.س/م) لمخزون العامل')
        
        conn.commit()
        print(f'🎉 تم إضافة {len(cables)} نوع كبل لمخزون العامل {worker_name}')
        
        # عرض المخزون الحالي
        print('\n📦 المخزون الحالي للعامل:')
        inventory = cursor.execute('''
            SELECT p.name, wi.quantity, p.unit_price
            FROM worker_inventory wi
            JOIN products p ON wi.product_id = p.id
            WHERE wi.worker_id = ? AND p.category = "cable"
            ORDER BY p.name
        ''', (worker_id,)).fetchall()
        
        for item in inventory:
            name, quantity, price = item
            print(f'  • {name}: {quantity}م - {price} ل.س/م')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    add_worker_inventory()

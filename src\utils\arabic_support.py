# -*- coding: utf-8 -*-
"""
دعم اللغة العربية
Arabic Language Support
"""

import arabic_reshaper
from bidi.algorithm import get_display
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def reshape_arabic_text(text):
    """إعادة تشكيل النص العربي للعرض الصحيح - الآن يستخدم الطريقة البسيطة"""
    # PyQt5 يتعامل مع العربية بشكل ممتاز بدون معالجة إضافية
    return simple_arabic_text(text)

def simple_arabic_text(text):
    """دالة بسيطة للنصوص العربية بدون معالجة معقدة"""
    if not text:
        return ""

    # للنصوص البسيطة، أرجعها كما هي
    # PyQt5 يتعامل مع العربية بشكل جيد في معظم الحالات
    return text

def get_display_text(text):
    """الحصول على النص للعرض - يختار الطريقة الأنسب"""
    if not text:
        return ""

    # جرب الطريقة البسيطة أولاً
    try:
        # إذا كان النص يحتوي على أرقام أو رموز، استخدم المعالجة البسيطة
        if any(char.isdigit() or char in '()[]{}+-*/.,:;' for char in text):
            return simple_arabic_text(text)

        # للنصوص العربية الخالصة، جرب المعالجة المتقدمة
        if any('\u0600' <= char <= '\u06FF' for char in text):
            try:
                reshaped = arabic_reshaper.reshape(text)
                return get_display(reshaped)
            except:
                return simple_arabic_text(text)

        # للنصوص الإنجليزية
        return text

    except Exception as e:
        print(f"تحذير في معالجة النص: {e}")
        return text

def setup_arabic_support(app):
    """إعداد الدعم العربي للتطبيق"""
    try:
        # تعيين اتجاه التطبيق من اليمين إلى اليسار
        app.setLayoutDirection(Qt.RightToLeft)

        # إعداد الخط العربي - جرب عدة خطوط
        arabic_fonts = [
            "Segoe UI", "Tahoma", "Arial Unicode MS",
            "Microsoft Sans Serif", "Calibri", "Verdana"
        ]

        font = QFont()
        font_set = False

        for font_name in arabic_fonts:
            font.setFamily(font_name)
            if font.exactMatch():
                print(f"✅ تم استخدام الخط: {font_name}")
                font_set = True
                break

        if not font_set:
            print("⚠️ لم يتم العثور على خط مناسب، سيتم استخدام الخط الافتراضي")
            font.setFamily("Arial")

        font.setPointSize(10)
        font.setWeight(QFont.Normal)
        app.setFont(font)

        # إعداد ترميز UTF-8
        import sys
        if hasattr(sys, 'setdefaultencoding'):
            sys.setdefaultencoding('utf-8')

        return True
    except Exception as e:
        print(f"خطأ في إعداد الدعم العربي: {e}")
        return False

def format_currency(amount, currency="ل.س"):
    """تنسيق المبلغ المالي"""
    try:
        if isinstance(amount, (int, float)):
            formatted_amount = f"{amount:,.0f}"
            return f"{formatted_amount} {currency}"
        return str(amount)
    except:
        return str(amount)

def format_number(number):
    """تنسيق الأرقام"""
    try:
        if isinstance(number, (int, float)):
            return f"{number:,.0f}"
        return str(number)
    except:
        return str(number)

class ArabicTextProcessor:
    """معالج النصوص العربية"""
    
    @staticmethod
    def process_text(text):
        """معالجة النص العربي"""
        return reshape_arabic_text(text)
    
    @staticmethod
    def process_mixed_text(text):
        """معالجة النص المختلط (عربي وإنجليزي)"""
        if not text:
            return ""
        
        try:
            # معالجة النص المختلط
            reshaped_text = arabic_reshaper.reshape(text)
            display_text = get_display(reshaped_text)
            return display_text
        except:
            return text
    
    @staticmethod
    def is_arabic(text):
        """فحص ما إذا كان النص يحتوي على أحرف عربية"""
        if not text:
            return False
        
        arabic_chars = set(range(0x0600, 0x06FF + 1))  # نطاق الأحرف العربية
        return any(ord(char) in arabic_chars for char in text)

def create_arabic_font(size=10, bold=False, family=None):
    """إنشاء خط عربي محسن"""
    font = QFont()

    # اختيار الخط المناسب
    if family:
        font.setFamily(family)
    else:
        # قائمة الخطوط العربية المفضلة
        preferred_fonts = [
            "Segoe UI", "Tahoma", "Arial Unicode MS",
            "Microsoft Sans Serif", "Calibri", "Verdana", "Arial"
        ]

        for font_name in preferred_fonts:
            font.setFamily(font_name)
            if font.exactMatch():
                break

    font.setPointSize(size)

    if bold:
        font.setBold(True)
        font.setWeight(QFont.Bold)
    else:
        font.setWeight(QFont.Normal)

    # تحسينات إضافية للخط
    font.setStyleHint(QFont.SansSerif)
    font.setStyleStrategy(QFont.PreferAntialias)

    return font

def get_best_arabic_font():
    """الحصول على أفضل خط عربي متاح"""
    test_fonts = [
        "Segoe UI", "Tahoma", "Arial Unicode MS",
        "Microsoft Sans Serif", "Calibri", "Verdana", "Arial"
    ]

    for font_name in test_fonts:
        font = QFont(font_name)
        if font.exactMatch():
            return font_name

    return "Arial"  # الخط الافتراضي

def apply_arabic_style(widget, font_size=10, bold=False):
    """تطبيق النمط العربي على ويدجت"""
    try:
        font = create_arabic_font(font_size, bold)
        widget.setFont(font)

        # تطبيق اتجاه النص
        if hasattr(widget, 'setLayoutDirection'):
            widget.setLayoutDirection(Qt.RightToLeft)

        # تطبيق محاذاة النص للعناصر النصية
        if hasattr(widget, 'setAlignment'):
            widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

    except Exception as e:
        print(f"خطأ في تطبيق النمط العربي: {e}")

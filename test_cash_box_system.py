#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إغلاق الصندوق المحدث
"""

import sys
import os
sys.path.append('src')

def test_cash_box_cycle():
    """اختبار دورة كاملة لإغلاق وفتح الصندوق"""
    
    print("🧪 اختبار دورة إغلاق وفتح الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        print("\n=== 1. فتح صندوق جديد ===")
        
        # فتح صندوق جديد
        treasury_manager.open_cash_box(user_id=user_id)
        
        # فحص الرصيد الأولي
        initial_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد الأولي: {initial_balance:,} ل.س")
        
        print("\n=== 2. محاكاة المبيعات ===")
        
        # محاكاة مبيعات متنوعة
        sales_operations = [
            ("اشتراك جديد", 75000),
            ("تسليم راوتر", 150000),
            ("تجديد باقة", 50000),
            ("اشتراك جديد آخر", 75000)
        ]
        
        total_sales = 0
        for operation, amount in sales_operations:
            treasury_manager.add_to_daily_treasury(user_id, 'SYP', amount)
            total_sales += amount
            print(f"✅ {operation}: {amount:,} ل.س")
        
        # فحص الرصيد بعد المبيعات
        balance_after_sales = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد بعد المبيعات: {balance_after_sales:,} ل.س")
        print(f"📊 إجمالي المبيعات: {total_sales:,} ل.س")
        
        print("\n=== 3. إغلاق الصندوق ===")
        
        # إغلاق الصندوق
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        
        if close_success:
            print("✅ تم إغلاق الصندوق بنجاح")
            
            # فحص الأرصدة بعد الإغلاق
            daily_after_close = treasury_manager.get_daily_balance(user_id, 'SYP')
            main_after_close = treasury_manager.get_main_balance(user_id, 'SYP')
            
            print(f"💰 الخزينة اليومية بعد الإغلاق: {daily_after_close:,} ل.س")
            print(f"💰 الخزينة الرئيسية بعد الإغلاق: {main_after_close:,} ل.س")
            
            # التحقق من إغلاق الجلسة
            is_active = treasury_manager.is_session_active(user_id=user_id)
            print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة ✅'}")
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
        print("\n=== 4. فتح صندوق جديد (محاكاة اليوم التالي) ===")
        
        # فتح صندوق جديد
        treasury_manager.open_cash_box(user_id=user_id)
        
        # فحص الرصيد الجديد (يجب أن يكون صفر)
        new_daily_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد اليومي الجديد: {new_daily_balance:,} ل.س")
        
        # إضافة قيمة الإغلاق كرصيد ابتدائي
        closing_amount = balance_after_sales  # قيمة الإغلاق السابقة
        if closing_amount > 0:
            treasury_manager.add_to_daily_treasury(user_id, 'SYP', closing_amount)
            print(f"✅ تم إضافة قيمة الإغلاق {closing_amount:,} ل.س كرصيد ابتدائي")
        
        # فحص الرصيد النهائي
        final_daily_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد اليومي النهائي: {final_daily_balance:,} ل.س")
        
        print("\n=== 5. التحقق من النتائج ===")
        
        # التحقق من صحة العملية
        if final_daily_balance == closing_amount:
            print("✅ دورة إغلاق وفتح الصندوق تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في دورة إغلاق وفتح الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دورة الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_stats():
    """اختبار إحصائيات الواجهة الرئيسية"""
    
    print("\n🧪 اختبار إحصائيات الواجهة الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # محاكاة مستخدم
        current_user = {
            'id': 1,
            'username': 'admin'
        }
        
        # محاكاة كلاس الواجهة الرئيسية
        class MockMainWindow:
            def __init__(self, db, treasury_manager, current_user):
                self.db_manager = db
                self.treasury_manager = treasury_manager
                self.current_user = current_user
            
            def get_today_stats(self):
                """الحصول على إحصائيات اليوم من النظام الموحد"""
                try:
                    from datetime import datetime
                    today = datetime.now().strftime('%Y-%m-%d')

                    # عدد المشتركين الجدد
                    new_subscribers = self.db_manager.fetch_one(
                        "SELECT COUNT(*) as count FROM subscribers WHERE DATE(created_at) = ? AND created_by = ?",
                        (today, self.current_user['username'])
                    )
                    
                    # إجمالي المبيعات من الخزينة الموحدة
                    daily_syp_balance = self.treasury_manager.get_daily_balance(
                        user_id=self.current_user['id'],
                        currency_type='SYP'
                    )

                    total_sales_amount = max(0, daily_syp_balance)
                    
                    # تفصيل المبيعات
                    breakdown = self.get_sales_breakdown(today)
                    
                    return {
                        'new_subscribers': new_subscribers['count'] if new_subscribers else 0,
                        'total_sales': total_sales_amount,
                        'breakdown': breakdown
                    }
                except Exception as e:
                    print(f"❌ خطأ في الإحصائيات: {e}")
                    return {}
            
            def get_sales_breakdown(self, today):
                """تفصيل المبيعات حسب النوع"""
                try:
                    breakdown = {
                        'new_subscriptions': 0,
                        'router_deliveries': 0,
                        'package_renewals': 0
                    }
                    
                    # اشتراكات جديدة
                    new_subs = self.db_manager.fetch_one("""
                        SELECT COALESCE(SUM(total_amount), 0) as total 
                        FROM new_subscriptions 
                        WHERE DATE(created_at) = ? AND created_by = ?
                    """, (today, self.current_user['username']))
                    
                    if new_subs:
                        breakdown['new_subscriptions'] = new_subs['total']
                    
                    # تسليم راوترات
                    router_deliveries = self.db_manager.fetch_one("""
                        SELECT COALESCE(SUM(total_amount), 0) as total 
                        FROM router_deliveries 
                        WHERE DATE(created_at) = ? AND created_by = ?
                    """, (today, self.current_user['username']))
                    
                    if router_deliveries:
                        breakdown['router_deliveries'] = router_deliveries['total']
                    
                    # تجديد باقات
                    renewals = self.db_manager.fetch_one("""
                        SELECT COALESCE(SUM(amount), 0) as total 
                        FROM package_renewals 
                        WHERE DATE(created_at) = ? AND created_by = ?
                    """, (today, self.current_user['username']))
                    
                    if renewals:
                        breakdown['package_renewals'] = renewals['total']
                    
                    return breakdown
                    
                except Exception as e:
                    print(f"❌ خطأ في تفصيل المبيعات: {e}")
                    return {'new_subscriptions': 0, 'router_deliveries': 0, 'package_renewals': 0}
        
        # إنشاء واجهة وهمية
        mock_window = MockMainWindow(db, treasury_manager, current_user)
        
        # الحصول على الإحصائيات
        stats = mock_window.get_today_stats()
        
        print(f"📊 إحصائيات الواجهة الرئيسية:")
        print(f"  • عدد المشتركين الجدد: {stats.get('new_subscribers', 0)}")
        print(f"  • إجمالي المبيعات: {stats.get('total_sales', 0):,} ل.س")
        
        breakdown = stats.get('breakdown', {})
        print(f"  📋 تفصيل المبيعات:")
        print(f"    • اشتراكات جديدة: {breakdown.get('new_subscriptions', 0):,} ل.س")
        print(f"    • تسليم راوترات: {breakdown.get('router_deliveries', 0):,} ل.س")
        print(f"    • تجديد باقات: {breakdown.get('package_renewals', 0):,} ل.س")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إحصائيات الواجهة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار نظام إغلاق الصندوق المحدث")
    print("=" * 70)
    
    # اختبار دورة الصندوق
    cycle_test = test_cash_box_cycle()
    
    # اختبار إحصائيات الواجهة الرئيسية
    stats_test = test_main_window_stats()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • دورة إغلاق وفتح الصندوق: {'✅ نجح' if cycle_test else '❌ فشل'}")
    print(f"  • إحصائيات الواجهة الرئيسية: {'✅ نجح' if stats_test else '❌ فشل'}")
    
    if cycle_test and stats_test:
        print("\n🎉 نظام إغلاق الصندوق يعمل بشكل مثالي!")
        
        print("\n📋 المميزات الجديدة:")
        print("  ✅ إعادة تعيين القيم إلى صفر بعد الإغلاق")
        print("  ✅ إضافة قيمة الإغلاق للخزينة اليومية الجديدة")
        print("  ✅ عرض إجمالي المبيعات في الواجهة الرئيسية")
        print("  ✅ تفصيل المبيعات حسب النوع")
        print("  ✅ تكامل مع النظام الموحد")
        print("  ✅ نسخ احتياطية تلقائية")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. سجل دخول: admin / 123")
        print("  3. قم بعمليات مبيعات (اشتراك جديد، تسليم راوتر، تجديد باقة)")
        print("  4. افتح 'إغلاق الصندوق'")
        print("  5. اضغط 'إغلاق الصندوق'")
        print("  6. افتح 'إغلاق الصندوق' مرة أخرى - ستجد القيم صفر")
        print("  7. الواجهة الرئيسية تعرض إجمالي المبيعات")
        print("  8. واجهة نقل الخزينة تعرض الخزينة اليومية المحدثة")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

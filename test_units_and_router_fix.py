#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح واجهة إدارة الوحدات وتسليم الراوتر
"""

import sys
import os
sys.path.append('src')

def test_units_management():
    """اختبار واجهة إدارة الوحدات"""
    
    print("🧪 اختبار واجهة إدارة الوحدات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from ui.units_management_window import UnitsManagementWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = UnitsManagementWindow(db, inventory_manager, current_user)
        
        print("✅ تم إنشاء واجهة إدارة الوحدات بنجاح")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إدارة الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_router_delivery_fix():
    """اختبار إصلاح واجهة تسليم الراوتر"""
    
    print("\n🧪 اختبار إصلاح واجهة تسليم الراوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة مع المعاملات الجديدة
        window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة تسليم الراوتر بنجاح")
        
        # اختبار تحميل البيانات
        window.load_data()
        print("✅ تم تحميل البيانات بنجاح")
        
        # اختبار تحميل الراوترات
        window.load_routers()
        print("✅ تم تحميل الراوترات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة تسليم الراوتر: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_router_inventory():
    """اختبار وجود راوترات في المخزون"""
    
    print("\n🧪 اختبار وجود راوترات في المخزون...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        # البحث عن راوترات
        routers = inventory_manager.get_products_by_category('راوتر')
        print(f"📊 عدد الراوترات المتوفرة: {len(routers)}")
        
        if len(routers) == 0:
            print("⚠️ لا توجد راوترات في المخزون - سأضيف راوتر تجريبي")
            
            # إضافة راوتر تجريبي
            result = db.execute_query("""
                INSERT INTO unified_products 
                (name, category, purchase_unit, sale_unit, conversion_factor,
                 purchase_price, sale_price, current_stock, min_stock, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "راوتر TP-Link AC1200", "راوتر", "كرتونة", "قطعة", 10.0,
                500000, 75000, 25, 5, 1
            ))
            
            if result:
                print("✅ تم إضافة راوتر تجريبي")
                
                # إعادة فحص الراوترات
                routers = inventory_manager.get_products_by_category('راوتر')
                print(f"📊 عدد الراوترات بعد الإضافة: {len(routers)}")
        
        for router in routers:
            print(f"  • {router['name']} - مخزون: {router['current_stock']:.0f} {router.get('sale_unit', 'قطعة')}")
        
        return len(routers) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مخزون الراوترات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح واجهة إدارة الوحدات وتسليم الراوتر")
    print("=" * 70)
    
    # اختبار واجهة إدارة الوحدات
    units_test = test_units_management()
    
    # اختبار مخزون الراوترات
    router_inventory_test = test_router_inventory()
    
    # اختبار واجهة تسليم الراوتر
    router_delivery_test = test_router_delivery_fix()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • واجهة إدارة الوحدات: {'✅ نجح' if units_test else '❌ فشل'}")
    print(f"  • مخزون الراوترات: {'✅ نجح' if router_inventory_test else '❌ فشل'}")
    print(f"  • واجهة تسليم الراوتر: {'✅ نجح' if router_delivery_test else '❌ فشل'}")
    
    if all([units_test, router_inventory_test, router_delivery_test]):
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ واجهة إدارة الوحدات تعمل مع النظام الموحد")
        print("  ✅ واجهة تسليم الراوتر تحصل على inventory_manager")
        print("  ✅ تحميل الراوترات من النظام الموحد")
        print("  ✅ عرض الراوترات مع المخزون المتوفر")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'إدارة الوحدات' من قائمة الإدارة")
        print("     - اختر منتج وحدث وحدات الشراء والبيع")
        print("     - حدد معامل التحويل")
        print("  3. اضغط 'تسليم راوتر' من القائمة الرئيسية")
        print("     - ستظهر الراوترات المتوفرة مع المخزون")
        print("     - اختر راوتر وأكمل التسليم")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نافذة إدارة المخزون المتقدمة
Advanced Inventory Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QTabWidget,
                            QDateEdit, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class InventoryManagementWindow(QDialog):
    """نافذة إدارة المخزون المتقدمة"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المخزون المتقدمة")
        self.setGeometry(50, 50, 1200, 800)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة المخزون المتقدمة")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # تبويبات إدارة المخزون
        self.tabs = QTabWidget()
        apply_arabic_style(self.tabs, 10)
        
        # تبويب المخزون الحالي
        current_inventory_tab = self.create_current_inventory_tab()
        self.tabs.addTab(current_inventory_tab, "📦 المخزون الحالي")
        
        # تبويب حركة المخزون
        inventory_movements_tab = self.create_inventory_movements_tab()
        self.tabs.addTab(inventory_movements_tab, "📊 حركة المخزون")
        
        # تبويب إضافة/تعديل الكميات
        adjust_quantities_tab = self.create_adjust_quantities_tab()
        self.tabs.addTab(adjust_quantities_tab, "⚙️ تعديل الكميات")
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_current_inventory_tab(self):
        """إنشاء تبويب المخزون الحالي"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)
        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث عن منتج...")
        
        category_label = QLabel("الفئة:")
        apply_arabic_style(category_label, 10)
        self.category_filter = QComboBox()
        apply_arabic_style(self.category_filter, 10)
        self.category_filter.addItem("جميع الفئات")
        
        low_stock_checkbox = QCheckBox("المخزون المنخفض فقط")
        apply_arabic_style(low_stock_checkbox, 10)
        self.low_stock_checkbox = low_stock_checkbox
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(low_stock_checkbox)
        search_layout.addStretch()
        
        # جدول المخزون
        self.inventory_table = QTableWidget()
        apply_arabic_style(self.inventory_table, 9)
        
        # إعداد الجدول
        columns = ["المعرف", "اسم المنتج", "الفئة", "الكمية الحالية", "الوحدة", 
                  "الحد الأدنى", "الحالة", "آخر تحديث", "القيمة الإجمالية"]
        self.inventory_table.setColumnCount(len(columns))
        self.inventory_table.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addLayout(search_layout)
        layout.addWidget(self.inventory_table)
        
        # ربط الأحداث
        self.search_edit.textChanged.connect(self.filter_inventory)
        self.category_filter.currentTextChanged.connect(self.filter_inventory)
        low_stock_checkbox.toggled.connect(self.filter_inventory)
        
        return widget
        
    def create_inventory_movements_tab(self):
        """إنشاء تبويب حركة المخزون"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # فلترة التواريخ
        date_layout = QHBoxLayout()
        
        from_date_label = QLabel("من تاريخ:")
        apply_arabic_style(from_date_label, 10)
        self.from_date_edit = QDateEdit()
        apply_arabic_style(self.from_date_edit, 10)
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        
        to_date_label = QLabel("إلى تاريخ:")
        apply_arabic_style(to_date_label, 10)
        self.to_date_edit = QDateEdit()
        apply_arabic_style(self.to_date_edit, 10)
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        
        movement_type_label = QLabel("نوع الحركة:")
        apply_arabic_style(movement_type_label, 10)
        self.movement_type_filter = QComboBox()
        apply_arabic_style(self.movement_type_filter, 10)
        self.movement_type_filter.addItems(["جميع الحركات", "إضافة", "خصم", "تعديل"])
        
        load_movements_button = QPushButton("تحميل الحركات")
        apply_arabic_style(load_movements_button, 10)
        load_movements_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        date_layout.addWidget(from_date_label)
        date_layout.addWidget(self.from_date_edit)
        date_layout.addWidget(to_date_label)
        date_layout.addWidget(self.to_date_edit)
        date_layout.addWidget(movement_type_label)
        date_layout.addWidget(self.movement_type_filter)
        date_layout.addWidget(load_movements_button)
        date_layout.addStretch()
        
        # جدول حركة المخزون
        self.movements_table = QTableWidget()
        apply_arabic_style(self.movements_table, 9)
        
        # إعداد الجدول
        movement_columns = ["التاريخ", "المنتج", "نوع الحركة", "الكمية", "الكمية السابقة", 
                           "الكمية الجديدة", "المستخدم", "الملاحظات"]
        self.movements_table.setColumnCount(len(movement_columns))
        self.movements_table.setHorizontalHeaderLabels(movement_columns)
        
        # تنسيق الجدول
        movements_header = self.movements_table.horizontalHeader()
        movements_header.setSectionResizeMode(QHeaderView.Stretch)
        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addLayout(date_layout)
        layout.addWidget(self.movements_table)
        
        # ربط الأحداث
        load_movements_button.clicked.connect(self.load_inventory_movements)
        
        return widget
        
    def create_adjust_quantities_tab(self):
        """إنشاء تبويب تعديل الكميات"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # مجموعة اختيار المنتج
        product_group = QGroupBox("اختيار المنتج")
        apply_arabic_style(product_group, 10, bold=True)
        
        product_layout = QGridLayout(product_group)
        product_layout.setSpacing(10)
        
        # اختيار المنتج
        product_label = QLabel("المنتج:")
        apply_arabic_style(product_label, 10)
        self.product_combo = QComboBox()
        apply_arabic_style(self.product_combo, 10)
        
        # الكمية الحالية
        current_qty_label = QLabel("الكمية الحالية:")
        apply_arabic_style(current_qty_label, 10)
        self.current_qty_display = QLabel("0")
        apply_arabic_style(self.current_qty_display, 12, bold=True)
        self.current_qty_display.setStyleSheet("color: #3498db; font-weight: bold;")
        
        product_layout.addWidget(product_label, 0, 0)
        product_layout.addWidget(self.product_combo, 0, 1)
        product_layout.addWidget(current_qty_label, 0, 2)
        product_layout.addWidget(self.current_qty_display, 0, 3)
        
        # مجموعة تعديل الكمية
        adjustment_group = QGroupBox("تعديل الكمية")
        apply_arabic_style(adjustment_group, 10, bold=True)
        
        adjustment_layout = QGridLayout(adjustment_group)
        adjustment_layout.setSpacing(10)
        
        # نوع التعديل
        adjustment_type_label = QLabel("نوع التعديل:")
        apply_arabic_style(adjustment_type_label, 10)
        self.adjustment_type_combo = QComboBox()
        apply_arabic_style(self.adjustment_type_combo, 10)
        self.adjustment_type_combo.addItems(["إضافة", "خصم", "تعديل مباشر"])
        
        # الكمية
        quantity_label = QLabel("الكمية:")
        apply_arabic_style(quantity_label, 10)
        self.quantity_spin = QSpinBox()
        apply_arabic_style(self.quantity_spin, 10)
        self.quantity_spin.setRange(0, 100000)
        
        # الكمية الجديدة (محسوبة)
        new_qty_label = QLabel("الكمية الجديدة:")
        apply_arabic_style(new_qty_label, 10)
        self.new_qty_display = QLabel("0")
        apply_arabic_style(self.new_qty_display, 12, bold=True)
        self.new_qty_display.setStyleSheet("color: #27ae60; font-weight: bold;")
        
        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات حول التعديل...")
        self.notes_edit.setMaximumHeight(60)
        
        adjustment_layout.addWidget(adjustment_type_label, 0, 0)
        adjustment_layout.addWidget(self.adjustment_type_combo, 0, 1)
        adjustment_layout.addWidget(quantity_label, 1, 0)
        adjustment_layout.addWidget(self.quantity_spin, 1, 1)
        adjustment_layout.addWidget(new_qty_label, 1, 2)
        adjustment_layout.addWidget(self.new_qty_display, 1, 3)
        adjustment_layout.addWidget(notes_label, 2, 0)
        adjustment_layout.addWidget(self.notes_edit, 2, 1, 1, 3)
        
        # زر التنفيذ
        execute_button = QPushButton("تنفيذ التعديل")
        apply_arabic_style(execute_button, 10, bold=True)
        execute_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        layout.addWidget(product_group)
        layout.addWidget(adjustment_group)
        layout.addWidget(execute_button)
        layout.addStretch()
        
        # ربط الأحداث
        self.product_combo.currentTextChanged.connect(self.update_current_quantity)
        self.adjustment_type_combo.currentTextChanged.connect(self.calculate_new_quantity)
        self.quantity_spin.valueChanged.connect(self.calculate_new_quantity)
        execute_button.clicked.connect(self.execute_adjustment)
        
        return widget

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        export_button = QPushButton("تصدير إلى Excel")
        apply_arabic_style(export_button, 10)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        layout.addWidget(refresh_button)
        layout.addWidget(export_button)
        layout.addStretch()
        layout.addWidget(close_button)

        refresh_button.clicked.connect(self.load_data)
        export_button.clicked.connect(self.export_to_excel)
        close_button.clicked.connect(self.accept)

        return layout

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_inventory_data()
            self.load_products_combo()
            self.load_categories()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")

    def load_inventory_data(self):
        """تحميل بيانات المخزون من النظام الموحد"""
        try:
            # استخدام النظام الموحد للمخزون
            inventory_data = self.inventory_manager.get_all_products()

            print(f"✅ تم تحميل {len(inventory_data)} منتج من النظام الموحد")

            self.inventory_table.setRowCount(len(inventory_data))

            for row, item in enumerate(inventory_data):
                self.inventory_table.setItem(row, 0, QTableWidgetItem(str(item.get('id', ''))))
                self.inventory_table.setItem(row, 1, QTableWidgetItem(item.get('name', '')))
                self.inventory_table.setItem(row, 2, QTableWidgetItem(item.get('category', '')))

                # الكمية مع تلوين حسب الحالة
                quantity = item.get('current_stock', 0)
                min_stock = item.get('min_stock', 0)

                quantity_item = QTableWidgetItem(str(int(quantity)))
                if quantity <= min_stock:
                    quantity_item.setBackground(QColor("#fadbd8"))  # أحمر فاتح
                elif quantity <= min_stock * 2:
                    quantity_item.setBackground(QColor("#fef9e7"))  # أصفر فاتح
                else:
                    quantity_item.setBackground(QColor("#d5f4e6"))  # أخضر فاتح
                self.inventory_table.setItem(row, 3, quantity_item)

                self.inventory_table.setItem(row, 4, QTableWidgetItem(item.get('sale_unit', 'قطعة')))
                self.inventory_table.setItem(row, 5, QTableWidgetItem(str(min_stock)))

                # حالة المخزون
                if quantity <= 0:
                    status = "نفد المخزون"
                    status_color = "#e74c3c"
                elif quantity <= min_stock:
                    status = "مخزون منخفض"
                    status_color = "#f39c12"
                else:
                    status = "متوفر"
                    status_color = "#27ae60"

                status_item = QTableWidgetItem(status)
                status_item.setForeground(QColor(status_color))
                self.inventory_table.setItem(row, 6, status_item)

                self.inventory_table.setItem(row, 7, QTableWidgetItem('محدث'))

                # القيمة الإجمالية
                unit_price = item.get('sale_price', 0)
                total_value = quantity * unit_price
                self.inventory_table.setItem(row, 8, QTableWidgetItem(format_currency(total_value)))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المخزون: {e}")

    def load_products_combo(self):
        """تحميل قائمة المنتجات"""
        try:
            self.product_combo.clear()
            products = self.db_manager.fetch_all("""
                SELECT id, name FROM products WHERE is_active = 1 ORDER BY name
            """)

            for product in products:
                self.product_combo.addItem(product['name'], product['id'])

        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")

    def load_categories(self):
        """تحميل الفئات من جدول categories (التوافق مع إدارة التصنيف)"""
        try:
            self.category_filter.clear()
            self.category_filter.addItem("جميع الفئات")

            # قراءة من جدول categories المدار بواجهة إدارة التصنيف
            categories = self.db_manager.fetch_all("""
                SELECT name FROM categories WHERE is_active = 1 ORDER BY name
            """)

            for category in categories:
                if category['name']:
                    self.category_filter.addItem(category['name'])

            print(f"✅ تم تحميل {len(categories)} تصنيف من جدول categories")

        except Exception as e:
            print(f"❌ خطأ في تحميل الفئات من جدول categories: {e}")
            # في حالة عدم وجود جدول categories، استخدم الطريقة القديمة
            try:
                categories = self.db_manager.fetch_all("""
                    SELECT DISTINCT category FROM products WHERE is_active = 1 ORDER BY category
                """)

                for category in categories:
                    if category['category']:
                        self.category_filter.addItem(category['category'])

                print(f"⚠️ تم استخدام التصنيفات من جدول products (طريقة احتياطية)")
            except Exception as fallback_error:
                print(f"❌ خطأ في تحميل الفئات (طريقة احتياطية): {fallback_error}")

    def filter_inventory(self):
        """فلترة المخزون"""
        search_text = self.search_edit.text().lower()
        selected_category = self.category_filter.currentText()
        show_low_stock_only = self.low_stock_checkbox.isChecked()

        for row in range(self.inventory_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                product_name = self.inventory_table.item(row, 1).text().lower()
                if search_text not in product_name:
                    show_row = False

            # فلترة الفئة
            if selected_category != "جميع الفئات":
                category = self.inventory_table.item(row, 2).text()
                if category != selected_category:
                    show_row = False

            # فلترة المخزون المنخفض
            if show_low_stock_only:
                status = self.inventory_table.item(row, 6).text()
                if status not in ["نفد المخزون", "مخزون منخفض"]:
                    show_row = False

            self.inventory_table.setRowHidden(row, not show_row)

    def update_current_quantity(self):
        """تحديث الكمية الحالية للمنتج المختار"""
        try:
            product_id = self.product_combo.currentData()
            if product_id:
                quantity = self.db_manager.fetch_one("""
                    SELECT COALESCE(quantity, 0) as quantity
                    FROM inventory WHERE product_id = ?
                """, (product_id,))

                current_qty = quantity['quantity'] if quantity else 0
                self.current_qty_display.setText(str(int(current_qty)))
                self.calculate_new_quantity()

        except Exception as e:
            print(f"خطأ في تحديث الكمية: {e}")

    def calculate_new_quantity(self):
        """حساب الكمية الجديدة"""
        try:
            current_qty = int(self.current_qty_display.text())
            adjustment_qty = self.quantity_spin.value()
            adjustment_type = self.adjustment_type_combo.currentText()

            if adjustment_type == "إضافة":
                new_qty = current_qty + adjustment_qty
            elif adjustment_type == "خصم":
                new_qty = current_qty - adjustment_qty
            else:  # تعديل مباشر
                new_qty = adjustment_qty

            # التأكد من عدم السماح بالكميات السالبة
            new_qty = max(0, new_qty)

            self.new_qty_display.setText(str(new_qty))

            # تغيير لون الكمية الجديدة حسب النوع
            if adjustment_type == "إضافة":
                self.new_qty_display.setStyleSheet("color: #27ae60; font-weight: bold;")
            elif adjustment_type == "خصم":
                self.new_qty_display.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.new_qty_display.setStyleSheet("color: #3498db; font-weight: bold;")

        except:
            self.new_qty_display.setText("0")

    def execute_adjustment(self):
        """تنفيذ تعديل الكمية"""
        try:
            product_id = self.product_combo.currentData()
            if not product_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
                return

            current_qty = int(self.current_qty_display.text())
            new_qty = int(self.new_qty_display.text())
            adjustment_type = self.adjustment_type_combo.currentText()
            notes = self.notes_edit.toPlainText().strip()

            if current_qty == new_qty:
                QMessageBox.warning(self, "تحذير", "لا يوجد تغيير في الكمية")
                return

            # تأكيد العملية
            product_name = self.product_combo.currentText()
            reply = QMessageBox.question(
                self,
                "تأكيد التعديل",
                f"هل تريد تعديل كمية {product_name}؟\n\n"
                f"الكمية الحالية: {current_qty}\n"
                f"الكمية الجديدة: {new_qty}\n"
                f"نوع التعديل: {adjustment_type}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تنفيذ التعديل في قاعدة البيانات
                self.db_manager.execute_query("BEGIN TRANSACTION")

                try:
                    # تحديث الكمية في المخزون
                    self.db_manager.execute_query("""
                        UPDATE inventory
                        SET quantity = ?, last_updated = datetime('now')
                        WHERE product_id = ?
                    """, (new_qty, product_id))

                    # تسجيل حركة المخزون
                    self.db_manager.execute_query("""
                        INSERT INTO inventory_movements
                        (product_id, movement_type, quantity_before, quantity_after,
                         quantity_change, user_id, notes, movement_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
                    """, (product_id, adjustment_type, current_qty, new_qty,
                          new_qty - current_qty, self.current_user['id'], notes))

                    self.db_manager.execute_query("COMMIT")

                    QMessageBox.information(self, "تم", "تم تعديل الكمية بنجاح")

                    # إعادة تحميل البيانات
                    self.load_data()
                    self.update_current_quantity()

                    # إعادة تعيين النموذج
                    self.quantity_spin.setValue(0)
                    self.notes_edit.clear()

                except Exception as e:
                    self.db_manager.execute_query("ROLLBACK")
                    raise e

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ التعديل: {e}")

    def load_inventory_movements(self):
        """تحميل حركة المخزون"""
        try:
            from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
            to_date = self.to_date_edit.date().toString("yyyy-MM-dd")
            movement_type = self.movement_type_filter.currentText()

            query = """
                SELECT im.movement_date, p.name as product_name, im.movement_type,
                       im.quantity_change, im.quantity_before, im.quantity_after,
                       u.full_name as user_name, im.notes
                FROM inventory_movements im
                JOIN products p ON im.product_id = p.id
                LEFT JOIN users u ON im.user_id = u.id
                WHERE DATE(im.movement_date) BETWEEN ? AND ?
            """
            params = [from_date, to_date]

            if movement_type != "جميع الحركات":
                query += " AND im.movement_type = ?"
                params.append(movement_type)

            query += " ORDER BY im.movement_date DESC"

            movements = self.db_manager.fetch_all(query, params)

            self.movements_table.setRowCount(len(movements))

            for row, movement in enumerate(movements):
                self.movements_table.setItem(row, 0, QTableWidgetItem(movement['movement_date']))
                self.movements_table.setItem(row, 1, QTableWidgetItem(movement['product_name']))
                self.movements_table.setItem(row, 2, QTableWidgetItem(movement['movement_type']))

                # تلوين الكمية حسب النوع
                change_item = QTableWidgetItem(str(movement['quantity_change']))
                if movement['quantity_change'] > 0:
                    change_item.setForeground(QColor("#27ae60"))  # أخضر للإضافة
                else:
                    change_item.setForeground(QColor("#e74c3c"))  # أحمر للخصم
                self.movements_table.setItem(row, 3, change_item)

                self.movements_table.setItem(row, 4, QTableWidgetItem(str(movement['quantity_before'])))
                self.movements_table.setItem(row, 5, QTableWidgetItem(str(movement['quantity_after'])))
                self.movements_table.setItem(row, 6, QTableWidgetItem(movement['user_name'] or 'غير محدد'))
                self.movements_table.setItem(row, 7, QTableWidgetItem(movement['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل حركة المخزون: {e}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        QMessageBox.information(self, "قريباً", "ميزة التصدير إلى Excel قيد التطوير")

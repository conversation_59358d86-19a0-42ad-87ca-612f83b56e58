#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح إغلاق الصندوق
"""

import sys
import os
sys.path.append('src')

def test_cash_box_closure_complete():
    """اختبار إغلاق الصندوق الكامل"""
    
    print("🧪 اختبار إغلاق الصندوق الكامل...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # 1. فتح شيفت جديد
        print("\n1️⃣ فتح شيفت جديد...")
        shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
        
        if shift_opened:
            print("✅ تم فتح شيفت جديد")
        else:
            print("⚠️ الشيفت مفتوح مسبقاً")
        
        # التحقق من حالة الجلسة
        is_active_before = treasury_manager.is_session_active(current_user['id'])
        print(f"📊 حالة الجلسة قبل الإغلاق: {'مفتوحة' if is_active_before else 'مغلقة'}")
        
        # 2. إضافة مبيعات للصندوق (محاكاة)
        print("\n2️⃣ إضافة مبيعات للصندوق...")
        sales_added = treasury_manager.add_to_cash_box(
            user_id=current_user['id'],
            amount=150000,
            description="مبيعات اختبار إغلاق الصندوق",
            transaction_type="sale"
        )
        
        if sales_added:
            cash_box_balance = treasury_manager.get_cash_box_balance(current_user['id'])
            print(f"✅ تم إضافة مبيعات - رصيد الصندوق: {cash_box_balance:,} ل.س")
        else:
            print("❌ فشل في إضافة المبيعات")
            cash_box_balance = 0
        
        # 3. فحص الخزينة اليومية قبل الإغلاق
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"💰 الخزينة اليومية قبل الإغلاق: {daily_balance_before:,} ل.س")
        
        # 4. إغلاق الصندوق
        print("\n3️⃣ إغلاق الصندوق...")
        closure_success = treasury_manager.close_cash_box_to_daily_treasury(
            user_id=current_user['id'],
            actual_cash=cash_box_balance,
            notes="اختبار إغلاق الصندوق الكامل"
        )
        
        if closure_success:
            print("✅ تم إغلاق الصندوق بنجاح")
            
            # التحقق من حالة الجلسة بعد الإغلاق
            is_active_after = treasury_manager.is_session_active(current_user['id'])
            print(f"📊 حالة الجلسة بعد الإغلاق: {'مفتوحة' if is_active_after else 'مغلقة'}")
            
            if not is_active_after:
                print("✅ تم إغلاق الجلسة بشكل صحيح")
            else:
                print("❌ الجلسة ما زالت مفتوحة!")
                return False
            
            # فحص الخزينة اليومية بعد الإغلاق
            daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
            print(f"💰 الخزينة اليومية بعد الإغلاق: {daily_balance_after:,} ل.س")
            
            # التحقق من النقل
            if cash_box_balance > 0:
                expected_increase = cash_box_balance
                actual_increase = daily_balance_after - daily_balance_before
                
                if abs(actual_increase - expected_increase) < 1:
                    print(f"✅ تم نقل المبلغ بشكل صحيح: {actual_increase:,} ل.س")
                else:
                    print(f"❌ خطأ في النقل: متوقع {expected_increase:,} ل.س، فعلي {actual_increase:,} ل.س")
                    return False
            
            return True
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reopening_after_closure():
    """اختبار إعادة فتح الواجهة بعد الإغلاق"""
    
    print("\n🧪 اختبار إعادة فتح الواجهة بعد الإغلاق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.cash_box_closure_window import CashBoxClosureWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # التحقق من حالة الجلسة
        is_active = treasury_manager.is_session_active(current_user['id'])
        print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة'}")
        
        if is_active:
            print("❌ الجلسة ما زالت مفتوحة - يجب أن تكون مغلقة")
            return False
        
        # محاولة إنشاء واجهة إغلاق الصندوق
        print("🖥️ إنشاء واجهة إغلاق الصندوق...")
        
        try:
            window = CashBoxClosureWindow(db, treasury_manager, current_user)
            print("✅ تم إنشاء الواجهة")
            
            # التحقق من أن الواجهة تظهر عدم وجود شيفت مفتوح
            if hasattr(window, 'current_shift_id') and window.current_shift_id:
                print("❌ الواجهة تظهر وجود شيفت مفتوح!")
                return False
            else:
                print("✅ الواجهة تظهر عدم وجود شيفت مفتوح")
                return True
                
        except Exception as window_error:
            print(f"❌ خطأ في إنشاء الواجهة: {window_error}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعادة الفتح: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_after_closure():
    """اختبار تسجيل الدخول بعد إغلاق الصندوق"""
    
    print("\n🧪 اختبار تسجيل الدخول بعد إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # التحقق من حالة الجلسة قبل تسجيل الدخول
        is_active_before = treasury_manager.is_session_active(current_user['id'])
        print(f"📊 حالة الجلسة قبل تسجيل الدخول: {'مفتوحة' if is_active_before else 'مغلقة'}")
        
        if is_active_before:
            print("❌ الجلسة مفتوحة - يجب أن تكون مغلقة")
            return False
        
        # محاكاة تسجيل الدخول (فتح شيفت جديد)
        print("🔓 محاكاة تسجيل الدخول...")
        shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
        
        if shift_opened:
            print("✅ تم فتح شيفت جديد عند تسجيل الدخول")
            
            # التحقق من حالة الجلسة بعد تسجيل الدخول
            is_active_after = treasury_manager.is_session_active(current_user['id'])
            print(f"📊 حالة الجلسة بعد تسجيل الدخول: {'مفتوحة' if is_active_after else 'مغلقة'}")
            
            if is_active_after:
                print("✅ تم فتح جلسة جديدة بشكل صحيح")
                return True
            else:
                print("❌ فشل في فتح جلسة جديدة")
                return False
        else:
            print("❌ فشل في فتح شيفت جديد")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار إصلاح إغلاق الصندوق")
    print("=" * 60)
    
    # اختبار إغلاق الصندوق الكامل
    closure_test = test_cash_box_closure_complete()
    
    # اختبار إعادة فتح الواجهة بعد الإغلاق
    reopening_test = test_reopening_after_closure()
    
    # اختبار تسجيل الدخول بعد الإغلاق
    login_test = test_login_after_closure()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • إغلاق الصندوق الكامل: {'✅ يعمل' if closure_test else '❌ لا يعمل'}")
    print(f"  • إعادة فتح الواجهة: {'✅ تعمل' if reopening_test else '❌ لا تعمل'}")
    print(f"  • تسجيل الدخول بعد الإغلاق: {'✅ يعمل' if login_test else '❌ لا يعمل'}")
    
    if all([closure_test, reopening_test, login_test]):
        print("\n🎉 تم إصلاح مشكلة إغلاق الصندوق بالكامل!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح إغلاق الجلسة")
        print("    • إغلاق الجلسة في unified_treasury")
        print("    • إغلاق الصندوق في cash_boxes")
        print("    • عدم فتح شيفت جديد فوراً")
        
        print("  ✅ إصلاح حالة الشيفت")
        print("    • الجلسة تصبح مغلقة بعد الإغلاق")
        print("    • واجهة إغلاق الصندوق تظهر عدم وجود شيفت")
        print("    • تسجيل الدخول يفتح شيفت جديد")
        
        print("\n🚀 النظام الآن:")
        print("  • إغلاق الصندوق يغلق الشيفت فعلياً")
        print("  • إعادة فتح الواجهة تظهر عدم وجود شيفت")
        print("  • تسجيل الدخول يفتح شيفت جديد")
        print("  • لا توجد رسالة 'شيفت مفتوح' خاطئة")
        
        print("\n🎯 للاستخدام:")
        print("  1. أغلق الصندوق → الشيفت يُغلق فعلياً")
        print("  2. أعد فتح الواجهة → لا توجد بيانات قديمة")
        print("  3. أغلق البرنامج وأعد فتحه → لا توجد رسالة شيفت مفتوح")
        print("  4. سجل دخول جديد → يفتح شيفت جديد")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

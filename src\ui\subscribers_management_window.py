# -*- coding: utf-8 -*-
"""
نافذة إدارة المشتركين
Subscribers Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class SubscribersManagementWindow(QDialog):
    """نافذة إدارة المشتركين"""
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المشتركين")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        # تطبيق الخط العربي
        apply_arabic_style(self, 10)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة المشتركين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # أدوات البحث والتحكم
        controls_layout = self.create_controls()
        
        # جدول المشتركين
        self.subscribers_table = self.create_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addLayout(controls_layout)
        main_layout.addWidget(self.subscribers_table)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_controls(self):
        """إنشاء أدوات البحث والتحكم"""
        layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)
        
        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث بالاسم أو الهاتف...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # زر البحث
        search_button = QPushButton("بحث")
        apply_arabic_style(search_button, 10)
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(search_label)
        layout.addWidget(self.search_edit)
        layout.addWidget(search_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        
        # ربط الأحداث
        search_button.clicked.connect(self.search_subscribers)
        refresh_button.clicked.connect(self.load_data)
        self.search_edit.returnPressed.connect(self.search_subscribers)
        
        return layout
        
    def create_table(self):
        """إنشاء جدول المشتركين"""
        table = QTableWidget()
        apply_arabic_style(table, 9)
        
        # تعيين الأعمدة
        columns = ["الرقم", "الاسم", "الهاتف", "العنوان", "نوع الباقة", "نوع الراوتر", "حالة الاشتراك", "تاريخ التسجيل"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)
        
        # تنسيق الرأس
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)
        
        # تنسيق الأعمدة
        table.setColumnWidth(0, 60)   # الرقم
        table.setColumnWidth(1, 150)  # الاسم
        table.setColumnWidth(2, 120)  # الهاتف
        table.setColumnWidth(3, 200)  # العنوان
        table.setColumnWidth(4, 120)  # الباقة
        table.setColumnWidth(5, 120)  # الراوتر
        table.setColumnWidth(6, 100)  # الحالة
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر إضافة مشترك جديد
        add_button = QPushButton("إضافة مشترك جديد")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        # زر تعديل
        edit_button = QPushButton("تعديل المشترك")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر حذف
        delete_button = QPushButton("حذف المشترك")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر عرض التفاصيل
        details_button = QPushButton("عرض التفاصيل")
        apply_arabic_style(details_button, 10)
        details_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(details_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        # ربط الأحداث
        add_button.clicked.connect(self.add_subscriber)
        edit_button.clicked.connect(self.edit_subscriber)
        delete_button.clicked.connect(self.delete_subscriber)
        details_button.clicked.connect(self.show_details)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل بيانات المشتركين"""
        try:
            subscribers = self.db_manager.fetch_all("""
                SELECT id, name, phone, address, package_type, router_type, 
                       subscription_paid, created_at 
                FROM subscribers 
                ORDER BY created_at DESC
            """)
            
            self.subscribers_table.setRowCount(len(subscribers))
            
            for row, subscriber in enumerate(subscribers):
                # الرقم
                self.subscribers_table.setItem(row, 0, QTableWidgetItem(str(subscriber['id'])))
                
                # الاسم
                self.subscribers_table.setItem(row, 1, QTableWidgetItem(subscriber['name'] or ""))
                
                # الهاتف
                self.subscribers_table.setItem(row, 2, QTableWidgetItem(subscriber['phone'] or ""))
                
                # العنوان
                address = subscriber['address'] or ""
                if len(address) > 30:
                    address = address[:30] + "..."
                self.subscribers_table.setItem(row, 3, QTableWidgetItem(address))
                
                # نوع الباقة
                self.subscribers_table.setItem(row, 4, QTableWidgetItem(subscriber['package_type'] or "غير محدد"))
                
                # نوع الراوتر
                self.subscribers_table.setItem(row, 5, QTableWidgetItem(subscriber['router_type'] or "غير محدد"))
                
                # حالة الاشتراك
                status = "مدفوع" if subscriber['subscription_paid'] else "غير مدفوع"
                status_item = QTableWidgetItem(status)
                if subscriber['subscription_paid']:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.yellow)
                self.subscribers_table.setItem(row, 6, status_item)
                
                # تاريخ التسجيل
                date_str = subscriber['created_at'][:10] if subscriber['created_at'] else ""
                self.subscribers_table.setItem(row, 7, QTableWidgetItem(date_str))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def search_subscribers(self):
        """البحث في المشتركين"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_data()
            return
            
        try:
            subscribers = self.db_manager.fetch_all("""
                SELECT id, name, phone, address, package_type, router_type, 
                       subscription_paid, created_at 
                FROM subscribers 
                WHERE name LIKE ? OR phone LIKE ?
                ORDER BY created_at DESC
            """, (f"%{search_text}%", f"%{search_text}%"))
            
            self.subscribers_table.setRowCount(len(subscribers))
            
            for row, subscriber in enumerate(subscribers):
                # نفس منطق load_data
                self.subscribers_table.setItem(row, 0, QTableWidgetItem(str(subscriber['id'])))
                self.subscribers_table.setItem(row, 1, QTableWidgetItem(subscriber['name'] or ""))
                self.subscribers_table.setItem(row, 2, QTableWidgetItem(subscriber['phone'] or ""))
                
                address = subscriber['address'] or ""
                if len(address) > 30:
                    address = address[:30] + "..."
                self.subscribers_table.setItem(row, 3, QTableWidgetItem(address))
                
                self.subscribers_table.setItem(row, 4, QTableWidgetItem(subscriber['package_type'] or "غير محدد"))
                self.subscribers_table.setItem(row, 5, QTableWidgetItem(subscriber['router_type'] or "غير محدد"))
                
                status = "مدفوع" if subscriber['subscription_paid'] else "غير مدفوع"
                status_item = QTableWidgetItem(status)
                if subscriber['subscription_paid']:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.yellow)
                self.subscribers_table.setItem(row, 6, status_item)
                
                date_str = subscriber['created_at'][:10] if subscriber['created_at'] else ""
                self.subscribers_table.setItem(row, 7, QTableWidgetItem(date_str))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def add_subscriber(self):
        """إضافة مشترك جديد"""
        from .new_subscription_window import NewSubscriptionWindow
        
        dialog = NewSubscriptionWindow(self.db_manager, self.config_manager, self.current_user, self)
        if dialog.exec_() == dialog.Accepted:
            self.load_data()
            
    def edit_subscriber(self):
        """تعديل مشترك"""
        current_row = self.subscribers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترك للتعديل")
            return
            
        QMessageBox.information(self, "قريباً", "ميزة تعديل المشترك قيد التطوير")
        
    def delete_subscriber(self):
        """حذف مشترك"""
        current_row = self.subscribers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترك للحذف")
            return
            
        subscriber_id = self.subscribers_table.item(current_row, 0).text()
        subscriber_name = self.subscribers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف المشترك '{subscriber_name}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM subscribers WHERE id = ?", (subscriber_id,))
                QMessageBox.information(self, "نجح", "تم حذف المشترك بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المشترك: {e}")
                
    def show_details(self):
        """عرض تفاصيل المشترك"""
        current_row = self.subscribers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترك لعرض التفاصيل")
            return
            
        QMessageBox.information(self, "قريباً", "ميزة عرض التفاصيل قيد التطوير")

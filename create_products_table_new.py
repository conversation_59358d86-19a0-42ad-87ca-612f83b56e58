#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول المنتجات الجديد مع دعم أسعار الشراء والبيع
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def create_products_table():
    """إنشاء جدول المنتجات الجديد"""
    
    print("🔄 إنشاء جدول المنتجات الجديد...")
    
    # الاتصال بقاعدة البيانات
    db_path = Path("data/company_system.db")
    if not db_path.exists():
        db_path = Path("company_system.db")
    
    db_manager = DatabaseManager(str(db_path))
    
    try:
        # إنشاء جدول المنتجات الجديد
        db_manager.execute_query("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT,
                description TEXT,
                
                -- أسعار ووحدات
                purchase_price REAL DEFAULT 0,
                sale_price REAL DEFAULT 0,
                purchase_unit TEXT DEFAULT 'قطعة',
                sale_unit TEXT DEFAULT 'قطعة',
                conversion_factor REAL DEFAULT 1.0,
                
                -- معلومات المخزون
                stock_quantity REAL DEFAULT 0,
                min_stock_level REAL DEFAULT 0,
                max_stock_level REAL DEFAULT 0,
                
                -- معلومات المورد
                supplier_id INTEGER,
                barcode TEXT,
                
                -- معلومات مالية
                profit_margin REAL DEFAULT 0,
                last_purchase_date DATE,
                last_purchase_price REAL,
                
                -- معلومات النظام
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                -- للتوافق مع النظام القديم
                unit_price REAL GENERATED ALWAYS AS (sale_price) STORED,
                unit_type TEXT GENERATED ALWAYS AS (sale_unit) STORED
            )
        """)
        
        print("✅ تم إنشاء جدول المنتجات")
        
        # إنشاء الفهارس
        indexes = [
            ("idx_products_name", "name"),
            ("idx_products_category", "category"),
            ("idx_products_barcode", "barcode"),
            ("idx_products_supplier", "supplier_id"),
            ("idx_products_active", "is_active"),
            ("idx_products_stock", "stock_quantity"),
            ("idx_products_purchase_unit", "purchase_unit"),
            ("idx_products_sale_unit", "sale_unit")
        ]
        
        for idx_name, column in indexes:
            try:
                db_manager.execute_query(f"""
                    CREATE INDEX IF NOT EXISTS {idx_name} ON products({column})
                """)
                print(f"✅ تم إنشاء فهرس {idx_name}")
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس {idx_name}: {e}")
        
        # إضافة منتجات تجريبية
        sample_products = [
            {
                'name': 'راوتر TP-Link',
                'category': 'router',
                'description': 'راوتر لاسلكي عالي السرعة',
                'purchase_price': 80000,
                'sale_price': 100000,
                'purchase_unit': 'قطعة',
                'sale_unit': 'قطعة',
                'conversion_factor': 1.0,
                'stock_quantity': 50,
                'min_stock_level': 10,
                'profit_margin': 25.0
            },
            {
                'name': 'كبل شبكة Cat6',
                'category': 'cable',
                'description': 'كبل شبكة عالي الجودة',
                'purchase_price': 150000,
                'sale_price': 200,
                'purchase_unit': 'بكرة',
                'sale_unit': 'متر',
                'conversion_factor': 305.0,  # بكرة واحدة = 305 متر
                'stock_quantity': 10,
                'min_stock_level': 2,
                'profit_margin': 33.3
            },
            {
                'name': 'موزع 8 منافذ',
                'category': 'switch',
                'description': 'موزع شبكة 8 منافذ',
                'purchase_price': 45000,
                'sale_price': 60000,
                'purchase_unit': 'قطعة',
                'sale_unit': 'قطعة',
                'conversion_factor': 1.0,
                'stock_quantity': 25,
                'min_stock_level': 5,
                'profit_margin': 33.3
            }
        ]
        
        for product in sample_products:
            # التحقق من عدم وجود المنتج
            existing = db_manager.fetch_one("SELECT id FROM products WHERE name = ?", (product['name'],))
            if not existing:
                db_manager.execute_query("""
                    INSERT INTO products (
                        name, category, description, purchase_price, sale_price,
                        purchase_unit, sale_unit, conversion_factor, stock_quantity,
                        min_stock_level, profit_margin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    product['name'], product['category'], product['description'],
                    product['purchase_price'], product['sale_price'],
                    product['purchase_unit'], product['sale_unit'], product['conversion_factor'],
                    product['stock_quantity'], product['min_stock_level'], product['profit_margin']
                ))
                print(f"✅ تم إضافة منتج تجريبي: {product['name']}")
        
        # عرض بنية الجدول
        table_info = db_manager.fetch_all("PRAGMA table_info(products)")
        print(f"\n📊 بنية جدول المنتجات:")
        for column in table_info:
            default_value = f" - Default: {column[4]}" if column[4] else ""
            print(f"  • {column[1]} - {column[2]}{default_value}")
        
        # عرض المنتجات
        products = db_manager.fetch_all("""
            SELECT name, purchase_price, sale_price, purchase_unit, sale_unit, 
                   conversion_factor, stock_quantity, profit_margin
            FROM products
        """)
        
        print(f"\n📝 المنتجات الموجودة ({len(products)}):")
        for product in products:
            print(f"  • {product[0]}:")
            print(f"    - شراء: {product[1]:,} ل.س/{product[3]}")
            print(f"    - بيع: {product[2]:,} ل.س/{product[4]}")
            print(f"    - تحويل: 1 {product[3]} = {product[5]} {product[4]}")
            print(f"    - مخزون: {product[6]} {product[3]}")
            print(f"    - ربح: {product[7]:.1f}%")
        
        print("\n🎉 تم إنشاء جدول المنتجات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول المنتجات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    create_products_table()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة تسجيل الدخول
"""

import sys
import os
import hashlib
sys.path.append('src')

def check_and_fix_users():
    """فحص وإصلاح جدول المستخدمين"""
    
    print("🔧 فحص وإصلاح جدول المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # فحص وجود الجدول
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        
        if not tables:
            print("❌ جدول المستخدمين غير موجود - سيتم إنشاؤه")
            
            # إنشاء جدول المستخدمين
            db.execute_query("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    role TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME
                )
            """)
            print("✅ تم إنشاء جدول المستخدمين")
        
        # فحص المستخدمين الموجودين
        users = db.fetch_all("SELECT id, username, password_hash, is_active FROM users")
        print(f"👥 المستخدمين الموجودين ({len(users)}):")
        
        for user in users:
            print(f"  • ID: {user[0]}, اسم المستخدم: '{user[1]}', نشط: {user[3]}")
            if user[2]:
                print(f"    كلمة المرور: {user[2][:20]}...")
            else:
                print(f"    كلمة المرور: غير موجودة")
        
        # إنشاء/تحديث المستخدم admin
        admin_password = '123'
        hashed_password = hashlib.sha256(admin_password.encode()).hexdigest()
        
        print(f"\n🔐 إعداد المستخدم admin:")
        print(f"كلمة المرور: '{admin_password}'")
        print(f"كلمة المرور المشفرة: {hashed_password}")
        
        # حذف المستخدم admin إذا كان موجوداً
        db.execute_query("DELETE FROM users WHERE username = 'admin'")
        
        # إنشاء المستخدم admin
        db.execute_query("""
            INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
            VALUES (?, ?, ?, ?, 1, datetime('now'))
        """, ('admin', hashed_password, 'المدير', 'admin'))
        
        print("✅ تم إنشاء المستخدم admin")
        
        # التحقق من النتيجة
        admin_user = db.fetch_one("SELECT * FROM users WHERE username = 'admin'")
        
        if admin_user:
            print(f"📊 بيانات المستخدم admin:")
            print(f"  • اسم المستخدم: '{admin_user['username']}'")
            print(f"  • كلمة المرور المشفرة: {admin_user['password_hash']}")
            print(f"  • الاسم الكامل: {admin_user['full_name']}")
            print(f"  • الدور: {admin_user['role']}")
            print(f"  • نشط: {admin_user['is_active']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_process():
    """اختبار عملية تسجيل الدخول"""
    
    print("\n🧪 اختبار عملية تسجيل الدخول...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # بيانات تسجيل الدخول
        username = 'admin'
        password = '123'
        
        print(f"اسم المستخدم: '{username}'")
        print(f"كلمة المرور: '{password}'")
        
        # تشفير كلمة المرور (نفس الطريقة في نافذة تسجيل الدخول)
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"كلمة المرور المشفرة: {hashed_password}")
        
        # البحث عن المستخدم بالاسم فقط أولاً
        user_check = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
        
        if user_check:
            print("✅ المستخدم موجود")
            print(f"كلمة المرور في قاعدة البيانات: {user_check['password_hash']}")
            print(f"المستخدم نشط: {user_check['is_active']}")
            
            if user_check['password_hash'] == hashed_password:
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور خاطئة")
                print(f"المتوقع: {user_check['password_hash']}")
                print(f"المدخل: {hashed_password}")
        else:
            print("❌ المستخدم غير موجود")
        
        # البحث الكامل (نفس الاستعلام في نافذة تسجيل الدخول)
        user = db.fetch_one("""
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, hashed_password))
        
        if user:
            print("✅ تسجيل الدخول نجح!")
            print(f"مرحباً {user['full_name']} ({user['role']})")
            return True
        else:
            print("❌ تسجيل الدخول فشل!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_additional_users():
    """إنشاء مستخدمين إضافيين"""
    
    print("\n👥 إنشاء مستخدمين إضافيين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        additional_users = [
            ('user1', '123', 'مستخدم 1', 'user'),
            ('cashier', '123', 'أمين الصندوق', 'cashier'),
            ('manager', '123', 'المدير العام', 'manager')
        ]
        
        for username, password, full_name, role in additional_users:
            # تشفير كلمة المرور
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # حذف المستخدم إذا كان موجوداً
            db.execute_query("DELETE FROM users WHERE username = ?", (username,))
            
            # إنشاء المستخدم
            db.execute_query("""
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (?, ?, ?, ?, 1, datetime('now'))
            """, (username, hashed_password, full_name, role))
            
            print(f"✅ تم إنشاء المستخدم: {username} / {password} ({role})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين الإضافيين: {e}")
        return False

def test_all_users():
    """اختبار جميع المستخدمين"""
    
    print("\n🧪 اختبار جميع المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # الحصول على جميع المستخدمين
        users = db.fetch_all("SELECT username FROM users WHERE is_active = 1")
        
        success_count = 0
        
        for user in users:
            username = user['username']
            password = '123'  # كلمة المرور الموحدة
            
            # تشفير كلمة المرور
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # اختبار تسجيل الدخول
            login_user = db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, hashed_password))
            
            if login_user:
                print(f"✅ {username} / {password} - نجح")
                success_count += 1
            else:
                print(f"❌ {username} / {password} - فشل")
        
        print(f"\n📊 النتيجة: {success_count}/{len(users)} مستخدمين يعملون")
        
        return success_count == len(users)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جميع المستخدمين: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مشكلة تسجيل الدخول")
    print("=" * 50)
    
    # فحص وإصلاح جدول المستخدمين
    users_fixed = check_and_fix_users()
    
    # اختبار عملية تسجيل الدخول
    login_test = test_login_process()
    
    # إنشاء مستخدمين إضافيين
    additional_users = create_additional_users()
    
    # اختبار جميع المستخدمين
    all_users_test = test_all_users()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"  • إصلاح جدول المستخدمين: {'✅ نجح' if users_fixed else '❌ فشل'}")
    print(f"  • اختبار تسجيل الدخول: {'✅ نجح' if login_test else '❌ فشل'}")
    print(f"  • إنشاء مستخدمين إضافيين: {'✅ نجح' if additional_users else '❌ فشل'}")
    print(f"  • اختبار جميع المستخدمين: {'✅ نجح' if all_users_test else '❌ فشل'}")
    
    if all([users_fixed, login_test, additional_users, all_users_test]):
        print("\n🎉 تم إصلاح مشكلة تسجيل الدخول بنجاح!")
        
        print("\n📋 المستخدمين المتاحين:")
        print("  • admin / 123 (المدير)")
        print("  • user1 / 123 (مستخدم)")
        print("  • cashier / 123 (أمين الصندوق)")
        print("  • manager / 123 (المدير العام)")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. استخدم أي من المستخدمين أعلاه")
        print("  3. تسجيل الدخول يجب أن يعمل الآن!")
        
        print("\n💡 ملاحظات:")
        print("  • جميع كلمات المرور: 123")
        print("  • جميع المستخدمين نشطين")
        print("  • تم تشفير كلمات المرور بـ SHA256")
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")
        
        if not users_fixed:
            print("  🔧 مراجعة جدول المستخدمين")
        if not login_test:
            print("  🔧 مراجعة عملية تسجيل الدخول")

if __name__ == "__main__":
    main()

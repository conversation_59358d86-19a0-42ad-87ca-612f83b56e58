#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة الشركة
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.append('src')

def main():
    """الدالة الرئيسية"""
    
    print("🚀 تشغيل نظام إدارة الشركة...")
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import QDir
        
        print("✅ تم تحميل PyQt5")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الترميز
        app.setApplicationName("نظام إدارة الشركة")
        app.setApplicationVersion("1.0")
        
        # إنشاء مجلد البيانات
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        print("✅ تم إنشاء مجلد البيانات")
        
        # استيراد الوحدات
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        
        print("✅ تم تحميل الوحدات")
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager('.')
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # إنشاء واجهة تسجيل الدخول
        login_window = LoginWindow(db_manager)
        
        print("✅ تم إنشاء واجهة تسجيل الدخول")
        
        # عرض واجهة تسجيل الدخول
        if login_window.exec_() == login_window.Accepted:
            current_user = login_window.current_user
            
            if current_user:
                print(f"✅ تم تسجيل الدخول: {current_user['username']}")
                
                # إنشاء مدير الإعدادات
                from utils.config_manager import ConfigManager
                config_manager = ConfigManager()

                # إنشاء الواجهة الرئيسية
                main_window = MainWindow(db_manager, config_manager, current_user)
                main_window.show()
                
                print("✅ تم عرض الواجهة الرئيسية")
                
                # تشغيل التطبيق
                return app.exec_()
            else:
                print("❌ فشل في تسجيل الدخول")
                return 1
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 1
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت PyQt5: pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

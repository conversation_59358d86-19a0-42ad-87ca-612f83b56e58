#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة نقل الخزينة مع النظام الجديد
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager

def test_treasury_transfer():
    """اختبار واجهة نقل الخزينة"""
    
    print("🧪 اختبار واجهة نقل الخزينة مع النظام الجديد...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = TreasuryManager(db)
    
    try:
        print("\n=== 1. إعداد البيانات الأولية ===")
        
        # إعادة تعيين الخزينة اليومية
        treasury_manager.reset_daily_treasury()
        print("✅ تم إعادة تعيين الخزينة اليومية")
        
        # إضافة مبيعات ومصاريف للخزينة اليومية
        sales_amount = 800000
        expenses_amount = 100000
        
        treasury_manager.update_daily_treasury(
            currency_type='SYP',
            sales_amount=sales_amount,
            expenses_amount=expenses_amount,
            user_id=1
        )
        print(f"✅ تم إضافة مبيعات: {sales_amount:,} ل.س")
        print(f"✅ تم إضافة مصاريف: {expenses_amount:,} ل.س")
        
        # إضافة دولار من صرف العملة
        treasury_manager.process_currency_exchange(
            from_currency='SYP',
            to_currency='USD',
            amount_from=150000,
            exchange_rate=15000,
            user_id=1
        )
        print(f"✅ تم صرف عملة: 150,000 ل.س → $10.00")
        
        print("\n=== 2. عرض الأرصدة قبل النقل ===")
        
        # الخزينة اليومية
        daily_syp = treasury_manager.get_daily_treasury_balance('SYP')
        daily_usd = treasury_manager.get_daily_treasury_balance('USD')
        
        print(f"📊 الخزينة اليومية:")
        print(f"  • الليرة السورية: {daily_syp:,} ل.س")
        print(f"  • الدولار الأمريكي: ${daily_usd:.2f}")
        
        # الخزينة الرئيسية
        main_syp = treasury_manager.get_main_treasury_balance('SYP')
        main_usd = treasury_manager.get_main_treasury_balance('USD')
        
        print(f"📊 الخزينة الرئيسية:")
        print(f"  • الليرة السورية: {main_syp:,} ل.س")
        print(f"  • الدولار الأمريكي: ${main_usd:.2f}")
        
        print("\n=== 3. محاكاة نقل جزئي ===")
        
        # نقل جزء من الليرة السورية
        transfer_syp_amount = 300000
        if daily_syp >= transfer_syp_amount:
            success = treasury_manager.transfer_to_main_treasury(
                currency_type='SYP',
                amount=transfer_syp_amount,
                notes='نقل جزئي - اختبار',
                user_id=1
            )
            
            if success:
                print(f"✅ تم نقل {transfer_syp_amount:,} ل.س للخزينة الرئيسية")
            else:
                print(f"❌ فشل في نقل الليرة السورية")
        else:
            print(f"❌ الرصيد غير كافي للنقل: متاح {daily_syp:,} ل.س")
        
        # نقل كامل الدولار
        if daily_usd > 0:
            success = treasury_manager.transfer_to_main_treasury(
                currency_type='USD',
                amount=daily_usd,
                notes='نقل كامل الدولار - اختبار',
                user_id=1
            )
            
            if success:
                print(f"✅ تم نقل ${daily_usd:.2f} للخزينة الرئيسية")
            else:
                print(f"❌ فشل في نقل الدولار")
        else:
            print(f"⚠️ لا يوجد دولار للنقل")
        
        print("\n=== 4. عرض الأرصدة بعد النقل ===")
        
        # الخزينة اليومية بعد النقل
        daily_syp_after = treasury_manager.get_daily_treasury_balance('SYP')
        daily_usd_after = treasury_manager.get_daily_treasury_balance('USD')
        
        print(f"📊 الخزينة اليومية بعد النقل:")
        print(f"  • الليرة السورية: {daily_syp_after:,} ل.س")
        print(f"  • الدولار الأمريكي: ${daily_usd_after:.2f}")
        
        # الخزينة الرئيسية بعد النقل
        main_syp_after = treasury_manager.get_main_treasury_balance('SYP')
        main_usd_after = treasury_manager.get_main_treasury_balance('USD')
        
        print(f"📊 الخزينة الرئيسية بعد النقل:")
        print(f"  • الليرة السورية: {main_syp_after:,} ل.س")
        print(f"  • الدولار الأمريكي: ${main_usd_after:.2f}")
        
        # حساب التغيير
        syp_transferred = main_syp_after - main_syp
        usd_transferred = main_usd_after - main_usd
        
        print(f"\n📈 المبالغ المنقولة:")
        print(f"  • الليرة السورية: {syp_transferred:+,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_transferred:+.2f}")
        
        print("\n=== 5. عرض سجل النقلات ===")
        
        # عرض آخر النقلات
        transfers = db.fetch_all("""
            SELECT transfer_date, currency_type, amount, notes, created_at
            FROM treasury_transfers 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        print(f"📋 آخر النقلات ({len(transfers)}):")
        for transfer in transfers:
            date = transfer[0]
            currency = transfer[1]
            amount = transfer[2]
            notes = transfer[3]
            created = transfer[4]
            
            currency_symbol = "ل.س" if currency == 'SYP' else "$"
            print(f"  • {date}: {amount:,.2f} {currency_symbol} - {notes} ({created})")
        
        print("\n=== 6. اختبار واجهة نقل الخزينة ===")
        
        print("📋 البيانات التي ستظهر في واجهة نقل الخزينة:")
        
        # محاكاة ما تعرضه الواجهة
        current_daily_syp = treasury_manager.get_daily_treasury_balance('SYP')
        current_daily_usd = treasury_manager.get_daily_treasury_balance('USD')
        current_main_syp = treasury_manager.get_main_treasury_balance('SYP')
        current_main_usd = treasury_manager.get_main_treasury_balance('USD')
        
        print(f"  الخزينة اليومية (المصدر):")
        print(f"    • الليرة السورية: {current_daily_syp:,} ل.س")
        print(f"    • الدولار الأمريكي: ${current_daily_usd:.2f}")
        
        print(f"  الخزينة الرئيسية (الهدف):")
        print(f"    • الليرة السورية: {current_main_syp:,} ل.س")
        print(f"    • الدولار الأمريكي: ${current_main_usd:.2f}")
        
        # التحقق من إمكانية النقل
        if current_daily_syp > 0 or current_daily_usd > 0:
            print(f"  ✅ يمكن النقل من الخزينة اليومية")
        else:
            print(f"  ⚠️ لا توجد أموال للنقل في الخزينة اليومية")
        
        print(f"\n🎉 انتهى اختبار واجهة نقل الخزينة بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_treasury_transfer()

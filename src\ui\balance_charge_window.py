# -*- coding: utf-8 -*-
"""
نافذة شحن الرصيد
Balance Charge Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTime, QDateTime, QDate
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class BalanceChargeWindow(QDialog):
    """نافذة شحن الرصيد"""
    
    charge_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("شحن رصيد الموزعين")
        self.setGeometry(100, 100, 600, 500)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("شحن رصيد الموزعين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # معلومات الشحن
        charge_info_group = self.create_charge_info_group()
        
        # تفاصيل الدفع
        payment_group = self.create_payment_group()
        
        # ملاحظات
        notes_group = self.create_notes_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(charge_info_group)
        main_layout.addWidget(payment_group)
        main_layout.addWidget(notes_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_charge_info_group(self):
        """إنشاء مجموعة معلومات الشحن"""
        group = QGroupBox("معلومات الشحن")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # الموزع
        distributor_label = QLabel("الموزع:")
        apply_arabic_style(distributor_label, 10)
        self.distributor_combo = QComboBox()
        apply_arabic_style(self.distributor_combo, 10)
        
        # الرصيد الحالي
        current_balance_label = QLabel("الرصيد الحالي:")
        apply_arabic_style(current_balance_label, 10)
        self.current_balance_label = QLabel("0 ل.س")
        apply_arabic_style(self.current_balance_label, 10, bold=True)
        self.current_balance_label.setStyleSheet("color: #3498db;")
        
        # مبلغ الشحن
        charge_amount_label = QLabel("مبلغ الشحن:")
        apply_arabic_style(charge_amount_label, 10)
        self.charge_amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.charge_amount_spin, 10)
        self.charge_amount_spin.setRange(1000, 10000000)
        self.charge_amount_spin.setSuffix(" ل.س")
        self.charge_amount_spin.setValue(50000)
        
        # الرصيد بعد الشحن
        new_balance_label = QLabel("الرصيد بعد الشحن:")
        apply_arabic_style(new_balance_label, 10)
        self.new_balance_label = QLabel("50,000 ل.س")
        apply_arabic_style(self.new_balance_label, 10, bold=True)
        self.new_balance_label.setStyleSheet("color: #27ae60;")
        
        # تاريخ الشحن
        date_label = QLabel("تاريخ الشحن:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        layout.addWidget(distributor_label, 0, 0)
        layout.addWidget(self.distributor_combo, 0, 1)
        layout.addWidget(current_balance_label, 0, 2)
        layout.addWidget(self.current_balance_label, 0, 3)
        layout.addWidget(charge_amount_label, 1, 0)
        layout.addWidget(self.charge_amount_spin, 1, 1)
        layout.addWidget(new_balance_label, 1, 2)
        layout.addWidget(self.new_balance_label, 1, 3)
        layout.addWidget(date_label, 2, 0)
        layout.addWidget(self.date_edit, 2, 1, 1, 3)
        
        return group
        
    def create_payment_group(self):
        """إنشاء مجموعة تفاصيل الدفع"""
        group = QGroupBox("تفاصيل الدفع")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # طريقة الدفع
        payment_method_label = QLabel("طريقة الدفع:")
        apply_arabic_style(payment_method_label, 10)
        self.payment_method_combo = QComboBox()
        apply_arabic_style(self.payment_method_combo, 10)
        self.payment_method_combo.addItems([
            "نقداً",
            "تحويل بنكي",
            "شيك",
            "بطاقة ائتمان",
            "محفظة إلكترونية"
        ])
        
        # رقم المرجع
        reference_label = QLabel("رقم المرجع:")
        apply_arabic_style(reference_label, 10)
        self.reference_edit = QLineEdit()
        apply_arabic_style(self.reference_edit, 10)
        self.reference_edit.setPlaceholderText("رقم الشيك أو التحويل...")
        
        # البنك
        bank_label = QLabel("البنك:")
        apply_arabic_style(bank_label, 10)
        self.bank_edit = QLineEdit()
        apply_arabic_style(self.bank_edit, 10)
        self.bank_edit.setPlaceholderText("اسم البنك (اختياري)")
        
        # العمولة
        commission_label = QLabel("العمولة (%):")
        apply_arabic_style(commission_label, 10)
        self.commission_spin = QDoubleSpinBox()
        apply_arabic_style(self.commission_spin, 10)
        self.commission_spin.setRange(0, 50)
        self.commission_spin.setSuffix("%")
        self.commission_spin.setValue(5)
        
        # قيمة العمولة
        commission_amount_label = QLabel("قيمة العمولة:")
        apply_arabic_style(commission_amount_label, 10)
        self.commission_amount_label = QLabel("2,500 ل.س")
        apply_arabic_style(self.commission_amount_label, 10, bold=True)
        self.commission_amount_label.setStyleSheet("color: #f39c12;")
        
        layout.addWidget(payment_method_label, 0, 0)
        layout.addWidget(self.payment_method_combo, 0, 1)
        layout.addWidget(reference_label, 0, 2)
        layout.addWidget(self.reference_edit, 0, 3)
        layout.addWidget(bank_label, 1, 0)
        layout.addWidget(self.bank_edit, 1, 1)
        layout.addWidget(commission_label, 1, 2)
        layout.addWidget(self.commission_spin, 1, 3)
        layout.addWidget(commission_amount_label, 2, 0)
        layout.addWidget(self.commission_amount_label, 2, 1, 1, 3)
        
        return group
        
    def create_notes_group(self):
        """إنشاء مجموعة الملاحظات"""
        group = QGroupBox("ملاحظات")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(80)
        
        layout.addWidget(self.notes_edit)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        charge_button = QPushButton("تأكيد الشحن")
        apply_arabic_style(charge_button, 10, bold=True)
        charge_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(charge_button)
        
        charge_button.clicked.connect(self.confirm_charge)
        cancel_button.clicked.connect(self.reject)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل الموزعين من جدول distributors
            print("تحميل الموزعين من جدول distributors...")
            distributors = self.db_manager.fetch_all("""
                SELECT id, name, phone, balance as current_balance, address, is_active
                FROM distributors
                WHERE is_active = 1
                ORDER BY name
            """)

            print(f"تم العثور على {len(distributors)} موزع نشط")

            self.distributor_combo.clear()
            if not distributors:
                self.distributor_combo.addItem("لا توجد موزعين نشطين", None)
                print("لا توجد موزعين نشطين في قاعدة البيانات")
            else:
                for distributor in distributors:
                    # تحويل sqlite3.Row إلى قاموس آمن
                    distributor_dict = {
                        'id': distributor[0],  # id
                        'name': distributor[1],  # name
                        'phone': distributor[2],  # phone
                        'current_balance': distributor[3] if distributor[3] is not None else 0,  # balance
                        'address': distributor[4] if len(distributor) > 4 else '',  # address
                        'is_active': distributor[5] if len(distributor) > 5 else 1  # is_active
                    }

                    display_name = f"{distributor_dict['name']} - {distributor_dict['phone']}"
                    self.distributor_combo.addItem(display_name, distributor_dict)
                    print(f"تم إضافة الموزع: {distributor_dict['name']} - الرصيد: {distributor_dict['current_balance']}")

                # تحديث الرصيد الحالي للموزع الأول
                if distributors:
                    self.update_current_balance()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.distributor_combo.currentIndexChanged.connect(self.update_current_balance)
        self.charge_amount_spin.valueChanged.connect(self.calculate_new_balance)
        self.commission_spin.valueChanged.connect(self.calculate_commission)
        
    def update_current_balance(self):
        """تحديث الرصيد الحالي"""
        print("=== تحديث الرصيد الحالي ===")
        if self.distributor_combo.currentIndex() >= 0:
            distributor = self.distributor_combo.currentData()
            print(f"الموزع المختار: {distributor}")
            if distributor:
                current_balance = distributor.get('current_balance', 0)
                print(f"الرصيد الحالي: {current_balance}")
                self.current_balance_label.setText(format_currency(current_balance))
                self.calculate_new_balance()
            else:
                print("لا يوجد بيانات للموزع المختار")
                self.current_balance_label.setText("0 ل.س")
        else:
            print("لم يتم اختيار موزع")
            self.current_balance_label.setText("0 ل.س")
                
    def calculate_new_balance(self):
        """حساب الرصيد الجديد"""
        try:
            print("=== حساب الرصيد الجديد ===")
            current_balance_text = self.current_balance_label.text()
            print(f"نص الرصيد الحالي: '{current_balance_text}'")

            # تنظيف النص وتحويله لرقم
            clean_text = current_balance_text.replace("ل.س", "").replace(",", "").strip()
            current_balance = float(clean_text) if clean_text else 0
            print(f"الرصيد الحالي (رقم): {current_balance}")

            charge_amount = self.charge_amount_spin.value()
            print(f"مبلغ الشحن: {charge_amount}")

            new_balance = current_balance + charge_amount
            print(f"الرصيد الجديد: {new_balance}")

            self.new_balance_label.setText(format_currency(new_balance))
            self.calculate_commission()
        except Exception as e:
            print(f"خطأ في حساب الرصيد الجديد: {e}")
            self.new_balance_label.setText("0 ل.س")
            
    def calculate_commission(self):
        """حساب العمولة"""
        try:
            charge_amount = self.charge_amount_spin.value()
            commission_rate = self.commission_spin.value() / 100
            commission_amount = charge_amount * commission_rate
            
            self.commission_amount_label.setText(format_currency(commission_amount))
        except:
            pass
            
    def confirm_charge(self):
        """تأكيد الشحن"""
        if self.distributor_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار موزع")
            return
            
        if self.charge_amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح للشحن")
            return
            
        distributor = self.distributor_combo.currentData()

        # التأكد من أن distributor هو قاموس
        if not isinstance(distributor, dict):
            QMessageBox.critical(self, "خطأ", "خطأ في بيانات الموزع")
            return

        # تأكيد العملية
        distributor_name = distributor.get('name', 'موزع غير معروف')
        reply = QMessageBox.question(
            self,
            "تأكيد الشحن",
            f"هل تريد شحن {format_currency(self.charge_amount_spin.value())} "
            f"لرصيد الموزع {distributor_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # بيانات الشحن
                charge_data = {
                    'distributor_id': distributor.get('id', 0),
                    'distributor_name': distributor.get('name', 'موزع غير معروف'),
                    'current_balance': float(self.current_balance_label.text().replace("ل.س", "").replace(",", "").strip()),
                    'charge_amount': self.charge_amount_spin.value(),
                    'new_balance': float(self.new_balance_label.text().replace("ل.س", "").replace(",", "").strip()),
                    'payment_method': self.payment_method_combo.currentText(),
                    'reference_number': self.reference_edit.text().strip(),
                    'bank': self.bank_edit.text().strip(),
                    'commission_rate': self.commission_spin.value(),
                    'commission_amount': float(self.commission_amount_label.text().replace("ل.س", "").replace(",", "").strip()),
                    'date': self.date_edit.date().toString("yyyy-MM-dd"),
                    'notes': self.notes_edit.toPlainText().strip(),
                    'user_id': self.current_user['id']
                }
                
                # حفظ في قاعدة البيانات
                print(f"شحن رصيد الموزع {charge_data['distributor_name']} بمبلغ {charge_data['charge_amount']}")

                # تحديث رصيد الموزع في جدول distributors
                self.db_manager.execute_query("""
                    UPDATE distributors
                    SET balance = ?
                    WHERE id = ?
                """, (charge_data['new_balance'], charge_data['distributor_id']))

                # حفظ سجل الشحن في جدول balance_charges
                self.db_manager.execute_query("""
                    INSERT INTO balance_charges (
                        distributor_id, charge_amount, previous_balance, new_balance,
                        payment_method, reference_number, bank, commission_rate,
                        commission_amount, charge_date, notes, user_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                """, (
                    charge_data['distributor_id'],
                    charge_data['charge_amount'],
                    charge_data['current_balance'],
                    charge_data['new_balance'],
                    charge_data['payment_method'],
                    charge_data['reference_number'],
                    charge_data['bank'],
                    charge_data['commission_rate'],
                    charge_data['commission_amount'],
                    charge_data['date'],
                    charge_data['notes'],
                    charge_data['user_id']
                ))

                print("تم حفظ عملية الشحن بنجاح")

                QMessageBox.information(
                    self,
                    "نجح",
                    f"تم شحن {format_currency(charge_data['charge_amount'])} "
                    f"لرصيد الموزع {charge_data['distributor_name']}\n"
                    f"الرصيد الجديد: {format_currency(charge_data['new_balance'])}"
                )
                
                # إرسال إشارة
                self.charge_completed.emit(charge_data)

                # سؤال المستخدم عن الطباعة
                reply = QMessageBox.question(
                    self,
                    "طباعة",
                    "هل تريد طباعة إيصال الشحن؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.print_receipt(charge_data)

                self.accept()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ الشحن: {e}")

    def print_receipt(self, charge_data):
        """طباعة إيصال الشحن"""
        try:
            # إعداد بيانات الإيصال
            receipt_data = {
                'اسم الموزع': charge_data['distributor_name'],
                'مبلغ الشحن': format_currency(charge_data['charge_amount']),
                'الرصيد السابق': format_currency(charge_data['previous_balance']),
                'الرصيد الجديد': format_currency(charge_data['new_balance']),
                'العمولة': format_currency(charge_data['commission_amount']),
                'التاريخ': charge_data['date'],
                'الوقت': QTime.currentTime().toString("hh:mm:ss"),
                'رقم الإيصال': f"CHG-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}",
                'ملاحظات': charge_data.get('notes', 'لا توجد ملاحظات')
            }

            # طباعة الإيصال
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_document(receipt_data, "إيصال شحن رصيد", show_preview=True)

            if success:
                QMessageBox.information(self, "تم", "تم إرسال إيصال الشحن للطباعة بنجاح")
            else:
                QMessageBox.warning(self, "تحذير", "تم إلغاء عملية الطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الإيصال: {e}")

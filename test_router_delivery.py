#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة تسليم الراوتر
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager

def main():
    print("=== اختبار واجهة تسليم الراوتر ===")
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    print("\n--- جميع المشتركين في قاعدة البيانات ---")
    all_subscribers = db_manager.fetch_all("""
        SELECT id, name, phone, subscription_paid, router_paid FROM subscribers
        ORDER BY name
    """)
    
    print(f"إجمالي المشتركين: {len(all_subscribers)}")
    for sub in all_subscribers:
        print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']} - اشتراك مدفوع: {sub['subscription_paid']} - راوتر مدفوع: {sub['router_paid']}")
    
    print("\n--- المشتركين الذين لم يستلموا راوتر (الاستعلام الحالي) ---")
    undelivered = db_manager.fetch_all("""
        SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid
        FROM subscribers s
        LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
        WHERE rd.id IS NULL
        ORDER BY s.name
    """)
    
    print(f"المشتركين غير المسلمين: {len(undelivered)}")
    for sub in undelivered:
        print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
    
    print("\n--- التحقق من جدول router_deliveries ---")
    deliveries = db_manager.fetch_all("""
        SELECT id, subscriber_id, delivery_date FROM router_deliveries
        ORDER BY delivery_date DESC
    """)
    
    print(f"عدد التسليمات المسجلة: {len(deliveries)}")
    for delivery in deliveries:
        print(f"  • تسليم ID: {delivery['id']} - مشترك ID: {delivery['subscriber_id']} - تاريخ: {delivery['delivery_date']}")
    
    # اختبار إضافة مشترك جديد
    print("\n--- إضافة مشترك جديد للاختبار ---")
    try:
        result = db_manager.execute_query("""
            INSERT INTO subscribers (name, phone, subscription_paid, router_paid, created_at, created_by)
            VALUES (?, ?, ?, ?, datetime('now'), ?)
        """, ("مشترك اختبار", "123456789", 1, 1, "admin"))
        
        if result:
            print("✅ تم إضافة مشترك اختبار بنجاح")
            
            # إعادة اختبار الاستعلام
            print("\n--- إعادة اختبار الاستعلام بعد الإضافة ---")
            undelivered_new = db_manager.fetch_all("""
                SELECT s.id, s.name, s.phone, s.subscription_paid, s.router_paid
                FROM subscribers s
                LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
                WHERE rd.id IS NULL
                ORDER BY s.name
            """)
            
            print(f"المشتركين غير المسلمين الآن: {len(undelivered_new)}")
            for sub in undelivered_new:
                print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
        else:
            print("❌ فشل في إضافة مشترك اختبار")
            
    except Exception as e:
        print(f"خطأ في إضافة مشترك اختبار: {e}")
    
    db_manager.close()

if __name__ == "__main__":
    main()

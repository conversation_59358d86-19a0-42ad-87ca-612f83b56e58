#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دوال إدارة الشيفتات
"""


def get_current_shift(db_manager, user_id, user_name):
    """الحصول على الشيفت الحالي للمستخدم أو إنشاء واحد جديد"""
    from datetime import datetime
    
    today = datetime.now().strftime('%Y-%m-%d')
    
    # البحث عن شيفت مفتوح لليوم
    current_shift = db_manager.fetch_one("""
        SELECT id, user_id, user_name, shift_date, start_time, status
        FROM shifts 
        WHERE user_id = ? AND shift_date = ? AND status = 'open'
    """, (user_id, today))
    
    if current_shift:
        print(f"📂 شيفت مفتوح موجود: ID {current_shift['id']} للمستخدم {current_shift['user_name']}")
        return current_shift['id']
    
    # إنشاء شيفت جديد
    try:
        cursor = db_manager.execute_query("""
            INSERT INTO shifts (user_id, user_name, shift_date, start_time, status)
            VALUES (?, ?, ?, ?, 'open')
        """, (user_id, user_name, today, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        shift_id = cursor.lastrowid if hasattr(cursor, 'lastrowid') else 1
        print(f"✅ تم إنشاء شيفت جديد: ID {shift_id} للمستخدم {user_name}")
        return shift_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشيفت: {e}")
        return None

def close_shift(db_manager, user_id, closing_balance=0, notes=""):
    """إغلاق الشيفت الحالي"""
    from datetime import datetime
    
    today = datetime.now().strftime('%Y-%m-%d')
    
    # البحث عن الشيفت المفتوح
    current_shift = db_manager.fetch_one("""
        SELECT id FROM shifts 
        WHERE user_id = ? AND shift_date = ? AND status = 'open'
    """, (user_id, today))
    
    if not current_shift:
        print("⚠️ لا يوجد شيفت مفتوح للإغلاق")
        return False
    
    shift_id = current_shift['id']
    
    # حساب إجمالي المبيعات والمصاريف للشيفت
    sales_total = db_manager.fetch_one("""
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM transactions 
        WHERE shift_id = ?
    """, (shift_id,))
    
    expenses_total = db_manager.fetch_one("""
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM expenses 
        WHERE shift_id = ?
    """, (shift_id,))
    
    total_sales = sales_total['total'] if sales_total else 0
    total_expenses = expenses_total['total'] if expenses_total else 0
    cash_difference = closing_balance - (total_sales - total_expenses)
    
    # إغلاق الشيفت
    try:
        db_manager.execute_query("""
            UPDATE shifts 
            SET end_time = ?, status = 'closed', closing_balance = ?, 
                total_sales = ?, total_expenses = ?, cash_difference = ?, notes = ?
            WHERE id = ?
        """, (
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            closing_balance, total_sales, total_expenses, cash_difference, notes, shift_id
        ))
        
        print(f"✅ تم إغلاق الشيفت {shift_id}")
        print(f"  • إجمالي المبيعات: {total_sales:,} ل.س")
        print(f"  • إجمالي المصاريف: {total_expenses:,} ل.س")
        print(f"  • الرصيد النهائي: {closing_balance:,} ل.س")
        print(f"  • فرق النقدية: {cash_difference:,} ل.س")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إغلاق الشيفت: {e}")
        return False

def get_shift_summary(db_manager, shift_id):
    """الحصول على ملخص الشيفت"""
    
    shift_info = db_manager.fetch_one("""
        SELECT * FROM shifts WHERE id = ?
    """, (shift_id,))
    
    if not shift_info:
        return None
    
    # تفصيل المبيعات
    sales_detail = db_manager.fetch_all("""
        SELECT type, COUNT(*) as count, SUM(amount) as total
        FROM transactions 
        WHERE shift_id = ?
        GROUP BY type
    """, (shift_id,))
    
    # تفصيل المصاريف
    expenses_detail = db_manager.fetch_all("""
        SELECT expense_type, COUNT(*) as count, SUM(amount) as total
        FROM expenses 
        WHERE shift_id = ?
        GROUP BY expense_type
    """, (shift_id,))
    
    return {
        'shift_info': shift_info,
        'sales_detail': sales_detail,
        'expenses_detail': expenses_detail
    }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد الخزينة اليومية برصيد ابتدائي
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def setup_daily_treasury():
    """إعداد الخزينة اليومية برصيد ابتدائي"""
    
    print("🏦 إعداد الخزينة اليومية...")
    
    db = DatabaseManager('data/company_system.db')
    
    try:
        # إضافة رصيد ابتدائي للليرة السورية (مليون ليرة للاختبار)
        initial_syp = 1000000
        
        # التحقق من وجود رصيد الليرة السورية
        syp_exists = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'SYP'")
        
        if syp_exists:
            print(f"✅ رصيد الليرة السورية موجود: {syp_exists['balance']:,} ل.س")
            
            # إذا كان الرصيد صفر، إضافة رصيد ابتدائي
            if syp_exists['balance'] == 0:
                db.execute_query("""
                    UPDATE treasury 
                    SET balance = ?, last_updated = datetime('now')
                    WHERE currency_type = 'SYP'
                """, (initial_syp,))
                print(f"💰 تم إضافة رصيد ابتدائي: {initial_syp:,} ل.س")
        else:
            # إنشاء رصيد جديد
            db.execute_query("""
                INSERT INTO treasury (currency_type, balance, exchange_rate, last_updated)
                VALUES ('SYP', ?, 1.0, datetime('now'))
            """, (initial_syp,))
            print(f"💰 تم إنشاء رصيد ابتدائي: {initial_syp:,} ل.س")
        
        # التأكد من وجود رصيد الدولار (يبدأ من صفر)
        usd_exists = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'USD'")
        
        if not usd_exists:
            db.execute_query("""
                INSERT INTO treasury (currency_type, balance, exchange_rate, last_updated)
                VALUES ('USD', 0.0, 15000.0, datetime('now'))
            """)
            print("💵 تم إنشاء رصيد الدولار: $0.00")
        else:
            print(f"✅ رصيد الدولار موجود: ${usd_exists['balance']:,.2f}")
        
        # عرض الأرصدة النهائية
        print(f"\n📊 الأرصدة الحالية:")
        
        currencies = db.fetch_all("""
            SELECT currency_type, balance, exchange_rate, last_updated
            FROM treasury
            WHERE currency_type IN ('SYP', 'USD')
            ORDER BY currency_type
        """)
        
        for currency in currencies:
            currency_type = currency[0]
            balance = currency[1]
            exchange_rate = currency[2]
            last_updated = currency[3]
            
            if currency_type == 'SYP':
                print(f"  • الليرة السورية: {balance:,} ل.س")
            elif currency_type == 'USD':
                print(f"  • الدولار الأمريكي: ${balance:,.2f} (سعر الصرف: {exchange_rate:,} ل.س)")
        
        # اختبار عملية صرف تجريبية
        print(f"\n🧪 محاكاة عملية صرف:")
        test_syp_amount = 150000
        test_exchange_rate = 15000
        test_usd_amount = test_syp_amount / test_exchange_rate
        
        current_syp = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'SYP'")['balance']
        current_usd = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'USD'")['balance']
        
        print(f"  • المبلغ المراد صرفه: {test_syp_amount:,} ل.س")
        print(f"  • سعر الصرف: {test_exchange_rate:,} ل.س/$")
        print(f"  • المبلغ المستلم: ${test_usd_amount:.2f}")
        
        if current_syp >= test_syp_amount:
            new_syp = current_syp - test_syp_amount
            new_usd = current_usd + test_usd_amount
            
            print(f"  ✅ العملية ممكنة:")
            print(f"    - الرصيد الجديد ل.س: {new_syp:,} ل.س")
            print(f"    - الرصيد الجديد $: ${new_usd:.2f}")
        else:
            print(f"  ❌ العملية غير ممكنة: الرصيد المتاح {current_syp:,} ل.س أقل من المطلوب")
        
        print(f"\n🎉 تم إعداد الخزينة اليومية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد الخزينة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    setup_daily_treasury()

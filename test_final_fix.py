#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتأكد من إصلاح مشكلة إغلاق التطبيق
"""

import sys
import os
sys.path.append('src')

def test_all_tables():
    """اختبار جميع الجداول"""
    
    print("🔍 اختبار جميع الجداول...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # اختبار الجداول المطلوبة
        tables = {
            'shifts': 'SELECT COUNT(*) as count FROM shifts',
            'subscribers': 'SELECT COUNT(*) as count FROM subscribers WHERE delivered = 0',
            'packages': 'SELECT COUNT(*) as count FROM packages',
            'router_deliveries': 'SELECT COUNT(*) as count FROM router_deliveries',
            'workers': 'SELECT COUNT(*) as count FROM workers',
            'products': 'SELECT COUNT(*) as count FROM products WHERE category = "router"'
        }
        
        all_good = True
        
        for table_name, query in tables.items():
            try:
                result = db.fetch_all(query)
                count = result[0]['count'] if result else 0
                print(f"✅ {table_name}: {count} سجل")
                
                if table_name in ['subscribers', 'packages', 'workers'] and count == 0:
                    print(f"⚠️ {table_name} فارغ - قد يسبب مشاكل")
                    all_good = False
                    
            except Exception as e:
                print(f"❌ {table_name}: خطأ - {e}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجداول: {e}")
        return False

def test_router_delivery_import():
    """اختبار استيراد واجهة تسليم الراوتر"""
    
    print("\n🔍 اختبار استيراد واجهة تسليم الراوتر...")
    
    try:
        from ui.router_delivery_window import RouterDeliveryWindow, get_current_shift
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # اختبار دالة الشيفت
        shift_id = get_current_shift(db, 1, 'admin')
        if shift_id:
            print(f"✅ دالة الشيفت تعمل: {shift_id}")
        else:
            print("❌ دالة الشيفت لا تعمل")
            return False
        
        print("✅ استيراد واجهة تسليم الراوتر نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sample_data():
    """اختبار البيانات التجريبية"""
    
    print("\n🔍 اختبار البيانات التجريبية...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # اختبار المشتركين غير المسلمين
        subscribers = db.fetch_all("SELECT name, phone FROM subscribers WHERE delivered = 0 LIMIT 3")
        print(f"👥 المشتركين غير المسلمين ({len(subscribers)}):")
        for sub in subscribers:
            print(f"  • {sub[0]} ({sub[1]})")
        
        # اختبار الباقات
        packages = db.fetch_all("SELECT name, price FROM packages LIMIT 3")
        print(f"📋 الباقات ({len(packages)}):")
        for pkg in packages:
            print(f"  • {pkg[0]}: {pkg[1]:,} ل.س")
        
        # اختبار العمال
        workers = db.fetch_all("SELECT name, role FROM workers LIMIT 3")
        print(f"👷 العمال ({len(workers)}):")
        for worker in workers:
            print(f"  • {worker[0]} ({worker[1]})")
        
        # اختبار الراوترات
        routers = db.fetch_all("SELECT name, price FROM products WHERE category = 'router' LIMIT 3")
        print(f"📦 الراوترات ({len(routers)}):")
        for router in routers:
            print(f"  • {router[0]}: {router[1]:,} ل.س")
        
        return len(subscribers) > 0 and len(packages) > 0 and len(workers) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار نهائي للتأكد من إصلاح مشكلة إغلاق التطبيق")
    print("=" * 70)
    
    # اختبار الجداول
    tables_ok = test_all_tables()
    
    # اختبار الاستيراد
    import_ok = test_router_delivery_import()
    
    # اختبار البيانات
    data_ok = test_sample_data()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الاختبار النهائي:")
    print(f"  • الجداول: {'✅ سليمة' if tables_ok else '❌ مشاكل'}")
    print(f"  • الاستيراد: {'✅ يعمل' if import_ok else '❌ لا يعمل'}")
    print(f"  • البيانات: {'✅ متوفرة' if data_ok else '❌ مفقودة'}")
    
    if all([tables_ok, import_ok, data_ok]):
        print("\n🎉 تم إصلاح مشكلة إغلاق التطبيق بالكامل!")
        print("\n📋 الآن يمكنك:")
        print("  1. تشغيل التطبيق: python system_launcher.py")
        print("  2. تسجيل الدخول: admin / 123")
        print("  3. فتح 'تسليم راوتر'")
        print("  4. اختيار مشترك من القائمة")
        print("  5. اختيار راوتر وباقة")
        print("  6. الضغط على 'حفظ وتسليم'")
        print("  7. ✅ التطبيق لن يغلق!")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("  ✅ إنشاء جميع الجداول المفقودة")
        print("  ✅ إدراج بيانات تجريبية كاملة")
        print("  ✅ إصلاح sys.exit() في الاستيراد")
        print("  ✅ معالجة أخطاء شاملة في save_delivery()")
        print("  ✅ ربط النظام الموحد للخزينة")
        print("  ✅ معالجة أخطاء الشيفت والعمليات المالية")
        
        print("\n💡 نصائح للاستخدام:")
        print("  • تأكد من ملء جميع الحقول المطلوبة")
        print("  • اختر مشترك من القائمة المنسدلة")
        print("  • اختر راوتر وباقة مناسبة")
        print("  • حدد عامل التركيب")
        print("  • أدخل عدد أمتار الكبل")
        print("  • اضغط 'حفظ وتسليم' - سيعمل بشكل مثالي!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")
        
        if not tables_ok:
            print("  🔧 مراجعة الجداول وإصلاحها")
        if not import_ok:
            print("  🔧 مراجعة ملفات الاستيراد")
        if not data_ok:
            print("  🔧 إضافة بيانات تجريبية")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة تقارير العمليات المالية
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QTableWidget, QTableWidgetItem, QPushButton, 
                             QDateEdit, QLabel, QComboBox, QGroupBox,
                             QHeaderView, QMessageBox, QTextEdit, QSplitter,
                             QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont
from utils.arabic_support import reshape_arabic_text, format_currency

class FinancialReportsWindow(QDialog):
    """واجهة تقارير العمليات المالية"""
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        self.init_ui()
        self.load_all_reports()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(reshape_arabic_text("تقارير العمليات المالية"))
        self.setGeometry(100, 100, 1400, 900)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط التحكم العلوي
        control_layout = self.create_control_panel()
        main_layout.addLayout(control_layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب إغلاق الصناديق
        self.cash_boxes_tab = self.create_cash_boxes_tab()
        self.tabs.addTab(self.cash_boxes_tab, reshape_arabic_text("إغلاق الصناديق"))
        
        # تبويب نقل الخزينة
        self.treasury_transfers_tab = self.create_treasury_transfers_tab()
        self.tabs.addTab(self.treasury_transfers_tab, reshape_arabic_text("نقل الخزينة"))
        
        # تبويب شراء الدولار
        self.currency_exchange_tab = self.create_currency_exchange_tab()
        self.tabs.addTab(self.currency_exchange_tab, reshape_arabic_text("شراء الدولار"))
        
        # تبويب الملخص المالي
        self.financial_summary_tab = self.create_financial_summary_tab()
        self.tabs.addTab(self.financial_summary_tab, reshape_arabic_text("الملخص المالي"))
        
        main_layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons_layout()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
    
    def create_control_panel(self):
        """إنشاء لوحة التحكم"""
        layout = QHBoxLayout()
        
        # تاريخ البداية
        layout.addWidget(QLabel(reshape_arabic_text("من تاريخ:")))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        layout.addWidget(self.start_date)
        
        # تاريخ النهاية
        layout.addWidget(QLabel(reshape_arabic_text("إلى تاريخ:")))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        layout.addWidget(self.end_date)
        
        # فلتر المستخدم
        layout.addWidget(QLabel(reshape_arabic_text("المستخدم:")))
        self.user_filter = QComboBox()
        self.load_users()
        layout.addWidget(self.user_filter)
        
        # زر التحديث
        refresh_btn = QPushButton(reshape_arabic_text("تحديث التقارير"))
        refresh_btn.clicked.connect(self.load_all_reports)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        return layout
    
    def create_cash_boxes_tab(self):
        """إنشاء تبويب إغلاق الصناديق"""
        widget = QSplitter(Qt.Vertical)
        
        # جدول الصناديق المغلقة
        self.cash_boxes_table = QTableWidget()
        self.cash_boxes_table.setColumnCount(8)
        headers = ["رقم الصندوق", "المستخدم", "رصيد الافتتاح", "إجمالي المبيعات", 
                  "إجمالي المصروفات", "رصيد الإغلاق", "تاريخ الإغلاق", "المصدر"]
        self.cash_boxes_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        self.cash_boxes_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        widget.addWidget(self.cash_boxes_table)
        
        # ملخص الصناديق
        summary_group = QGroupBox(reshape_arabic_text("ملخص الصناديق"))
        summary_layout = QVBoxLayout()
        self.cash_boxes_summary = QTextEdit()
        self.cash_boxes_summary.setMaximumHeight(150)
        summary_layout.addWidget(self.cash_boxes_summary)
        summary_group.setLayout(summary_layout)
        widget.addWidget(summary_group)
        
        return widget
    
    def create_treasury_transfers_tab(self):
        """إنشاء تبويب نقل الخزينة"""
        widget = QSplitter(Qt.Vertical)
        
        # جدول نقل الخزينة
        self.treasury_table = QTableWidget()
        self.treasury_table.setColumnCount(7)
        headers = ["رقم العملية", "المستخدم", "العملة", "المبلغ", "المستلم", "التاريخ", "الملاحظات"]
        self.treasury_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        self.treasury_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        widget.addWidget(self.treasury_table)
        
        # ملخص نقل الخزينة
        summary_group = QGroupBox(reshape_arabic_text("ملخص نقل الخزينة"))
        summary_layout = QVBoxLayout()
        self.treasury_summary = QTextEdit()
        self.treasury_summary.setMaximumHeight(150)
        summary_layout.addWidget(self.treasury_summary)
        summary_group.setLayout(summary_layout)
        widget.addWidget(summary_group)
        
        return widget
    
    def create_currency_exchange_tab(self):
        """إنشاء تبويب شراء الدولار"""
        widget = QSplitter(Qt.Vertical)
        
        # جدول شراء الدولار
        self.currency_table = QTableWidget()
        self.currency_table.setColumnCount(7)
        headers = ["رقم العملية", "المستخدم", "مبلغ الليرة", "مبلغ الدولار", "سعر الصرف", "التاريخ", "الملاحظات"]
        self.currency_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        self.currency_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        widget.addWidget(self.currency_table)
        
        # ملخص شراء الدولار
        summary_group = QGroupBox(reshape_arabic_text("ملخص شراء الدولار"))
        summary_layout = QVBoxLayout()
        self.currency_summary = QTextEdit()
        self.currency_summary.setMaximumHeight(150)
        summary_layout.addWidget(self.currency_summary)
        summary_group.setLayout(summary_layout)
        widget.addWidget(summary_group)
        
        return widget
    
    def create_financial_summary_tab(self):
        """إنشاء تبويب الملخص المالي"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # ملخص الأرصدة الحالية
        balances_group = QGroupBox(reshape_arabic_text("الأرصدة الحالية"))
        balances_layout = QVBoxLayout()
        self.current_balances = QTextEdit()
        self.current_balances.setMaximumHeight(200)
        balances_layout.addWidget(self.current_balances)
        balances_group.setLayout(balances_layout)
        layout.addWidget(balances_group)
        
        # ملخص العمليات
        operations_group = QGroupBox(reshape_arabic_text("ملخص العمليات"))
        operations_layout = QVBoxLayout()
        self.operations_summary = QTextEdit()
        operations_layout.addWidget(self.operations_summary)
        operations_group.setLayout(operations_layout)
        layout.addWidget(operations_group)
        
        return widget
    
    def create_buttons_layout(self):
        """إنشاء تخطيط الأزرار"""
        layout = QHBoxLayout()
        
        # زر طباعة
        print_btn = QPushButton(reshape_arabic_text("طباعة التقرير"))
        print_btn.clicked.connect(self.print_report)
        layout.addWidget(print_btn)
        
        # زر تصدير
        export_btn = QPushButton(reshape_arabic_text("تصدير إلى Excel"))
        export_btn.clicked.connect(self.export_to_excel)
        layout.addWidget(export_btn)
        
        layout.addStretch()
        
        # زر إغلاق
        close_btn = QPushButton(reshape_arabic_text("إغلاق"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        return layout
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            self.user_filter.addItem(reshape_arabic_text("جميع المستخدمين"), "all")
            
            # البحث في جداول متعددة للمستخدمين
            users = set()
            
            # من جدول unified_treasury
            try:
                treasury_users = self.db_manager.fetch_all("SELECT DISTINCT user_id FROM unified_treasury")
                for user in treasury_users:
                    users.add(user['user_id'])
            except:
                pass
            
            # من جدول transactions
            try:
                transaction_users = self.db_manager.fetch_all("SELECT DISTINCT user_name FROM transactions WHERE user_name LIKE 'user_%'")
                for user in transaction_users:
                    try:
                        user_id = int(user['user_name'].replace('user_', ''))
                        users.add(user_id)
                    except:
                        pass
            except:
                pass
            
            # إضافة المستخدمين للقائمة
            for user_id in sorted(users):
                self.user_filter.addItem(f"المستخدم {user_id}", user_id)
                
        except Exception as e:
            print(f"❌ خطأ في تحميل المستخدمين: {e}")
    
    def get_date_filter(self):
        """الحصول على فلتر التاريخ"""
        start_date = self.start_date.date().toString('yyyy-MM-dd')
        end_date = self.end_date.date().toString('yyyy-MM-dd')
        return start_date, end_date
    
    def get_user_filter(self):
        """الحصول على فلتر المستخدم"""
        return self.user_filter.currentData()
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, reshape_arabic_text("طباعة"), 
                              reshape_arabic_text("سيتم تطوير ميزة الطباعة قريباً"))
    
    def export_to_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, reshape_arabic_text("تصدير"),
                              reshape_arabic_text("سيتم تطوير ميزة التصدير قريباً"))

    def load_all_reports(self):
        """تحميل جميع التقارير"""
        try:
            self.load_cash_boxes_report()
            self.load_treasury_transfers_report()
            self.load_currency_exchange_report()
            self.load_financial_summary()

        except Exception as e:
            print(f"❌ خطأ في تحميل التقارير: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل التقارير: {e}"))

    def load_cash_boxes_report(self):
        """تحميل تقرير إغلاق الصناديق"""
        try:
            start_date, end_date = self.get_date_filter()
            user_filter = self.get_user_filter()

            # البحث في جداول متعددة للصناديق المغلقة
            cash_boxes = []

            # البحث في جدول cash_boxes
            try:
                query = """
                    SELECT id, user_id, opening_balance, total_sales, total_expenses,
                           closing_balance, closed_at, notes, 'cash_boxes' as source
                    FROM cash_boxes
                    WHERE is_closed = 1
                    AND DATE(closed_at) BETWEEN ? AND ?
                """
                params = [start_date, end_date]

                if user_filter != "all":
                    query += " AND user_id = ?"
                    params.append(user_filter)

                cash_boxes.extend(self.db_manager.fetch_all(query, params))
            except Exception as e:
                print(f"⚠️ لا يمكن قراءة جدول cash_boxes: {e}")

            # البحث في جدول shifts
            try:
                query = """
                    SELECT id, user_id, 0 as opening_balance, total_sales, total_expenses,
                           (total_sales - total_expenses) as closing_balance,
                           closed_at, notes, 'shifts' as source
                    FROM shifts
                    WHERE status = 'closed'
                    AND DATE(closed_at) BETWEEN ? AND ?
                """
                params = [start_date, end_date]

                if user_filter != "all":
                    query += " AND user_id = ?"
                    params.append(user_filter)

                cash_boxes.extend(self.db_manager.fetch_all(query, params))
            except Exception as e:
                print(f"⚠️ لا يمكن قراءة جدول shifts: {e}")

            # البحث في جدول unified_treasury للصناديق المغلقة
            try:
                query = """
                    SELECT user_id, session_date, 0 as opening_balance, 0 as total_sales,
                           0 as total_expenses, daily_balance as closing_balance,
                           last_updated as closed_at, 'من النظام الموحد' as notes,
                           'unified_treasury' as source,
                           ROW_NUMBER() OVER (ORDER BY last_updated) as id
                    FROM unified_treasury
                    WHERE is_session_active = 0
                    AND daily_balance > 0
                    AND DATE(session_date) BETWEEN ? AND ?
                """
                params = [start_date, end_date]

                if user_filter != "all":
                    query += " AND user_id = ?"
                    params.append(user_filter)

                unified_boxes = self.db_manager.fetch_all(query, params)
                cash_boxes.extend(unified_boxes)
            except Exception as e:
                print(f"⚠️ لا يمكن قراءة جدول unified_treasury: {e}")

            # ترتيب النتائج
            cash_boxes.sort(key=lambda x: x.get('closed_at', ''), reverse=True)

            # ملء الجدول
            self.cash_boxes_table.setRowCount(len(cash_boxes))

            total_opening = 0
            total_sales = 0
            total_expenses = 0
            total_closing = 0

            for row, box in enumerate(cash_boxes):
                self.cash_boxes_table.setItem(row, 0, QTableWidgetItem(str(box['id'])))
                self.cash_boxes_table.setItem(row, 1, QTableWidgetItem(f"المستخدم {box['user_id']}"))
                self.cash_boxes_table.setItem(row, 2, QTableWidgetItem(format_currency(box['opening_balance'] or 0)))
                self.cash_boxes_table.setItem(row, 3, QTableWidgetItem(format_currency(box['total_sales'] or 0)))
                self.cash_boxes_table.setItem(row, 4, QTableWidgetItem(format_currency(box['total_expenses'] or 0)))
                self.cash_boxes_table.setItem(row, 5, QTableWidgetItem(format_currency(box['closing_balance'] or 0)))
                self.cash_boxes_table.setItem(row, 6, QTableWidgetItem(str(box['closed_at'] or '')))
                self.cash_boxes_table.setItem(row, 7, QTableWidgetItem(str(box['source'])))

                total_opening += box['opening_balance'] or 0
                total_sales += box['total_sales'] or 0
                total_expenses += box['total_expenses'] or 0
                total_closing += box['closing_balance'] or 0

            # ملخص الصناديق
            summary = f"""
إجمالي الصناديق المغلقة: {len(cash_boxes)}
إجمالي رصيد الافتتاح: {format_currency(total_opening)}
إجمالي المبيعات: {format_currency(total_sales)}
إجمالي المصروفات: {format_currency(total_expenses)}
إجمالي رصيد الإغلاق: {format_currency(total_closing)}
صافي الربح: {format_currency(total_sales - total_expenses)}
            """
            self.cash_boxes_summary.setText(reshape_arabic_text(summary.strip()))

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير الصناديق: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تقرير الصناديق: {e}")

    def load_treasury_transfers_report(self):
        """تحميل تقرير نقل الخزينة"""
        try:
            start_date, end_date = self.get_date_filter()
            user_filter = self.get_user_filter()

            # البحث في جدول transactions عن عمليات نقل الخزينة
            transfers = []

            try:
                query = """
                    SELECT id, user_name, description, amount, created_at
                    FROM transactions
                    WHERE (type LIKE '%نقل%خزينة%' OR description LIKE '%نقل%خزينة%')
                    AND DATE(created_at) BETWEEN ? AND ?
                """
                params = [start_date, end_date]

                if user_filter != "all":
                    query += " AND user_name = ?"
                    params.append(f"user_{user_filter}")

                query += " ORDER BY created_at DESC"

                transfers = self.db_manager.fetch_all(query, params)
            except Exception as e:
                print(f"⚠️ لا يمكن قراءة عمليات نقل الخزينة: {e}")

            # ملء الجدول
            self.treasury_table.setRowCount(len(transfers))

            total_syp = 0
            total_usd = 0

            for row, transfer in enumerate(transfers):
                # استخراج معلومات النقل من الوصف
                description = transfer['description'] or ''
                amount = transfer['amount'] or 0

                # تحديد العملة من الوصف
                currency = "SYP"
                if "$" in description or "USD" in description:
                    currency = "USD"
                    total_usd += amount
                else:
                    total_syp += amount

                # استخراج المستلم من الوصف
                receiver = "غير محدد"
                if "للمستلم:" in description:
                    receiver = description.split("للمستلم:")[1].split("-")[0].strip()
                elif "المستلم:" in description:
                    receiver = description.split("المستلم:")[1].split("-")[0].strip()

                self.treasury_table.setItem(row, 0, QTableWidgetItem(str(transfer['id'])))
                self.treasury_table.setItem(row, 1, QTableWidgetItem(transfer['user_name'] or ''))
                self.treasury_table.setItem(row, 2, QTableWidgetItem(currency))
                self.treasury_table.setItem(row, 3, QTableWidgetItem(format_currency(amount)))
                self.treasury_table.setItem(row, 4, QTableWidgetItem(receiver))
                self.treasury_table.setItem(row, 5, QTableWidgetItem(str(transfer['created_at'] or '')))
                self.treasury_table.setItem(row, 6, QTableWidgetItem(description))

            # ملخص نقل الخزينة
            summary = f"""
إجمالي عمليات النقل: {len(transfers)}
إجمالي المبالغ المنقولة بالليرة: {format_currency(total_syp)}
إجمالي المبالغ المنقولة بالدولار: {format_currency(total_usd)} $
            """
            self.treasury_summary.setText(reshape_arabic_text(summary.strip()))

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير نقل الخزينة: {e}")

    def load_currency_exchange_report(self):
        """تحميل تقرير شراء الدولار"""
        try:
            start_date, end_date = self.get_date_filter()
            user_filter = self.get_user_filter()

            # البحث في جدول transactions عن عمليات شراء الدولار
            exchanges = []

            try:
                query = """
                    SELECT id, user_name, description, amount, created_at
                    FROM transactions
                    WHERE (type LIKE '%شراء%دولار%' OR type LIKE '%صرف%عملة%' OR description LIKE '%دولار%')
                    AND DATE(created_at) BETWEEN ? AND ?
                """
                params = [start_date, end_date]

                if user_filter != "all":
                    query += " AND user_name = ?"
                    params.append(f"user_{user_filter}")

                query += " ORDER BY created_at DESC"

                exchanges = self.db_manager.fetch_all(query, params)
            except Exception as e:
                print(f"⚠️ لا يمكن قراءة عمليات شراء الدولار: {e}")

            # ملء الجدول
            self.currency_table.setRowCount(len(exchanges))

            total_syp = 0
            total_usd = 0

            for row, exchange in enumerate(exchanges):
                description = exchange['description'] or ''
                amount = exchange['amount'] or 0

                # استخراج معلومات الصرف من الوصف
                syp_amount = 0
                usd_amount = 0
                exchange_rate = 0

                # محاولة استخراج المبالغ من الوصف
                if "ل.س" in description and "$" in description:
                    try:
                        # البحث عن الأرقام في الوصف
                        import re
                        numbers = re.findall(r'[\d,]+\.?\d*', description)
                        if len(numbers) >= 2:
                            syp_amount = float(numbers[0].replace(',', ''))
                            usd_amount = float(numbers[1].replace(',', ''))
                            if usd_amount > 0:
                                exchange_rate = syp_amount / usd_amount
                    except:
                        syp_amount = amount

                total_syp += syp_amount
                total_usd += usd_amount

                self.currency_table.setItem(row, 0, QTableWidgetItem(str(exchange['id'])))
                self.currency_table.setItem(row, 1, QTableWidgetItem(exchange['user_name'] or ''))
                self.currency_table.setItem(row, 2, QTableWidgetItem(format_currency(syp_amount)))
                self.currency_table.setItem(row, 3, QTableWidgetItem(f"${usd_amount:.2f}"))
                self.currency_table.setItem(row, 4, QTableWidgetItem(f"{exchange_rate:,.0f}" if exchange_rate > 0 else "غير محدد"))
                self.currency_table.setItem(row, 5, QTableWidgetItem(str(exchange['created_at'] or '')))
                self.currency_table.setItem(row, 6, QTableWidgetItem(description))

            # ملخص شراء الدولار
            avg_rate = total_syp / total_usd if total_usd > 0 else 0
            summary = f"""
إجمالي عمليات الشراء: {len(exchanges)}
إجمالي الليرة المصروفة: {format_currency(total_syp)}
إجمالي الدولار المشترى: ${total_usd:.2f}
متوسط سعر الصرف: {avg_rate:,.0f} ل.س/$
            """
            self.currency_summary.setText(reshape_arabic_text(summary.strip()))

        except Exception as e:
            print(f"❌ خطأ في تحميل تقرير شراء الدولار: {e}")

    def load_financial_summary(self):
        """تحميل الملخص المالي"""
        try:
            # الأرصدة الحالية
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            treasury_manager = UnifiedTreasuryManager(self.db_manager)

            # الأرصدة اليومية والرئيسية
            daily_syp = treasury_manager.get_total_daily_balance('SYP')
            daily_usd = treasury_manager.get_total_daily_balance('USD')
            main_syp = treasury_manager.get_main_balance('SYP')
            main_usd = treasury_manager.get_main_balance('USD')

            balances_summary = f"""
الأرصدة الحالية:

الخزينة اليومية:
• الليرة السورية: {format_currency(daily_syp)}
• الدولار الأمريكي: ${daily_usd:.2f}

الخزينة الرئيسية:
• الليرة السورية: {format_currency(main_syp)}
• الدولار الأمريكي: ${main_usd:.2f}

إجمالي الأرصدة:
• الليرة السورية: {format_currency(daily_syp + main_syp)}
• الدولار الأمريكي: ${daily_usd + main_usd:.2f}
            """
            self.current_balances.setText(reshape_arabic_text(balances_summary.strip()))

            # ملخص العمليات
            start_date, end_date = self.get_date_filter()

            # إحصائيات الصناديق المغلقة
            closed_boxes_count = 0
            total_sales = 0
            total_expenses = 0

            try:
                # من جدول cash_boxes
                cash_stats = self.db_manager.fetch_one("""
                    SELECT COUNT(*) as count,
                           COALESCE(SUM(total_sales), 0) as sales,
                           COALESCE(SUM(total_expenses), 0) as expenses
                    FROM cash_boxes
                    WHERE is_closed = 1
                    AND DATE(closed_at) BETWEEN ? AND ?
                """, (start_date, end_date))

                if cash_stats:
                    closed_boxes_count += cash_stats['count']
                    total_sales += cash_stats['sales']
                    total_expenses += cash_stats['expenses']
            except:
                pass

            try:
                # من جدول shifts
                shift_stats = self.db_manager.fetch_one("""
                    SELECT COUNT(*) as count,
                           COALESCE(SUM(total_sales), 0) as sales,
                           COALESCE(SUM(total_expenses), 0) as expenses
                    FROM shifts
                    WHERE status = 'closed'
                    AND DATE(closed_at) BETWEEN ? AND ?
                """, (start_date, end_date))

                if shift_stats:
                    closed_boxes_count += shift_stats['count']
                    total_sales += shift_stats['sales']
                    total_expenses += shift_stats['expenses']
            except:
                pass

            # إحصائيات نقل الخزينة
            treasury_transfers_count = 0
            try:
                transfers_stats = self.db_manager.fetch_one("""
                    SELECT COUNT(*) as count
                    FROM transactions
                    WHERE (type LIKE '%نقل%خزينة%' OR description LIKE '%نقل%خزينة%')
                    AND DATE(created_at) BETWEEN ? AND ?
                """, (start_date, end_date))

                if transfers_stats:
                    treasury_transfers_count = transfers_stats['count']
            except:
                pass

            # إحصائيات شراء الدولار
            currency_exchanges_count = 0
            try:
                exchanges_stats = self.db_manager.fetch_one("""
                    SELECT COUNT(*) as count
                    FROM transactions
                    WHERE (type LIKE '%شراء%دولار%' OR type LIKE '%صرف%عملة%' OR description LIKE '%دولار%')
                    AND DATE(created_at) BETWEEN ? AND ?
                """, (start_date, end_date))

                if exchanges_stats:
                    currency_exchanges_count = exchanges_stats['count']
            except:
                pass

            operations_summary = f"""
ملخص العمليات (من {start_date} إلى {end_date}):

الصناديق المغلقة:
• عدد الصناديق: {closed_boxes_count}
• إجمالي المبيعات: {format_currency(total_sales)}
• إجمالي المصروفات: {format_currency(total_expenses)}
• صافي الربح: {format_currency(total_sales - total_expenses)}

عمليات نقل الخزينة:
• عدد العمليات: {treasury_transfers_count}

عمليات شراء الدولار:
• عدد العمليات: {currency_exchanges_count}

إجمالي العمليات المالية: {closed_boxes_count + treasury_transfers_count + currency_exchanges_count}
            """
            self.operations_summary.setText(reshape_arabic_text(operations_summary.strip()))

        except Exception as e:
            print(f"❌ خطأ في تحميل الملخص المالي: {e}")
            import traceback
            traceback.print_exc()

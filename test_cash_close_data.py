#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بيانات إغلاق الصندوق
"""

import sqlite3
from datetime import date

def main():
    conn = sqlite3.connect('data/company_system.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    today = date.today().strftime('%Y-%m-%d')
    username = 'admin'
    
    print(f'=== اختبار بيانات إغلاق الصندوق لـ {username} في {today} ===')

    # اختبار المبيعات من transactions
    print('\n--- المبيعات من transactions ---')
    cursor.execute("""
        SELECT type, description, amount, created_at FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') AND amount > 0
        ORDER BY created_at DESC
    """, (today, username))
    
    sales_transactions = cursor.fetchall()
    total_sales = 0
    
    for sale in sales_transactions:
        print(f"• {sale['type']}: {sale['amount']} ل.س - {sale['description']}")
        total_sales += sale['amount']
    
    print(f"إجمالي المبيعات من transactions: {total_sales} ل.س")

    # اختبار المقبوضات من receipts
    print('\n--- المقبوضات من receipts ---')
    cursor.execute("""
        SELECT amount, description, user_name, created_at FROM receipts
        WHERE DATE(receipt_date) = ? AND user_name = ?
        ORDER BY created_at DESC
    """, (today, username))
    
    receipts = cursor.fetchall()
    total_receipts = 0
    
    for receipt in receipts:
        print(f"• سند قبض: {receipt['amount']} ل.س - {receipt['description']}")
        total_receipts += receipt['amount']
    
    print(f"إجمالي المقبوضات من receipts: {total_receipts} ل.س")

    # اختبار المصاريف من transactions
    print('\n--- المصاريف من transactions ---')
    cursor.execute("""
        SELECT description, ABS(amount) as amount, created_at FROM transactions
        WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف' AND amount < 0
        ORDER BY created_at DESC
    """, (today, username))
    
    expense_transactions = cursor.fetchall()
    total_expense_transactions = 0
    
    for expense in expense_transactions:
        print(f"• مصروف: {expense['amount']} ل.س - {expense['description']}")
        total_expense_transactions += expense['amount']
    
    print(f"إجمالي المصاريف من transactions: {total_expense_transactions} ل.س")

    # اختبار المصاريف من expenses
    print('\n--- المصاريف من expenses ---')
    cursor.execute("""
        SELECT expense_type, amount, description, created_at FROM expenses
        WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type != 'رواتب'
        ORDER BY created_at DESC
    """, (today, username))
    
    expenses = cursor.fetchall()
    total_expenses = 0
    
    for expense in expenses:
        print(f"• {expense['expense_type']}: {expense['amount']} ل.س - {expense['description']}")
        total_expenses += expense['amount']
    
    print(f"إجمالي المصاريف من expenses: {total_expenses} ل.س")

    # الحسابات النهائية
    print(f'\n=== الحسابات النهائية ===')
    total_income = total_sales + total_receipts
    total_outgoing = total_expense_transactions + total_expenses
    net_amount = total_income - total_outgoing
    
    print(f"إجمالي الدخل: {total_income} ل.س")
    print(f"إجمالي المصاريف: {total_outgoing} ل.س")
    print(f"الصافي: {net_amount} ل.س")

    conn.close()

if __name__ == "__main__":
    main()

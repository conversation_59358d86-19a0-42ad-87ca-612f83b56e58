# -*- coding: utf-8 -*-
"""
نافذة إغلاق الصندوق
Cash Close Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont
from datetime import datetime, date

try:
    from ..utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency

class CashCloseWindow(QDialog):
    """نافذة إغلاق الصندوق"""
    
    cash_closed = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, treasury_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد إذا لم يتم تمريره
        if treasury_manager:
            self.treasury_manager = treasury_manager
        else:
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            self.treasury_manager = UnifiedTreasuryManager(db_manager)

        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(reshape_arabic_text("إغلاق الصندوق"))
        self.setFixedSize(800, 700)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel(reshape_arabic_text(f"إغلاق صندوق يوم {date.today().strftime('%Y-%m-%d')}"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        
        # إحصائيات اليوم
        stats_group = self.create_stats_group()
        
        # العملات
        currency_group = self.create_currency_group()
        
        # المبلغ الفعلي في الصندوق
        actual_group = self.create_actual_amount_group()
        
        # النتيجة
        result_group = self.create_result_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addWidget(stats_group)
        main_layout.addWidget(currency_group)
        main_layout.addWidget(actual_group)
        main_layout.addWidget(result_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_stats_group(self):
        """إنشاء مجموعة الإحصائيات"""
        group = QGroupBox(reshape_arabic_text("إحصائيات اليوم"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # إجمالي المبيعات
        sales_label = QLabel(reshape_arabic_text("إجمالي المبيعات:"))
        sales_label.setFont(create_arabic_font(10))
        self.total_sales_label = QLabel("0 ل.س")
        self.total_sales_label.setFont(create_arabic_font(12, bold=True))
        self.total_sales_label.setStyleSheet("color: #27ae60;")
        
        # إجمالي المصاريف
        expenses_label = QLabel(reshape_arabic_text("إجمالي المصاريف:"))
        expenses_label.setFont(create_arabic_font(10))
        self.total_expenses_label = QLabel("0 ل.س")
        self.total_expenses_label.setFont(create_arabic_font(12, bold=True))
        self.total_expenses_label.setStyleSheet("color: #e74c3c;")
        
        # إجمالي القبض
        receipts_label = QLabel(reshape_arabic_text("إجمالي القبض:"))
        receipts_label.setFont(create_arabic_font(10))
        self.total_receipts_label = QLabel("0 ل.س")
        self.total_receipts_label.setFont(create_arabic_font(12, bold=True))
        self.total_receipts_label.setStyleSheet("color: #3498db;")
        
        # الصافي المتوقع
        expected_label = QLabel(reshape_arabic_text("الصافي المتوقع:"))
        expected_label.setFont(create_arabic_font(10, bold=True))
        self.expected_total_label = QLabel("0 ل.س")
        self.expected_total_label.setFont(create_arabic_font(14, bold=True))
        self.expected_total_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #f8f9fa;
                padding: 8px;
                border: 2px solid #2c3e50;
                border-radius: 5px;
            }
        """)
        
        layout.addWidget(sales_label, 0, 0)
        layout.addWidget(self.total_sales_label, 0, 1)
        layout.addWidget(expenses_label, 1, 0)
        layout.addWidget(self.total_expenses_label, 1, 1)
        layout.addWidget(receipts_label, 2, 0)
        layout.addWidget(self.total_receipts_label, 2, 1)
        layout.addWidget(expected_label, 3, 0)
        layout.addWidget(self.expected_total_label, 3, 1)
        
        return group
        
    def create_currency_group(self):
        """إنشاء مجموعة العملات"""
        group = QGroupBox(reshape_arabic_text("نوع العملة"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QHBoxLayout(group)
        
        currency_label = QLabel(reshape_arabic_text("العملة:"))
        currency_label.setFont(create_arabic_font(10))
        
        self.currency_combo = QComboBox()
        self.currency_combo.setFont(create_arabic_font(10))
        self.currency_combo.addItem("ليرة سورية (ل.س)", "SYP")
        self.currency_combo.addItem("دولار أمريكي ($)", "USD")
        self.currency_combo.addItem("يورو (€)", "EUR")
        
        layout.addWidget(currency_label)
        layout.addWidget(self.currency_combo)
        layout.addStretch()
        
        return group
        
    def create_actual_amount_group(self):
        """إنشاء مجموعة المبلغ الفعلي"""
        group = QGroupBox(reshape_arabic_text("المبلغ الموجود فعلياً في الصندوق"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QHBoxLayout(group)
        
        actual_label = QLabel(reshape_arabic_text("المبلغ الفعلي:"))
        actual_label.setFont(create_arabic_font(12))
        
        self.actual_amount_spin = QDoubleSpinBox()
        self.actual_amount_spin.setFont(create_arabic_font(12))
        self.actual_amount_spin.setRange(0, 999999999)
        self.actual_amount_spin.setDecimals(0)
        self.actual_amount_spin.setMinimumWidth(200)
        self.actual_amount_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        
        layout.addWidget(actual_label)
        layout.addWidget(self.actual_amount_spin)
        layout.addStretch()
        
        return group
        
    def create_result_group(self):
        """إنشاء مجموعة النتيجة"""
        group = QGroupBox(reshape_arabic_text("نتيجة المقارنة"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # الفرق
        difference_label = QLabel(reshape_arabic_text("الفرق:"))
        difference_label.setFont(create_arabic_font(12))
        self.difference_label = QLabel("0 ل.س")
        self.difference_label.setFont(create_arabic_font(14, bold=True))
        
        # الحالة
        status_label = QLabel(reshape_arabic_text("حالة الصندوق:"))
        status_label.setFont(create_arabic_font(12))
        self.status_label = QLabel(reshape_arabic_text("متوازن"))
        self.status_label.setFont(create_arabic_font(14, bold=True))
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                border-radius: 5px;
                background-color: #95a5a6;
                color: white;
            }
        """)
        
        layout.addWidget(difference_label, 0, 0)
        layout.addWidget(self.difference_label, 0, 1)
        layout.addWidget(status_label, 1, 0)
        layout.addWidget(self.status_label, 1, 1)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الإلغاء
        self.cancel_button = QPushButton(reshape_arabic_text("إلغاء"))
        self.cancel_button.setFont(create_arabic_font(10))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        # زر التقرير
        self.report_button = QPushButton(reshape_arabic_text("تقرير مفصل"))
        self.report_button.setFont(create_arabic_font(10))
        self.report_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر التحديث
        self.refresh_button = QPushButton(reshape_arabic_text("تحديث"))
        self.refresh_button.setFont(create_arabic_font(10))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # زر الإغلاق
        self.close_button = QPushButton(reshape_arabic_text("إغلاق الصندوق"))
        self.close_button.setFont(create_arabic_font(10, bold=True))
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(self.cancel_button)
        layout.addWidget(self.refresh_button)
        layout.addStretch()
        layout.addWidget(self.report_button)
        layout.addWidget(self.close_button)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_today_stats()
        self.update_comparison()
        
    def load_today_stats(self):
        """تحميل إحصائيات اليوم"""
        try:
            today = date.today().strftime('%Y-%m-%d')
            print(f"=== تحميل إحصائيات اليوم: {today} ===")

            # الحصول على صندوق اليوم للمستخدم الحالي
            cash_box = self.db_manager.get_or_create_cash_box(
                self.current_user['id'],
                self.current_user['username'],
                today
            )
            if cash_box:
                print(f"صندوق اليوم: ID={cash_box['id']}, المستخدم={self.current_user['full_name']}")

            # إجمالي المقبوضات من جدول transactions (جميع المبيعات)
            print(f"البحث عن جميع المبيعات للمستخدم: {self.current_user['username']}")

            # البحث في جميع المبيعات (بدون تحديد التاريخ أولاً للاختبار)
            all_sales = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM transactions
                WHERE user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة') AND amount > 0
            """, (self.current_user['username'],))

            print(f"إجمالي جميع المبيعات للمستخدم: {all_sales['total'] if all_sales else 0}")

            # البحث في مبيعات المستخدم من لحظة فتح الصندوق (من بداية اليوم)
            today_sales = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ?
                AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
                AND amount > 0
            """, (today, self.current_user['username']))

            total_from_operations = today_sales['total'] if today_sales else 0
            print(f"إجمالي مبيعات المستخدم من فتح الصندوق: {total_from_operations}")

            # تفصيل المبيعات حسب النوع
            sales_detail = self.db_manager.fetch_all("""
                SELECT type, COUNT(*) as count, SUM(amount) as total FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ?
                AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
                AND amount > 0
                GROUP BY type
            """, (today, self.current_user['username']))

            for detail in sales_detail:
                print(f"  • {detail['type']}: {detail['count']} عملية - {detail['total']:,} ل.س")

            # إضافة المقبوضات من جدول receipts (سندات القبض)
            print("البحث عن المقبوضات الإضافية...")
            additional_receipts = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM receipts
                WHERE DATE(receipt_date) = ? AND user_name = ?
            """, (today, self.current_user['username']))

            total_additional = additional_receipts['total'] if additional_receipts else 0
            print(f"إجمالي المقبوضات الإضافية: {total_additional}")

            # الإجمالي الكلي للمقبوضات
            total_receipts = total_from_operations + total_additional
            receipts_result = {'total': total_receipts}
            print(f"إجمالي المقبوضات الكلي: {total_receipts}")

            # البحث في جدول router_deliveries إذا لم توجد مقبوضات
            if not receipts_result or receipts_result['total'] == 0:
                print("البحث في جدول router_deliveries...")

                # مبيعات تسليم الراوترات
                router_deliveries = self.db_manager.fetch_one("""
                    SELECT COALESCE(SUM(total_amount), 0) as total
                    FROM router_deliveries
                    WHERE DATE(delivery_date) = ? AND user_id = ?
                """, (today, self.current_user['id']))

                if router_deliveries and router_deliveries['total'] > 0:
                    receipts_result = router_deliveries
                    print(f"إجمالي المبيعات من router_deliveries: {router_deliveries['total']}")
                else:
                    print("لا توجد مبيعات في router_deliveries")

            # إذا لم توجد مقبوضات، ابحث في جميع الجداول الممكنة
            if not receipts_result or receipts_result['total'] == 0:
                print("البحث الشامل في جميع الجداول...")
                total_from_all = 0

                # البحث في جدول subscribers (الاشتراكات الجديدة)
                try:
                    subscribers_today = self.db_manager.fetch_all("""
                        SELECT * FROM subscribers
                        WHERE DATE(created_at) = ?
                    """, (today,))

                    if subscribers_today:
                        print(f"تم العثور على {len(subscribers_today)} مشترك جديد اليوم")
                        # حساب إجمالي رسوم الاشتراك (افتراضي 50000)
                        total_from_all += len(subscribers_today) * 50000
                except Exception as e:
                    print(f"خطأ في البحث في subscribers: {e}")

                # البحث في جدول vouchers (القسائم)
                try:
                    vouchers_today = self.db_manager.fetch_one("""
                        SELECT COALESCE(SUM(amount), 0) as total FROM vouchers
                        WHERE DATE(created_at) = ? AND user_name = ?
                    """, (today, self.current_user['username']))

                    if vouchers_today and vouchers_today['total'] > 0:
                        total_from_all += vouchers_today['total']
                        print(f"إجمالي القسائم: {vouchers_today['total']}")
                except Exception as e:
                    print(f"خطأ في البحث في vouchers: {e}")

                if total_from_all > 0:
                    receipts_result = {'total': total_from_all}
                    print(f"إجمالي المبيعات من جميع المصادر: {total_from_all}")

            # تشخيص مفصل للعمليات المالية
            print(f"=== تفصيل العمليات المالية لليوم ({today}) للمستخدم {self.current_user['username']} ===")

            # عرض العمليات الأساسية (اشتراك جديد، تسليم راوتر، تجديد باقة)
            main_operations = self.db_manager.fetch_all("""
                SELECT type, description, amount, created_at FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
                ORDER BY created_at DESC
            """, (today, self.current_user['username']))

            print("--- العمليات الأساسية ---")
            for op in main_operations:
                print(f"• {op['type']}: {op['amount']} ل.س - {op['description']}")

            # عرض المقبوضات الإضافية
            additional_receipts_detail = self.db_manager.fetch_all("""
                SELECT amount, description, user_name, created_at FROM receipts
                WHERE DATE(receipt_date) = ? AND user_name = ?
                ORDER BY created_at DESC
            """, (today, self.current_user['username']))

            if additional_receipts_detail:
                print("--- المقبوضات الإضافية ---")
                for receipt in additional_receipts_detail:
                    print(f"• سند قبض: {receipt['amount']} ل.س - {receipt['description']}")

            total_receipts = receipts_result['total'] if receipts_result else 0
            print(f"إجمالي المقبوضات اليوم: {total_receipts} ل.س")
            print(f"تحديث واجهة المقبوضات...")
            self.total_receipts_label.setText(format_currency(total_receipts))
            
            # إجمالي المصاريف العادية للمستخدم من لحظة فتح الصندوق (من بداية اليوم)
            print(f"البحث عن مصاريف المستخدم من فتح الصندوق: {self.current_user['username']}")
            regular_expenses = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM expenses
                WHERE DATE(expense_date) = ? AND user_name = ?
                AND expense_type NOT IN ('راتب', 'رواتب')
            """, (today, self.current_user['username']))

            total_regular_expenses = regular_expenses['total'] if regular_expenses else 0
            print(f"إجمالي المصاريف العادية للمستخدم: {total_regular_expenses}")

            # تفصيل المصاريف حسب النوع
            expenses_detail = self.db_manager.fetch_all("""
                SELECT expense_type, COUNT(*) as count, SUM(amount) as total FROM expenses
                WHERE DATE(expense_date) = ? AND user_name = ?
                AND expense_type NOT IN ('راتب', 'رواتب')
                GROUP BY expense_type
            """, (today, self.current_user['username']))

            for detail in expenses_detail:
                print(f"  • {detail['expense_type']}: {detail['count']} مصروف - {detail['total']:,} ل.س")

            # إضافة المصاريف من جدول transactions
            print("البحث عن المصاريف من transactions...")
            transactions_expenses = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(ABS(amount)), 0) as total FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف' AND amount < 0
            """, (today, self.current_user['username']))

            total_transaction_expenses = transactions_expenses['total'] if transactions_expenses else 0
            print(f"إجمالي المصاريف من transactions: {total_transaction_expenses}")

            # الإجمالي الكلي للمصاريف (باستثناء الرواتب)
            total_expenses = total_regular_expenses + total_transaction_expenses
            expenses_result = {'total': total_expenses}
            print(f"إجمالي المصاريف الكلي (بدون رواتب): {total_expenses}")

            # عرض الرواتب منفصلة (للمعلومات فقط - لا تُحسب في الصندوق)
            salary_expenses = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM expenses
                WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type IN ('راتب', 'رواتب')
            """, (today, self.current_user['username']))

            total_salaries = salary_expenses['total'] if salary_expenses else 0
            if total_salaries > 0:
                print(f"ملاحظة: رواتب تم خصمها من الخزينة: {total_salaries} ل.س")

            # تشخيص مفصل للمصاريف
            print("--- المصاريف ---")

            # عرض المصاريف من transactions
            transaction_expenses = self.db_manager.fetch_all("""
                SELECT description, ABS(amount) as amount, created_at FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف' AND amount < 0
                ORDER BY created_at DESC
            """, (today, self.current_user['username']))

            for exp in transaction_expenses:
                print(f"• مصروف: {exp['amount']} ل.س - {exp['description']}")

            # عرض المصاريف الإضافية من جدول expenses
            additional_expenses_detail = self.db_manager.fetch_all("""
                SELECT expense_type, amount, description, created_at FROM expenses
                WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type != 'رواتب'
                ORDER BY created_at DESC
            """, (today, self.current_user['username']))

            if additional_expenses_detail:
                print("--- المصاريف الإضافية ---")
                for expense in additional_expenses_detail:
                    print(f"• {expense['expense_type']}: {expense['amount']} ل.س - {expense['description']}")

            total_expenses = expenses_result['total'] if expenses_result else 0
            print(f"إجمالي المصاريف اليوم: {total_expenses} ل.س")
            print(f"تحديث واجهة المصاريف...")
            self.total_expenses_label.setText(format_currency(total_expenses))

            # الصافي المتوقع (المقبوضات - المصاريف)
            expected_total = total_receipts - total_expenses
            print(f"الصافي المتوقع: {total_receipts} - {total_expenses} = {expected_total} ل.س")
            self.expected_total_label.setText(format_currency(expected_total))
            
        except Exception as e:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"), 
                              reshape_arabic_text(f"خطأ في تحميل الإحصائيات: {e}"))
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.actual_amount_spin.valueChanged.connect(self.update_comparison)
        self.currency_combo.currentIndexChanged.connect(self.update_comparison)
        
        self.close_button.clicked.connect(self.close_cash)
        self.report_button.clicked.connect(self.show_detailed_report)
        self.refresh_button.clicked.connect(self.load_today_stats)
        self.cancel_button.clicked.connect(self.reject)
        
    def update_comparison(self):
        """تحديث المقارنة"""
        try:
            # الحصول على المبلغ المتوقع
            expected_text = self.expected_total_label.text().replace(" ل.س", "").replace(",", "")
            expected_amount = float(expected_text) if expected_text != "0" else 0
            
            # المبلغ الفعلي
            actual_amount = self.actual_amount_spin.value()
            
            # حساب الفرق
            difference = actual_amount - expected_amount
            
            # عرض الفرق
            self.difference_label.setText(format_currency(abs(difference)))
            
            # تحديد الحالة
            if difference == 0:
                self.status_label.setText(reshape_arabic_text("صحيح ✓"))
                self.status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px;
                        border-radius: 5px;
                        background-color: #27ae60;
                        color: white;
                    }
                """)
                self.difference_label.setStyleSheet("color: #27ae60;")
            elif difference > 0:
                self.status_label.setText(reshape_arabic_text("زائد ↑"))
                self.status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px;
                        border-radius: 5px;
                        background-color: #3498db;
                        color: white;
                    }
                """)
                self.difference_label.setStyleSheet("color: #3498db;")
            else:
                self.status_label.setText(reshape_arabic_text("ناقص ↓"))
                self.status_label.setStyleSheet("""
                    QLabel {
                        padding: 10px;
                        border-radius: 5px;
                        background-color: #e74c3c;
                        color: white;
                    }
                """)
                self.difference_label.setStyleSheet("color: #e74c3c;")
                
        except Exception as e:
            print(f"خطأ في تحديث المقارنة: {e}")
            
    def close_cash(self):
        """إغلاق الصندوق"""
        try:
            # التأكد من وجود المبلغ الفعلي
            if not hasattr(self, 'actual_amount_spin') or self.actual_amount_spin.value() <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ الفعلي للصندوق")
                return

            today = date.today().strftime('%Y-%m-%d')
            user_id = self.current_user['id']

            print(f"=== إغلاق الصندوق لليوم {today} للمستخدم {user_id} ===")

            # الحصول على صندوق اليوم
            cash_box = self.db_manager.get_or_create_cash_box(
                user_id,
                self.current_user['username'],
                today
            )
            if not cash_box:
                QMessageBox.critical(self, "خطأ", "لا يمكن العثور على صندوق اليوم")
                return

            # تحديث إجماليات الصندوق
            totals = self.db_manager.update_cash_box_totals(cash_box['id'])

            # المبلغ الفعلي المدخل
            actual_amount = self.actual_amount_spin.value()
            expected_amount = totals['closing_balance']
            difference = actual_amount - expected_amount

            print(f"المبلغ المتوقع: {expected_amount}")
            print(f"المبلغ الفعلي: {actual_amount}")
            print(f"الفرق: {difference}")

            # إغلاق الصندوق
            close_result = self.db_manager.execute_query("""
                UPDATE cash_boxes
                SET is_closed = 1, closed_at = CURRENT_TIMESTAMP,
                    actual_amount = ?, difference = ?
                WHERE id = ?
            """, (actual_amount, difference, cash_box['id']))

            if close_result is not None:
                # إضافة المبلغ إلى الخزينة
                self.add_to_treasury(actual_amount)

                # مسح مصاريف المستخدم الحالي لليوم (لضمان عدم اختلاط الصناديق)
                self.clear_user_expenses(today)

                # تصفير جميع البيانات للمستخدم (فتح صندوق جديد)
                self.reset_user_data_for_new_box(today)

                # إنشاء نسخة احتياطية
                self.create_backup()

                # إرسال إشارة نجح الإغلاق
                close_data = {
                    'date': today,
                    'expected_amount': expected_amount,
                    'actual_amount': actual_amount,
                    'difference': difference,
                    'status': 'مغلق ✓',
                    'closed_by': self.current_user.get('full_name', 'مستخدم')
                }

                self.cash_closed.emit(close_data)

                QMessageBox.information(self, "تم",
                    f"تم إغلاق الصندوق بنجاح\n"
                    f"المبلغ المضاف للخزينة: {actual_amount:,} ل.س\n"
                    f"تم إنشاء نسخة احتياطية")

                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إغلاق الصندوق")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إغلاق الصندوق: {e}")

    def add_to_treasury(self, amount):
        """إضافة المبلغ إلى الخزينة اليومية"""
        try:
            # إضافة المبلغ إلى الخزينة اليومية
            daily_treasury_result = self.db_manager.execute_query("""
                UPDATE daily_treasury
                SET balance = balance + ?
                WHERE currency_type = 'SYP'
            """, (amount,))

            # استخدام النظام الموحد لإضافة المبلغ للخزينة اليومية
            if hasattr(self, 'treasury_manager') and self.treasury_manager:
                success = self.treasury_manager.add_to_daily_treasury(
                    user_id=self.current_user['id'],
                    currency_type='SYP',
                    amount=amount
                )

                if success:
                    print(f"✅ تم إضافة {amount:,} ل.س إلى الخزينة اليومية الموحدة")

                    # تسجيل العملية في سجل المعاملات
                    self.db_manager.execute_query("""
                        INSERT INTO transactions (type, description, amount, user_name, created_at)
                        VALUES (?, ?, ?, ?, datetime('now'))
                    """, (
                        "إضافة لرصيد الخزينة اليومية",
                        f"إغلاق صندوق {self.current_user['username']} - صافي: {amount:,} ل.س",
                        amount,
                        self.current_user['username']
                    ))
                else:
                    print("❌ فشل في إضافة المبلغ للخزينة اليومية الموحدة")
            else:
                print("⚠️ مدير الخزينة الموحد غير متوفر - استخدام الطريقة القديمة")
                # الطريقة القديمة كاحتياط
                if daily_treasury_result:
                    print(f"✅ تم إضافة {amount:,} ل.س إلى الخزينة اليومية (طريقة قديمة)")
                else:
                    print("⚠️ تحذير: لم يتم تحديث الخزينة اليومية")
                    self.create_daily_treasury_if_not_exists()
                    self.db_manager.execute_query("""
                        UPDATE daily_treasury
                        SET balance = balance + ?
                        WHERE currency_type = 'SYP'
                    """, (amount,))

        except Exception as e:
            print(f"خطأ في إضافة المبلغ للخزينة: {e}")

    def create_daily_treasury_if_not_exists(self):
        """إنشاء جدول الخزينة اليومية إذا لم يكن موجوداً"""
        try:
            # إنشاء جدول الخزينة اليومية
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS daily_treasury (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    currency_type TEXT NOT NULL DEFAULT 'SYP',
                    balance DECIMAL(15,2) DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إدراج سجل افتراضي إذا لم يكن موجوداً
            existing = self.db_manager.fetch_one("""
                SELECT id FROM daily_treasury WHERE currency_type = 'SYP'
            """)

            if not existing:
                self.db_manager.execute_query("""
                    INSERT INTO daily_treasury (currency_type, balance)
                    VALUES ('SYP', 0)
                """)
                print("✅ تم إنشاء جدول الخزينة اليومية")

        except Exception as e:
            print(f"خطأ في إنشاء جدول الخزينة اليومية: {e}")

    def clear_user_expenses(self, close_date):
        """مسح مصاريف المستخدم الحالي لليوم المحدد لضمان عدم اختلاط الصناديق"""
        try:
            # نقل المصاريف إلى جدول أرشيف أو حذفها
            # هنا سنقوم بتحديث حالتها لتصبح "مؤرشفة"
            result = self.db_manager.execute_query("""
                UPDATE expenses
                SET archived = 1, archived_date = CURRENT_TIMESTAMP
                WHERE DATE(expense_date) = ? AND (user_id = ? OR user_name = ?)
            """, (close_date, self.current_user['id'], self.current_user['username']))

            print(f"تم أرشفة مصاريف المستخدم {self.current_user['username']} لليوم {close_date}")

        except Exception as e:
            print(f"خطأ في مسح مصاريف المستخدم: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            import shutil
            import os
            from datetime import datetime

            # مسار قاعدة البيانات الحالية
            db_path = "data/company_system.db"

            # مسار النسخة الاحتياطية
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)

            print(f"تم إنشاء نسخة احتياطية: {backup_path}")

        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")

    def process_salary_payments(self, close_date):
        """خصم الرواتب من الخزينة بعد إغلاق الصندوق"""
        try:
            # جلب جميع الرواتب لهذا اليوم التي لم يتم خصمها من الخزينة بعد
            salary_expenses = self.db_manager.fetch_all("""
                SELECT id, amount, description FROM expenses
                WHERE DATE(expense_date) = ? AND expense_type = 'رواتب'
            """, (close_date,))

            total_salaries = 0
            for salary in salary_expenses:
                # خصم كل راتب من الخزينة
                self.db_manager.execute_query("""
                    INSERT INTO treasury_transfers (transfer_type, amount, description, user_name, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    "سحب",
                    salary['amount'],
                    f"خصم راتب بعد إغلاق الصندوق: {salary['description']}",
                    self.current_user['username'],
                    f"{close_date} 23:59:59"  # وقت متأخر لضمان أنه بعد إغلاق الصندوق
                ))
                total_salaries += salary['amount']

            if total_salaries > 0:
                print(f"تم خصم إجمالي رواتب بمبلغ {total_salaries} ل.س من الخزينة بعد إغلاق الصندوق")
            else:
                print("لا توجد رواتب لخصمها من الخزينة اليوم")

        except Exception as e:
            print(f"خطأ في خصم الرواتب من الخزينة: {e}")
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text(f"تم إغلاق الصندوق لكن حدث خطأ في خصم الرواتب: {e}"))

    def clear_user_expenses(self, date):
        """مسح مصاريف المستخدم الحالي العادية (بدون الرواتب) بعد إغلاق الصندوق"""
        try:
            print(f"مسح مصاريف المستخدم {self.current_user['username']} لتاريخ {date}")

            # مسح المصاريف العادية فقط من جدول expenses (الرواتب تبقى للسجل)
            result1 = self.db_manager.execute_query("""
                DELETE FROM expenses
                WHERE DATE(expense_date) = ? AND user_name = ? AND expense_type NOT IN ('راتب', 'رواتب')
            """, (date, self.current_user['username']))

            # مسح من جدول transactions (المصاريف العادية)
            result2 = self.db_manager.execute_query("""
                DELETE FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type = 'مصروف'
            """, (date, self.current_user['username']))

            print("✅ تم مسح المصاريف العادية للمستخدم - واجهة المصاريف ستصبح فارغة")
            print("ملاحظة: الرواتب محفوظة في السجل ولم يتم مسحها")

        except Exception as e:
            print(f"خطأ في مسح مصاريف المستخدم: {e}")

    def show_detailed_report(self):
        """عرض التقرير المفصل"""
        try:
            # جمع بيانات التقرير
            report_data = {
                'تاريخ الإغلاق': QDate.currentDate().toString("yyyy-MM-dd"),
                'وقت الإغلاق': QTime.currentTime().toString("hh:mm:ss"),
                'إجمالي النقد المتوقع': format_currency(self.expected_cash_spin.value()),
                'النقد الفعلي': format_currency(self.actual_cash_spin.value()),
                'الفرق': format_currency(self.difference_spin.value()),
                'ملاحظات': self.notes_edit.toPlainText().strip() or 'لا توجد ملاحظات'
            }

            # طباعة التقرير
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_document(report_data, "تقرير إغلاق الصندوق", show_preview=True)

            if success:
                QMessageBox.information(self, reshape_arabic_text("تم"),
                                      reshape_arabic_text("تم إرسال تقرير إغلاق الصندوق للطباعة بنجاح"))
            else:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("تم إلغاء عملية الطباعة"))

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في طباعة التقرير: {e}"))

    def reset_user_data_for_new_box(self, date):
        """تصفير بيانات المستخدم لفتح صندوق جديد"""
        try:
            print(f"تصفير بيانات المستخدم {self.current_user['username']} لفتح صندوق جديد...")

            # مسح جميع معاملات المبيعات لليوم (لتصفير الواجهة الرئيسية)
            self.db_manager.execute_query("""
                DELETE FROM transactions
                WHERE DATE(created_at) = ? AND user_name = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
            """, (date, self.current_user['username']))

            # مسح المقبوضات الإضافية لليوم
            self.db_manager.execute_query("""
                DELETE FROM receipts
                WHERE DATE(receipt_date) = ? AND user_name = ?
            """, (date, self.current_user['username']))

            print("✅ تم تصفير بيانات المستخدم - الصندوق جاهز للعمل من جديد")

        except Exception as e:
            print(f"خطأ في تصفير بيانات المستخدم: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح عملية شراء الدولار
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def test_currency_exchange():
    """اختبار عملية شراء الدولار"""
    
    print("🧪 اختبار عملية شراء الدولار...")
    
    db = DatabaseManager('data/company_system.db')
    
    try:
        # عرض الرصيد قبل العملية
        print("\n📊 الرصيد قبل العملية:")
        
        syp_before = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'SYP'")
        usd_before = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'USD'")
        
        syp_balance_before = syp_before['balance'] if syp_before else 0
        usd_balance_before = usd_before['balance'] if usd_before else 0
        
        print(f"  • الليرة السورية: {syp_balance_before:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_balance_before:,.2f}")
        
        # محاكاة عملية شراء دولار
        syp_amount = 150000  # المبلغ المراد صرفه
        exchange_rate = 15000  # سعر الصرف
        usd_amount = syp_amount / exchange_rate  # المبلغ المستلم
        
        print(f"\n💱 محاكاة عملية الصرف:")
        print(f"  • المبلغ المراد صرفه: {syp_amount:,} ل.س")
        print(f"  • سعر الصرف: {exchange_rate:,} ل.س/$")
        print(f"  • المبلغ المستلم: ${usd_amount:.2f}")
        
        # التحقق من توفر الرصيد
        if syp_balance_before >= syp_amount:
            print(f"  ✅ الرصيد كافي للعملية")
            
            # تنفيذ العملية
            db.execute_query("BEGIN TRANSACTION")
            
            try:
                # 1. خصم من الليرة السورية
                syp_updated = db.execute_query("""
                    UPDATE treasury 
                    SET balance = balance - ?, last_updated = datetime('now')
                    WHERE currency_type = 'SYP' AND balance >= ?
                """, (syp_amount, syp_amount))
                
                if syp_updated == 0:
                    raise Exception("فشل في خصم المبلغ من الليرة السورية")
                
                # 2. إضافة للدولار
                usd_updated = db.execute_query("""
                    UPDATE treasury 
                    SET balance = balance + ?, last_updated = datetime('now')
                    WHERE currency_type = 'USD'
                """, (usd_amount,))
                
                if usd_updated == 0:
                    # إنشاء سجل جديد
                    db.execute_query("""
                        INSERT INTO treasury (currency_type, balance, exchange_rate, last_updated)
                        VALUES ('USD', ?, ?, datetime('now'))
                    """, (usd_amount, exchange_rate))
                
                # 3. تسجيل العملية
                db.execute_query("""
                    INSERT INTO currency_exchange
                    (from_currency, to_currency, exchange_rate, amount_from, amount_to, user_id, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
                """, ("SYP", "USD", exchange_rate, syp_amount, usd_amount, 1, "اختبار تلقائي"))
                
                db.execute_query("COMMIT")
                
                print(f"  ✅ تم تنفيذ العملية بنجاح")
                
            except Exception as e:
                db.execute_query("ROLLBACK")
                print(f"  ❌ فشل في تنفيذ العملية: {e}")
                return
        else:
            print(f"  ❌ الرصيد غير كافي: متاح {syp_balance_before:,} ل.س، مطلوب {syp_amount:,} ل.س")
            return
        
        # عرض الرصيد بعد العملية
        print(f"\n📊 الرصيد بعد العملية:")
        
        syp_after = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'SYP'")
        usd_after = db.fetch_one("SELECT balance FROM treasury WHERE currency_type = 'USD'")
        
        syp_balance_after = syp_after['balance'] if syp_after else 0
        usd_balance_after = usd_after['balance'] if usd_after else 0
        
        print(f"  • الليرة السورية: {syp_balance_after:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_balance_after:,.2f}")
        
        # حساب التغيير
        syp_change = syp_balance_after - syp_balance_before
        usd_change = usd_balance_after - usd_balance_before
        
        print(f"\n📈 التغيير:")
        print(f"  • الليرة السورية: {syp_change:+,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_change:+.2f}")
        
        # التحقق من صحة العملية
        if abs(syp_change + syp_amount) < 0.01 and abs(usd_change - usd_amount) < 0.01:
            print(f"  ✅ العملية صحيحة: تم خصم {syp_amount:,} ل.س وإضافة ${usd_amount:.2f}")
        else:
            print(f"  ❌ خطأ في العملية: التغيير غير متطابق")
        
        print(f"\n🎉 انتهى الاختبار!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_currency_exchange()

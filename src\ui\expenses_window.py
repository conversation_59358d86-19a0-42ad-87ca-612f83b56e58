# -*- coding: utf-8 -*-
"""
نافذة إدارة المصاريف
Expenses Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QDoubleSpinBox, QGroupBox, 
                            QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency

# دالة الحصول على الشيفت الحالي
def get_current_shift(db_manager, user_id, user_name):
    """الحصول على الشيفت الحالي للمستخدم أو إنشاء واحد جديد"""
    from datetime import datetime

    today = datetime.now().strftime('%Y-%m-%d')

    # البحث عن شيفت مفتوح لليوم
    current_shift = db_manager.fetch_one("""
        SELECT id FROM shifts
        WHERE user_id = ? AND shift_date = ? AND status = 'open'
    """, (user_id, today))

    if current_shift:
        return current_shift['id']

    # إنشاء شيفت جديد
    try:
        cursor = db_manager.execute_query("""
            INSERT INTO shifts (user_id, user_name, shift_date, start_time, status)
            VALUES (?, ?, ?, ?, 'open')
        """, (user_id, user_name, today, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

        return cursor.lastrowid if hasattr(cursor, 'lastrowid') else 1
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشيفت: {e}")
        return None


class ExpensesWindow(QDialog):
    """نافذة إدارة المصاريف"""
    
    expense_added = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المصاريف")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة المصاريف")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # نموذج إضافة مصروف
        expense_form = self.create_expense_form()
        
        # جدول المصاريف
        self.expenses_table = self.create_expenses_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addWidget(expense_form)
        layout.addWidget(self.expenses_table)
        layout.addLayout(buttons_layout)
        
    def create_expense_form(self):
        """إنشاء نموذج إضافة مصروف"""
        group = QGroupBox("إضافة مصروف جديد")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # نوع المصروف
        type_label = QLabel("نوع المصروف:")
        apply_arabic_style(type_label, 10)
        self.expense_type_combo = QComboBox()
        apply_arabic_style(self.expense_type_combo, 10)
        self.expense_type_combo.addItems([
            "كهرباء", "ماء", "إنترنت", "إيجار", "رواتب", "صيانة", 
            "وقود", "مواصلات", "قرطاسية", "تسويق", "أخرى"
        ])
        
        # المبلغ
        amount_label = QLabel("المبلغ:")
        apply_arabic_style(amount_label, 10)
        self.amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.amount_spin, 10)
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setSuffix(" ل.س")
        
        # التاريخ
        date_label = QLabel("التاريخ:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # الوصف
        description_label = QLabel("الوصف:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QLineEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setPlaceholderText("وصف المصروف...")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        
        # زر الإضافة
        add_button = QPushButton("إضافة مصروف")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        layout.addWidget(type_label, 0, 0)
        layout.addWidget(self.expense_type_combo, 0, 1)
        layout.addWidget(amount_label, 0, 2)
        layout.addWidget(self.amount_spin, 0, 3)
        layout.addWidget(date_label, 1, 0)
        layout.addWidget(self.date_edit, 1, 1)
        layout.addWidget(description_label, 1, 2)
        layout.addWidget(self.description_edit, 1, 3)
        layout.addWidget(notes_label, 2, 0)
        layout.addWidget(self.notes_edit, 2, 1, 1, 2)
        layout.addWidget(add_button, 2, 3)
        
        add_button.clicked.connect(self.add_expense)
        
        return group
        
    def create_expenses_table(self):
        """إنشاء جدول المصاريف"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "الرقم", "النوع", "المبلغ", "التاريخ", "الوصف", "المستخدم", "الملاحظات"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(4, header.Stretch)  # عمود الوصف
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر حذف مصروف
        delete_button = QPushButton("حذف مصروف")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(delete_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        delete_button.clicked.connect(self.delete_expense)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل بيانات المصاريف للمستخدم الحالي فقط"""
        try:
            # تحميل مصاريف المستخدم الحالي فقط (التي لم يتم إغلاق الصندوق عليها بعد)
            expenses = self.db_manager.fetch_all("""
                SELECT id, expense_type, amount, expense_date, description,
                       user_name, notes, created_at
                FROM expenses
                WHERE user_name = ? AND expense_type NOT IN ('راتب', 'رواتب')
                ORDER BY expense_date DESC, created_at DESC
            """, (self.current_user['username'],))

            print(f"تم تحميل {len(expenses)} مصروف للمستخدم {self.current_user['username']}")
            
            self.expenses_table.setRowCount(len(expenses))
            
            for row, expense in enumerate(expenses):
                self.expenses_table.setItem(row, 0, QTableWidgetItem(str(expense['id'])))
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense['expense_type']))
                self.expenses_table.setItem(row, 2, QTableWidgetItem(format_currency(expense['amount'])))
                self.expenses_table.setItem(row, 3, QTableWidgetItem(expense['expense_date']))
                self.expenses_table.setItem(row, 4, QTableWidgetItem(expense['description'] or ''))
                self.expenses_table.setItem(row, 5, QTableWidgetItem(expense['user_name'] or ''))
                self.expenses_table.setItem(row, 6, QTableWidgetItem(expense['notes'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المصاريف: {e}")
            
    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            # التحقق من البيانات
            if self.amount_spin.value() <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if not self.description_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف للمصروف")
                return
            
            expense_data = {
                'expense_type': self.expense_type_combo.currentText(),
                'amount': self.amount_spin.value(),
                'expense_date': self.date_edit.date().toString("yyyy-MM-dd"),
                'description': self.description_edit.text().strip(),
                'notes': self.notes_edit.toPlainText().strip(),
                'user_name': self.current_user['username']
            }
            
            # الحصول على الشيفت الحالي
            try:
                current_shift_id = get_current_shift(self.db_manager, self.current_user['id'], self.current_user['username'])
                if not current_shift_id:
                    raise ValueError("فشل في الحصول على الشيفت الحالي")
                print(f"📂 الشيفت الحالي: {current_shift_id}")
            except Exception as shift_error:
                print(f"❌ خطأ في الحصول على الشيفت: {shift_error}")
                current_shift_id = 1

            # حفظ المصروف مع تشخيص مفصل
            print(f"=== إضافة مصروف جديد ===")
            print(f"النوع: {expense_data['expense_type']}")
            print(f"المبلغ: {expense_data['amount']}")
            print(f"التاريخ: {expense_data['expense_date']}")
            print(f"الوصف: {expense_data['description']}")
            print(f"المستخدم: {expense_data['user_name']}")

            # حفظ المصروف في قاعدة البيانات
            result = self.db_manager.execute_query("""
                INSERT INTO expenses (expense_type, amount, expense_date, description, notes, user_name, shift_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                expense_data['expense_type'], expense_data['amount'], expense_data['expense_date'],
                expense_data['description'], expense_data['notes'], expense_data['user_name'], current_shift_id
            ))

            print(f"تم حفظ المصروف في جدول expenses بنجاح")
            
            # تحديد مصدر الخصم حسب نوع المصروف
            if expense_data['expense_type'] in ['راتب', 'رواتب']:
                # الرواتب تُخصم فوراً من الخزينة اليومية
                print(f"خصم راتب بمبلغ {expense_data['amount']} ل.س من الخزينة اليومية...")

                # خصم من الخزينة
                treasury_result = self.db_manager.execute_query("""
                    UPDATE treasury
                    SET balance = balance - ?
                    WHERE currency_type = 'SYP'
                """, (expense_data['amount'],))

                if treasury_result:
                    print(f"✅ تم خصم الراتب من الخزينة بنجاح")

                    # تسجيل العملية في سجل الخزينة
                    self.db_manager.execute_query("""
                        INSERT INTO transactions (type, description, amount, user_name, created_at)
                        VALUES (?, ?, ?, ?, datetime('now'))
                    """, (
                        "خصم راتب من الخزينة",
                        f"خصم راتب: {expense_data['description']}",
                        -expense_data['amount'],
                        expense_data['user_name']
                    ))
                else:
                    print("⚠️ تحذير: لم يتم خصم الراتب من الخزينة")
            else:
                # المصاريف العادية تبقى في الصندوق حتى الإغلاق
                print(f"تم حفظ مصروف {expense_data['expense_type']} بمبلغ {expense_data['amount']} ل.س")
                print("سيتم خصمه من الصندوق عند الإغلاق")
            
            # رسالة النجاح حسب نوع المصروف
            if expense_data['expense_type'] in ['راتب', 'رواتب']:
                QMessageBox.information(self, "تم",
                    f"تم إضافة الراتب وخصمه من الخزينة اليومية\n"
                    f"المبلغ: {expense_data['amount']:,} ل.س")
            else:
                QMessageBox.information(self, "تم",
                    f"تم إضافة المصروف بنجاح\n"
                    f"سيتم خصمه من الصندوق عند الإغلاق\n"
                    f"المبلغ: {expense_data['amount']:,} ل.س")
            
            # إرسال إشارة
            self.expense_added.emit(expense_data)
            
            # إعادة تعيين النموذج
            self.reset_form()
            
            # تحديث الجدول
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المصروف: {e}")
            
    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للحذف")
            return
            
        expense_id = self.expenses_table.item(current_row, 0).text()
        expense_type = self.expenses_table.item(current_row, 1).text()
        amount = self.expenses_table.item(current_row, 2).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف المصروف '{expense_type}' بقيمة {amount}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM expenses WHERE id = ?", (expense_id,))
                QMessageBox.information(self, "تم", "تم حذف المصروف بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المصروف: {e}")
                
    def reset_form(self):
        """إعادة تعيين النموذج"""
        self.amount_spin.setValue(0)
        self.date_edit.setDate(QDate.currentDate())
        self.description_edit.clear()
        self.notes_edit.clear()
        self.expense_type_combo.setCurrentIndex(0)

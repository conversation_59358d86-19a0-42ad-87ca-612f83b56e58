#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل مع جميع الواجهات
Complete System Run with All Interfaces
"""

import sys
import os
from pathlib import Path

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت - النسخة الكاملة")
    print("=" * 60)
    print("✅ الواجهات المتاحة:")
    print("   • اشتراك جديد")
    print("   • تسليم راوتر") 
    print("   • تجديد باقة")
    print("   • إغلاق الصندوق")
    print("   • إدارة المشتركين")
    print("   • إدارة المنتجات")
    print("   • الإعدادات")
    print("=" * 60)
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        print("📦 تحميل المكتبات...")
        
        # تجاهل تحذيرات Qt
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
        
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("📁 تحميل وحدات النظام...")
        from src.database.database_manager import DatabaseManager
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        
        print("✅ تم تحميل جميع الوحدات بنجاح")
        
        # إنشاء التطبيق
        print("🚀 بدء تشغيل التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        if setup_arabic_support(app):
            print("✅ تم إعداد الدعم العربي")
        else:
            print("⚠️ تحذير: مشكلة في إعداد الدعم العربي")
        
        # إنشاء مجلد البيانات
        project_dir = project_root / "data"
        project_dir.mkdir(exist_ok=True)
        
        print(f"📁 مجلد البيانات: {project_dir}")
        
        # إنشاء قاعدة البيانات والإعدادات
        db_manager = DatabaseManager(str(project_dir))
        config_manager = ConfigManager(str(project_dir))
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
            
        print("✅ تم تهيئة قاعدة البيانات")
        
        # عرض نافذة تسجيل الدخول
        print("🔐 عرض نافذة تسجيل الدخول...")
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() != login_window.Accepted:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
        current_user = login_window.get_current_user()
        print(f"✅ تم تسجيل الدخول: {current_user['full_name']}")
        
        # عرض النافذة الرئيسية
        print("🏠 عرض النافذة الرئيسية...")
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        print("🎉 تم تشغيل النظام الكامل بنجاح!")
        print("=" * 60)
        print("📋 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 60)
        print("🔧 الواجهات المتاحة:")
        print("   ✅ اشتراك جديد - كامل")
        print("   ✅ تسليم راوتر - كامل")
        print("   ✅ تجديد باقة - كامل")
        print("   ✅ إغلاق الصندوق - كامل")
        print("   ✅ إدارة المشتركين - كامل")
        print("   ✅ إدارة المنتجات - كامل")
        print("   ✅ الإعدادات - كامل")
        print("   🚧 المشتريات - قيد التطوير")
        print("   🚧 شحن الرصيد - قيد التطوير")
        print("   🚧 التقارير - قيد التطوير")
        print("=" * 60)
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

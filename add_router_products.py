#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة منتجات راوتر لقاعدة البيانات
"""

import sqlite3

def add_router_products():
    """إضافة منتجات راوتر"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        # منتجات الراوتر
        routers = [
            ('راوتر TP-Link AC1200', 'router', 85000, 50, 'قطعة'),
            ('راوتر Huawei HG8245H', 'router', 120000, 30, 'قطعة'),
            ('راوتر ZTE F670L', 'router', 95000, 40, 'قطعة'),
            ('راوتر Mikrotik hAP ac2', 'router', 180000, 20, 'قطعة'),
            ('راوتر ASUS RT-AC66U', 'router', 250000, 15, 'قطعة'),
            ('راوتر Netgear R6120', 'router', 110000, 25, 'قطعة'),
            ('راوتر D-Link DIR-825', 'router', 75000, 35, 'قطعة'),
        ]
        
        print('📡 إضافة منتجات الراوتر...')
        
        for router in routers:
            name, category, unit_price, stock_quantity, unit_type = router
            
            # التحقق من وجود المنتج
            existing = cursor.execute('SELECT id FROM products WHERE name = ?', (name,)).fetchone()
            
            if existing:
                print(f'⚠️ المنتج موجود بالفعل: {name}')
                continue
            
            # إضافة المنتج
            cursor.execute('''
                INSERT INTO products (name, category, unit_price, stock_quantity, unit_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, category, unit_price, stock_quantity, unit_type))
            
            print(f'✅ تم إضافة: {name} - {unit_price:,} ل.س (مخزون: {stock_quantity} {unit_type})')
        
        conn.commit()
        print('🎉 تم إضافة جميع منتجات الراوتر بنجاح!')
        
        # عرض المنتجات المضافة
        print('\n📡 منتجات الراوتر المتوفرة:')
        routers_in_db = cursor.execute('''
            SELECT id, name, unit_price, stock_quantity, unit_type
            FROM products 
            WHERE category = "router"
            ORDER BY name
        ''').fetchall()
        
        for router in routers_in_db:
            router_id, name, price, qty, unit = router
            print(f'  • ID: {router_id} | {name}: {price:,} ل.س (مخزون: {qty} {unit})')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    add_router_products()

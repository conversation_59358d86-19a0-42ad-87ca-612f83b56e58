# -*- coding: utf-8 -*-
"""
نافذة سند دفع للموردين
Payment Voucher to Suppliers Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QDoubleSpinBox, QGroupBox, 
                            QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency


class VoucherWindow(QDialog):
    """نافذة سند دفع للموردين"""
    
    voucher_added = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)

        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سند دفع للموردين")
        self.setGeometry(100, 100, 1000, 700)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("سند دفع للموردين")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #fadbd8;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # نموذج إضافة سند دفع
        voucher_form = self.create_voucher_form()
        
        # جدول سندات الدفع
        self.vouchers_table = self.create_vouchers_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addWidget(voucher_form)
        layout.addWidget(self.vouchers_table)
        layout.addLayout(buttons_layout)
        
    def create_voucher_form(self):
        """إنشاء نموذج إضافة سند دفع"""
        group = QGroupBox("إضافة سند دفع جديد")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # رقم السند
        voucher_number_label = QLabel("رقم السند:")
        apply_arabic_style(voucher_number_label, 10)
        self.voucher_number_edit = QLineEdit()
        apply_arabic_style(self.voucher_number_edit, 10)
        self.voucher_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        self.voucher_number_edit.setReadOnly(True)
        
        # المورد
        supplier_label = QLabel("المورد:")
        apply_arabic_style(supplier_label, 10)
        self.supplier_combo = QComboBox()
        apply_arabic_style(self.supplier_combo, 10)
        
        # العملة
        currency_label = QLabel("العملة:")
        apply_arabic_style(currency_label, 10)
        self.currency_combo = QComboBox()
        apply_arabic_style(self.currency_combo, 10)
        self.currency_combo.addItem("الليرة السورية", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي", "USD")
        self.currency_combo.addItem("اليورو", "EUR")

        # المبلغ
        amount_label = QLabel("المبلغ:")
        apply_arabic_style(amount_label, 10)
        self.amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.amount_spin, 10)
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setSuffix(" ل.س")
        
        # التاريخ
        date_label = QLabel("التاريخ:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # الوصف
        description_label = QLabel("الوصف:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QLineEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setPlaceholderText("وصف سند الدفع...")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        
        # زر الإضافة
        add_button = QPushButton("إضافة سند دفع")
        apply_arabic_style(add_button, 10)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        layout.addWidget(voucher_number_label, 0, 0)
        layout.addWidget(self.voucher_number_edit, 0, 1)
        layout.addWidget(supplier_label, 0, 2)
        layout.addWidget(self.supplier_combo, 0, 3)
        layout.addWidget(currency_label, 1, 0)
        layout.addWidget(self.currency_combo, 1, 1)
        layout.addWidget(amount_label, 1, 2)
        layout.addWidget(self.amount_spin, 1, 3)
        layout.addWidget(date_label, 2, 0)
        layout.addWidget(self.date_edit, 2, 1)
        layout.addWidget(description_label, 2, 2)
        layout.addWidget(self.description_edit, 2, 3)
        layout.addWidget(notes_label, 3, 0)
        layout.addWidget(self.notes_edit, 3, 1, 1, 2)
        layout.addWidget(add_button, 3, 3)
        
        add_button.clicked.connect(self.add_voucher)
        self.currency_combo.currentTextChanged.connect(self.update_currency_suffix)

        return group

    def update_currency_suffix(self):
        """تحديث رمز العملة في حقل المبلغ"""
        currency = self.currency_combo.currentData()
        if currency == "SYP":
            self.amount_spin.setSuffix(" ل.س")
        elif currency == "USD":
            self.amount_spin.setSuffix(" $")
        elif currency == "EUR":
            self.amount_spin.setSuffix(" €")
        
    def create_vouchers_table(self):
        """إنشاء جدول سندات الدفع"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(9)
        table.setHorizontalHeaderLabels([
            "الرقم", "رقم السند", "المورد", "العملة", "المبلغ", "التاريخ",
            "الوصف", "المستخدم", "الملاحظات"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(5, header.Stretch)  # عمود الوصف
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر حذف سند
        delete_button = QPushButton("حذف سند")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر طباعة
        print_button = QPushButton("طباعة سند")
        apply_arabic_style(print_button, 10)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(delete_button)
        layout.addWidget(print_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        delete_button.clicked.connect(self.delete_voucher)
        print_button.clicked.connect(self.print_voucher)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_suppliers()
        self.load_vouchers_data()
        self.generate_voucher_number()
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all("""
                SELECT id, name FROM suppliers 
                WHERE is_active = 1 ORDER BY name
            """)
            
            self.supplier_combo.clear()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['name'], supplier)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموردين: {e}")
            
    def load_vouchers_data(self):
        """تحميل بيانات سندات الدفع"""
        try:
            vouchers = self.db_manager.fetch_all("""
                SELECT id, voucher_number, supplier_name, amount, 
                       voucher_date, description, user_name, notes, created_at
                FROM vouchers 
                ORDER BY voucher_date DESC, created_at DESC
            """)
            
            self.vouchers_table.setRowCount(len(vouchers))
            
            for row, voucher in enumerate(vouchers):
                self.vouchers_table.setItem(row, 0, QTableWidgetItem(str(voucher['id'])))
                self.vouchers_table.setItem(row, 1, QTableWidgetItem(voucher['voucher_number']))
                self.vouchers_table.setItem(row, 2, QTableWidgetItem(voucher['supplier_name']))
                self.vouchers_table.setItem(row, 3, QTableWidgetItem(format_currency(voucher['amount'])))
                self.vouchers_table.setItem(row, 4, QTableWidgetItem(voucher['voucher_date']))
                self.vouchers_table.setItem(row, 5, QTableWidgetItem(voucher['description'] or ''))
                self.vouchers_table.setItem(row, 6, QTableWidgetItem(voucher['user_name'] or ''))
                self.vouchers_table.setItem(row, 7, QTableWidgetItem(voucher['notes'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات سندات الدفع: {e}")
            
    def generate_voucher_number(self):
        """إنشاء رقم سند جديد"""
        try:
            # جلب آخر رقم سند
            last_voucher = self.db_manager.fetch_one("""
                SELECT voucher_number FROM vouchers 
                ORDER BY id DESC LIMIT 1
            """)
            
            if last_voucher and last_voucher['voucher_number']:
                # استخراج الرقم من آخر سند
                last_number = int(last_voucher['voucher_number'].replace('PAY-', ''))
                new_number = last_number + 1
            else:
                new_number = 1
                
            voucher_number = f"PAY-{new_number:06d}"
            self.voucher_number_edit.setText(voucher_number)
            
        except Exception as e:
            # في حالة الخطأ، استخدم رقم افتراضي
            self.voucher_number_edit.setText("PAY-000001")
            
    def add_voucher(self):
        """إضافة سند دفع جديد"""
        try:
            # التحقق من البيانات
            if self.amount_spin.value() <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return
                
            if not self.description_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال وصف لسند الدفع")
                return
                
            supplier_data = self.supplier_combo.currentData()
            if not supplier_data:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد")
                return
            
            currency = self.currency_combo.currentData()
            currency_name = self.currency_combo.currentText()

            voucher_data = {
                'voucher_number': self.voucher_number_edit.text(),
                'supplier_name': supplier_data['name'],
                'currency': currency,
                'currency_name': currency_name,
                'amount': self.amount_spin.value(),
                'voucher_date': self.date_edit.date().toString("yyyy-MM-dd"),
                'description': self.description_edit.text().strip(),
                'notes': self.notes_edit.toPlainText().strip(),
                'user_name': self.current_user['username']
            }
            
            # حفظ سند الدفع
            self.db_manager.execute_query("""
                INSERT INTO vouchers (voucher_number, supplier_name, currency, amount,
                                    voucher_date, description, notes, user_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                voucher_data['voucher_number'], voucher_data['supplier_name'],
                voucher_data['currency'], voucher_data['amount'], voucher_data['voucher_date'],
                voucher_data['description'], voucher_data['notes'],
                voucher_data['user_name']
            ))
            
            # خصم من الخزينة اليومية مباشرة باستخدام النظام الموحد (بالعملة المحددة)
            treasury_success = self.treasury_manager.subtract_from_daily_treasury(
                user_id=self.current_user['id'],
                currency_type=voucher_data['currency'],
                amount=voucher_data['amount']
            )

            if not treasury_success:
                raise Exception(f"فشل في خصم المبلغ من الخزينة اليومية ({voucher_data['currency_name']})")
            
            QMessageBox.information(self, "تم", f"تم إضافة سند الدفع وخصم {voucher_data['amount']:,.2f} {voucher_data['currency_name']} من الخزينة اليومية بنجاح")
            
            # إرسال إشارة
            self.voucher_added.emit(voucher_data)
            
            # إعادة تعيين النموذج
            self.reset_form()
            
            # تحديث البيانات
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة سند الدفع: {e}")
            
    def delete_voucher(self):
        """حذف سند دفع"""
        current_row = self.vouchers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سند للحذف")
            return
            
        voucher_id = self.vouchers_table.item(current_row, 0).text()
        voucher_number = self.vouchers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف سند الدفع '{voucher_number}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM vouchers WHERE id = ?", (voucher_id,))
                QMessageBox.information(self, "تم", "تم حذف سند الدفع بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف سند الدفع: {e}")
                
    def print_voucher(self):
        """طباعة سند الدفع"""
        QMessageBox.information(self, "قريباً", "ميزة طباعة سند الدفع قيد التطوير")
        
    def reset_form(self):
        """إعادة تعيين النموذج"""
        self.amount_spin.setValue(0)
        self.date_edit.setDate(QDate.currentDate())
        self.description_edit.clear()
        self.notes_edit.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.generate_voucher_number()

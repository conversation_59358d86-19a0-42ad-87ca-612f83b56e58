#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث واجهة نقل الخزينة لدعم العملات المتعددة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def update_treasury_transfer_support():
    """تحديث دعم العملات المتعددة في نقل الخزينة"""
    
    print("🔄 تحديث دعم العملات المتعددة في نقل الخزينة...")
    
    # الاتصال بقاعدة البيانات
    db_path = Path("data/company_system.db")
    if not db_path.exists():
        db_path = Path("company_system.db")
    
    db_manager = DatabaseManager(str(db_path))
    
    try:
        # التحقق من جدول الخزينة
        treasury_columns = db_manager.fetch_all("PRAGMA table_info(treasury)")
        column_names = [col[1] for col in treasury_columns]
        
        print(f"📊 أعمدة جدول الخزينة: {column_names}")
        
        # إضافة أعمدة جديدة إذا لم تكن موجودة
        new_columns = [
            ('currency_type', 'TEXT DEFAULT "SYP"', 'نوع العملة'),
            ('exchange_rate', 'REAL DEFAULT 1.0', 'سعر الصرف مقابل الليرة السورية'),
            ('last_updated', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'آخر تحديث')
        ]
        
        for col_name, col_type, description in new_columns:
            if col_name not in column_names:
                try:
                    db_manager.execute_query(f"ALTER TABLE treasury ADD COLUMN {col_name} {col_type}")
                    print(f"✅ تم إضافة عمود {col_name} - {description}")
                except Exception as e:
                    print(f"⚠️ تحذير في إضافة عمود {col_name}: {e}")
            else:
                print(f"✅ عمود {col_name} موجود بالفعل")
        
        # إنشاء فهرس فريد للعملة
        try:
            db_manager.execute_query("""
                CREATE UNIQUE INDEX IF NOT EXISTS idx_treasury_currency 
                ON treasury(currency_type)
            """)
            print("✅ تم إنشاء فهرس العملة")
        except Exception as e:
            print(f"⚠️ تحذير في إنشاء فهرس العملة: {e}")
        
        # إضافة عملات افتراضية
        default_currencies = [
            ('SYP', 0, 1.0, 'الليرة السورية'),
            ('USD', 0, 15000.0, 'الدولار الأمريكي'),
            ('EUR', 0, 16000.0, 'اليورو')
        ]
        
        for currency_type, balance, exchange_rate, description in default_currencies:
            existing = db_manager.fetch_one("""
                SELECT currency_type FROM treasury WHERE currency_type = ?
            """, (currency_type,))
            
            if not existing:
                db_manager.execute_query("""
                    INSERT INTO treasury (currency_type, balance, exchange_rate, last_updated)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (currency_type, balance, exchange_rate))
                print(f"✅ تم إضافة عملة {description} ({currency_type})")
            else:
                # تحديث سعر الصرف
                db_manager.execute_query("""
                    UPDATE treasury 
                    SET exchange_rate = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE currency_type = ?
                """, (exchange_rate, currency_type))
                print(f"✅ تم تحديث سعر صرف {description}")
        
        # تحديث جدول نقل الخزينة
        transfer_columns = db_manager.fetch_all("PRAGMA table_info(treasury_transfers)")
        transfer_column_names = [col[1] for col in transfer_columns]
        
        print(f"📊 أعمدة جدول نقل الخزينة: {transfer_column_names}")
        
        # إضافة أعمدة جديدة لجدول النقل
        transfer_new_columns = [
            ('currency_type', 'TEXT DEFAULT "SYP"', 'نوع العملة المنقولة'),
            ('exchange_rate_used', 'REAL DEFAULT 1.0', 'سعر الصرف المستخدم'),
            ('amount_in_syp', 'REAL', 'المبلغ بالليرة السورية')
        ]
        
        for col_name, col_type, description in transfer_new_columns:
            if col_name not in transfer_column_names:
                try:
                    db_manager.execute_query(f"ALTER TABLE treasury_transfers ADD COLUMN {col_name} {col_type}")
                    print(f"✅ تم إضافة عمود {col_name} - {description}")
                except Exception as e:
                    print(f"⚠️ تحذير في إضافة عمود {col_name}: {e}")
        
        # تحديث البيانات الموجودة
        try:
            # تحديث العملة الافتراضية للنقلات الموجودة
            db_manager.execute_query("""
                UPDATE treasury_transfers 
                SET currency_type = 'SYP', exchange_rate_used = 1.0, amount_in_syp = amount
                WHERE currency_type IS NULL
            """)
            print("✅ تم تحديث النقلات الموجودة")
        except Exception as e:
            print(f"⚠️ تحذير في تحديث النقلات: {e}")
        
        # عرض حالة العملات
        currencies = db_manager.fetch_all("""
            SELECT currency_type, balance, exchange_rate, last_updated
            FROM treasury
            ORDER BY currency_type
        """)
        
        print(f"\n💱 العملات المتوفرة في النظام:")
        for currency in currencies:
            currency_name = {
                'SYP': 'الليرة السورية',
                'USD': 'الدولار الأمريكي',
                'EUR': 'اليورو'
            }.get(currency[0], currency[0])
            
            if currency[0] == 'SYP':
                print(f"  • {currency_name}: {currency[1]:,.0f} ل.س")
            else:
                print(f"  • {currency_name}: {currency[1]:,.2f} {currency[0]} (سعر الصرف: {currency[2]:,.0f} ل.س)")
        
        # إضافة بيانات تجريبية للعملات
        test_amounts = [
            ('USD', 500.0, 'رصيد تجريبي بالدولار'),
            ('EUR', 300.0, 'رصيد تجريبي باليورو')
        ]
        
        for currency_type, amount, description in test_amounts:
            current_balance = db_manager.fetch_one("""
                SELECT balance FROM treasury WHERE currency_type = ?
            """, (currency_type,))
            
            if current_balance and current_balance[0] == 0:
                db_manager.execute_query("""
                    UPDATE treasury 
                    SET balance = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE currency_type = ?
                """, (amount, currency_type))
                print(f"✅ تم إضافة {description}: {amount} {currency_type}")
        
        print("\n🎉 تم تحديث دعم العملات المتعددة بنجاح!")
        
        # عرض الحالة النهائية
        final_currencies = db_manager.fetch_all("""
            SELECT currency_type, balance, exchange_rate
            FROM treasury
            WHERE balance > 0
            ORDER BY currency_type
        """)
        
        print(f"\n💰 العملات المتوفرة حالياً:")
        total_syp_value = 0
        for currency in final_currencies:
            if currency[0] == 'SYP':
                print(f"  • الليرة السورية: {currency[1]:,.0f} ل.س")
                total_syp_value += currency[1]
            else:
                syp_equivalent = currency[1] * currency[2]
                total_syp_value += syp_equivalent
                currency_name = {
                    'USD': 'الدولار الأمريكي',
                    'EUR': 'اليورو'
                }.get(currency[0], currency[0])
                print(f"  • {currency_name}: {currency[1]:,.2f} {currency[0]} (≈ {syp_equivalent:,.0f} ل.س)")
        
        print(f"\n📊 إجمالي القيمة بالليرة السورية: {total_syp_value:,.0f} ل.س")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث دعم العملات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    update_treasury_transfer_support()

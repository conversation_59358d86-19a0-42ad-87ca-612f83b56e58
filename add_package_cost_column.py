#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود package_cost لجدول router_deliveries
"""

import sqlite3

def add_package_cost_column():
    """إضافة عمود package_cost"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        # التحقق من أعمدة جدول router_deliveries
        columns = cursor.execute('PRAGMA table_info(router_deliveries)').fetchall()
        print('أعمدة جدول router_deliveries:')
        for col in columns:
            print(f'  • {col[1]} - {col[2]}')
        
        # إضافة عمود package_cost إذا لم يكن موجوداً
        try:
            cursor.execute('ALTER TABLE router_deliveries ADD COLUMN package_cost REAL DEFAULT 0')
            print('✅ تم إضافة عمود package_cost')
        except Exception as e:
            if 'duplicate column name' in str(e):
                print('ℹ️ عمود package_cost موجود بالفعل')
            else:
                print(f'❌ خطأ في إضافة العمود: {e}')
        
        conn.commit()
        
        # التحقق من الأعمدة بعد الإضافة
        print('\nأعمدة الجدول بعد التحديث:')
        columns = cursor.execute('PRAGMA table_info(router_deliveries)').fetchall()
        for col in columns:
            print(f'  • {col[1]} - {col[2]}')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ خطأ عام: {e}')

if __name__ == "__main__":
    add_package_cost_column()

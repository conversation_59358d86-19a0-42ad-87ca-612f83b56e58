#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء مستخدم بسيط للاختبار
"""

import sqlite3
import hashlib

def create_simple_user():
    """إنشاء مستخدم بسيط للاختبار"""
    print("=== إنشاء مستخدم بسيط للاختبار ===")
    
    db_path = "data/company_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حذف المستخدم admin الحالي
        cursor.execute("DELETE FROM users WHERE username = 'admin'")
        print("🗑️ تم حذف المستخدم admin القديم")
        
        # إنشاء مستخدم admin جديد بكلمة مرور بسيطة
        username = "admin"
        password = "123"  # كلمة مرور بسيطة جداً
        full_name = "المدير العام"
        email = "<EMAIL>"
        role = "admin"
        is_active = 1
        
        # تشفير كلمة المرور
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        print(f"📝 إنشاء مستخدم جديد:")
        print(f"   اسم المستخدم: {username}")
        print(f"   كلمة المرور: {password}")
        print(f"   كلمة المرور المشفرة: {password_hash[:20]}...")
        
        # إدراج المستخدم الجديد
        cursor.execute("""
            INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (username, password_hash, full_name, email, role, is_active))
        
        conn.commit()
        print("✅ تم إنشاء المستخدم الجديد")
        
        # التحقق من الإنشاء
        cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
        new_user = cursor.fetchone()
        
        if new_user:
            print("✅ تم التحقق من إنشاء المستخدم")
            
            # اختبار تسجيل الدخول
            test_hash = hashlib.sha256(password.encode()).hexdigest()
            cursor.execute("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, test_hash))
            
            login_test = cursor.fetchone()
            
            if login_test:
                print("✅ اختبار تسجيل الدخول نجح")
                print("\n📋 بيانات الدخول الجديدة:")
                print(f"   اسم المستخدم: {username}")
                print(f"   كلمة المرور: {password}")
                print("=" * 50)
                print("⚠️ تذكر: كلمة المرور الآن هي '123' وليس 'admin123'")
                print("=" * 50)
            else:
                print("❌ فشل اختبار تسجيل الدخول")
        else:
            print("❌ فشل في إنشاء المستخدم")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    create_simple_user()

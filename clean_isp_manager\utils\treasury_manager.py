#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الخزينة الموحد المحسن
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from database.database_manager import DatabaseManager

class TreasuryManager:
    """مدير الخزينة الموحد المحسن"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة مدير الخزينة
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    def open_cash_box(self, user_id: int, opening_balance: float = 0.0, 
                     currency: str = 'SYP') -> bool:
        """
        فتح صندوق نقدي جديد
        
        Args:
            user_id: معرف المستخدم
            opening_balance: الرصيد الافتتاحي
            currency: نوع العملة
            
        Returns:
            bool: True إذا تم الفتح بنجاح
        """
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            # التحقق من وجود جلسة نشطة
            existing_session = self.db_manager.fetch_one("""
                SELECT id FROM treasury 
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (user_id, session_date, currency))
            
            if existing_session:
                self.logger.info(f"جلسة نشطة موجودة للمستخدم {user_id}")
                return True
            
            # إنشاء جلسة جديدة
            success = self.db_manager.execute_query("""
                INSERT INTO treasury (user_id, currency, daily_balance, main_balance, 
                                    session_date, is_session_active)
                VALUES (?, ?, ?, 0, ?, 1)
            """, (user_id, currency, opening_balance, session_date))
            
            if success:
                self.logger.info(f"تم فتح صندوق نقدي للمستخدم {user_id}")
                
                # تسجيل العملية
                self.log_transaction(user_id, 'open_cash_box', 'فتح صندوق نقدي', 
                                   opening_balance, currency)
            
            return success
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح الصندوق النقدي: {e}")
            return False
    
    def close_cash_box(self, user_id: int, currency: str = 'SYP') -> Dict:
        """
        إغلاق الصندوق النقدي
        
        Args:
            user_id: معرف المستخدم
            currency: نوع العملة
            
        Returns:
            Dict: تفاصيل الإغلاق
        """
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            # الحصول على الجلسة النشطة
            session = self.db_manager.fetch_one("""
                SELECT * FROM treasury 
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (user_id, session_date, currency))
            
            if not session:
                return {'success': False, 'message': 'لا توجد جلسة نشطة'}
            
            # حساب إجمالي المبيعات والمصروفات
            sales_total = self.get_user_sales_total(user_id, session_date, currency)
            expenses_total = self.get_user_expenses_total(user_id, session_date, currency)
            
            closing_balance = session['daily_balance']
            
            # إغلاق الجلسة
            success = self.db_manager.execute_query("""
                UPDATE treasury 
                SET is_session_active = 0, last_updated = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (session['id'],))
            
            if success:
                # تسجيل العملية
                self.log_transaction(user_id, 'close_cash_box', 'إغلاق صندوق نقدي', 
                                   closing_balance, currency)
                
                return {
                    'success': True,
                    'opening_balance': session['daily_balance'],
                    'sales_total': sales_total,
                    'expenses_total': expenses_total,
                    'closing_balance': closing_balance,
                    'net_profit': sales_total - expenses_total
                }
            
            return {'success': False, 'message': 'فشل في إغلاق الجلسة'}
            
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الصندوق النقدي: {e}")
            return {'success': False, 'message': str(e)}
    
    def add_to_daily_balance(self, user_id: int, amount: float, 
                           currency: str = 'SYP', description: str = '') -> bool:
        """
        إضافة مبلغ للرصيد اليومي
        
        Args:
            user_id: معرف المستخدم
            amount: المبلغ
            currency: نوع العملة
            description: وصف العملية
            
        Returns:
            bool: True إذا تمت الإضافة بنجاح
        """
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            # تحديث الرصيد اليومي
            success = self.db_manager.execute_query("""
                UPDATE treasury 
                SET daily_balance = daily_balance + ?, last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (amount, user_id, session_date, currency))
            
            if success:
                # تسجيل المعاملة
                self.log_transaction(user_id, 'add_daily', description or 'إضافة للرصيد اليومي', 
                                   amount, currency)
                
                self.logger.info(f"تم إضافة {amount} {currency} للمستخدم {user_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المبلغ للرصيد اليومي: {e}")
            return False
    
    def deduct_from_daily_balance(self, user_id: int, amount: float, 
                                currency: str = 'SYP', description: str = '') -> bool:
        """
        خصم مبلغ من الرصيد اليومي
        
        Args:
            user_id: معرف المستخدم
            amount: المبلغ
            currency: نوع العملة
            description: وصف العملية
            
        Returns:
            bool: True إذا تم الخصم بنجاح
        """
        try:
            # التحقق من توفر الرصيد
            current_balance = self.get_user_daily_balance(user_id, currency)
            
            if current_balance < amount:
                self.logger.warning(f"رصيد غير كافي للمستخدم {user_id}: {current_balance} < {amount}")
                return False
            
            session_date = date.today().strftime('%Y-%m-%d')
            
            # خصم المبلغ
            success = self.db_manager.execute_query("""
                UPDATE treasury 
                SET daily_balance = daily_balance - ?, last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (amount, user_id, session_date, currency))
            
            if success:
                # تسجيل المعاملة
                self.log_transaction(user_id, 'deduct_daily', description or 'خصم من الرصيد اليومي', 
                                   -amount, currency)
                
                self.logger.info(f"تم خصم {amount} {currency} من المستخدم {user_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"خطأ في خصم المبلغ من الرصيد اليومي: {e}")
            return False
    
    def transfer_to_main_treasury(self, user_id: int, amount: float, 
                                currency: str = 'SYP', receiver: str = '', 
                                notes: str = '') -> bool:
        """
        نقل مبلغ من الخزينة اليومية إلى الرئيسية
        
        Args:
            user_id: معرف المستخدم
            amount: المبلغ
            currency: نوع العملة
            receiver: المستلم
            notes: ملاحظات
            
        Returns:
            bool: True إذا تم النقل بنجاح
        """
        try:
            # التحقق من توفر الرصيد في الخزينة اليومية الإجمالية
            total_daily = self.get_total_daily_balance(currency)
            
            if total_daily < amount:
                self.logger.warning(f"رصيد يومي إجمالي غير كافي: {total_daily} < {amount}")
                return False
            
            session_date = date.today().strftime('%Y-%m-%d')
            
            # خصم من الأرصدة اليومية بالتناسب
            users_balances = self.get_all_users_daily_balances(currency)
            total_balance = sum(balance['daily_balance'] for balance in users_balances if balance['daily_balance'] > 0)
            
            if total_balance <= 0:
                return False
            
            # خصم من كل مستخدم بالتناسب
            for user_balance in users_balances:
                if user_balance['daily_balance'] > 0:
                    user_share = (user_balance['daily_balance'] / total_balance) * amount
                    
                    self.db_manager.execute_query("""
                        UPDATE treasury 
                        SET daily_balance = daily_balance - ?, last_updated = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
                    """, (user_share, user_balance['user_id'], session_date, currency))
            
            # إضافة للخزينة الرئيسية
            main_updated = self.db_manager.execute_query("""
                UPDATE treasury 
                SET main_balance = main_balance + ?, last_updated = CURRENT_TIMESTAMP
                WHERE user_id = ? AND session_date = ? AND currency = ?
                ORDER BY last_updated DESC LIMIT 1
            """, (amount, user_id, session_date, currency))
            
            if not main_updated:
                # إنشاء سجل جديد للخزينة الرئيسية
                self.db_manager.execute_query("""
                    INSERT INTO treasury (user_id, currency, daily_balance, main_balance, 
                                        session_date, is_session_active)
                    VALUES (?, ?, 0, ?, ?, 0)
                """, (user_id, currency, amount, session_date))
            
            # تسجيل عملية النقل
            transfer_desc = f"نقل إلى الخزينة الرئيسية - المستلم: {receiver}"
            if notes:
                transfer_desc += f" - ملاحظات: {notes}"
            
            self.log_transaction(user_id, 'transfer_to_main', transfer_desc, amount, currency)
            
            self.logger.info(f"تم نقل {amount} {currency} إلى الخزينة الرئيسية")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في نقل المبلغ للخزينة الرئيسية: {e}")
            return False
    
    def exchange_currency(self, user_id: int, from_currency: str, to_currency: str,
                         from_amount: float, exchange_rate: float, notes: str = '') -> bool:
        """
        صرف العملة
        
        Args:
            user_id: معرف المستخدم
            from_currency: العملة المصدر
            to_currency: العملة الهدف
            from_amount: المبلغ المصدر
            exchange_rate: سعر الصرف
            notes: ملاحظات
            
        Returns:
            bool: True إذا تم الصرف بنجاح
        """
        try:
            # حساب المبلغ المحول
            to_amount = from_amount / exchange_rate if to_currency == 'USD' else from_amount * exchange_rate
            
            # خصم من العملة المصدر
            if not self.deduct_from_daily_balance(user_id, from_amount, from_currency, 
                                                f"صرف عملة من {from_currency} إلى {to_currency}"):
                return False
            
            # إضافة للعملة الهدف
            if not self.add_to_daily_balance(user_id, to_amount, to_currency, 
                                           f"صرف عملة من {from_currency} إلى {to_currency}"):
                # إعادة المبلغ المخصوم في حالة الفشل
                self.add_to_daily_balance(user_id, from_amount, from_currency, "إعادة مبلغ صرف فاشل")
                return False
            
            # تسجيل عملية الصرف
            exchange_desc = f"صرف {from_amount} {from_currency} إلى {to_amount} {to_currency} بسعر {exchange_rate}"
            if notes:
                exchange_desc += f" - {notes}"
            
            self.log_transaction(user_id, 'currency_exchange', exchange_desc, 0, 'EXCHANGE')
            
            self.logger.info(f"تم صرف {from_amount} {from_currency} إلى {to_amount} {to_currency}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في صرف العملة: {e}")
            return False
    
    def get_user_daily_balance(self, user_id: int, currency: str = 'SYP') -> float:
        """الحصول على الرصيد اليومي للمستخدم"""
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            result = self.db_manager.fetch_one("""
                SELECT daily_balance FROM treasury 
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (user_id, session_date, currency))
            
            return result['daily_balance'] if result else 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الرصيد اليومي: {e}")
            return 0.0
    
    def get_total_daily_balance(self, currency: str = 'SYP') -> float:
        """الحصول على إجمالي الرصيد اليومي لجميع المستخدمين"""
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            # الحصول على أحدث رصيد لكل مستخدم
            users = self.db_manager.fetch_all("""
                SELECT DISTINCT user_id FROM treasury WHERE currency = ?
            """, (currency,))
            
            total = 0.0
            for user in users:
                user_balance = self.db_manager.fetch_one("""
                    SELECT daily_balance FROM treasury
                    WHERE user_id = ? AND currency = ?
                    ORDER BY session_date DESC, last_updated DESC
                    LIMIT 1
                """, (user['user_id'], currency))
                
                if user_balance and user_balance['daily_balance'] > 0:
                    total += user_balance['daily_balance']
            
            return total
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إجمالي الرصيد اليومي: {e}")
            return 0.0
    
    def get_main_balance(self, currency: str = 'SYP') -> float:
        """الحصول على رصيد الخزينة الرئيسية"""
        try:
            result = self.db_manager.fetch_one("""
                SELECT SUM(main_balance) as total FROM treasury 
                WHERE currency = ?
            """, (currency,))
            
            return result['total'] if result and result['total'] else 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على رصيد الخزينة الرئيسية: {e}")
            return 0.0
    
    def get_all_users_daily_balances(self, currency: str = 'SYP') -> List[Dict]:
        """الحصول على أرصدة جميع المستخدمين اليومية"""
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            return self.db_manager.fetch_all("""
                SELECT user_id, daily_balance FROM treasury 
                WHERE session_date = ? AND currency = ? AND is_session_active = 1
                ORDER BY daily_balance DESC
            """, (session_date, currency))
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أرصدة المستخدمين: {e}")
            return []
    
    def get_user_sales_total(self, user_id: int, date_filter: str = None, 
                           currency: str = 'SYP') -> float:
        """الحصول على إجمالي مبيعات المستخدم"""
        try:
            if not date_filter:
                date_filter = date.today().strftime('%Y-%m-%d')
            
            result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM transactions 
                WHERE user_id = ? AND currency = ? AND category = 'sales' 
                AND DATE(created_at) = ?
            """, (user_id, currency, date_filter))
            
            return result['total'] if result else 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إجمالي المبيعات: {e}")
            return 0.0
    
    def get_user_expenses_total(self, user_id: int, date_filter: str = None, 
                              currency: str = 'SYP') -> float:
        """الحصول على إجمالي مصروفات المستخدم"""
        try:
            if not date_filter:
                date_filter = date.today().strftime('%Y-%m-%d')
            
            result = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total FROM expenses 
                WHERE user_id = ? AND currency = ? 
                AND DATE(created_at) = ?
            """, (user_id, currency, date_filter))
            
            return result['total'] if result else 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إجمالي المصروفات: {e}")
            return 0.0
    
    def log_transaction(self, user_id: int, transaction_type: str, description: str,
                       amount: float, currency: str = 'SYP') -> bool:
        """تسجيل معاملة مالية"""
        try:
            return self.db_manager.execute_query("""
                INSERT INTO transactions (user_id, type, category, amount, currency, description)
                VALUES (?, ?, 'treasury', ?, ?, ?)
            """, (user_id, transaction_type, amount, currency, description))
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل المعاملة: {e}")
            return False
    
    def is_session_active(self, user_id: int, currency: str = 'SYP') -> bool:
        """التحقق من وجود جلسة نشطة"""
        try:
            session_date = date.today().strftime('%Y-%m-%d')
            
            result = self.db_manager.fetch_one("""
                SELECT id FROM treasury 
                WHERE user_id = ? AND session_date = ? AND currency = ? AND is_session_active = 1
            """, (user_id, session_date, currency))
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الجلسة النشطة: {e}")
            return False

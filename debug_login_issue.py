#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة كلمة المرور في تسجيل الدخول
"""

import sys
import os
import hashlib
sys.path.append('src')

def debug_login_process():
    """تشخيص مفصل لعملية تسجيل الدخول"""
    
    print("🔍 تشخيص مفصل لعملية تسجيل الدخول...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # بيانات تسجيل الدخول
        username = input("أدخل اسم المستخدم: ").strip()
        password = input("أدخل كلمة المرور: ").strip()
        
        print(f"\n📝 البيانات المدخلة:")
        print(f"  • اسم المستخدم: '{username}'")
        print(f"  • كلمة المرور: '{password}'")
        print(f"  • طول اسم المستخدم: {len(username)}")
        print(f"  • طول كلمة المرور: {len(password)}")
        
        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"  • كلمة المرور المشفرة: {hashed_password}")
        
        # البحث عن المستخدم بالاسم فقط
        print(f"\n🔍 البحث عن المستخدم '{username}'...")
        user_check = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
        
        if user_check:
            print("✅ المستخدم موجود!")
            print(f"  • ID: {user_check['id']}")
            print(f"  • اسم المستخدم: '{user_check['username']}'")
            print(f"  • الاسم الكامل: '{user_check['full_name']}'")
            print(f"  • الدور: '{user_check['role']}'")
            print(f"  • نشط: {user_check['is_active']}")
            print(f"  • كلمة المرور في قاعدة البيانات: {user_check['password_hash']}")
            
            # مقارنة كلمات المرور
            print(f"\n🔐 مقارنة كلمات المرور:")
            print(f"  • المدخلة (مشفرة): {hashed_password}")
            print(f"  • في قاعدة البيانات: {user_check['password_hash']}")
            print(f"  • متطابقة: {'✅ نعم' if hashed_password == user_check['password_hash'] else '❌ لا'}")
            
            # فحص الحالة النشطة
            print(f"\n📊 فحص الحالة:")
            print(f"  • المستخدم نشط: {'✅ نعم' if user_check['is_active'] == 1 else '❌ لا'}")
            
            # اختبار تسجيل الدخول الكامل
            print(f"\n🧪 اختبار تسجيل الدخول الكامل...")
            login_user = db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, hashed_password))
            
            if login_user:
                print("✅ تسجيل الدخول نجح!")
                return True
            else:
                print("❌ تسجيل الدخول فشل!")
                
                # تشخيص السبب
                if hashed_password != user_check['password_hash']:
                    print("  السبب: كلمة المرور خاطئة")
                elif user_check['is_active'] != 1:
                    print("  السبب: المستخدم غير نشط")
                else:
                    print("  السبب: غير معروف")
                
                return False
        else:
            print("❌ المستخدم غير موجود!")
            
            # عرض المستخدمين المتاحين
            print("\n👥 المستخدمين المتاحين:")
            all_users = db.fetch_all("SELECT username, full_name, is_active FROM users")
            for user in all_users:
                status = "نشط" if user['is_active'] == 1 else "غير نشط"
                print(f"  • {user['username']} ({user['full_name']}) - {status}")
            
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_users_with_common_passwords():
    """اختبار جميع المستخدمين بكلمات مرور شائعة"""
    
    print("\n🧪 اختبار جميع المستخدمين بكلمات مرور شائعة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # كلمات مرور شائعة للاختبار
        common_passwords = ['123', 'admin', 'password', '1234', '12345', 'test']
        
        # الحصول على جميع المستخدمين
        users = db.fetch_all("SELECT username, password_hash, is_active FROM users")
        
        print(f"👥 اختبار {len(users)} مستخدم مع {len(common_passwords)} كلمة مرور...")
        
        success_results = []
        
        for user in users:
            username = user['username']
            stored_hash = user['password_hash']
            is_active = user['is_active']
            
            print(f"\n🔍 اختبار المستخدم: {username}")
            print(f"  • كلمة المرور المحفوظة: {stored_hash}")
            print(f"  • نشط: {'نعم' if is_active == 1 else 'لا'}")
            
            found_password = False
            
            for password in common_passwords:
                test_hash = hashlib.sha256(password.encode()).hexdigest()
                
                if test_hash == stored_hash:
                    print(f"  ✅ كلمة المرور الصحيحة: '{password}'")
                    success_results.append((username, password))
                    found_password = True
                    break
            
            if not found_password:
                print(f"  ❌ لم يتم العثور على كلمة المرور من القائمة الشائعة")
        
        print(f"\n📊 النتائج النهائية:")
        print(f"  • عدد المستخدمين: {len(users)}")
        print(f"  • المستخدمين الذين تم العثور على كلمة مرورهم: {len(success_results)}")
        
        if success_results:
            print(f"\n✅ المستخدمين وكلمات مرورهم:")
            for username, password in success_results:
                print(f"  • {username} / {password}")
        
        return success_results
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كلمات المرور: {e}")
        return []

def reset_user_password():
    """إعادة تعيين كلمة مرور مستخدم"""
    
    print("\n🔧 إعادة تعيين كلمة مرور مستخدم...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # عرض المستخدمين المتاحين
        users = db.fetch_all("SELECT username, full_name FROM users")
        print("👥 المستخدمين المتاحين:")
        for i, user in enumerate(users, 1):
            print(f"  {i}. {user['username']} ({user['full_name']})")
        
        # اختيار المستخدم
        try:
            choice = int(input("\nاختر رقم المستخدم: ")) - 1
            if 0 <= choice < len(users):
                selected_user = users[choice]['username']
            else:
                print("❌ اختيار غير صحيح")
                return False
        except ValueError:
            print("❌ يرجى إدخال رقم صحيح")
            return False
        
        # كلمة المرور الجديدة
        new_password = input("أدخل كلمة المرور الجديدة: ").strip()
        
        if not new_password:
            print("❌ كلمة المرور لا يمكن أن تكون فارغة")
            return False
        
        # تشفير كلمة المرور الجديدة
        new_hash = hashlib.sha256(new_password.encode()).hexdigest()
        
        print(f"\n📝 تحديث كلمة المرور:")
        print(f"  • المستخدم: {selected_user}")
        print(f"  • كلمة المرور الجديدة: {new_password}")
        print(f"  • كلمة المرور المشفرة: {new_hash}")
        
        # تحديث كلمة المرور
        result = db.execute_query("""
            UPDATE users 
            SET password_hash = ? 
            WHERE username = ?
        """, (new_hash, selected_user))
        
        if result:
            print("✅ تم تحديث كلمة المرور بنجاح!")
            
            # اختبار تسجيل الدخول
            test_user = db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (selected_user, new_hash))
            
            if test_user:
                print("✅ اختبار تسجيل الدخول نجح!")
                return True
            else:
                print("❌ اختبار تسجيل الدخول فشل!")
                return False
        else:
            print("❌ فشل في تحديث كلمة المرور")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين كلمة المرور: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص وإصلاح مشكلة كلمة المرور")
    print("=" * 50)
    
    while True:
        print("\n📋 الخيارات المتاحة:")
        print("1. تشخيص مفصل لتسجيل الدخول")
        print("2. اختبار جميع المستخدمين بكلمات مرور شائعة")
        print("3. إعادة تعيين كلمة مرور مستخدم")
        print("4. خروج")
        
        choice = input("\nاختر رقم الخيار: ").strip()
        
        if choice == '1':
            debug_login_process()
        elif choice == '2':
            test_all_users_with_common_passwords()
        elif choice == '3':
            reset_user_password()
        elif choice == '4':
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخزينة اليومية الإجمالية (مجموع جميع المستخدمين)
"""

import sys
import os
sys.path.append('src')

def test_total_daily_balance():
    """اختبار الخزينة اليومية الإجمالية"""
    
    print("🧪 اختبار الخزينة اليومية الإجمالية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        print("✅ تم إنشاء مدير الخزينة الموحد")
        
        # فحص الأرصدة الفردية لكل مستخدم
        print("\n💰 الأرصدة الفردية لكل مستخدم:")
        users = db.fetch_all("""
            SELECT DISTINCT user_id FROM unified_treasury 
            WHERE currency_type = 'SYP' AND daily_balance > 0
        """)
        
        total_manual = 0
        for user in users:
            user_balance = treasury_manager.get_daily_balance(user['user_id'], 'SYP')
            total_manual += user_balance
            print(f"  • المستخدم {user['user_id']}: {user_balance:,} ل.س")
        
        print(f"  • المجموع اليدوي: {total_manual:,} ل.س")
        
        # اختبار الدالة الجديدة
        print("\n🔧 اختبار الدالة الجديدة:")
        total_daily = treasury_manager.get_total_daily_balance('SYP')
        print(f"  • get_total_daily_balance: {total_daily:,} ل.س")
        
        # مقارنة النتائج
        if abs(total_daily - total_manual) < 1:
            print("✅ الحساب صحيح - الدالة تُرجع نفس المجموع اليدوي")
            return True, total_daily
        else:
            print(f"❌ خطأ في الحساب: دالة={total_daily:,} يدوي={total_manual:,}")
            return False, total_daily
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخزينة اليومية الإجمالية: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_interfaces_with_total_daily():
    """اختبار الواجهات مع الخزينة اليومية الإجمالية"""
    
    print("\n🧪 اختبار الواجهات مع الخزينة اليومية الإجمالية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.currency_exchange_window import CurrencyExchangeWindow
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الخزينة اليومية الإجمالية
        total_daily = treasury_manager.get_total_daily_balance('SYP')
        print(f"💰 الخزينة اليومية الإجمالية: {total_daily:,} ل.س")
        
        # اختبار واجهة شراء الدولار
        print("\n🖥️ اختبار واجهة شراء الدولار:")
        currency_window = CurrencyExchangeWindow(db, treasury_manager, current_user)
        displayed_syp = currency_window.syp_balance_label.text()
        print(f"  • ما تعرضه الواجهة: {displayed_syp}")
        
        if total_daily > 0 and str(int(total_daily)) in displayed_syp.replace(",", ""):
            print("  ✅ واجهة شراء الدولار تعرض الخزينة اليومية الإجمالية")
            currency_success = True
        else:
            print(f"  ❌ واجهة شراء الدولار لا تعرض الرصيد الصحيح")
            print(f"     متوقع: {total_daily:,} ل.س، معروض: {displayed_syp}")
            currency_success = False
        
        # اختبار واجهة نقل الخزينة
        print("\n🖥️ اختبار واجهة نقل الخزينة:")
        transfer_window = EnhancedTreasuryTransferWindow(db, current_user)
        displayed_daily = transfer_window.daily_syp_label.text()
        print(f"  • ما تعرضه الواجهة: {displayed_daily}")
        
        if total_daily > 0 and str(int(total_daily)) in displayed_daily.replace(",", ""):
            print("  ✅ واجهة نقل الخزينة تعرض الخزينة اليومية الإجمالية")
            transfer_success = True
        else:
            print(f"  ❌ واجهة نقل الخزينة لا تعرض الرصيد الصحيح")
            print(f"     متوقع: {total_daily:,} ل.س، معروض: {displayed_daily}")
            transfer_success = False
        
        return currency_success and transfer_success, total_daily
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهات: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def show_detailed_breakdown():
    """عرض تفصيلي للخزينة"""
    
    print("\n📊 التفصيل الكامل للخزينة:")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # عرض جميع السجلات
        records = db.fetch_all("""
            SELECT user_id, session_date, daily_balance, main_balance, is_session_active
            FROM unified_treasury 
            WHERE currency_type = 'SYP' AND daily_balance > 0
            ORDER BY user_id, session_date DESC
        """)
        
        print(f"📋 جميع السجلات ({len(records)} سجل):")
        user_totals = {}
        
        for record in records:
            user_id = record['user_id']
            status = "نشط" if record['is_session_active'] else "مغلق"
            
            if user_id not in user_totals:
                user_totals[user_id] = {'daily': 0, 'main': 0, 'sessions': 0}
            
            user_totals[user_id]['daily'] = max(user_totals[user_id]['daily'], record['daily_balance'])
            user_totals[user_id]['main'] = max(user_totals[user_id]['main'], record['main_balance'])
            user_totals[user_id]['sessions'] += 1
            
            print(f"  • مستخدم {user_id} - {record['session_date']}: يومي {record['daily_balance']:,} ل.س - رئيسي {record['main_balance']:,} ل.س - {status}")
        
        print(f"\n📊 ملخص لكل مستخدم:")
        total_daily_all = 0
        total_main_all = 0
        
        for user_id, totals in user_totals.items():
            total_daily_all += totals['daily']
            total_main_all += totals['main']
            print(f"  • المستخدم {user_id}: أعلى يومي {totals['daily']:,} ل.س - أعلى رئيسي {totals['main']:,} ل.س ({totals['sessions']} جلسة)")
        
        print(f"\n💰 الإجماليات:")
        print(f"  • إجمالي الخزينة اليومية: {total_daily_all:,} ل.س")
        print(f"  • إجمالي الخزينة الرئيسية: {total_main_all:,} ل.س")
        
        # مقارنة مع الدوال
        calculated_daily = treasury_manager.get_total_daily_balance('SYP')
        calculated_main = treasury_manager.get_main_balance('SYP')
        
        print(f"\n🔧 مقارنة مع الدوال:")
        print(f"  • get_total_daily_balance: {calculated_daily:,} ل.س")
        print(f"  • get_main_balance: {calculated_main:,} ل.س")
        
        return total_daily_all, total_main_all
        
    except Exception as e:
        print(f"❌ خطأ في عرض التفصيل: {e}")
        return 0, 0

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار الخزينة اليومية الإجمالية (مجموع جميع المستخدمين)")
    print("=" * 80)
    
    # 1. عرض التفصيل الكامل
    manual_daily, manual_main = show_detailed_breakdown()
    
    # 2. اختبار الدالة الجديدة
    balance_test, calculated_daily = test_total_daily_balance()
    
    # 3. اختبار الواجهات
    if calculated_daily > 0:
        interface_test, interface_daily = test_interfaces_with_total_daily()
    else:
        interface_test = False
        interface_daily = 0
    
    print("\n" + "=" * 80)
    print("📊 ملخص النتائج:")
    print(f"  • الحساب اليدوي: {manual_daily:,} ل.س")
    print(f"  • دالة get_total_daily_balance: {calculated_daily:,} ل.س")
    print(f"  • ما تعرضه الواجهات: {interface_daily:,} ل.س")
    
    print(f"\n🧪 نتائج الاختبارات:")
    print(f"  • اختبار الدالة: {'✅ نجح' if balance_test else '❌ فشل'}")
    print(f"  • اختبار الواجهات: {'✅ نجح' if interface_test else '❌ فشل'}")
    
    if all([balance_test, interface_test]) and calculated_daily > 0:
        print("\n🎉 تم إصلاح مفهوم الخزينة اليومية بنجاح!")
        
        print("\n📋 النتائج:")
        print("  ✅ الخزينة اليومية تجمع من جميع المستخدمين")
        print("  ✅ واجهة شراء الدولار تعرض الإجمالي الصحيح")
        print("  ✅ واجهة نقل الخزينة تعرض الإجمالي الصحيح")
        print(f"  ✅ إجمالي الخزينة اليومية: {calculated_daily:,} ل.س")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام: python system_launcher.py")
        print("  2. سجل دخول: admin / admin123")
        print("  3. اضغط على 'شراء دولار' - ستجد إجمالي الخزينة اليومية")
        print("  4. اضغط على 'نقل الخزينة' - ستجد إجمالي الخزينة اليومية")
        
    else:
        print("\n❌ لا تزال هناك مشاكل:")
        
        if not balance_test:
            print("  • دالة get_total_daily_balance لا تعمل بشكل صحيح")
        if not interface_test:
            print("  • الواجهات لا تعرض الإجمالي الصحيح")
        if calculated_daily == 0:
            print("  • لا توجد أرصدة في الخزينة اليومية")

if __name__ == "__main__":
    main()

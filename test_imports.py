#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستيرادات
Test Imports
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
src_path = project_root / "src"

sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

def test_reports_import():
    """اختبار استيراد واجهة التقارير"""
    try:
        print("🧪 اختبار استيراد واجهة التقارير...")
        from ui.reports_window import ReportsWindow
        print("✅ تم استيراد ReportsWindow بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد ReportsWindow: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_suppliers_import():
    """اختبار استيراد واجهة الموردين"""
    try:
        print("🧪 اختبار استيراد واجهة الموردين...")
        from ui.suppliers_management_window import SuppliersManagementWindow, SupplierDialog
        print("✅ تم استيراد SuppliersManagementWindow و SupplierDialog بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد واجهة الموردين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_users_import():
    """اختبار استيراد واجهة المستخدمين"""
    try:
        print("🧪 اختبار استيراد واجهة المستخدمين...")
        from ui.users_management_window import UsersManagementWindow, UserDialog
        print("✅ تم استيراد UsersManagementWindow و UserDialog بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد واجهة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_arabic_support():
    """اختبار دعم اللغة العربية"""
    try:
        print("🧪 اختبار دعم اللغة العربية...")
        from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
        
        # اختبار تنسيق العملة
        test_amount = 150000
        formatted = format_currency(test_amount)
        print(f"✅ تنسيق العملة: {test_amount} -> {formatted}")
        return True
    except Exception as e:
        print(f"❌ خطأ في دعم اللغة العربية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("🧪 اختبار قاعدة البيانات...")
        from database.database_manager import DatabaseManager
        
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        
        # اختبار الاتصال
        if db_manager.connect():
            print("✅ تم الاتصال بقاعدة البيانات")
            
            # اختبار جلب المستخدمين
            users = db_manager.fetch_all("SELECT COUNT(*) as count FROM users")
            print(f"✅ عدد المستخدمين: {users[0]['count']}")
            
            # اختبار جلب الموردين
            suppliers = db_manager.fetch_all("SELECT COUNT(*) as count FROM suppliers")
            print(f"✅ عدد الموردين: {suppliers[0]['count']}")
            
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار شامل للمكونات")
    print("=" * 50)
    
    tests = [
        ("دعم اللغة العربية", test_arabic_support),
        ("قاعدة البيانات", test_database),
        ("واجهة التقارير", test_reports_import),
        ("واجهة الموردين", test_suppliers_import),
        ("واجهة المستخدمين", test_users_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار {test_name}:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المتكامل للخزينة والمخزون
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager
from utils.inventory_manager import InventoryManager

def test_integrated_system():
    """اختبار النظام المتكامل"""
    
    print("🧪 اختبار النظام المتكامل للخزينة والمخزون...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = TreasuryManager(db)
    inventory_manager = InventoryManager(db)
    
    try:
        print("\n=== 1. اختبار نظام الخزينة ===")
        
        # إعادة تعيين الخزينة اليومية
        treasury_manager.reset_daily_treasury()
        print("✅ تم إعادة تعيين الخزينة اليومية")
        
        # محاكاة مبيعات
        sales_amount = 500000
        treasury_manager.update_daily_treasury(
            currency_type='SYP',
            sales_amount=sales_amount,
            user_id=1
        )
        print(f"✅ تم تسجيل مبيعات: {sales_amount:,} ل.س")
        
        # محاكاة مصاريف
        expenses_amount = 50000
        treasury_manager.update_daily_treasury(
            currency_type='SYP',
            expenses_amount=expenses_amount,
            user_id=1
        )
        print(f"✅ تم تسجيل مصاريف: {expenses_amount:,} ل.س")
        
        # عرض الرصيد اليومي
        daily_balance = treasury_manager.get_daily_treasury_balance('SYP')
        print(f"📊 الرصيد اليومي: {daily_balance:,} ل.س")
        
        # محاكاة صرف عملة
        exchange_amount = 150000
        treasury_manager.process_currency_exchange(
            from_currency='SYP',
            to_currency='USD',
            amount_from=exchange_amount,
            exchange_rate=15000,
            user_id=1
        )
        print(f"✅ تم صرف عملة: {exchange_amount:,} ل.س → ${exchange_amount/15000:.2f}")
        
        # عرض الأرصدة بعد الصرف
        syp_balance = treasury_manager.get_daily_treasury_balance('SYP')
        usd_balance = treasury_manager.get_daily_treasury_balance('USD')
        print(f"📊 الرصيد بعد الصرف:")
        print(f"  • الليرة السورية: {syp_balance:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_balance:.2f}")
        
        # محاكاة إغلاق الصندوق
        print(f"\n🔒 محاكاة إغلاق الصندوق...")
        treasury_manager.close_daily_treasury(user_id=1)
        
        # عرض الأرصدة بعد الإغلاق
        syp_daily_after = treasury_manager.get_daily_treasury_balance('SYP')
        usd_daily_after = treasury_manager.get_daily_treasury_balance('USD')
        syp_main = treasury_manager.get_main_treasury_balance('SYP')
        usd_main = treasury_manager.get_main_treasury_balance('USD')
        
        print(f"📊 الأرصدة بعد إغلاق الصندوق:")
        print(f"  الخزينة اليومية:")
        print(f"    • الليرة السورية: {syp_daily_after:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_daily_after:.2f}")
        print(f"  الخزينة الرئيسية:")
        print(f"    • الليرة السورية: {syp_main:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_main:.2f}")
        
        print("\n=== 2. اختبار نظام المخزون ===")
        
        # مزامنة مع جدول المنتجات
        inventory_manager.sync_with_products_table()
        print("✅ تم مزامنة المخزون مع جدول المنتجات")
        
        # عرض المنتجات منخفضة المخزون
        low_stock = inventory_manager.get_low_stock_products()
        print(f"📦 المنتجات منخفضة المخزون ({len(low_stock)}):")
        for product in low_stock[:3]:  # أول 3 منتجات
            print(f"  • {product[1]}: {product[3]} {product[4]} (الحد الأدنى: {product[2]})")
        
        # محاكاة تسليم راوتر
        router_products = db.fetch_all("""
            SELECT id, name, stock_quantity FROM products 
            WHERE category = 'router' AND stock_quantity > 0 
            LIMIT 1
        """)
        
        if router_products:
            router = router_products[0]
            router_id = router[0]
            router_name = router[1]
            current_stock = router[2]
            
            print(f"\n📡 محاكاة تسليم راوتر:")
            print(f"  • الراوتر: {router_name}")
            print(f"  • المخزون الحالي: {current_stock}")
            
            # خصم راوتر
            success = inventory_manager.remove_stock(
                product_id=router_id,
                quantity=1,
                unit_type='قطعة',
                reference_type='router_delivery',
                notes='تسليم راوتر للمشترك - اختبار',
                user_id=1
            )
            
            if success:
                new_stock = inventory_manager.get_stock(router_id)
                print(f"  ✅ تم خصم الراوتر. المخزون الجديد: {new_stock}")
            else:
                print(f"  ❌ فشل في خصم الراوتر")
        
        # محاكاة نقل كبل للعامل
        cable_products = db.fetch_all("""
            SELECT id, name, stock_quantity FROM products 
            WHERE category = 'cable' AND stock_quantity > 0 
            LIMIT 1
        """)
        
        if cable_products:
            cable = cable_products[0]
            cable_id = cable[0]
            cable_name = cable[1]
            current_stock = cable[2]
            
            print(f"\n🔌 محاكاة نقل كبل للعامل:")
            print(f"  • الكبل: {cable_name}")
            print(f"  • المخزون الحالي: {current_stock}")
            
            # نقل كبل للعامل
            transfer_amount = min(100, current_stock)  # نقل 100 متر أو المتاح
            success = inventory_manager.transfer_stock(
                product_id=cable_id,
                quantity=transfer_amount,
                unit_type='متر',
                from_location_type='main',
                from_location_id='main',
                to_location_type='worker',
                to_location_id='1',  # عامل رقم 1
                reference_type='worker_transfer',
                notes='نقل كبل للعامل - اختبار',
                user_id=1
            )
            
            if success:
                main_stock = inventory_manager.get_stock(cable_id, 'main', 'main')
                worker_stock = inventory_manager.get_stock(cable_id, 'worker', '1')
                print(f"  ✅ تم نقل {transfer_amount} متر")
                print(f"    - المخزون الرئيسي: {main_stock} متر")
                print(f"    - مخزون العامل: {worker_stock} متر")
            else:
                print(f"  ❌ فشل في نقل الكبل")
        
        print("\n=== 3. عرض حركات المخزون الأخيرة ===")
        movements = inventory_manager.get_inventory_movements(limit=5)
        print(f"📋 آخر {len(movements)} حركات:")
        for movement in movements:
            movement_type = movement[2]
            quantity = movement[3]
            unit_type = movement[4]
            product_name = movement[10] if len(movement) > 10 else 'غير معروف'
            created_at = movement[9]
            
            if movement_type == 'in':
                print(f"  ⬆️ إضافة {quantity} {unit_type} من {product_name} - {created_at}")
            elif movement_type == 'out':
                print(f"  ⬇️ خصم {quantity} {unit_type} من {product_name} - {created_at}")
            elif movement_type == 'transfer':
                print(f"  ↔️ نقل {quantity} {unit_type} من {product_name} - {created_at}")
        
        print(f"\n🎉 انتهى اختبار النظام المتكامل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_integrated_system()

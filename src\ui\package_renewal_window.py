# -*- coding: utf-8 -*-
"""
نافذة تجديد الباقة
Package Renewal Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QCompleter)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime, QTime, QStringListModel
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import (reshape_arabic_text, create_arabic_font,
                                       format_currency, apply_arabic_style)
    from ..utils.shift_manager import get_current_shift
except ImportError:
    from utils.arabic_support import (reshape_arabic_text, create_arabic_font,
                                     format_currency, apply_arabic_style)
    from utils.shift_manager import get_current_shift

class PackageRenewalWindow(QDialog):
    """نافذة تجديد الباقة"""
    
    renewal_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(reshape_arabic_text("تجديد باقة"))
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # تطبيق الخط العربي على النافذة
        apply_arabic_style(self, 10)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel(reshape_arabic_text("تجديد باقة مشترك"))
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        
        # معلومات المشترك
        subscriber_group = self.create_subscriber_group()
        
        # معلومات الباقة الجديدة
        package_group = self.create_package_group()
        
        # معلومات الدفع
        payment_group = self.create_payment_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addWidget(subscriber_group)
        main_layout.addWidget(package_group)
        main_layout.addWidget(payment_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_subscriber_group(self):
        """إنشاء مجموعة معلومات المشترك"""
        group = QGroupBox(reshape_arabic_text("معلومات المشترك"))
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # اسم المشترك مع البحث التلقائي
        name_label = QLabel(reshape_arabic_text("اسم المشترك:"))
        apply_arabic_style(name_label, 10)
        
        self.subscriber_combo = QComboBox()
        apply_arabic_style(self.subscriber_combo, 11)
        self.subscriber_combo.setEditable(True)
        self.subscriber_combo.setMinimumHeight(35)
        self.subscriber_combo.setPlaceholderText(reshape_arabic_text("ابحث عن المشترك"))
        self.subscriber_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
            }
        """)
        
        # الباقة الحالية
        current_package_label = QLabel(reshape_arabic_text("الباقة الحالية:"))
        apply_arabic_style(current_package_label, 10)
        
        self.current_package_label = QLabel(reshape_arabic_text("غير محدد"))
        apply_arabic_style(self.current_package_label, 10, bold=True)
        self.current_package_label.setStyleSheet("color: #7f8c8d;")
        
        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.subscriber_combo, 0, 1)
        layout.addWidget(current_package_label, 1, 0)
        layout.addWidget(self.current_package_label, 1, 1)
        
        return group
        
    def create_package_group(self):
        """إنشاء مجموعة معلومات الباقة الجديدة"""
        group = QGroupBox(reshape_arabic_text("الباقة الجديدة"))
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # نوع الباقة الجديدة
        package_label = QLabel(reshape_arabic_text("نوع الباقة:"))
        apply_arabic_style(package_label, 10)
        
        self.package_combo = QComboBox()
        apply_arabic_style(self.package_combo, 11)
        self.package_combo.setMinimumHeight(35)
        self.package_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
            QComboBox:focus {
                border-color: #27ae60;
            }
        """)
        
        # سعر الباقة
        price_label = QLabel(reshape_arabic_text("سعر الباقة:"))
        apply_arabic_style(price_label, 10)
        
        self.package_price_label = QLabel("0 ل.س")
        apply_arabic_style(self.package_price_label, 12, bold=True)
        self.package_price_label.setMinimumHeight(35)
        self.package_price_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: #f8f9fa;
                padding: 8px;
                border: 2px solid #27ae60;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        
        # وصف الباقة
        description_label = QLabel(reshape_arabic_text("وصف الباقة:"))
        apply_arabic_style(description_label, 10)
        
        self.package_description_label = QLabel(reshape_arabic_text("اختر باقة لعرض الوصف"))
        apply_arabic_style(self.package_description_label, 9)
        self.package_description_label.setStyleSheet("color: #7f8c8d;")
        self.package_description_label.setWordWrap(True)
        
        layout.addWidget(package_label, 0, 0)
        layout.addWidget(self.package_combo, 0, 1)
        layout.addWidget(price_label, 1, 0)
        layout.addWidget(self.package_price_label, 1, 1)
        layout.addWidget(description_label, 2, 0)
        layout.addWidget(self.package_description_label, 2, 1)
        
        return group
        
    def create_payment_group(self):
        """إنشاء مجموعة معلومات الدفع"""
        group = QGroupBox(reshape_arabic_text("معلومات الدفع"))
        apply_arabic_style(group, 10, bold=True)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # حالة الدفع
        self.payment_check = QCheckBox(reshape_arabic_text("تم دفع رسوم التجديد"))
        apply_arabic_style(self.payment_check, 10, bold=True)
        self.payment_check.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #27ae60;
                border-color: #27ae60;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #229954;
            }
        """)
        
        # ملاحظات
        notes_label = QLabel(reshape_arabic_text("ملاحظات (اختياري):"))
        apply_arabic_style(notes_label, 10)
        
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText(reshape_arabic_text("أدخل أي ملاحظات إضافية"))
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        
        layout.addWidget(self.payment_check)
        layout.addWidget(notes_label)
        layout.addWidget(self.notes_edit)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الإلغاء
        self.cancel_button = QPushButton(reshape_arabic_text("إلغاء"))
        apply_arabic_style(self.cancel_button, 10)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        
        # زر الطباعة
        self.print_button = QPushButton(reshape_arabic_text("طباعة"))
        apply_arabic_style(self.print_button, 10)
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        
        # زر التجديد
        self.renew_button = QPushButton(reshape_arabic_text("تجديد الباقة"))
        apply_arabic_style(self.renew_button, 10, bold=True)
        self.renew_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        layout.addWidget(self.cancel_button)
        layout.addStretch()
        layout.addWidget(self.print_button)
        layout.addWidget(self.renew_button)
        
        return layout

    def load_data(self):
        """تحميل البيانات"""
        self.load_subscribers()
        self.load_packages()

    def load_subscribers(self):
        """تحميل المشتركين"""
        try:
            subscribers = self.db_manager.fetch_all(
                "SELECT * FROM subscribers ORDER BY name"
            )

            self.subscriber_combo.clear()
            subscriber_names = []

            for subscriber in subscribers:
                display_name = subscriber['name']
                self.subscriber_combo.addItem(display_name, subscriber)
                subscriber_names.append(display_name)

            # إعداد البحث التلقائي
            completer = QCompleter(subscriber_names)
            completer.setCaseSensitivity(Qt.CaseInsensitive)
            completer.setFilterMode(Qt.MatchContains)
            self.subscriber_combo.setCompleter(completer)

        except Exception as e:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text(f"خطأ في تحميل المشتركين: {e}"))

    def load_packages(self):
        """تحميل الباقات"""
        try:
            packages = self.db_manager.fetch_all(
                "SELECT * FROM packages WHERE is_active = 1 ORDER BY price"
            )

            self.package_combo.clear()
            self.package_combo.addItem(reshape_arabic_text("اختر الباقة الجديدة"), None)

            for package in packages:
                display_text = f"{package['name']} - {format_currency(package['price'])}"
                self.package_combo.addItem(reshape_arabic_text(display_text), package)

        except Exception as e:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text(f"خطأ في تحميل الباقات: {e}"))

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.subscriber_combo.currentIndexChanged.connect(self.on_subscriber_changed)
        self.package_combo.currentIndexChanged.connect(self.on_package_changed)

        self.renew_button.clicked.connect(self.renew_package)
        self.print_button.clicked.connect(self.print_invoice)
        self.cancel_button.clicked.connect(self.reject)

    def on_subscriber_changed(self):
        """معالجة تغيير المشترك"""
        subscriber_data = self.subscriber_combo.currentData()
        if subscriber_data:
            # عرض الباقة الحالية
            current_package = subscriber_data['package_type'] if subscriber_data['package_type'] else 'غير محدد'
            self.current_package_label.setText(reshape_arabic_text(current_package))
            self.current_package_label.setStyleSheet("color: #3498db; font-weight: bold;")
        else:
            self.current_package_label.setText(reshape_arabic_text("غير محدد"))
            self.current_package_label.setStyleSheet("color: #7f8c8d;")

    def on_package_changed(self):
        """معالجة تغيير نوع الباقة"""
        package_data = self.package_combo.currentData()
        if package_data:
            # عرض سعر الباقة
            price_text = format_currency(package_data['price'])
            self.package_price_label.setText(price_text)

            # عرض وصف الباقة
            description = package_data['description'] if package_data['description'] else 'لا يوجد وصف'
            speed = package_data['speed'] if package_data['speed'] else ''
            if speed:
                description = f"{description} - السرعة: {speed}"
            self.package_description_label.setText(reshape_arabic_text(description))
            self.package_description_label.setStyleSheet("color: #2c3e50;")
        else:
            self.package_price_label.setText("0 ل.س")
            self.package_description_label.setText(reshape_arabic_text("اختر باقة لعرض الوصف"))
            self.package_description_label.setStyleSheet("color: #7f8c8d;")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.subscriber_combo.currentText().strip():
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text("يرجى اختيار المشترك"))
            self.subscriber_combo.setFocus()
            return False

        if self.package_combo.currentData() is None:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text("يرجى اختيار الباقة الجديدة"))
            self.package_combo.setFocus()
            return False

        return True

    def renew_package(self):
        """تجديد الباقة"""
        if not self.validate_data():
            return

        try:
            # الحصول على الشيفت الحالي
            try:
                current_shift_id = get_current_shift(self.db_manager, self.current_user['id'], self.current_user['username'])
                if not current_shift_id:
                    raise ValueError("فشل في الحصول على الشيفت الحالي")
                print(f"📂 الشيفت الحالي: {current_shift_id}")
            except Exception as shift_error:
                print(f"❌ خطأ في الحصول على الشيفت: {shift_error}")
                current_shift_id = 1

            subscriber_data = self.subscriber_combo.currentData()
            package_data = self.package_combo.currentData()

            # بيانات التجديد
            renewal_data = {
                'subscriber_id': subscriber_data['id'],
                'subscriber_name': subscriber_data['name'],
                'old_package': subscriber_data['package_type'] if subscriber_data['package_type'] else 'غير محدد',
                'new_package': package_data['name'],
                'package_price': package_data['price'],
                'payment_status': self.payment_check.isChecked(),
                'notes': self.notes_edit.toPlainText().strip(),
                'renewed_by': self.current_user['username']
            }

            # تحديث باقة المشترك
            self.db_manager.execute_query("""
                UPDATE subscribers SET package_type = ? WHERE id = ?
            """, (renewal_data['new_package'], renewal_data['subscriber_id']))

            # تسجيل العملية المالية
            self.db_manager.execute_query("""
                INSERT INTO transactions (type, description, amount, currency, payment_method, reference_id, user_name, shift_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "تجديد باقة",
                f"تجديد باقة للمشترك: {renewal_data['subscriber_name']} من {renewal_data['old_package']} إلى {renewal_data['new_package']}",
                float(renewal_data['package_price']),
                'SYP',
                'نقداً',
                renewal_data['subscriber_id'],
                renewal_data['renewed_by'],
                current_shift_id
            ))

            print(f"تم تسجيل تجديد باقة بمبلغ: {renewal_data['package_price']} ل.س")  # للتشخيص

            # إرسال إشارة نجح التجديد
            self.renewal_completed.emit(renewal_data)

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text(f"تم تجديد باقة المشترك {renewal_data['subscriber_name']} بنجاح"))

            # سؤال المستخدم عن الطباعة
            reply = QMessageBox.question(
                self,
                reshape_arabic_text("طباعة"),
                reshape_arabic_text("هل تريد طباعة فاتورة التجديد؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.print_invoice()

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تجديد الباقة: {e}"))

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # جمع بيانات الفاتورة
            subscriber_name = self.subscriber_combo.currentText()
            package_name = self.package_combo.currentText()
            amount = self.amount_spin.value()

            invoice_data = {
                'customer_name': subscriber_name,
                'package_name': package_name,
                'amount': amount,
                'invoice_number': f"REN-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}",
                'description': f"تجديد باقة - {package_name}",
                'date': QDate.currentDate().toString("yyyy-MM-dd"),
                'time': QTime.currentTime().toString("hh:mm:ss")
            }

            # طباعة الفاتورة
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_invoice(invoice_data)

            if success:
                QMessageBox.information(self, reshape_arabic_text("تم"),
                                      reshape_arabic_text("تم إرسال الفاتورة للطباعة بنجاح"))
            else:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("تم إلغاء عملية الطباعة"))

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في طباعة الفاتورة: {e}"))

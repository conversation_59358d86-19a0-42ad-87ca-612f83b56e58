#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة منتجات كبل لقاعدة البيانات
"""

import sqlite3

def add_cable_products():
    """إضافة منتجات كبل"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        # منتجات الكبل
        cables = [
            ('كبل شبكة Cat5e', 'cable', 500, 1000, 'متر'),
            ('كبل شبكة Cat6', 'cable', 750, 800, 'متر'),
            ('كبل فايبر أوبتك', 'cable', 1200, 500, 'متر'),
            ('كبل كوكسيال RG6', 'cable', 300, 1200, 'متر'),
            ('كبل كهرباء 2.5mm', 'cable', 400, 600, 'متر'),
        ]
        
        print('🔌 إضافة منتجات الكبل...')
        
        for cable in cables:
            name, category, unit_price, quantity, unit = cable
            
            # التحقق من وجود المنتج
            existing = cursor.execute('SELECT id FROM products WHERE name = ?', (name,)).fetchone()
            
            if existing:
                print(f'⚠️ المنتج موجود بالفعل: {name}')
                continue
            
            # إضافة المنتج
            cursor.execute('''
                INSERT INTO products (name, category, unit_price, stock_quantity, unit_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, category, unit_price, quantity, unit))
            
            print(f'✅ تم إضافة: {name} - {unit_price} ل.س/{unit} (متوفر: {quantity}{unit})')
        
        conn.commit()
        print('🎉 تم إضافة جميع منتجات الكبل بنجاح!')
        
        # عرض المنتجات المضافة
        print('\n📦 منتجات الكبل المتوفرة:')
        cables_in_db = cursor.execute('''
            SELECT name, unit_price, stock_quantity, unit_type
            FROM products
            WHERE category = "cable"
            ORDER BY name
        ''').fetchall()
        
        for cable in cables_in_db:
            name, price, qty, unit = cable
            print(f'  • {name}: {price} ل.س/{unit} (متوفر: {qty}{unit})')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    add_cable_products()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قالب إنشاء واجهة جديدة

هذا القالب يوضح كيفية إنشاء واجهة جديدة بسهولة
بدون الحاجة لفهم عميق في البرمجة

خطوات إنشاء واجهة جديدة:
1. انسخ هذا الملف إلى مجلد ui/[category]/
2. غير اسم الفئة من TemplateWindow إلى اسم واجهتك
3. عدل المحتوى حسب احتياجاتك
4. أضف الواجهة في ملف app_config.py
5. أعد تشغيل التطبيق
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QGridLayout, QPush<PERSON>utton, Q<PERSON><PERSON><PERSON>, Q<PERSON>ineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QRadioButton, QGroupBox, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFileDialog, QProgressBar,
                             QDateEdit, QTimeEdit, QFrame, QSplitter)
from PyQt5.QtCore import Qt, QDate, QTime, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from database.database_manager import DatabaseManager
from utils.window_utils import center_window, apply_modern_style
from config.app_config import AppConfig

class TemplateWindow(QDialog):
    """
    قالب الواجهة الجديدة
    
    غير اسم هذه الفئة إلى اسم واجهتك الجديدة
    مثال: NewSubscriptionWindow, InventoryWindow, إلخ
    """
    
    # إشارات مخصصة (اختيارية)
    data_saved = pyqtSignal(dict)  # إشارة عند حفظ البيانات
    data_updated = pyqtSignal(dict)  # إشارة عند تحديث البيانات
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """
        تهيئة الواجهة
        
        Args:
            db_manager: مدير قاعدة البيانات
            current_user: المستخدم الحالي
            parent: النافذة الأب (اختياري)
        """
        super().__init__(parent)
        
        # حفظ المراجع
        self.db_manager = db_manager
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        
        # متغيرات البيانات
        self.current_data = {}
        self.is_editing = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        self.logger.info("تم فتح واجهة القالب")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إعداد النافذة
        self.setWindowTitle("عنوان الواجهة الجديدة")  # غير هذا
        self.setModal(True)
        self.resize(800, 600)  # غير الحجم حسب الحاجة
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الأقسام
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        content_section = self.create_content_section()
        main_layout.addWidget(content_section, 1)  # يأخذ المساحة المتبقية
        
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        self.setLayout(main_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # أيقونة الواجهة (اختيارية)
        icon_label = QLabel("📋")  # غير هذا إلى أيقونة مناسبة
        icon_label.setStyleSheet("font-size: 24px;")
        layout.addWidget(icon_label)
        
        # عنوان الواجهة
        title_label = QLabel("عنوان الواجهة الجديدة")  # غير هذا
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
            }
        """)
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # معلومات إضافية (اختيارية)
        info_label = QLabel(f"المستخدم: {self.current_user['full_name']}")
        info_label.setStyleSheet("font-size: 12px; color: #ecf0f1;")
        layout.addWidget(info_label)
        
        return frame
    
    def create_content_section(self) -> QWidget:
        """إنشاء قسم المحتوى الرئيسي"""
        
        # يمكنك اختيار نوع التخطيط المناسب:
        
        # 1. تبويبات (للواجهات المعقدة)
        return self.create_tabbed_content()
        
        # 2. نموذج بسيط (للواجهات البسيطة)
        # return self.create_simple_form()
        
        # 3. جدول مع نموذج (لإدارة البيانات)
        # return self.create_table_with_form()
    
    def create_tabbed_content(self) -> QTabWidget:
        """إنشاء محتوى بتبويبات"""
        
        tabs = QTabWidget()
        
        # التبويب الأول: البيانات الأساسية
        basic_tab = self.create_basic_data_tab()
        tabs.addTab(basic_tab, "البيانات الأساسية")
        
        # التبويب الثاني: تفاصيل إضافية
        details_tab = self.create_details_tab()
        tabs.addTab(details_tab, "التفاصيل")
        
        # التبويب الثالث: الملاحظات
        notes_tab = self.create_notes_tab()
        tabs.addTab(notes_tab, "الملاحظات")
        
        return tabs
    
    def create_basic_data_tab(self) -> QWidget:
        """إنشاء تبويب البيانات الأساسية"""
        
        widget = QWidget()
        layout = QFormLayout(widget)
        layout.setSpacing(15)
        
        # حقول البيانات الأساسية
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل الاسم")
        layout.addRow("الاسم:", self.name_edit)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        layout.addRow("الهاتف:", self.phone_edit)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني")
        layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("أدخل العنوان")
        layout.addRow("العنوان:", self.address_edit)
        
        # قائمة منسدلة
        self.category_combo = QComboBox()
        self.category_combo.addItems(["الفئة الأولى", "الفئة الثانية", "الفئة الثالثة"])
        layout.addRow("الفئة:", self.category_combo)
        
        # حقل رقمي
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setSuffix(" ل.س")
        layout.addRow("المبلغ:", self.amount_spin)
        
        # تاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addRow("التاريخ:", self.date_edit)
        
        # مربع اختيار
        self.active_checkbox = QCheckBox("نشط")
        self.active_checkbox.setChecked(True)
        layout.addRow("الحالة:", self.active_checkbox)
        
        return widget
    
    def create_details_tab(self) -> QWidget:
        """إنشاء تبويب التفاصيل"""
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("الإعدادات")
        settings_layout = QGridLayout(settings_group)
        
        # أزرار راديو
        self.option1_radio = QRadioButton("الخيار الأول")
        self.option1_radio.setChecked(True)
        settings_layout.addWidget(self.option1_radio, 0, 0)
        
        self.option2_radio = QRadioButton("الخيار الثاني")
        settings_layout.addWidget(self.option2_radio, 0, 1)
        
        self.option3_radio = QRadioButton("الخيار الثالث")
        settings_layout.addWidget(self.option3_radio, 1, 0)
        
        layout.addWidget(settings_group)
        
        # مجموعة الخيارات
        options_group = QGroupBox("الخيارات الإضافية")
        options_layout = QVBoxLayout(options_group)
        
        self.option_a_check = QCheckBox("الخيار أ")
        options_layout.addWidget(self.option_a_check)
        
        self.option_b_check = QCheckBox("الخيار ب")
        options_layout.addWidget(self.option_b_check)
        
        self.option_c_check = QCheckBox("الخيار ج")
        options_layout.addWidget(self.option_c_check)
        
        layout.addWidget(options_group)
        
        layout.addStretch()
        
        return widget
    
    def create_notes_tab(self) -> QWidget:
        """إنشاء تبويب الملاحظات"""
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # حقل الملاحظات
        notes_label = QLabel("الملاحظات:")
        notes_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(notes_label)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية هنا...")
        layout.addWidget(self.notes_edit)
        
        return widget
    
    def create_simple_form(self) -> QWidget:
        """إنشاء نموذج بسيط (بديل للتبويبات)"""
        
        widget = QWidget()
        layout = QFormLayout(widget)
        layout.setSpacing(15)
        
        # أضف حقولك هنا
        self.field1_edit = QLineEdit()
        layout.addRow("الحقل الأول:", self.field1_edit)
        
        self.field2_edit = QLineEdit()
        layout.addRow("الحقل الثاني:", self.field2_edit)
        
        # أضف المزيد من الحقول حسب الحاجة
        
        return widget
    
    def create_table_with_form(self) -> QSplitter:
        """إنشاء جدول مع نموذج (لإدارة البيانات)"""
        
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(4)  # غير عدد الأعمدة
        self.table.setHorizontalHeaderLabels(["العمود 1", "العمود 2", "العمود 3", "العمود 4"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        splitter.addWidget(self.table)
        
        # النموذج
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        
        # أضف حقول النموذج هنا
        self.form_field1 = QLineEdit()
        form_layout.addRow("الحقل 1:", self.form_field1)
        
        self.form_field2 = QLineEdit()
        form_layout.addRow("الحقل 2:", self.form_field2)
        
        splitter.addWidget(form_widget)
        
        # تعيين النسب
        splitter.setSizes([600, 200])
        
        return splitter
    
    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار"""
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.setMinimumHeight(35)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.save_button)
        
        # زر التحديث (يظهر عند التعديل)
        self.update_button = QPushButton("تحديث")
        self.update_button.setMinimumHeight(35)
        self.update_button.setVisible(False)
        layout.addWidget(self.update_button)
        
        # زر الحذف (يظهر عند التعديل)
        self.delete_button = QPushButton("حذف")
        self.delete_button.setMinimumHeight(35)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.delete_button.setVisible(False)
        layout.addWidget(self.delete_button)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setMinimumHeight(35)
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)
        
        return frame
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        
        # ربط الأزرار
        self.save_button.clicked.connect(self.save_data)
        self.update_button.clicked.connect(self.update_data)
        self.delete_button.clicked.connect(self.delete_data)
        
        # ربط إشارات أخرى حسب الحاجة
        # مثال: تغيير في القائمة المنسدلة
        # self.category_combo.currentTextChanged.connect(self.on_category_changed)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        
        try:
            # تحميل البيانات من قاعدة البيانات
            # مثال:
            # data = self.db_manager.fetch_all("SELECT * FROM your_table")
            # self.populate_table(data)
            
            self.logger.info("تم تحميل البيانات الأولية")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
    
    def save_data(self):
        """حفظ البيانات"""
        
        try:
            # التحقق من صحة البيانات
            if not self.validate_data():
                return
            
            # جمع البيانات من الحقول
            data = self.collect_form_data()
            
            # حفظ في قاعدة البيانات
            # مثال:
            # self.db_manager.execute_query(
            #     "INSERT INTO your_table (field1, field2) VALUES (?, ?)",
            #     (data['field1'], data['field2'])
            # )
            
            # إرسال إشارة النجاح
            self.data_saved.emit(data)
            
            QMessageBox.information(self, "نجح", "تم حفظ البيانات بنجاح")
            
            # مسح النموذج أو إغلاق النافذة
            self.clear_form()
            
            self.logger.info("تم حفظ البيانات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {e}")
    
    def update_data(self):
        """تحديث البيانات"""
        
        try:
            # التحقق من صحة البيانات
            if not self.validate_data():
                return
            
            # جمع البيانات
            data = self.collect_form_data()
            
            # تحديث في قاعدة البيانات
            # مثال:
            # self.db_manager.execute_query(
            #     "UPDATE your_table SET field1=?, field2=? WHERE id=?",
            #     (data['field1'], data['field2'], self.current_data['id'])
            # )
            
            # إرسال إشارة التحديث
            self.data_updated.emit(data)
            
            QMessageBox.information(self, "نجح", "تم تحديث البيانات بنجاح")
            
            self.logger.info("تم تحديث البيانات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث البيانات: {e}")
    
    def delete_data(self):
        """حذف البيانات"""
        
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه البيانات؟",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # حذف من قاعدة البيانات
                # مثال:
                # self.db_manager.execute_query(
                #     "DELETE FROM your_table WHERE id=?",
                #     (self.current_data['id'],)
                # )
                
                QMessageBox.information(self, "نجح", "تم حذف البيانات بنجاح")
                
                # إغلاق النافذة
                self.close()
                
                self.logger.info("تم حذف البيانات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف البيانات: {e}")
    
    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        
        # أضف قواعد التحقق هنا
        # مثال:
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم")
            self.name_edit.setFocus()
            return False
        
        # أضف المزيد من قواعد التحقق حسب الحاجة
        
        return True
    
    def collect_form_data(self) -> dict:
        """جمع البيانات من النموذج"""
        
        data = {
            'name': self.name_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'category': self.category_combo.currentText(),
            'amount': self.amount_spin.value(),
            'date': self.date_edit.date().toString('yyyy-MM-dd'),
            'is_active': self.active_checkbox.isChecked(),
            'notes': self.notes_edit.toPlainText().strip(),
            'created_by': self.current_user['id'],
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return data
    
    def clear_form(self):
        """مسح النموذج"""
        
        self.name_edit.clear()
        self.phone_edit.clear()
        self.email_edit.clear()
        self.address_edit.clear()
        self.category_combo.setCurrentIndex(0)
        self.amount_spin.setValue(0)
        self.date_edit.setDate(QDate.currentDate())
        self.active_checkbox.setChecked(True)
        self.notes_edit.clear()
    
    def load_data_for_editing(self, data: dict):
        """تحميل البيانات للتعديل"""
        
        self.current_data = data
        self.is_editing = True
        
        # ملء الحقول بالبيانات
        self.name_edit.setText(data.get('name', ''))
        self.phone_edit.setText(data.get('phone', ''))
        self.email_edit.setText(data.get('email', ''))
        self.address_edit.setPlainText(data.get('address', ''))
        # ... ملء باقي الحقول
        
        # إظهار أزرار التعديل والحذف
        self.save_button.setVisible(False)
        self.update_button.setVisible(True)
        self.delete_button.setVisible(True)
        
        # تغيير عنوان النافذة
        self.setWindowTitle("تعديل البيانات")

# ملاحظات للمطور:
# 1. غير اسم الفئة من TemplateWindow إلى اسم واجهتك
# 2. عدل الحقول والتخطيط حسب احتياجاتك
# 3. أضف الواجهة في app_config.py
# 4. أنشئ ملف الواجهة في المجلد المناسب
# 5. أعد تشغيل التطبيق لرؤية الواجهة الجديدة

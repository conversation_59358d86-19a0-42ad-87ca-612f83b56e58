#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام النهائي مع النصوص العربية الصحيحة
Final System Run with Correct Arabic Text
"""

import sys
import os
from pathlib import Path

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت - الإصدار النهائي")
    print("=" * 60)
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        print("📦 تحميل المكتبات...")
        
        # تجاهل تحذيرات Qt
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
        
        from PyQt5.QtWidgets import QApplication, QM<PERSON>ageBox, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>idget, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("📁 تحميل وحدات النظام...")
        from src.database.database_manager import DatabaseManager
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support, simple_arabic_text
        
        print("✅ تم تحميل جميع الوحدات بنجاح")
        
        # إنشاء التطبيق
        print("🚀 بدء تشغيل التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        
        # إنشاء مجلد البيانات
        project_dir = project_root / "data"
        project_dir.mkdir(exist_ok=True)
        
        print(f"📁 مجلد البيانات: {project_dir}")
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager(str(project_dir))
        config_manager = ConfigManager(str(project_dir))
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return 1
            
        print("✅ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة اختبار للنصوص العربية
        print("🖥️ إنشاء نافذة اختبار...")
        
        window = QWidget()
        window.setWindowTitle("نظام إدارة شركة الإنترنت - اختبار النصوص العربية")
        window.setGeometry(200, 200, 500, 400)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("🌟 نظام إدارة شركة الإنترنت")
        title.setFont(QFont("Tahoma", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # النصوص العربية للاختبار
        arabic_texts = [
            "مرحباً بك في نظام إدارة شركة الإنترنت",
            "بيانات الدخول:",
            "اسم المستخدم: admin",
            "كلمة المرور: admin123",
            "",
            "الوظائف المتاحة:",
            "• اشتراك جديد",
            "• تسليم راوتر", 
            "• تجديد باقة",
            "• إغلاق الصندوق",
            "",
            "المبلغ: 150,000 ل.س",
            "التاريخ: 2025-01-12"
        ]
        
        for text in arabic_texts:
            if text:  # تجاهل الأسطر الفارغة
                label = QLabel(text)
                label.setFont(QFont("Tahoma", 11))
                label.setAlignment(Qt.AlignRight)
                label.setStyleSheet("padding: 5px; color: #34495e;")
            else:
                label = QLabel("")
                label.setMaximumHeight(10)
            layout.addWidget(label)
        
        # زر تشغيل النظام الكامل
        run_button = QPushButton("🚀 تشغيل النظام الكامل")
        run_button.setFont(QFont("Tahoma", 12, QFont.Bold))
        run_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        def run_full_system():
            """تشغيل النظام الكامل"""
            try:
                window.hide()
                
                from src.ui.login_window import LoginWindow
                from src.ui.main_window import MainWindow
                
                # نافذة تسجيل الدخول
                login_window = LoginWindow(db_manager)
                
                if login_window.exec_() == login_window.Accepted:
                    current_user = login_window.get_current_user()
                    
                    # النافذة الرئيسية
                    main_window = MainWindow(db_manager, config_manager, current_user)
                    main_window.show()
                    
                    print("🎉 تم تشغيل النظام الكامل بنجاح!")
                else:
                    window.show()
                    
            except Exception as e:
                print(f"❌ خطأ في تشغيل النظام الكامل: {e}")
                QMessageBox.critical(None, "خطأ", f"خطأ في تشغيل النظام: {str(e)}")
                window.show()
        
        run_button.clicked.connect(run_full_system)
        layout.addWidget(run_button)
        
        window.setLayout(layout)
        window.show()
        
        print("✅ تم عرض نافذة الاختبار")
        print("🔍 تحقق من النصوص العربية في النافذة")
        print("🚀 اضغط على زر 'تشغيل النظام الكامل' لتشغيل النظام")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

# -*- coding: utf-8 -*-
"""
واجهة إدارة الوحدات
Units Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton,
                            QMessageBox, QFrame, QTableWidget,
                            QTableWidgetItem, QDoubleSpinBox, QGroupBox,
                            QTextEdit, QHeaderView, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency, reshape_arabic_text
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    import sys
    sys.exit(1)


class UnitsManagementWindow(QDialog):
    """نافذة إدارة الوحدات"""

    units_updated = pyqtSignal()

    def __init__(self, db_manager, inventory_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = inventory_manager
        self.current_user = current_user

        self.setWindowTitle(reshape_arabic_text("إدارة الوحدات"))
        self.setModal(True)
        self.resize(900, 600)

        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان النافذة
        title_label = QLabel(reshape_arabic_text("إدارة وحدات القياس"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)

        # مجموعة تحديث الوحدات
        units_group = QGroupBox(reshape_arabic_text("تحديث وحدات المنتجات"))
        units_group.setFont(create_arabic_font(12, bold=True))
        units_layout = QGridLayout(units_group)

        # اختيار المنتج
        units_layout.addWidget(QLabel(reshape_arabic_text("المنتج:")), 0, 0)
        self.product_combo = QComboBox()
        self.product_combo.setFont(create_arabic_font(11))
        self.product_combo.setMinimumHeight(35)
        units_layout.addWidget(self.product_combo, 0, 1, 1, 2)

        # وحدة الشراء
        units_layout.addWidget(QLabel(reshape_arabic_text("وحدة الشراء:")), 1, 0)
        self.purchase_unit_edit = QLineEdit()
        self.purchase_unit_edit.setFont(create_arabic_font(11))
        self.purchase_unit_edit.setPlaceholderText("مثال: بكرة، كرتونة، قطعة")
        units_layout.addWidget(self.purchase_unit_edit, 1, 1)

        # وحدة البيع
        units_layout.addWidget(QLabel(reshape_arabic_text("وحدة البيع:")), 1, 2)
        self.sale_unit_edit = QLineEdit()
        self.sale_unit_edit.setFont(create_arabic_font(11))
        self.sale_unit_edit.setPlaceholderText("مثال: متر، قطعة")
        units_layout.addWidget(self.sale_unit_edit, 1, 3)

        # معامل التحويل
        units_layout.addWidget(QLabel(reshape_arabic_text("معامل التحويل:")), 2, 0)
        self.conversion_factor_spin = QDoubleSpinBox()
        self.conversion_factor_spin.setFont(create_arabic_font(11))
        self.conversion_factor_spin.setRange(0.001, 10000)
        self.conversion_factor_spin.setValue(1.0)
        self.conversion_factor_spin.setDecimals(3)
        self.conversion_factor_spin.setSuffix(" (وحدة بيع لكل وحدة شراء)")
        units_layout.addWidget(self.conversion_factor_spin, 2, 1, 1, 2)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.update_units_btn = QPushButton(reshape_arabic_text("تحديث الوحدات"))
        self.update_units_btn.setFont(create_arabic_font(11, bold=True))
        self.update_units_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(self.update_units_btn)

        self.reset_btn = QPushButton(reshape_arabic_text("إعادة تعيين"))
        self.reset_btn.setFont(create_arabic_font(11))
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        buttons_layout.addWidget(self.reset_btn)

        buttons_layout.addStretch()
        units_layout.addLayout(buttons_layout, 3, 0, 1, 4)

        layout.addWidget(units_group)

        # جدول المنتجات مع الوحدات
        table_group = QGroupBox(reshape_arabic_text("المنتجات ووحداتها"))
        table_group.setFont(create_arabic_font(12, bold=True))
        table_layout = QVBoxLayout(table_group)

        self.products_table = QTableWidget()
        self.products_table.setFont(create_arabic_font(10))
        self.products_table.setColumnCount(7)
        self.products_table.setHorizontalHeaderLabels([
            "المعرف", "اسم المنتج", "الفئة", "وحدة الشراء",
            "وحدة البيع", "معامل التحويل", "المخزون الحالي"
        ])

        # تنسيق الجدول
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المنتج
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # وحدة الشراء
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # وحدة البيع
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # معامل التحويل
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # المخزون

        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)

        table_layout.addWidget(self.products_table)
        layout.addWidget(table_group)

        # أزرار الإغلاق
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        self.close_btn = QPushButton(reshape_arabic_text("إغلاق"))
        self.close_btn.setFont(create_arabic_font(11))
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_layout.addWidget(self.close_btn)

        layout.addLayout(close_layout)

        # ربط الأحداث
        self.connect_signals()

    def connect_signals(self):
        """ربط الإشارات"""
        self.product_combo.currentIndexChanged.connect(self.on_product_selected)
        self.update_units_btn.clicked.connect(self.update_product_units)
        self.reset_btn.clicked.connect(self.reset_form)
        self.close_btn.clicked.connect(self.accept)
        self.products_table.itemSelectionChanged.connect(self.on_table_selection_changed)

    def load_data(self):
        """تحميل البيانات"""
        self.load_products()
        self.load_products_table()

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            products = self.inventory_manager.get_all_products()

            self.product_combo.clear()
            self.product_combo.addItem(reshape_arabic_text("-- اختر منتج --"), None)

            for product in products:
                display_text = f"{product['name']} ({product['category']})"
                self.product_combo.addItem(display_text, product)

            print(f"✅ تم تحميل {len(products)} منتج")

        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المنتجات: {e}")

    def load_products_table(self):
        """تحميل جدول المنتجات"""
        try:
            products = self.inventory_manager.get_all_products()

            self.products_table.setRowCount(len(products))

            for row, product in enumerate(products):
                self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
                self.products_table.setItem(row, 1, QTableWidgetItem(product['name']))
                self.products_table.setItem(row, 2, QTableWidgetItem(product['category']))
                self.products_table.setItem(row, 3, QTableWidgetItem(product.get('purchase_unit', 'قطعة')))
                self.products_table.setItem(row, 4, QTableWidgetItem(product.get('sale_unit', 'قطعة')))

                conversion_factor = product.get('conversion_factor', 1.0)
                self.products_table.setItem(row, 5, QTableWidgetItem(f"{conversion_factor:.3f}"))

                current_stock = product.get('current_stock', 0)
                sale_unit = product.get('sale_unit', 'قطعة')
                self.products_table.setItem(row, 6, QTableWidgetItem(f"{current_stock:.1f} {sale_unit}"))

        except Exception as e:
            print(f"❌ خطأ في تحميل جدول المنتجات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الجدول: {e}")

    def on_product_selected(self):
        """عند اختيار منتج"""
        product_data = self.product_combo.currentData()
        if product_data:
            try:
                # معالجة آمنة للحقول
                purchase_unit = product_data['purchase_unit'] if 'purchase_unit' in product_data and product_data['purchase_unit'] else 'قطعة'
                sale_unit = product_data['sale_unit'] if 'sale_unit' in product_data and product_data['sale_unit'] else 'قطعة'
                conversion_factor = product_data['conversion_factor'] if 'conversion_factor' in product_data and product_data['conversion_factor'] else 1.0

                self.purchase_unit_edit.setText(purchase_unit)
                self.sale_unit_edit.setText(sale_unit)
                self.conversion_factor_spin.setValue(conversion_factor)
            except (KeyError, TypeError) as e:
                print(f"❌ خطأ في تحميل بيانات المنتج: {e}")
                self.reset_form()
        else:
            self.reset_form()

    def on_table_selection_changed(self):
        """عند تغيير اختيار الجدول"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_id = int(self.products_table.item(current_row, 0).text())

            # البحث عن المنتج في القائمة المنسدلة
            for i in range(self.product_combo.count()):
                product_data = self.product_combo.itemData(i)
                if product_data and product_data['id'] == product_id:
                    self.product_combo.setCurrentIndex(i)
                    break

    def update_product_units(self):
        """تحديث وحدات المنتج"""
        product_data = self.product_combo.currentData()
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
            return

        purchase_unit = self.purchase_unit_edit.text().strip()
        sale_unit = self.sale_unit_edit.text().strip()
        conversion_factor = self.conversion_factor_spin.value()

        if not purchase_unit or not sale_unit:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال وحدات الشراء والبيع")
            return

        if conversion_factor <= 0:
            QMessageBox.warning(self, "تحذير", "معامل التحويل يجب أن يكون أكبر من صفر")
            return

        try:
            # تحديث في قاعدة البيانات
            self.db_manager.execute_query("""
                UPDATE unified_products
                SET purchase_unit = ?, sale_unit = ?, conversion_factor = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (purchase_unit, sale_unit, conversion_factor, product_data['id']))

            QMessageBox.information(self, "نجح", f"تم تحديث وحدات المنتج '{product_data['name']}' بنجاح")

            # إعادة تحميل البيانات
            self.load_data()

            # إرسال إشارة التحديث
            self.units_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في تحديث الوحدات: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الوحدات: {e}")

    def reset_form(self):
        """إعادة تعيين النموذج"""
        self.product_combo.setCurrentIndex(0)
        self.purchase_unit_edit.clear()
        self.sale_unit_edit.clear()
        self.conversion_factor_spin.setValue(1.0)

    def create_add_unit_group(self):
        """إنشاء مجموعة إضافة وحدة جديدة"""
        group = QGroupBox(reshape_arabic_text("إضافة وحدة جديدة"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # اسم الوحدة
        layout.addWidget(QLabel(reshape_arabic_text("اسم الوحدة:")), 0, 0)
        self.unit_name_edit = QLineEdit()
        self.unit_name_edit.setFont(create_arabic_font(10))
        self.unit_name_edit.setPlaceholderText("مثال: متر، كيلو، قطعة")
        layout.addWidget(self.unit_name_edit, 0, 1)
        
        # الرمز
        layout.addWidget(QLabel(reshape_arabic_text("الرمز:")), 0, 2)
        self.unit_symbol_edit = QLineEdit()
        self.unit_symbol_edit.setFont(create_arabic_font(10))
        self.unit_symbol_edit.setPlaceholderText("مثال: م، كغ، ق")
        layout.addWidget(self.unit_symbol_edit, 0, 3)
        
        # النوع
        layout.addWidget(QLabel(reshape_arabic_text("النوع:")), 1, 0)
        self.unit_type_combo = QComboBox()
        self.unit_type_combo.setFont(create_arabic_font(10))
        self.unit_type_combo.addItems([
            "وحدة أساسية",
            "وحدة فرعية",
            "وحدة مركبة"
        ])
        layout.addWidget(self.unit_type_combo, 1, 1)
        
        # معامل التحويل
        layout.addWidget(QLabel(reshape_arabic_text("معامل التحويل:")), 1, 2)
        self.conversion_factor_spin = QDoubleSpinBox()
        self.conversion_factor_spin.setFont(create_arabic_font(10))
        self.conversion_factor_spin.setRange(0.001, 999999)
        self.conversion_factor_spin.setValue(1.0)
        self.conversion_factor_spin.setDecimals(3)
        layout.addWidget(self.conversion_factor_spin, 1, 3)
        
        # زر الإضافة
        add_button = QPushButton(reshape_arabic_text("إضافة الوحدة"))
        add_button.setFont(create_arabic_font(10, bold=True))
        add_button.clicked.connect(self.add_unit)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(add_button, 2, 0, 1, 4)
        
        return group
        
    def create_units_table(self):
        """إنشاء جدول الوحدات"""
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "الرقم", "اسم الوحدة", "الرمز", "النوع", "معامل التحويل", "تاريخ الإنشاء"
        ])
        
        # تنسيق الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر التحديث
        refresh_button = QPushButton(reshape_arabic_text("تحديث"))
        refresh_button.setFont(create_arabic_font(10))
        refresh_button.clicked.connect(self.load_units)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(refresh_button)
        
        # زر الحذف
        delete_button = QPushButton(reshape_arabic_text("حذف الوحدة"))
        delete_button.setFont(create_arabic_font(10))
        delete_button.clicked.connect(self.delete_unit)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(delete_button)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton(reshape_arabic_text("إغلاق"))
        close_button.setFont(create_arabic_font(10))
        close_button.clicked.connect(self.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        layout.addWidget(close_button)
        
        return layout

    def create_units_table(self):
        """إنشاء جدول الوحدات في قاعدة البيانات"""
        try:
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS units (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    symbol TEXT UNIQUE NOT NULL,
                    conversion_factor REAL DEFAULT 1.0,
                    base_unit_id INTEGER,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (base_unit_id) REFERENCES units(id)
                )
            """)

            # إضافة وحدات افتراضية
            default_units = [
                ('قطعة', 'ق', 1.0, None, 'وحدة العد الأساسية'),
                ('متر', 'م', 1.0, None, 'وحدة الطول الأساسية'),
                ('بكرة', 'بكرة', 1.0, None, 'بكرة كاملة'),
                ('كيلوغرام', 'كغ', 1.0, None, 'وحدة الوزن الأساسية'),
                ('لتر', 'ل', 1.0, None, 'وحدة الحجم الأساسية')
            ]

            for unit in default_units:
                existing = self.db_manager.fetch_one("SELECT id FROM units WHERE name = ?", (unit[0],))
                if not existing:
                    self.db_manager.execute_query("""
                        INSERT INTO units (name, symbol, conversion_factor, base_unit_id, description)
                        VALUES (?, ?, ?, ?, ?)
                    """, unit)

            print("✅ تم إنشاء جدول الوحدات مع الوحدات الافتراضية")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الوحدات: {e}")

    def load_units(self):
        """تحميل الوحدات"""
        try:
            units = self.db_manager.fetch_all("""
                SELECT u.id, u.name, u.symbol, u.conversion_factor,
                       bu.name as base_unit_name, u.description, u.created_at
                FROM units u
                LEFT JOIN units bu ON u.base_unit_id = bu.id
                ORDER BY u.name
            """)

            self.units_table.setRowCount(len(units))

            for row, unit in enumerate(units):
                # ID
                id_item = QTableWidgetItem(str(unit[0]))
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 0, id_item)

                # اسم الوحدة
                name_item = QTableWidgetItem(str(unit[1]))
                name_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 1, name_item)

                # الرمز
                symbol_item = QTableWidgetItem(str(unit[2]))
                symbol_item.setTextAlignment(Qt.AlignCenter)
                symbol_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 2, symbol_item)

                # معامل التحويل
                factor_item = QTableWidgetItem(str(unit[3]))
                factor_item.setTextAlignment(Qt.AlignCenter)
                factor_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 3, factor_item)

                # الوحدة الأساسية
                base_unit = str(unit[4]) if unit[4] else 'وحدة أساسية'
                base_item = QTableWidgetItem(base_unit)
                base_item.setTextAlignment(Qt.AlignCenter)
                base_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 4, base_item)

                # الوصف
                desc_item = QTableWidgetItem(str(unit[5]) if unit[5] else '')
                desc_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 5, desc_item)

                # تاريخ الإنشاء
                created_at = unit[6][:10] if unit[6] else ''
                date_item = QTableWidgetItem(created_at)
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFont(create_arabic_font(10))
                self.units_table.setItem(row, 6, date_item)

            # تنسيق الجدول
            header = self.units_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
            header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم الوحدة
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الرمز
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # معامل التحويل
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الوحدة الأساسية
            header.setSectionResizeMode(5, QHeaderView.Stretch)           # الوصف
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ الإنشاء

            # تحديث قائمة الوحدات الأساسية
            self.load_base_units()

            print(f"✅ تم تحميل {len(units)} وحدة")

        except Exception as e:
            print(f"❌ خطأ في تحميل الوحدات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل الوحدات: {e}"))

    def load_base_units(self):
        """تحميل الوحدات الأساسية في القائمة المنسدلة"""
        try:
            units = self.db_manager.fetch_all("SELECT id, name FROM units ORDER BY name")

            self.base_unit_combo.clear()
            self.base_unit_combo.addItem(reshape_arabic_text("وحدة أساسية"), "")

            for unit in units:
                self.base_unit_combo.addItem(unit[1], unit[0])

        except Exception as e:
            print(f"❌ خطأ في تحميل الوحدات الأساسية: {e}")

    def on_unit_selected(self):
        """عند تحديد وحدة من الجدول"""
        try:
            current_row = self.units_table.currentRow()
            if current_row >= 0:
                # تحميل بيانات الوحدة المحددة
                unit_id = self.units_table.item(current_row, 0).text()
                unit = self.db_manager.fetch_one("SELECT * FROM units WHERE id = ?", (unit_id,))

                if unit:
                    self.unit_name_edit.setText(unit[1])
                    self.unit_symbol_edit.setText(unit[2])
                    self.conversion_factor_spin.setValue(unit[3])
                    self.description_edit.setPlainText(unit[5] if unit[5] else '')

                    # تحديد الوحدة الأساسية
                    if unit[4]:  # base_unit_id
                        for i in range(self.base_unit_combo.count()):
                            if self.base_unit_combo.itemData(i) == unit[4]:
                                self.base_unit_combo.setCurrentIndex(i)
                                break
                    else:
                        self.base_unit_combo.setCurrentIndex(0)

                    # تفعيل زر التعديل
                    self.edit_btn.setEnabled(True)
                    self.add_btn.setText(reshape_arabic_text("➕ إضافة وحدة"))

        except Exception as e:
            print(f"❌ خطأ في تحديد الوحدة: {e}")

    def add_unit(self):
        """إضافة وحدة جديدة"""
        try:
            name = self.unit_name_edit.text().strip()
            symbol = self.unit_symbol_edit.text().strip()
            conversion_factor = self.conversion_factor_spin.value()
            base_unit_id = self.base_unit_combo.currentData()
            description = self.description_edit.toPlainText().strip()

            if not name or not symbol:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال اسم الوحدة والرمز"))
                return

            # التحقق من عدم التكرار
            existing = self.db_manager.fetch_one("""
                SELECT id FROM units WHERE name = ? OR symbol = ?
            """, (name, symbol))

            if existing:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("اسم الوحدة أو الرمز موجود مسبقاً"))
                return

            # إضافة الوحدة
            self.db_manager.execute_query("""
                INSERT INTO units (name, symbol, conversion_factor, base_unit_id, description)
                VALUES (?, ?, ?, ?, ?)
            """, (name, symbol, conversion_factor, base_unit_id if base_unit_id else None, description))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم إضافة الوحدة بنجاح"))

            self.clear_form()
            self.load_units()
            self.units_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في إضافة الوحدة: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في إضافة الوحدة: {e}"))

    def edit_unit(self):
        """تعديل الوحدة المحددة"""
        try:
            current_row = self.units_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى تحديد وحدة للتعديل"))
                return

            unit_id = self.units_table.item(current_row, 0).text()
            name = self.unit_name_edit.text().strip()
            symbol = self.unit_symbol_edit.text().strip()
            conversion_factor = self.conversion_factor_spin.value()
            base_unit_id = self.base_unit_combo.currentData()
            description = self.description_edit.toPlainText().strip()

            if not name or not symbol:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال اسم الوحدة والرمز"))
                return

            # التحقق من عدم التكرار (عدا الوحدة الحالية)
            existing = self.db_manager.fetch_one("""
                SELECT id FROM units WHERE (name = ? OR symbol = ?) AND id != ?
            """, (name, symbol, unit_id))

            if existing:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("اسم الوحدة أو الرمز موجود مسبقاً"))
                return

            # تعديل الوحدة
            self.db_manager.execute_query("""
                UPDATE units
                SET name = ?, symbol = ?, conversion_factor = ?, base_unit_id = ?, description = ?
                WHERE id = ?
            """, (name, symbol, conversion_factor, base_unit_id if base_unit_id else None, description, unit_id))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم تعديل الوحدة بنجاح"))

            self.clear_form()
            self.load_units()
            self.units_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في تعديل الوحدة: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تعديل الوحدة: {e}"))

    def delete_unit(self):
        """حذف الوحدة المحددة"""
        try:
            current_row = self.units_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى تحديد وحدة للحذف"))
                return

            unit_id = self.units_table.item(current_row, 0).text()
            unit_name = self.units_table.item(current_row, 1).text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                reshape_arabic_text("تأكيد الحذف"),
                reshape_arabic_text(f"هل أنت متأكد من حذف الوحدة '{unit_name}'؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # التحقق من استخدام الوحدة
            usage_check = self.db_manager.fetch_one("""
                SELECT COUNT(*) as count FROM products WHERE unit_type = ?
            """, (unit_name,))

            if usage_check and usage_check[0] > 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text(f"لا يمكن حذف الوحدة لأنها مستخدمة في {usage_check[0]} منتج"))
                return

            # حذف الوحدة
            self.db_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text("تم حذف الوحدة بنجاح"))

            self.clear_form()
            self.load_units()
            self.units_updated.emit()

        except Exception as e:
            print(f"❌ خطأ في حذف الوحدة: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في حذف الوحدة: {e}"))

    def clear_form(self):
        """مسح النموذج"""
        self.unit_name_edit.clear()
        self.unit_symbol_edit.clear()
        self.conversion_factor_spin.setValue(1.0)
        self.base_unit_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.edit_btn.setEnabled(False)
        self.add_btn.setText(reshape_arabic_text("➕ إضافة وحدة"))
        self.units_table.clearSelection()
        
    def add_unit(self):
        """إضافة وحدة جديدة"""
        try:
            # التحقق من البيانات
            name = self.unit_name_edit.text().strip()
            symbol = self.unit_symbol_edit.text().strip()
            
            if not name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الوحدة")
                return
                
            if not symbol:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الوحدة")
                return
            
            # التحقق من عدم وجود الوحدة مسبقاً
            existing = self.db_manager.fetch_one("""
                SELECT id FROM units WHERE name = ? OR symbol = ?
            """, (name, symbol))
            
            if existing:
                QMessageBox.warning(self, "تحذير", "الوحدة موجودة مسبقاً")
                return
            
            # إضافة الوحدة
            self.db_manager.execute_query("""
                INSERT INTO units (name, symbol, unit_type, conversion_factor)
                VALUES (?, ?, ?, ?)
            """, (
                name,
                symbol,
                self.unit_type_combo.currentText(),
                self.conversion_factor_spin.value()
            ))
            
            # مسح الحقول
            self.unit_name_edit.clear()
            self.unit_symbol_edit.clear()
            self.conversion_factor_spin.setValue(1.0)
            
            # تحديث الجدول
            self.load_units()
            
            QMessageBox.information(self, "نجح", "تم إضافة الوحدة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الوحدة: {e}")
            
    def load_units(self):
        """تحميل الوحدات"""
        try:
            # إنشاء جدول الوحدات إذا لم يكن موجوداً
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS units (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    symbol TEXT NOT NULL UNIQUE,
                    unit_type TEXT DEFAULT 'وحدة أساسية',
                    conversion_factor REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            units = self.db_manager.fetch_all("""
                SELECT id, name, symbol, unit_type, conversion_factor, created_at
                FROM units
                ORDER BY created_at DESC
            """)
            
            self.units_table.setRowCount(len(units))
            
            for row, unit in enumerate(units):
                self.units_table.setItem(row, 0, QTableWidgetItem(str(unit['id'])))
                self.units_table.setItem(row, 1, QTableWidgetItem(unit['name']))
                self.units_table.setItem(row, 2, QTableWidgetItem(unit['symbol']))
                self.units_table.setItem(row, 3, QTableWidgetItem(unit['unit_type']))
                self.units_table.setItem(row, 4, QTableWidgetItem(str(unit['conversion_factor'])))
                self.units_table.setItem(row, 5, QTableWidgetItem(str(unit['created_at'])))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الوحدات: {e}")
            
    def delete_unit(self):
        """حذف وحدة"""
        try:
            current_row = self.units_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار وحدة للحذف")
                return
                
            unit_id = self.units_table.item(current_row, 0).text()
            unit_name = self.units_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل أنت متأكد من حذف الوحدة '{unit_name}'؟",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.db_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))
                self.load_units()
                QMessageBox.information(self, "تم", "تم حذف الوحدة بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الوحدة: {e}")

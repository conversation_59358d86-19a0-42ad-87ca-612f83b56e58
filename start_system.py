#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة شركة الإنترنت - الإصدار النهائي
Internet Company Management System - Final Version
"""

import sys
import os
from pathlib import Path
import traceback

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    missing_modules = []
    
    # فحص PyQt5
    try:
        import PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 متوفر")
    except ImportError:
        missing_modules.append("PyQt5")
        
    # فحص arabic_reshaper
    try:
        import arabic_reshaper
        print("✅ arabic_reshaper متوفر")
    except ImportError:
        missing_modules.append("arabic_reshaper")
        
    # فحص python_bidi
    try:
        import bidi
        print("✅ python-bidi متوفر")
    except ImportError:
        missing_modules.append("python-bidi")
        
    if missing_modules:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
        print("يرجى تثبيتها باستخدام:")
        for module in missing_modules:
            if module == "arabic_reshaper":
                print(f"pip install arabic-reshaper")
            elif module == "python-bidi":
                print(f"pip install python-bidi")
            else:
                print(f"pip install {module}")
        return False
        
    return True

def run_system():
    """تشغيل النظام"""
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # استيراد الوحدات
        from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        from src.database.database_manager import DatabaseManager
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        
        print("✅ تم تحميل جميع الوحدات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        if setup_arabic_support(app):
            print("✅ تم إعداد الدعم العربي بنجاح")
        else:
            print("⚠️ تحذير: مشكلة في إعداد الدعم العربي")

        print("🚀 بدء تشغيل النظام...")
        
        # اختيار مجلد المشروع
        dialog = QFileDialog()
        dialog.setFileMode(QFileDialog.Directory)
        dialog.setWindowTitle("اختر مجلد حفظ بيانات النظام")
        dialog.setDirectory(str(Path.home() / "Desktop"))
        
        if not dialog.exec_():
            print("❌ لم يتم اختيار مجلد")
            return 1
            
        project_dir = dialog.selectedFiles()[0]
        print(f"📁 مجلد البيانات: {project_dir}")
        
        # إنشاء مدير الإعدادات
        config_manager = ConfigManager(project_dir)
        print("✅ تم إنشاء مدير الإعدادات")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager(project_dir)
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # تهيئة قاعدة البيانات
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
            
        print("✅ تم تهيئة قاعدة البيانات")
        
        # عرض نافذة تسجيل الدخول
        login_window = LoginWindow(db_manager)
        print("🔐 عرض نافذة تسجيل الدخول...")
        
        if login_window.exec_() != login_window.Accepted:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
        current_user = login_window.get_current_user()
        print(f"✅ تم تسجيل الدخول: {current_user['full_name']}")
        
        # عرض النافذة الرئيسية
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        print("🎉 تم تشغيل النظام بنجاح!")
        
        # تشغيل حلقة الأحداث
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        return 1

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🌟 نظام إدارة شركة الإنترنت")
    print("Internet Company Management System")
    print("الإصدار 1.0.0")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return 1
        
    print("\n🎯 جميع المتطلبات متوفرة")
    
    # تشغيل النظام
    try:
        return run_system()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())

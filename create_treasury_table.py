#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def create_treasury_table():
    print("إنشاء جدول الخزينة...")
    
    db = DatabaseManager('data/company_system.db')
    
    try:
        # إنشاء جدول الخزينة
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS treasury (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_type TEXT UNIQUE NOT NULL DEFAULT 'SYP',
                balance REAL DEFAULT 0,
                exchange_rate REAL DEFAULT 1.0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول الخزينة")
        
        # إضافة العملات الافتراضية
        currencies = [
            ('SYP', 0, 1.0, 'الليرة السورية'),
            ('USD', 500.0, 15000.0, 'الدولار الأمريكي'),
            ('EUR', 300.0, 16000.0, 'اليورو')
        ]
        
        for currency_type, balance, exchange_rate, name in currencies:
            # التحقق من وجود العملة
            existing = db.fetch_one("SELECT id FROM treasury WHERE currency_type = ?", (currency_type,))
            
            if not existing:
                db.execute_query("""
                    INSERT INTO treasury (currency_type, balance, exchange_rate)
                    VALUES (?, ?, ?)
                """, (currency_type, balance, exchange_rate))
                print(f"✅ تم إضافة {name}: {balance} {currency_type}")
            else:
                print(f"✅ {name} موجود بالفعل")
        
        # عرض النتائج
        all_currencies = db.fetch_all("SELECT * FROM treasury ORDER BY currency_type")
        print(f"\n💱 العملات في النظام ({len(all_currencies)}):")
        
        total_syp_value = 0
        for curr in all_currencies:
            currency_type = curr[1]
            balance = curr[2]
            exchange_rate = curr[3]
            
            if currency_type == 'SYP':
                print(f"  • الليرة السورية: {balance:,.0f} ل.س")
                total_syp_value += balance
            elif currency_type == 'USD':
                syp_value = balance * exchange_rate
                total_syp_value += syp_value
                print(f"  • الدولار الأمريكي: ${balance:,.2f} (≈ {syp_value:,.0f} ل.س)")
            elif currency_type == 'EUR':
                syp_value = balance * exchange_rate
                total_syp_value += syp_value
                print(f"  • اليورو: €{balance:,.2f} (≈ {syp_value:,.0f} ل.س)")
            else:
                syp_value = balance * exchange_rate
                total_syp_value += syp_value
                print(f"  • {currency_type}: {balance:,.2f} (≈ {syp_value:,.0f} ل.س)")
        
        print(f"\n📊 إجمالي القيمة: {total_syp_value:,.0f} ل.س")
        
        print("\n🎉 تم إنشاء جدول الخزينة بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_treasury_table()

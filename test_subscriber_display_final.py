#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض المشتركين النهائي
"""

import sys
import os
sys.path.append('src')

def test_subscriber_display():
    """اختبار عرض المشتركين في واجهة تسليم الراوتر"""
    
    print("🧪 اختبار عرض المشتركين في واجهة تسليم الراوتر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # إنشاء واجهة تسليم الراوتر
        window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        print("✅ تم إنشاء واجهة تسليم الراوتر")
        
        # اختبار تحميل المشتركين غير المسلمين
        print("\n🔍 اختبار تحميل المشتركين غير المسلمين...")
        
        window.load_undelivered_subscribers()
        
        # فحص عدد المشتركين في الـ combo box
        subscriber_count = window.subscriber_combo.count() - 1  # -1 للعنصر الأول "-- اختر مشتركاً --"
        print(f"📊 عدد المشتركين المتاحين للتسليم: {subscriber_count}")
        
        if subscriber_count > 0:
            print("✅ يتم عرض المشتركين غير المسلمين")
            
            # عرض أول 5 مشتركين
            print("\n📋 أول 5 مشتركين:")
            for i in range(1, min(6, window.subscriber_combo.count())):
                item_text = window.subscriber_combo.itemText(i)
                item_data = window.subscriber_combo.itemData(i)
                print(f"  • {item_text}")
                if item_data:
                    print(f"    ID: {item_data.get('id', 'N/A')}, هاتف: {item_data.get('phone', 'غير محدد')}")
            
            return True
        else:
            print("❌ لا يتم عرض أي مشتركين")
            
            # فحص السبب
            print("\n🔍 فحص السبب...")
            
            # فحص إجمالي المشتركين
            total_subscribers = db.fetch_one("SELECT COUNT(*) as count FROM subscribers")
            print(f"📊 إجمالي المشتركين في قاعدة البيانات: {total_subscribers['count'] if total_subscribers else 0}")
            
            # فحص المشتركين غير المسلمين
            undelivered = db.fetch_one("SELECT COUNT(*) as count FROM subscribers WHERE delivered = 0")
            print(f"📊 المشتركين غير المسلمين: {undelivered['count'] if undelivered else 0}")
            
            # فحص التسليمات
            deliveries = db.fetch_one("SELECT COUNT(*) as count FROM router_deliveries")
            print(f"📦 عدد التسليمات: {deliveries['count'] if deliveries else 0}")
            
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عرض المشتركين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_process():
    """اختبار عملية التسليم الكاملة"""
    
    print("\n🧪 اختبار عملية التسليم الكاملة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        from ui.router_delivery_window import RouterDeliveryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء واجهة تسليم الراوتر
        window = RouterDeliveryWindow(db, inventory_manager, treasury_manager, current_user)
        
        # إضافة مشترك تجريبي للاختبار
        print("👤 إضافة مشترك تجريبي...")
        
        test_subscriber_name = "مشترك اختبار العرض النهائي"
        
        # التحقق من وجود المشترك
        existing = db.fetch_one("SELECT id FROM subscribers WHERE name = ?", (test_subscriber_name,))
        
        if not existing:
            subscriber_result = db.execute_query("""
                INSERT INTO subscribers (name, phone, address, delivered)
                VALUES (?, ?, ?, ?)
            """, (test_subscriber_name, "123456789", "عنوان تجريبي", 0))
            
            if subscriber_result:
                print(f"✅ تم إضافة المشترك التجريبي - ID: {subscriber_result.lastrowid}")
            else:
                print("❌ فشل في إضافة المشترك التجريبي")
                return False
        else:
            print("✅ المشترك التجريبي موجود")
        
        # إعادة تحميل المشتركين
        window.load_undelivered_subscribers()
        
        # البحث عن المشترك التجريبي في القائمة
        found_subscriber = False
        for i in range(window.subscriber_combo.count()):
            item_text = window.subscriber_combo.itemText(i)
            if test_subscriber_name in item_text:
                found_subscriber = True
                print(f"✅ تم العثور على المشترك التجريبي في القائمة: {item_text}")
                break
        
        if not found_subscriber:
            print("❌ لم يتم العثور على المشترك التجريبي في القائمة")
            return False
        
        # محاكاة عملية التسليم
        print("\n📦 محاكاة عملية التسليم...")
        
        # بيانات تجريبية
        router_data = {'id': 1, 'name': 'راوتر تجريبي', 'unit_price': 75000}
        cable_data = {'id': 2, 'name': 'كبل تجريبي', 'unit_price': 500}
        worker_data = {'id': 1, 'name': 'عامل تجريبي'}
        package_data = {'id': 1, 'name': 'باقة تجريبية', 'price': 0}
        
        # حفظ التسليم
        delivery_id = window.save_delivery_record(
            test_subscriber_name,
            router_data,
            cable_data,
            worker_data,
            package_data,
            50,  # cable_meters
            100000  # total_amount
        )
        
        if delivery_id:
            print(f"✅ تم حفظ التسليم - ID: {delivery_id}")
            
            # التحقق من تحديث حالة التسليم
            updated_subscriber = db.fetch_one("""
                SELECT delivered FROM subscribers WHERE name = ?
            """, (test_subscriber_name,))
            
            if updated_subscriber and updated_subscriber['delivered'] == 1:
                print("✅ تم تحديث حالة التسليم للمشترك")
                
                # إعادة تحميل المشتركين للتأكد من الإخفاء
                window.load_undelivered_subscribers()
                
                # التحقق من عدم ظهور المشترك المسلم
                subscriber_hidden = True
                for i in range(window.subscriber_combo.count()):
                    item_text = window.subscriber_combo.itemText(i)
                    if test_subscriber_name in item_text:
                        subscriber_hidden = False
                        break
                
                if subscriber_hidden:
                    print("✅ تم إخفاء المشترك المسلم من القائمة")
                    return True
                else:
                    print("⚠️ المشترك المسلم ما زال يظهر في القائمة")
                    return False
            else:
                print("❌ لم يتم تحديث حالة التسليم للمشترك")
                return False
        else:
            print("❌ فشل في حفظ التسليم")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عملية التسليم: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار عرض المشتركين النهائي")
    print("=" * 60)
    
    # اختبار عرض المشتركين
    display_test = test_subscriber_display()
    
    # اختبار عملية التسليم الكاملة
    delivery_test = test_delivery_process()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • عرض المشتركين: {'✅ يعمل' if display_test else '❌ لا يعمل'}")
    print(f"  • عملية التسليم الكاملة: {'✅ تعمل' if delivery_test else '❌ لا تعمل'}")
    
    if all([display_test, delivery_test]):
        print("\n🎉 تم إصلاح مشكلة عرض المشتركين بالكامل!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إصلاح جدول router_deliveries")
        print("    • إضافة عمود subscriber_id للربط الصحيح")
        print("    • تحديث البيانات الموجودة")
        
        print("  ✅ إصلاح جدول subscribers")
        print("    • إضافة عمود delivered لتتبع حالة التسليم")
        print("    • تحديث حالة المشتركين الحالية")
        
        print("  ✅ إصلاح واجهة تسليم الراوتر")
        print("    • استعلام محسن لعرض المشتركين غير المسلمين")
        print("    • تحديث تلقائي لحالة التسليم عند الحفظ")
        print("    • إخفاء المشتركين المسلمين من القائمة")
        
        print("\n🚀 النظام الآن:")
        print("  • يعرض فقط المشتركين الذين لم يستلموا راوترهم")
        print("  • يحفظ التسليم ويحدث حالة المشترك تلقائياً")
        print("  • يخفي المشتركين المسلمين من القائمة")
        print("  • يربط التسليمات بالمشتركين بشكل صحيح")
        
        print("\n🎯 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'تسليم راوتر' من القائمة الرئيسية")
        print("  3. ستظهر قائمة بالمشتركين الذين لم يستلموا")
        print("  4. اختر مشترك واكمل بيانات التسليم")
        print("  5. اضغط 'حفظ وتسليم' - سيختفي المشترك من القائمة")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")
        
        if not display_test:
            print("  • راجع تحميل المشتركين في الواجهة")
        if not delivery_test:
            print("  • راجع عملية التسليم وتحديث الحالة")

if __name__ == "__main__":
    main()

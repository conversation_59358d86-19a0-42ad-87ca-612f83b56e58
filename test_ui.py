#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهات
Test UI Components
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
src_path = project_root / "src"

sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# تجاهل تحذيرات Qt
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

def test_reports_window():
    """اختبار واجهة التقارير"""
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from ui.reports_window import ReportsWindow
        
        app = QApplication([])
        
        # إعداد قاعدة البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
        
        # إنشاء مستخدم وهمي
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير العام',
            'role': 'admin'
        }
        
        # إنشاء واجهة التقارير
        reports_window = ReportsWindow(db_manager, config_manager, current_user)
        
        print("✅ تم إنشاء واجهة التقارير بنجاح")
        
        # اختبار فتح الواجهة
        reports_window.show()
        print("✅ تم عرض واجهة التقارير")
        
        reports_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التقارير: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supplier_dialog():
    """اختبار حوار إضافة المورد"""
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.suppliers_management_window import SupplierDialog
        
        app = QApplication([])
        
        # إعداد قاعدة البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
        
        # إنشاء حوار المورد
        supplier_dialog = SupplierDialog(db_manager)
        
        print("✅ تم إنشاء حوار المورد بنجاح")
        
        # اختبار فتح الحوار
        supplier_dialog.show()
        print("✅ تم عرض حوار المورد")
        
        supplier_dialog.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار المورد: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_users_window():
    """اختبار واجهة إدارة المستخدمين"""
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from ui.users_management_window import UsersManagementWindow
        
        app = QApplication([])
        
        # إعداد قاعدة البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        if not db_manager.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
        
        # إنشاء مستخدم وهمي
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير العام',
            'role': 'admin'
        }
        
        # إنشاء واجهة إدارة المستخدمين
        users_window = UsersManagementWindow(db_manager, config_manager, current_user)
        
        print("✅ تم إنشاء واجهة إدارة المستخدمين بنجاح")
        
        # اختبار فتح الواجهة
        users_window.show()
        print("✅ تم عرض واجهة إدارة المستخدمين")
        
        users_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إدارة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🖥️ اختبار الواجهات")
    print("=" * 40)
    
    tests = [
        ("واجهة التقارير", test_reports_window),
        ("حوار إضافة المورد", test_supplier_dialog),
        ("واجهة إدارة المستخدمين", test_users_window),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار {test_name}:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات الواجهات نجحت!")
        print("✅ المشاكل المبلغ عنها تم إصلاحها:")
        print("   ✅ واجهة التقارير تفتح بنجاح")
        print("   ✅ حوار إضافة المورد يعمل")
        print("   ✅ واجهة إدارة المستخدمين تعمل")
    else:
        print("⚠️ بعض اختبارات الواجهات فشلت")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إغلاق الصندوق
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QMessageBox, QHeaderView,
                            QTextEdit, QFrame, QGroupBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
    from utils.shift_manager import get_current_shift, close_shift, get_shift_summary
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class CashBoxClosureWindow(QDialog):
    """واجهة إغلاق الصندوق"""
    
    cash_box_closed = pyqtSignal()
    
    def __init__(self, db_manager, treasury_manager=None, current_user=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        self.current_shift_id = None

        # استخدام مدير الخزينة الممرر أو إنشاء واحد جديد
        if treasury_manager:
            self.treasury_manager = treasury_manager
        else:
            # إنشاء مدير الخزينة الموحد
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            self.treasury_manager = UnifiedTreasuryManager(db_manager)
        
        self.setWindowTitle(reshape_arabic_text("إغلاق الصندوق"))
        self.setModal(True)
        self.resize(800, 600)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_shift_data()

        # التحقق من حالة الصندوق
        self.check_cash_box_status()
        
        print("✅ تم إنشاء واجهة إغلاق الصندوق")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("💰 إغلاق صندوق اليوم"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الشيفت
        shift_info_group = self.create_shift_info_section()
        layout.addWidget(shift_info_group)
        
        # ملخص المبيعات والمصاريف
        summary_layout = QHBoxLayout()
        
        # المبيعات
        sales_group = self.create_sales_section()
        summary_layout.addWidget(sales_group)
        
        # المصاريف
        expenses_group = self.create_expenses_section()
        summary_layout.addWidget(expenses_group)
        
        layout.addLayout(summary_layout)
        
        # الحسابات النهائية
        final_calc_group = self.create_final_calculations_section()
        layout.addWidget(final_calc_group)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_shift_info_section(self):
        """إنشاء قسم معلومات الشيفت"""
        group = QGroupBox(reshape_arabic_text("📊 معلومات الشيفت"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # اسم المستخدم
        layout.addWidget(QLabel(reshape_arabic_text("المستخدم:")), 0, 0)
        self.user_label = QLabel(self.current_user['username'])
        self.user_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.user_label, 0, 1)
        
        # تاريخ الشيفت
        layout.addWidget(QLabel(reshape_arabic_text("التاريخ:")), 0, 2)
        self.date_label = QLabel(datetime.now().strftime('%Y-%m-%d'))
        self.date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.date_label, 0, 3)
        
        # وقت البداية
        layout.addWidget(QLabel(reshape_arabic_text("وقت البداية:")), 1, 0)
        self.start_time_label = QLabel("--:--")
        layout.addWidget(self.start_time_label, 1, 1)
        
        # وقت الإغلاق المتوقع
        layout.addWidget(QLabel(reshape_arabic_text("وقت الإغلاق:")), 1, 2)
        self.end_time_label = QLabel(datetime.now().strftime('%H:%M:%S'))
        layout.addWidget(self.end_time_label, 1, 3)
        
        return group

    def create_sales_section(self):
        """إنشاء قسم المبيعات"""
        group = QGroupBox(reshape_arabic_text("📈 إجمالي المبيعات"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # اشتراكات جديدة
        layout.addWidget(QLabel(reshape_arabic_text("اشتراكات جديدة:")), 0, 0)
        self.new_subscriptions_label = QLabel("0 ل.س")
        self.new_subscriptions_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(self.new_subscriptions_label, 0, 1)
        
        # تسليم راوترات
        layout.addWidget(QLabel(reshape_arabic_text("تسليم راوترات:")), 1, 0)
        self.router_deliveries_label = QLabel("0 ل.س")
        self.router_deliveries_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(self.router_deliveries_label, 1, 1)
        
        # تجديد باقات
        layout.addWidget(QLabel(reshape_arabic_text("تجديد باقات:")), 2, 0)
        self.package_renewals_label = QLabel("0 ل.س")
        self.package_renewals_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(self.package_renewals_label, 2, 1)
        
        # سندات قبض
        layout.addWidget(QLabel(reshape_arabic_text("سندات قبض:")), 3, 0)
        self.receipts_label = QLabel("0 ل.س")
        self.receipts_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(self.receipts_label, 3, 1)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line, 4, 0, 1, 2)
        
        # الإجمالي
        layout.addWidget(QLabel(reshape_arabic_text("الإجمالي:")), 5, 0)
        self.total_sales_label = QLabel("0 ل.س")
        self.total_sales_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 14px;")
        layout.addWidget(self.total_sales_label, 5, 1)
        
        return group

    def create_expenses_section(self):
        """إنشاء قسم المصاريف"""
        group = QGroupBox(reshape_arabic_text("📉 إجمالي المصاريف"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # مصاريف عامة
        layout.addWidget(QLabel(reshape_arabic_text("مصاريف عامة:")), 0, 0)
        self.general_expenses_label = QLabel("0 ل.س")
        self.general_expenses_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        layout.addWidget(self.general_expenses_label, 0, 1)
        
        # مصاريف تشغيلية
        layout.addWidget(QLabel(reshape_arabic_text("مصاريف تشغيلية:")), 1, 0)
        self.operational_expenses_label = QLabel("0 ل.س")
        self.operational_expenses_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        layout.addWidget(self.operational_expenses_label, 1, 1)
        
        # رواتب (مخصومة من الخزينة)
        layout.addWidget(QLabel(reshape_arabic_text("رواتب (مخصومة):")), 2, 0)
        self.salaries_label = QLabel("0 ل.س")
        self.salaries_label.setStyleSheet("color: #f39c12; font-weight: bold;")
        layout.addWidget(self.salaries_label, 2, 1)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line, 3, 0, 1, 2)
        
        # الإجمالي
        layout.addWidget(QLabel(reshape_arabic_text("الإجمالي:")), 4, 0)
        self.total_expenses_label = QLabel("0 ل.س")
        self.total_expenses_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 14px;")
        layout.addWidget(self.total_expenses_label, 4, 1)
        
        return group

    def create_final_calculations_section(self):
        """إنشاء قسم الحسابات النهائية"""
        group = QGroupBox(reshape_arabic_text("🧮 الحسابات النهائية"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # الصافي المتوقع
        layout.addWidget(QLabel(reshape_arabic_text("الصافي المتوقع:")), 0, 0)
        self.expected_net_label = QLabel("0 ل.س")
        self.expected_net_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 16px;")
        layout.addWidget(self.expected_net_label, 0, 1)
        
        # النقدية الفعلية
        layout.addWidget(QLabel(reshape_arabic_text("النقدية الفعلية:")), 1, 0)
        self.actual_cash_spin = QDoubleSpinBox()
        self.actual_cash_spin.setRange(0, 999999999)
        self.actual_cash_spin.setDecimals(0)
        self.actual_cash_spin.setSuffix(" ل.س")
        self.actual_cash_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.actual_cash_spin.valueChanged.connect(self.calculate_difference)
        layout.addWidget(self.actual_cash_spin, 1, 1)
        
        # الفرق
        layout.addWidget(QLabel(reshape_arabic_text("الفرق:")), 2, 0)
        self.difference_label = QLabel("0 ل.س")
        self.difference_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
        layout.addWidget(self.difference_label, 2, 1)
        
        # ملاحظات
        layout.addWidget(QLabel(reshape_arabic_text("ملاحظات:")), 3, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات حول إغلاق الصندوق...")
        layout.addWidget(self.notes_edit, 3, 1)
        
        return group

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر تحديث
        refresh_btn = QPushButton(reshape_arabic_text("🔄 تحديث"))
        refresh_btn.setFont(create_arabic_font(11))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_shift_data)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        
        # زر إغلاق الصندوق
        close_btn = QPushButton(reshape_arabic_text("💰 إغلاق الصندوق"))
        close_btn.setFont(create_arabic_font(11, bold=True))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        close_btn.clicked.connect(self.close_cash_box)
        layout.addWidget(close_btn)
        
        # زر إلغاء
        cancel_btn = QPushButton(reshape_arabic_text("❌ إلغاء"))
        cancel_btn.setFont(create_arabic_font(11))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        return layout

    def load_shift_data(self):
        """تحميل بيانات الشيفت"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # البحث عن الشيفت المفتوح
            current_shift = self.db_manager.fetch_one("""
                SELECT id, start_time FROM shifts
                WHERE user_id = ? AND shift_date = ? AND status = 'open'
            """, (self.current_user['id'], today))

            if not current_shift:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("لا يوجد شيفت مفتوح لإغلاقه"))
                self.reject()
                return

            self.current_shift_id = current_shift['id']
            self.start_time_label.setText(current_shift['start_time'].split(' ')[1][:8])

            # تحميل المبيعات
            self.load_sales_data()

            # تحميل المصاريف
            self.load_expenses_data()

            # حساب الصافي المتوقع
            self.calculate_expected_net()

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الشيفت: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل البيانات: {e}"))

    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        try:
            # اشتراكات جديدة
            new_subs = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE shift_id = ? AND type = 'اشتراك جديد'
            """, (self.current_shift_id,))

            # تسليم راوترات (من جدول transactions + جدول router_deliveries)
            router_deliveries_transactions = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE shift_id = ? AND type = 'تسليم راوتر'
            """, (self.current_shift_id,))

            # تسليم راوترات من الجدول الجديد (اليوم الحالي للمستخدم الحالي)
            router_deliveries_new = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(total_amount), 0) as total
                FROM router_deliveries
                WHERE DATE(created_at) = DATE('now') AND user_id = ?
            """, (self.current_user['id'],))

            # إجمالي تسليم الراوترات
            router_deliveries_total = (
                (router_deliveries_transactions['total'] if router_deliveries_transactions else 0) +
                (router_deliveries_new['total'] if router_deliveries_new else 0)
            )

            print(f"📊 تسليم راوترات - transactions: {router_deliveries_transactions['total'] if router_deliveries_transactions else 0:,} ل.س")
            print(f"📊 تسليم راوترات - router_deliveries: {router_deliveries_new['total'] if router_deliveries_new else 0:,} ل.س")
            print(f"📊 إجمالي تسليم راوترات: {router_deliveries_total:,} ل.س")

            # تجديد باقات
            renewals = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE shift_id = ? AND type = 'تجديد باقة'
            """, (self.current_shift_id,))

            # سندات قبض
            receipts = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM transactions
                WHERE shift_id = ? AND type = 'سند قبض'
            """, (self.current_shift_id,))

            # تحديث التسميات
            self.new_subscriptions_amount = new_subs['total'] if new_subs else 0
            self.router_deliveries_amount = router_deliveries_total
            self.renewals_amount = renewals['total'] if renewals else 0
            self.receipts_amount = receipts['total'] if receipts else 0

            self.new_subscriptions_label.setText(format_currency(self.new_subscriptions_amount))
            self.router_deliveries_label.setText(format_currency(self.router_deliveries_amount))
            self.package_renewals_label.setText(format_currency(self.renewals_amount))
            self.receipts_label.setText(format_currency(self.receipts_amount))

            # إجمالي المبيعات
            self.total_sales_amount = (self.new_subscriptions_amount +
                                     self.router_deliveries_amount +
                                     self.renewals_amount +
                                     self.receipts_amount)
            self.total_sales_label.setText(format_currency(self.total_sales_amount))

        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات: {e}")

    def load_expenses_data(self):
        """تحميل بيانات المصاريف"""
        try:
            # مصاريف عامة (غير الرواتب)
            general_expenses = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE shift_id = ? AND expense_type != 'رواتب'
            """, (self.current_shift_id,))

            # رواتب (مخصومة من الخزينة اليومية)
            salaries = self.db_manager.fetch_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM expenses
                WHERE shift_id = ? AND expense_type = 'رواتب'
            """, (self.current_shift_id,))

            # تحديث التسميات
            self.general_expenses_amount = general_expenses['total'] if general_expenses else 0
            self.operational_expenses_amount = 0  # يمكن تقسيم المصاريف لاحقاً
            self.salaries_amount = salaries['total'] if salaries else 0

            self.general_expenses_label.setText(format_currency(self.general_expenses_amount))
            self.operational_expenses_label.setText(format_currency(self.operational_expenses_amount))
            self.salaries_label.setText(format_currency(self.salaries_amount))

            # إجمالي المصاريف (بدون الرواتب لأنها مخصومة من الخزينة)
            self.total_expenses_amount = self.general_expenses_amount + self.operational_expenses_amount
            self.total_expenses_label.setText(format_currency(self.total_expenses_amount))

        except Exception as e:
            print(f"❌ خطأ في تحميل المصاريف: {e}")

    def calculate_expected_net(self):
        """حساب الصافي المتوقع"""
        try:
            # الصافي المتوقع = إجمالي المبيعات - إجمالي المصاريف
            self.expected_net_amount = self.total_sales_amount - self.total_expenses_amount
            self.expected_net_label.setText(format_currency(self.expected_net_amount))

            # تحديث لون الصافي
            if self.expected_net_amount > 0:
                self.expected_net_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 16px;")
            elif self.expected_net_amount < 0:
                self.expected_net_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 16px;")
            else:
                self.expected_net_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 16px;")

        except Exception as e:
            print(f"❌ خطأ في حساب الصافي المتوقع: {e}")

    def calculate_difference(self):
        """حساب الفرق بين المتوقع والفعلي"""
        try:
            actual_cash = self.actual_cash_spin.value()
            difference = actual_cash - self.expected_net_amount

            self.difference_label.setText(format_currency(difference))

            # تحديث لون الفرق
            if difference > 0:
                self.difference_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 14px;")
            elif difference < 0:
                self.difference_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
            else:
                self.difference_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 14px;")

        except Exception as e:
            print(f"❌ خطأ في حساب الفرق: {e}")

    def close_cash_box(self):
        """إغلاق الصندوق مع النظام الموحد"""
        try:
            if not self.current_shift_id:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("لا يوجد شيفت لإغلاقه"))
                return

            actual_cash = self.actual_cash_spin.value()
            notes = self.notes_edit.toPlainText().strip()

            # تأكيد الإغلاق
            reply = QMessageBox.question(
                self,
                reshape_arabic_text("تأكيد الإغلاق"),
                reshape_arabic_text(f"هل أنت متأكد من إغلاق الصندوق؟\n\nالصافي المتوقع: {format_currency(self.expected_net_amount)}\nالنقدية الفعلية: {format_currency(actual_cash)}\nالفرق: {format_currency(actual_cash - self.expected_net_amount)}"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            print("🔒 بدء عملية إغلاق الصندوق مع النظام الموحد...")

            # إغلاق الشيفت التقليدي
            success = close_shift(self.db_manager, self.current_user['id'], actual_cash, notes)

            if success:
                print("✅ تم إغلاق الشيفت التقليدي")

                # إغلاق الصندوق في النظام الموحد (نقل للخزينة اليومية + نسخ احتياطي)
                close_success = self.treasury_manager.close_cash_box_to_daily_treasury(
                    user_id=self.current_user['id'],
                    actual_cash=actual_cash,
                    notes=notes
                )

                if close_success:
                    print("✅ تم إغلاق الصندوق في النظام الموحد")
                    print("🔄 تم نقل رصيد الصندوق للخزينة اليومية")
                    print("💰 الصندوق أصبح مغلق والرصيد في الخزينة اليومية")
                    print("🔒 الشيفت مغلق - سيفتح شيفت جديد عند تسجيل الدخول التالي")

                    QMessageBox.information(self, reshape_arabic_text("نجح"),
                                          reshape_arabic_text(f"تم إغلاق الصندوق بنجاح!\n\n• تم نقل رصيد الصندوق للخزينة اليومية\n• تم إنشاء نسخة احتياطية\n• تم إغلاق الشيفت\n• سيفتح شيفت جديد عند تسجيل الدخول التالي"))
                else:
                    QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                      reshape_arabic_text("تم إغلاق الشيفت لكن فشل في إغلاق الصندوق"))

                self.cash_box_closed.emit()
                self.accept()
            else:
                QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                                   reshape_arabic_text("فشل في إغلاق الصندوق"))

        except Exception as e:
            print(f"❌ خطأ في إغلاق الصندوق: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في إغلاق الصندوق: {e}"))

    def check_cash_box_status(self):
        """التحقق من حالة الصندوق وإعادة تعيين القيم إذا لزم الأمر"""
        try:
            # التحقق من وجود جلسة مفتوحة
            is_active = self.treasury_manager.is_session_active(user_id=self.current_user['id'])

            if not is_active:
                print("ℹ️ لا توجد جلسة مفتوحة")

                # إعادة تعيين جميع القيم إلى صفر
                self.reset_all_values()

                # عرض رسالة للمستخدم
                QMessageBox.information(
                    self,
                    reshape_arabic_text("لا يوجد شيفت مفتوح"),
                    reshape_arabic_text("لا يوجد شيفت مفتوح حالياً.\n\nيرجى تسجيل الدخول لفتح شيفت جديد.")
                )

                # إغلاق النافذة
                self.reject()
            else:
                print("ℹ️ يوجد صندوق مفتوح - تحميل البيانات الحالية")

        except Exception as e:
            print(f"❌ خطأ في فحص حالة الصندوق: {e}")

    def reset_all_values(self):
        """إعادة تعيين جميع القيم إلى صفر"""
        try:
            print("🔄 إعادة تعيين جميع القيم إلى صفر...")

            # إعادة تعيين المتغيرات
            self.total_sales_amount = 0
            self.total_expenses_amount = 0
            self.expected_net_amount = 0

            print("✅ تم إعادة تعيين جميع القيم")

        except Exception as e:
            print(f"❌ خطأ في إعادة تعيين القيم: {e}")

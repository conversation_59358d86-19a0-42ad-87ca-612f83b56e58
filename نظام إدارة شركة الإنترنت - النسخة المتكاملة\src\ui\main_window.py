# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - النسخة المتكاملة
Main Window - Integrated Version

النافذة الرئيسية المحسنة مع تنظيم أفضل للقوائم والعمليات
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QAction, QStatusBar, QLabel,
                            QToolBar, QPushButton, QFrame, QGridLayout,
                            QMessageBox, QApplication, QSplitter, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QTimer, QDateTime, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor, QPalette
import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.arabic_support import (apply_arabic_style, create_arabic_font, 
                                 apply_rtl_layout, format_arabic_currency,
                                 format_arabic_number, apply_table_style)

class MainWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    # إشارات مخصصة
    user_logged_out = pyqtSignal()
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        
        # متغيرات النظام
        self.current_cash_box = None
        self.stats_data = {}
        
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # تطبيق النمط العربي
        apply_arabic_style(self, 10)
        apply_rtl_layout(self)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"نظام إدارة شركة الإنترنت - {self.current_user['full_name']}")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد المكونات الرئيسية
        self.setup_menu()
        self.setup_toolbar()
        self.setup_central_widget()
        self.setup_status_bar()
        
        # تطبيق النمط
        self.apply_main_style()
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        apply_arabic_style(menubar, 10)
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        self.add_menu_actions(file_menu, [
            ("نسخ احتياطي", self.backup_database),
            ("استعادة", self.restore_database),
            ("تصدير البيانات", self.export_data),
            ("---", None),
            ("تسجيل الخروج", self.logout),
            ("خروج", self.close)
        ])
        
        # قائمة العمليات
        operations_menu = menubar.addMenu("العمليات")
        self.add_menu_actions(operations_menu, [
            ("اشتراك جديد", self.open_new_subscription),
            ("تجديد باقة", self.open_package_renewal),
            ("تسليم راوتر", self.open_router_delivery),
            ("إغلاق الصندوق", self.open_cash_close),
            ("---", None),
            ("سند قبض", self.open_receipt_voucher),
            ("سند دفع", self.open_payment_voucher),
            ("نقل الخزينة", self.open_treasury_transfer),
            ("المشتريات", self.open_purchases)
        ])
        
        # قائمة الإدارة
        management_menu = menubar.addMenu("الإدارة")
        self.add_menu_actions(management_menu, [
            ("إدارة المستخدمين", self.open_users_management),
            ("إدارة الموردين", self.open_suppliers_management),
            ("إدارة الموزعين", self.open_distributors_management),
            ("إدارة المنتجات", self.open_products_management),
            ("إدارة الباقات", self.open_packages_management),
            ("إدارة العمال", self.open_workers_management),
            ("إدارة المشتركين", self.open_subscribers_management)
        ])
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        self.add_menu_actions(reports_menu, [
            ("تقرير المبيعات", self.open_sales_report),
            ("تقرير المصاريف", self.open_expenses_report),
            ("تقرير المخزون", self.open_inventory_report),
            ("تقرير المشتركين", self.open_subscribers_report),
            ("---", None),
            ("تقرير يومي", self.open_daily_report),
            ("تقرير شهري", self.open_monthly_report),
            ("تقرير سنوي", self.open_yearly_report)
        ])
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        self.add_menu_actions(help_menu, [
            ("دليل المستخدم", self.show_user_guide),
            ("حول النظام", self.show_about),
            ("تحديث النظام", self.check_updates)
        ])
    
    def add_menu_actions(self, menu, actions):
        """إضافة إجراءات إلى القائمة"""
        for text, callback in actions:
            if text == "---":
                menu.addSeparator()
            else:
                action = QAction(text, self)
                if callback:
                    action.triggered.connect(callback)
                menu.addAction(action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        apply_arabic_style(toolbar, 10)
        
        # أزرار شريط الأدوات للعمليات السريعة
        toolbar_actions = [
            ("اشتراك جديد", "📝", self.open_new_subscription),
            ("تسليم راوتر", "📡", self.open_router_delivery),
            ("شحن رصيد", "💰", self.open_balance_charge),
            ("إغلاق الصندوق", "🔒", self.open_cash_close),
        ]
        
        for text, icon, callback in toolbar_actions:
            action = QAction(f"{icon} {text}", self)
            action.triggered.connect(callback)
            toolbar.addAction(action)
    
    def setup_central_widget(self):
        """إعداد الويدجت المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيمن - لوحة التحكم
        self.create_dashboard_panel(splitter)
        
        # الجانب الأيسر - الإحصائيات والإشعارات
        self.create_stats_panel(splitter)
        
        # تعيين النسب
        splitter.setSizes([800, 400])
        
        main_layout.addWidget(splitter)
    
    def create_dashboard_panel(self, parent):
        """إنشاء لوحة التحكم"""
        dashboard_frame = QFrame()
        dashboard_frame.setFrameStyle(QFrame.StyledPanel)
        dashboard_layout = QVBoxLayout(dashboard_frame)
        
        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        dashboard_layout.addWidget(title_label)
        
        # أزرار العمليات الرئيسية
        self.create_main_buttons(dashboard_layout)
        
        # معلومات الصندوق الحالي
        self.create_cash_box_info(dashboard_layout)
        
        parent.addWidget(dashboard_frame)
    
    def create_main_buttons(self, layout):
        """إنشاء أزرار العمليات الرئيسية"""
        buttons_frame = QFrame()
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # تعريف الأزرار - العمليات اليومية فقط
        buttons_data = [
            ("شحن رصيد", "#16a085", self.open_balance_charge),
            ("شراء دولار", "#e74c3c", self.open_currency_exchange),
            ("المصاريف", "#e67e22", self.open_expenses),
            ("أمر صيانة", "#34495e", self.open_maintenance_order),
            ("تسليم للعمال", "#1abc9c", self.open_worker_delivery),
            ("التقارير", "#d35400", self.open_reports),
            ("الإعدادات", "#7f8c8d", self.open_settings),
        ]
        
        # إنشاء الأزرار
        row, col = 0, 0
        for text, color, callback in buttons_data:
            button = self.create_main_button(text, color, callback)
            buttons_layout.addWidget(button, row, col)
            
            col += 1
            if col >= 3:  # 3 أزرار في كل صف
                col = 0
                row += 1
        
        layout.addWidget(buttons_frame)
    
    def create_main_button(self, text, color, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton(text)
        button.setFont(create_arabic_font(11, bold=True))
        button.setMinimumSize(150, 80)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        """)
        
        button.clicked.connect(callback)
        return button
    
    def darken_color(self, color, factor=0.2):
        """تغميق اللون"""
        # تحويل اللون السادس عشري إلى RGB
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        
        # تطبيق التغميق
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        
        # تحويل إلى سادس عشري
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

# -*- coding: utf-8 -*-
"""
نافذة إدارة المنتجات
Products Management Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

class ProductsManagementWindow(QDialog):
    """نافذة إدارة المنتجات"""
    
    def __init__(self, db_manager, inventory_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = inventory_manager
        self.config_manager = config_manager
        self.current_user = current_user

        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المنتجات")
        self.setGeometry(100, 100, 900, 600)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إدارة المنتجات والمخزون")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # أدوات التحكم
        controls_layout = self.create_controls()
        
        # جدول المنتجات
        self.products_table = self.create_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addLayout(controls_layout)
        main_layout.addWidget(self.products_table)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_controls(self):
        """إنشاء أدوات التحكم"""
        layout = QHBoxLayout()
        
        # فلتر التصنيف
        category_label = QLabel("التصنيف:")
        apply_arabic_style(category_label, 10)
        
        self.category_combo = QComboBox()
        apply_arabic_style(self.category_combo, 10)
        self.category_combo.addItem("جميع التصنيفات", "")
        self.category_combo.addItem("راوتر", "راوتر")
        self.category_combo.addItem("كبل", "كبل")
        self.category_combo.addItem("موصلات", "موصلات")
        self.category_combo.addItem("أخرى", "أخرى")
        
        # البحث
        search_label = QLabel("البحث:")
        apply_arabic_style(search_label, 10)
        
        self.search_edit = QLineEdit()
        apply_arabic_style(self.search_edit, 10)
        self.search_edit.setPlaceholderText("ابحث بالاسم...")
        
        # زر البحث
        search_button = QPushButton("بحث")
        apply_arabic_style(search_button, 10)
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout.addWidget(category_label)
        layout.addWidget(self.category_combo)
        layout.addWidget(search_label)
        layout.addWidget(self.search_edit)
        layout.addWidget(search_button)
        layout.addStretch()
        
        search_button.clicked.connect(self.search_products)
        self.category_combo.currentTextChanged.connect(self.filter_by_category)
        
        return layout
        
    def create_table(self):
        """إنشاء جدول المنتجات"""
        table = QTableWidget()
        apply_arabic_style(table, 9)
        
        columns = ["الرقم", "اسم المنتج", "التصنيف", "السعر", "وحدة البيع", "الكمية المتوفرة", "الحد الأدنى"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)
        
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)
        
        table.setColumnWidth(0, 60)   # الرقم
        table.setColumnWidth(1, 200)  # اسم المنتج
        table.setColumnWidth(2, 100)  # التصنيف
        table.setColumnWidth(3, 120)  # السعر
        table.setColumnWidth(4, 100)  # وحدة البيع
        table.setColumnWidth(5, 120)  # الكمية
        table.setColumnWidth(6, 100)  # الحد الأدنى
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة منتج جديد")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        edit_button = QPushButton("تعديل المنتج")
        apply_arabic_style(edit_button, 10)
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        delete_button = QPushButton("حذف المنتج")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        inventory_button = QPushButton("إدارة المخزون")
        apply_arabic_style(inventory_button, 10)
        inventory_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(add_button)
        layout.addWidget(edit_button)
        layout.addWidget(delete_button)
        layout.addWidget(inventory_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        add_button.clicked.connect(self.add_product)
        edit_button.clicked.connect(self.edit_product)
        delete_button.clicked.connect(self.delete_product)
        inventory_button.clicked.connect(self.manage_inventory)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل بيانات المنتجات من النظام الموحد"""
        try:
            # نقل البيانات الموجودة إلى النظام الموحد
            self.inventory_manager.migrate_existing_data()

            # تحميل المنتجات من الجدول الموحد
            products = self.inventory_manager.get_all_products()
            
            self.products_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
                self.products_table.setItem(row, 1, QTableWidgetItem(product['name'] or ""))
                self.products_table.setItem(row, 2, QTableWidgetItem(product['category'] or ""))

                # عرض سعر البيع من النظام الموحد
                try:
                    sale_price = product['sale_price'] if product['sale_price'] is not None else 0
                except (KeyError, TypeError):
                    sale_price = 0
                self.products_table.setItem(row, 3, QTableWidgetItem(format_currency(sale_price)))

                # عرض وحدة البيع
                try:
                    unit_type = product['sale_unit'] if 'sale_unit' in product and product['sale_unit'] else (product['unit_type'] if product['unit_type'] else "قطعة")
                except (KeyError, TypeError):
                    unit_type = "قطعة"
                self.products_table.setItem(row, 4, QTableWidgetItem(unit_type))

                # الكمية المتوفرة مع تلوين حسب المستوى (من النظام الموحد)
                try:
                    quantity = product['current_stock'] if product['current_stock'] is not None else 0
                except (KeyError, TypeError):
                    quantity = 0

                try:
                    min_level = product['min_stock'] if product['min_stock'] is not None else 0
                except (KeyError, TypeError):
                    min_level = 0

                quantity_item = QTableWidgetItem(f"{quantity:.1f}")

                if quantity <= min_level:
                    quantity_item.setBackground(Qt.red)
                    quantity_item.setForeground(Qt.white)
                elif quantity <= min_level * 2:
                    quantity_item.setBackground(Qt.yellow)
                    quantity_item.setForeground(Qt.black)
                else:
                    quantity_item.setBackground(Qt.green)
                    quantity_item.setForeground(Qt.white)

                self.products_table.setItem(row, 5, quantity_item)
                self.products_table.setItem(row, 6, QTableWidgetItem(f"{min_level:.1f}"))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def search_products(self):
        """البحث في المنتجات"""
        search_text = self.search_edit.text().strip()
        category = self.category_combo.currentData()
        
        try:
            query = """
                SELECT p.id, p.name, p.category, p.unit_price, p.unit_type,
                       p.min_stock_level, p.stock_quantity
                FROM products p
                WHERE 1=1
            """
            params = []
            
            if search_text:
                query += " AND p.name LIKE ?"
                params.append(f"%{search_text}%")
                
            if category:
                query += " AND p.category = ?"
                params.append(category)
                
            query += " ORDER BY p.category, p.name"
            
            products = self.db_manager.fetch_all(query, params)
            
            self.products_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
                self.products_table.setItem(row, 1, QTableWidgetItem(product['name'] or ""))
                self.products_table.setItem(row, 2, QTableWidgetItem(product['category'] or ""))
                self.products_table.setItem(row, 3, QTableWidgetItem(format_currency(product['unit_price'])))
                self.products_table.setItem(row, 4, QTableWidgetItem(product['unit_type'] or ""))
                
                quantity = product['stock_quantity'] or 0
                min_level = product['min_stock_level'] or 0
                quantity_item = QTableWidgetItem(str(quantity))
                
                if quantity <= min_level:
                    quantity_item.setBackground(Qt.red)
                    quantity_item.setForeground(Qt.black)
                elif quantity <= min_level * 2:
                    quantity_item.setBackground(Qt.yellow)
                    quantity_item.setForeground(Qt.black)
                else:
                    quantity_item.setBackground(Qt.green)
                    quantity_item.setForeground(Qt.black)
                    
                self.products_table.setItem(row, 5, quantity_item)
                self.products_table.setItem(row, 6, QTableWidgetItem(str(min_level)))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {e}")
            
    def filter_by_category(self):
        """فلترة حسب التصنيف"""
        self.search_products()
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def add_product(self):
        """إضافة منتج جديد"""
        dialog = ProductDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
            QMessageBox.information(self, "تم", "تم إضافة المنتج بنجاح")
        
    def edit_product(self):
        """تعديل منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return

        # الحصول على معرف المنتج
        product_id = int(self.products_table.item(current_row, 0).text())

        dialog = ProductDialog(self.db_manager, product_id, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
            QMessageBox.information(self, "تم", "تم تعديل المنتج بنجاح")
        
    def delete_product(self):
        """حذف منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return

        # التحقق من وجود البيانات في الصف
        product_id_item = self.products_table.item(current_row, 0)
        product_name_item = self.products_table.item(current_row, 1)

        if not product_id_item or not product_name_item:
            QMessageBox.warning(self, "تحذير", "بيانات المنتج غير صحيحة")
            return

        product_id = product_id_item.text()
        product_name = product_name_item.text()
        
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف",
            f"هل تريد حذف المنتج '{product_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # التحقق من وجود مخزون للمنتج
                current_stock = self.inventory_manager.get_product_stock(int(product_id))
                if current_stock > 0:
                    QMessageBox.warning(self, "تحذير",
                                      f"لا يمكن حذف المنتج لأنه يحتوي على مخزون: {current_stock:.1f}")
                    return

                # حذف المنتج من النظام الموحد (إلغاء تفعيل بدلاً من الحذف)
                self.db_manager.execute_query("""
                    UPDATE unified_products
                    SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (product_id,))

                QMessageBox.information(self, "نجح", "تم إلغاء تفعيل المنتج بنجاح")
                self.load_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المنتج: {e}")
                
    def manage_inventory(self):
        """إدارة المخزون الموحد"""
        try:
            from ui.unified_inventory_window import UnifiedInventoryWindow
            dialog = UnifiedInventoryWindow(self.db_manager, self.current_user, self)
            dialog.inventory_updated.connect(self.load_data)
            dialog.exec_()
            # تحديث البيانات بعد إغلاق نافذة المخزون
            self.load_data()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المخزون الموحد: {e}")


class ProductDialog(QDialog):
    """حوار إضافة/تعديل منتج"""

    def __init__(self, db_manager, product_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_id = product_id
        self.is_edit_mode = product_id is not None

        # إنشاء مدير المخزون الموحد
        from utils.unified_inventory_manager import UnifiedInventoryManager
        self.inventory_manager = UnifiedInventoryManager(db_manager)

        self.setup_ui()
        if self.is_edit_mode:
            self.load_product_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل منتج" if self.is_edit_mode else "إضافة منتج جديد"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setModal(True)

        apply_arabic_style(self, 10)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الحوار
        title_label = QLabel(title)
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)

        # نموذج البيانات
        form_layout = QGridLayout()
        form_layout.setSpacing(10)

        # اسم المنتج
        name_label = QLabel("اسم المنتج:")
        apply_arabic_style(name_label, 10)
        self.name_edit = QLineEdit()
        apply_arabic_style(self.name_edit, 10)
        self.name_edit.setPlaceholderText("أدخل اسم المنتج")

        # الفئة
        category_label = QLabel("الفئة:")
        apply_arabic_style(category_label, 10)
        self.category_combo = QComboBox()
        apply_arabic_style(self.category_combo, 10)
        self.category_combo.setEditable(True)
        self.category_combo.addItems(["راوتر", "كبل", "موصلات", "أجهزة شبكة", "إكسسوارات", "أخرى"])

        # سعر الوحدة
        price_label = QLabel("سعر الوحدة:")
        apply_arabic_style(price_label, 10)
        self.price_spin = QDoubleSpinBox()
        apply_arabic_style(self.price_spin, 10)
        self.price_spin.setRange(0, 10000000)
        self.price_spin.setSuffix(" ل.س")
        self.price_spin.setDecimals(0)

        # نوع الوحدة
        unit_label = QLabel("نوع الوحدة:")
        apply_arabic_style(unit_label, 10)
        self.unit_combo = QComboBox()
        apply_arabic_style(self.unit_combo, 10)
        self.unit_combo.setEditable(True)
        self.unit_combo.addItems(["قطعة", "متر", "كيلو", "علبة", "حزمة", "لفة"])

        # الكمية الحالية (للعرض فقط في وضع التعديل)
        current_qty_label = QLabel("الكمية الحالية:")
        apply_arabic_style(current_qty_label, 10)
        self.current_qty_label = QLabel("0")
        apply_arabic_style(self.current_qty_label, 10, bold=True)
        self.current_qty_label.setStyleSheet("color: #3498db; font-weight: bold;")

        # الحد الأدنى للمخزون
        min_stock_label = QLabel("الحد الأدنى للمخزون:")
        apply_arabic_style(min_stock_label, 10)
        self.min_stock_spin = QSpinBox()
        apply_arabic_style(self.min_stock_spin, 10)
        self.min_stock_spin.setRange(0, 1000)
        self.min_stock_spin.setValue(10)

        # الوصف
        description_label = QLabel("الوصف:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QTextEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setPlaceholderText("أدخل وصف المنتج (اختياري)")
        self.description_edit.setMaximumHeight(80)

        # الحالة
        self.is_active_checkbox = QCheckBox("المنتج نشط")
        apply_arabic_style(self.is_active_checkbox, 10)
        self.is_active_checkbox.setChecked(True)

        # ترتيب الحقول
        form_layout.addWidget(name_label, 0, 0)
        form_layout.addWidget(self.name_edit, 0, 1)
        form_layout.addWidget(category_label, 1, 0)
        form_layout.addWidget(self.category_combo, 1, 1)
        form_layout.addWidget(price_label, 2, 0)
        form_layout.addWidget(self.price_spin, 2, 1)
        form_layout.addWidget(unit_label, 3, 0)
        form_layout.addWidget(self.unit_combo, 3, 1)
        form_layout.addWidget(current_qty_label, 4, 0)
        form_layout.addWidget(self.current_qty_label, 4, 1)
        form_layout.addWidget(min_stock_label, 5, 0)
        form_layout.addWidget(self.min_stock_spin, 5, 1)
        form_layout.addWidget(description_label, 6, 0)
        form_layout.addWidget(self.description_edit, 6, 1)
        form_layout.addWidget(self.is_active_checkbox, 7, 0, 1, 2)

        # إخفاء الكمية الحالية في وضع الإضافة
        if not self.is_edit_mode:
            current_qty_label.hide()
            self.current_qty_label.hide()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        layout.addWidget(title_label)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

        # ربط الأحداث
        save_button.clicked.connect(self.save_product)
        cancel_button.clicked.connect(self.reject)

    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل من النظام الموحد"""
        try:
            product = self.db_manager.fetch_one("""
                SELECT name, category, purchase_price, sale_price, unit_type,
                       current_stock, min_stock, is_active
                FROM unified_products
                WHERE id = ?
            """, (self.product_id,))

            if product:
                self.name_edit.setText(product['name'])
                self.category_combo.setCurrentText(product['category'])

                # استخدام سعر البيع كسعر افتراضي
                try:
                    sale_price = product['sale_price'] if product['sale_price'] is not None else 0
                except (KeyError, TypeError):
                    sale_price = 0
                self.price_spin.setValue(sale_price)

                self.unit_combo.setCurrentText(product['unit_type'])
                self.current_qty_label.setText(f"{product['current_stock']:.1f}")
                self.min_stock_spin.setValue(product['min_stock'])
                self.description_edit.setPlainText("")  # لا يوجد وصف في النظام الموحد حالياً
                self.is_active_checkbox.setChecked(product['is_active'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المنتج: {e}")

    def save_product(self):
        """حفظ المنتج"""
        # التحقق من البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المنتج")
            return

        if self.price_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح للمنتج")
            return

        try:
            name = self.name_edit.text().strip()
            category = self.category_combo.currentText().strip()
            unit_price = self.price_spin.value()
            unit_type = self.unit_combo.currentText().strip()
            min_stock_level = self.min_stock_spin.value()
            description = self.description_edit.toPlainText().strip() or None
            is_active = self.is_active_checkbox.isChecked()

            if self.is_edit_mode:
                # تحديث المنتج في النظام الموحد
                self.db_manager.execute_query("""
                    UPDATE unified_products
                    SET name = ?, category = ?, sale_price = ?, unit_type = ?,
                        min_stock = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (name, category, unit_price, unit_type, min_stock_level,
                      is_active, self.product_id))

                print(f"✅ تم تحديث المنتج: {name}")
            else:
                # إضافة منتج جديد في النظام الموحد
                self.db_manager.execute_query("""
                    INSERT INTO unified_products (name, category, unit_type, purchase_price,
                                                sale_price, current_stock, min_stock, is_active)
                    VALUES (?, ?, ?, ?, ?, 0, ?, 1)
                """, (name, category, unit_type, unit_price, unit_price, min_stock_level))

                print(f"✅ تم إضافة منتج جديد: {name}")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المنتج: {e}")

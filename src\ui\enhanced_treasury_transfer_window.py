#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نقل الخزينة المحسنة - دعم العملات المتعددة
Enhanced Treasury Transfer Window - Multi-Currency Support
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QMessageBox, QHeaderView,
                            QTextEdit, QFrame, QGroupBox, QDoubleSpinBox,
                            QComboBox, QSpinBox, QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class EnhancedTreasuryTransferWindow(QDialog):
    """واجهة نقل الخزينة المحسنة مع دعم العملات المتعددة"""
    
    transfer_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user

        # إنشاء مدير الخزينة الموحد
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        self.treasury_manager = UnifiedTreasuryManager(db_manager)

        self.setWindowTitle(reshape_arabic_text("نقل الخزينة - العملات المتعددة"))
        self.setModal(True)
        self.resize(800, 700)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_treasury_data()
        
        print("✅ تم إنشاء واجهة نقل الخزينة المحسنة")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("💰 نقل الأموال - دعم العملات المتعددة"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # قسم أرصدة الخزائن
        treasuries_group = self.create_treasuries_section()
        layout.addWidget(treasuries_group)
        
        # قسم تفاصيل النقل
        transfer_group = self.create_transfer_section()
        layout.addWidget(transfer_group)
        
        # قسم الملخص
        summary_group = self.create_summary_section()
        layout.addWidget(summary_group)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_treasuries_section(self):
        """إنشاء قسم أرصدة الخزائن"""
        group = QGroupBox(reshape_arabic_text("📊 أرصدة الخزائن الحالية"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # الخزينة اليومية
        daily_title = QLabel(reshape_arabic_text("الخزينة اليومية:"))
        daily_title.setFont(create_arabic_font(11, bold=True))
        daily_title.setStyleSheet("color: #e74c3c;")
        layout.addWidget(daily_title, 0, 0, 1, 2)
        
        # الليرة السورية - يومية
        layout.addWidget(QLabel(reshape_arabic_text("الليرة السورية:")), 1, 0)
        self.daily_syp_label = QLabel("0 ل.س")
        self.daily_syp_label.setFont(create_arabic_font(11, bold=True))
        self.daily_syp_label.setStyleSheet("color: #e74c3c;")
        layout.addWidget(self.daily_syp_label, 1, 1)
        
        # الدولار - يومية
        layout.addWidget(QLabel(reshape_arabic_text("الدولار الأمريكي:")), 2, 0)
        self.daily_usd_label = QLabel("$0.00")
        self.daily_usd_label.setFont(create_arabic_font(11, bold=True))
        self.daily_usd_label.setStyleSheet("color: #e74c3c;")
        layout.addWidget(self.daily_usd_label, 2, 1)
        
        # اليورو - يومية
        layout.addWidget(QLabel(reshape_arabic_text("اليورو:")), 3, 0)
        self.daily_eur_label = QLabel("€0.00")
        self.daily_eur_label.setFont(create_arabic_font(11, bold=True))
        self.daily_eur_label.setStyleSheet("color: #e74c3c;")
        layout.addWidget(self.daily_eur_label, 3, 1)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line, 4, 0, 1, 4)
        
        # الخزينة الرئيسية
        main_title = QLabel(reshape_arabic_text("الخزينة الرئيسية:"))
        main_title.setFont(create_arabic_font(11, bold=True))
        main_title.setStyleSheet("color: #27ae60;")
        layout.addWidget(main_title, 5, 0, 1, 2)
        
        # الليرة السورية - رئيسية
        layout.addWidget(QLabel(reshape_arabic_text("الليرة السورية:")), 6, 0)
        self.main_syp_label = QLabel("0 ل.س")
        self.main_syp_label.setFont(create_arabic_font(11, bold=True))
        self.main_syp_label.setStyleSheet("color: #27ae60;")
        layout.addWidget(self.main_syp_label, 6, 1)
        
        # الدولار - رئيسية
        layout.addWidget(QLabel(reshape_arabic_text("الدولار الأمريكي:")), 7, 0)
        self.main_usd_label = QLabel("$0.00")
        self.main_usd_label.setFont(create_arabic_font(11, bold=True))
        self.main_usd_label.setStyleSheet("color: #27ae60;")
        layout.addWidget(self.main_usd_label, 7, 1)
        
        # اليورو - رئيسية
        layout.addWidget(QLabel(reshape_arabic_text("اليورو:")), 8, 0)
        self.main_eur_label = QLabel("€0.00")
        self.main_eur_label.setFont(create_arabic_font(11, bold=True))
        self.main_eur_label.setStyleSheet("color: #27ae60;")
        layout.addWidget(self.main_eur_label, 8, 1)
        
        return group

    def create_transfer_section(self):
        """إنشاء قسم تفاصيل النقل"""
        group = QGroupBox(reshape_arabic_text("💸 تفاصيل عملية النقل"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # نوع العملة
        layout.addWidget(QLabel(reshape_arabic_text("نوع العملة:")), 0, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItem(reshape_arabic_text("الليرة السورية (SYP)"), "SYP")
        self.currency_combo.addItem(reshape_arabic_text("الدولار الأمريكي (USD)"), "USD")
        self.currency_combo.addItem(reshape_arabic_text("اليورو (EUR)"), "EUR")
        self.currency_combo.setFont(create_arabic_font(11))
        self.currency_combo.currentTextChanged.connect(self.on_currency_changed)
        self.currency_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.currency_combo, 0, 1)
        
        # المبلغ المنقول
        layout.addWidget(QLabel(reshape_arabic_text("المبلغ المنقول:")), 1, 0)
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" ل.س")
        self.amount_spin.setFont(create_arabic_font(11))
        self.amount_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #27ae60;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.amount_spin, 1, 1)
        
        # تاريخ النقل
        layout.addWidget(QLabel(reshape_arabic_text("تاريخ النقل:")), 2, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setFont(create_arabic_font(11))
        self.date_edit.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #9b59b6;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.date_edit, 2, 1)
        
        # المستلم
        layout.addWidget(QLabel(reshape_arabic_text("المستلم:")), 3, 0)
        self.receiver_edit = QLineEdit()
        self.receiver_edit.setPlaceholderText(reshape_arabic_text("اسم المستلم أو الجهة"))
        self.receiver_edit.setFont(create_arabic_font(11))
        self.receiver_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.receiver_edit, 3, 1)
        
        # الملاحظات
        layout.addWidget(QLabel(reshape_arabic_text("الملاحظات:")), 4, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText(reshape_arabic_text("ملاحظات حول عملية النقل..."))
        self.notes_edit.setFont(create_arabic_font(11))
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #34495e;
                border-radius: 5px;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.notes_edit, 4, 1)
        
        return group

    def create_summary_section(self):
        """إنشاء قسم الملخص"""
        group = QGroupBox(reshape_arabic_text("📋 ملخص العملية"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # المبلغ بالعملة الأصلية
        layout.addWidget(QLabel(reshape_arabic_text("المبلغ المنقول:")), 0, 0)
        self.summary_amount_label = QLabel("0 ل.س")
        self.summary_amount_label.setFont(create_arabic_font(11, bold=True))
        self.summary_amount_label.setStyleSheet("color: #2c3e50;")
        layout.addWidget(self.summary_amount_label, 0, 1)
        
        # المبلغ بالليرة السورية (إذا كانت عملة أخرى)
        layout.addWidget(QLabel(reshape_arabic_text("المكافئ بالليرة:")), 1, 0)
        self.summary_syp_equivalent_label = QLabel("0 ل.س")
        self.summary_syp_equivalent_label.setFont(create_arabic_font(11))
        self.summary_syp_equivalent_label.setStyleSheet("color: #7f8c8d;")
        layout.addWidget(self.summary_syp_equivalent_label, 1, 1)
        
        # الرصيد المتبقي
        layout.addWidget(QLabel(reshape_arabic_text("الرصيد المتبقي:")), 2, 0)
        self.summary_remaining_label = QLabel("0 ل.س")
        self.summary_remaining_label.setFont(create_arabic_font(11, bold=True))
        self.summary_remaining_label.setStyleSheet("color: #e67e22;")
        layout.addWidget(self.summary_remaining_label, 2, 1)
        
        return group

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر تنفيذ النقل
        transfer_btn = QPushButton(reshape_arabic_text("💸 تنفيذ النقل"))
        transfer_btn.setFont(create_arabic_font(12, bold=True))
        transfer_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        transfer_btn.clicked.connect(self.execute_transfer)
        layout.addWidget(transfer_btn)
        
        # زر إلغاء
        cancel_btn = QPushButton(reshape_arabic_text("❌ إلغاء"))
        cancel_btn.setFont(create_arabic_font(12))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        layout.addStretch()
        
        return layout

    def load_treasury_data(self):
        """تحميل بيانات الخزائن من النظام الموحد"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # تحميل الخزينة اليومية الإجمالية (مجموع جميع الصناديق المغلقة)
            daily_syp = self.treasury_manager.get_total_daily_balance(currency_type='SYP')
            daily_usd = self.treasury_manager.get_total_daily_balance(currency_type='USD')

            # تحميل الخزينة الرئيسية (مجموع الصناديق المغلقة)
            main_syp = self.treasury_manager.get_main_balance('SYP')
            main_usd = self.treasury_manager.get_main_balance('USD')

            # تنظيم البيانات من النظام الموحد
            self.treasury_balances = {
                'daily': {
                    'SYP': daily_syp,
                    'USD': daily_usd,
                    'EUR': 0
                },
                'main': {
                    'SYP': main_syp,
                    'USD': main_usd,
                    'EUR': 0
                },
                'exchange_rates': {
                    'SYP': 1.0,
                    'USD': 15000.0,
                    'EUR': 16000.0
                }
            }

            print(f"📊 الخزينة اليومية: SYP={daily_syp:,}, USD=${daily_usd:.2f}")
            print(f"📊 الخزينة الرئيسية: SYP={main_syp:,}, USD=${main_usd:.2f}")

            # تحديث التسميات
            self.update_balance_labels()

            # تحديث الحد الأقصى للمبلغ
            self.update_max_amount()

            print(f"📊 تم تحميل أرصدة الخزائن: {self.treasury_balances}")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الخزائن: {e}")
            import traceback
            traceback.print_exc()

    def update_balance_labels(self):
        """تحديث تسميات الأرصدة"""
        try:
            # الخزينة اليومية
            self.daily_syp_label.setText(format_currency(self.treasury_balances['daily']['SYP']))
            self.daily_usd_label.setText(f"${self.treasury_balances['daily']['USD']:,.2f}")
            self.daily_eur_label.setText(f"€{self.treasury_balances['daily']['EUR']:,.2f}")

            # الخزينة الرئيسية
            self.main_syp_label.setText(format_currency(self.treasury_balances['main']['SYP']))
            self.main_usd_label.setText(f"${self.treasury_balances['main']['USD']:,.2f}")
            self.main_eur_label.setText(f"€{self.treasury_balances['main']['EUR']:,.2f}")

        except Exception as e:
            print(f"❌ خطأ في تحديث التسميات: {e}")

    def on_currency_changed(self):
        """عند تغيير نوع العملة"""
        try:
            selected_currency = self.currency_combo.currentData()

            # تحديث وحدة المبلغ
            if selected_currency == "SYP":
                self.amount_spin.setSuffix(" ل.س")
                self.amount_spin.setDecimals(0)
            elif selected_currency == "USD":
                self.amount_spin.setSuffix(" $")
                self.amount_spin.setDecimals(2)
            elif selected_currency == "EUR":
                self.amount_spin.setSuffix(" €")
                self.amount_spin.setDecimals(2)

            # تحديث الحد الأقصى للمبلغ
            self.update_max_amount()

            # تحديث الملخص
            self.update_summary()

        except Exception as e:
            print(f"❌ خطأ في تغيير العملة: {e}")

    def update_max_amount(self):
        """تحديث الحد الأقصى للمبلغ"""
        try:
            selected_currency = self.currency_combo.currentData()

            if selected_currency == "SYP":
                max_amount = self.treasury_balances['daily']['SYP']
            else:
                max_amount = self.treasury_balances['daily'][selected_currency]

            self.amount_spin.setMaximum(max(0, max_amount))
            self.amount_spin.setValue(max(0, max_amount))

            # تحديث الملخص
            self.update_summary()

        except Exception as e:
            print(f"❌ خطأ في تحديث الحد الأقصى: {e}")

    def update_summary(self):
        """تحديث ملخص العملية"""
        try:
            selected_currency = self.currency_combo.currentData()
            amount = self.amount_spin.value()

            # المبلغ بالعملة الأصلية
            if selected_currency == "SYP":
                self.summary_amount_label.setText(format_currency(amount))
                self.summary_syp_equivalent_label.setText("--")
            elif selected_currency == "USD":
                self.summary_amount_label.setText(f"${amount:,.2f}")
                syp_equivalent = amount * self.treasury_balances['exchange_rates']['USD']
                self.summary_syp_equivalent_label.setText(format_currency(syp_equivalent))
            elif selected_currency == "EUR":
                self.summary_amount_label.setText(f"€{amount:,.2f}")
                syp_equivalent = amount * self.treasury_balances['exchange_rates']['EUR']
                self.summary_syp_equivalent_label.setText(format_currency(syp_equivalent))

            # الرصيد المتبقي
            current_balance = self.treasury_balances['daily'][selected_currency]
            remaining = current_balance - amount

            if selected_currency == "SYP":
                self.summary_remaining_label.setText(format_currency(remaining))
            elif selected_currency == "USD":
                self.summary_remaining_label.setText(f"${remaining:,.2f}")
            elif selected_currency == "EUR":
                self.summary_remaining_label.setText(f"€{remaining:,.2f}")

            # تلوين الرصيد المتبقي
            if remaining < 0:
                self.summary_remaining_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            elif remaining == 0:
                self.summary_remaining_label.setStyleSheet("color: #f39c12; font-weight: bold;")
            else:
                self.summary_remaining_label.setStyleSheet("color: #27ae60; font-weight: bold;")

        except Exception as e:
            print(f"❌ خطأ في تحديث الملخص: {e}")

    def execute_transfer(self):
        """تنفيذ عملية النقل"""
        try:
            selected_currency = self.currency_combo.currentData()
            amount = self.amount_spin.value()
            transfer_date = self.date_edit.date().toString('yyyy-MM-dd')
            receiver = self.receiver_edit.text().strip()
            notes = self.notes_edit.toPlainText().strip()

            # التحقق من صحة البيانات
            if amount <= 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال مبلغ صحيح"))
                return

            if not receiver:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال اسم المستلم"))
                return

            # التحقق من توفر الرصيد
            available_balance = self.treasury_balances['daily'][selected_currency]
            if amount > available_balance:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text(f"الرصيد المتاح غير كافي. الرصيد المتاح: {available_balance:,.2f}"))
                return

            # تأكيد العملية
            currency_names = {
                'SYP': 'الليرة السورية',
                'USD': 'الدولار الأمريكي',
                'EUR': 'اليورو'
            }

            currency_symbols = {
                'SYP': 'ل.س',
                'USD': '$',
                'EUR': '€'
            }

            confirmation_text = f"""هل أنت متأكد من نقل المبلغ التالي؟

العملة: {currency_names[selected_currency]}
المبلغ: {amount:,.2f} {currency_symbols[selected_currency]}
المستلم: {receiver}
التاريخ: {transfer_date}

الرصيد الحالي: {available_balance:,.2f} {currency_symbols[selected_currency]}
الرصيد بعد النقل: {available_balance - amount:,.2f} {currency_symbols[selected_currency]}"""

            reply = QMessageBox.question(
                self,
                reshape_arabic_text("تأكيد النقل"),
                reshape_arabic_text(confirmation_text),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تسجيل عملية النقل
            self.db_manager.execute_query("""
                INSERT INTO treasury_transfers (
                    transfer_date, currency_type, amount, exchange_rate_used,
                    amount_in_syp, receiver, notes, transferred_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                transfer_date,
                selected_currency,
                amount,
                self.treasury_balances['exchange_rates'][selected_currency],
                amount * self.treasury_balances['exchange_rates'][selected_currency],
                receiver,
                notes,
                self.current_user.get('username', 'unknown'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))

            # تحديث رصيد الخزينة باستخدام النظام الموحد
            transfer_success = self.treasury_manager.transfer_from_total_daily_to_main(
                currency_type=selected_currency,
                amount=amount,
                date=transfer_date
            )

            if not transfer_success:
                QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                                   reshape_arabic_text("فشل في تنفيذ عملية النقل"))
                return

            # تحديث الأرصدة في الواجهة فوراً
            print("🔄 تحديث الأرصدة في الواجهة...")
            self.force_refresh_interface()

            # إرسال إشارة النجاح
            transfer_data = {
                'currency': selected_currency,
                'amount': amount,
                'receiver': receiver,
                'date': transfer_date,
                'notes': notes
            }
            self.transfer_completed.emit(transfer_data)

            QMessageBox.information(
                self,
                reshape_arabic_text("نجح"),
                reshape_arabic_text(f"تم نقل {amount:,.2f} {currency_symbols[selected_currency]} بنجاح")
            )

            self.accept()

        except Exception as e:
            print(f"❌ خطأ في تنفيذ النقل: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تنفيذ النقل: {e}"))

    def force_refresh_interface(self):
        """تحديث قوي للواجهة"""
        try:
            print("🔄 تحديث قوي للواجهة...")

            # إعادة تحميل البيانات من قاعدة البيانات
            self.load_treasury_data()

            # تحديث جميع العناصر
            self.update_balance_labels()
            self.update_max_amount()
            self.update_summary()

            # إجبار إعادة رسم الواجهة
            self.repaint()
            self.update()

            # تحديث النافذة الأب إذا كانت موجودة
            if self.parent():
                self.parent().repaint()
                self.parent().update()

            print("✅ تم التحديث القوي للواجهة")

        except Exception as e:
            print(f"❌ خطأ في التحديث القوي: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير توافق النظام - ضمان التوافق بين جميع الجداول والواجهات
"""

class SystemCompatibilityManager:
    """مدير توافق النظام"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def ensure_system_compatibility(self):
        """ضمان التوافق بين جميع أجزاء النظام"""
        try:
            print("🔧 فحص وضمان التوافق بين جداول النظام...")
            
            # 1. ضمان وجود جدول categories
            self.ensure_categories_table()
            
            # 2. ضمان التوافق بين products و categories
            self.ensure_products_categories_compatibility()
            
            # 3. ضمان التوافق بين unified_products و categories
            self.ensure_unified_products_categories_compatibility()
            
            # 4. ض<PERSON><PERSON> التوافق بين النظام الموحد للخزينة
            self.ensure_treasury_compatibility()
            
            # 5. ض<PERSON>ان التوافق بين المخزون الموحد
            self.ensure_inventory_compatibility()

            # 6. نقل الصناديق المغلقة للنظام الموحد
            self.migrate_closed_cash_boxes()

            print("✅ تم ضمان التوافق بين جميع أجزاء النظام")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في ضمان التوافق: {e}")
            return False
    
    def ensure_categories_table(self):
        """ضمان وجود جدول التصنيفات"""
        try:
            # إنشاء جدول categories إذا لم يكن موجود
            self.db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            # إضافة التصنيفات الأساسية
            basic_categories = [
                ('راوتر', 'أجهزة راوتر وأجهزة الشبكة'),
                ('كبل', 'كبلات الشبكة والكهرباء'),
                ('إكسسوارات', 'إكسسوارات وقطع غيار'),
                ('أدوات', 'أدوات التركيب والصيانة'),
                ('مواد استهلاكية', 'مواد قابلة للاستهلاك')
            ]
            
            for name, description in basic_categories:
                try:
                    self.db_manager.execute_query("""
                        INSERT OR IGNORE INTO categories (name, description, created_by)
                        VALUES (?, ?, ?)
                    """, (name, description, 'system'))
                except:
                    pass
            
            print("✅ تم ضمان وجود جدول التصنيفات")
            
        except Exception as e:
            print(f"❌ خطأ في ضمان جدول التصنيفات: {e}")
    
    def ensure_products_categories_compatibility(self):
        """ضمان التوافق بين جدول products وجدول categories"""
        try:
            # الحصول على التصنيفات الموجودة في products
            existing_categories = self.db_manager.fetch_all("""
                SELECT DISTINCT category FROM products 
                WHERE category IS NOT NULL AND category != ''
            """)
            
            # إضافة التصنيفات المفقودة إلى جدول categories
            for category_row in existing_categories:
                category_name = category_row['category']
                if category_name:
                    try:
                        self.db_manager.execute_query("""
                            INSERT OR IGNORE INTO categories (name, description, created_by)
                            VALUES (?, ?, ?)
                        """, (category_name, f"تصنيف {category_name}", 'system_sync'))
                    except:
                        pass
            
            print(f"✅ تم ضمان التوافق بين products و categories ({len(existing_categories)} تصنيف)")
            
        except Exception as e:
            print(f"❌ خطأ في ضمان التوافق بين products و categories: {e}")
    
    def ensure_unified_products_categories_compatibility(self):
        """ضمان التوافق بين unified_products وجدول categories"""
        try:
            # فحص وجود جدول unified_products
            tables = self.db_manager.fetch_all("""
                SELECT name FROM sqlite_master WHERE type='table' AND name='unified_products'
            """)
            
            if not tables:
                print("⚠️ جدول unified_products غير موجود - سيتم إنشاؤه عند الحاجة")
                return
            
            # الحصول على التصنيفات الموجودة في unified_products
            existing_categories = self.db_manager.fetch_all("""
                SELECT DISTINCT category FROM unified_products 
                WHERE category IS NOT NULL AND category != ''
            """)
            
            # إضافة التصنيفات المفقودة إلى جدول categories
            for category_row in existing_categories:
                category_name = category_row['category']
                if category_name:
                    try:
                        self.db_manager.execute_query("""
                            INSERT OR IGNORE INTO categories (name, description, created_by)
                            VALUES (?, ?, ?)
                        """, (category_name, f"تصنيف {category_name}", 'unified_sync'))
                    except:
                        pass
            
            print(f"✅ تم ضمان التوافق بين unified_products و categories ({len(existing_categories)} تصنيف)")
            
        except Exception as e:
            print(f"❌ خطأ في ضمان التوافق بين unified_products و categories: {e}")
    
    def ensure_treasury_compatibility(self):
        """ضمان التوافق في النظام الموحد للخزينة"""
        try:
            # فحص وجود جدول unified_treasury
            tables = self.db_manager.fetch_all("""
                SELECT name FROM sqlite_master WHERE type='table' AND name='unified_treasury'
            """)
            
            if tables:
                print("✅ جدول unified_treasury موجود - النظام الموحد للخزينة يعمل")
            else:
                print("⚠️ جدول unified_treasury غير موجود - سيتم إنشاؤه عند الحاجة")
            
            # فحص الجداول القديمة
            old_tables = ['daily_treasury', 'main_treasury', 'treasury_transfers']
            for table in old_tables:
                check = self.db_manager.fetch_all(f"""
                    SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'
                """)
                if check:
                    print(f"⚠️ جدول قديم موجود: {table} - قد يسبب تضارب")
            
        except Exception as e:
            print(f"❌ خطأ في فحص توافق الخزينة: {e}")
    
    def ensure_inventory_compatibility(self):
        """ضمان التوافق في نظام المخزون الموحد"""
        try:
            # فحص وجود جداول المخزون الموحد
            unified_tables = ['unified_products', 'unified_inventory_movements', 'worker_inventory']
            
            for table in unified_tables:
                check = self.db_manager.fetch_all(f"""
                    SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'
                """)
                if check:
                    print(f"✅ جدول المخزون الموحد موجود: {table}")
                else:
                    print(f"⚠️ جدول المخزون الموحد غير موجود: {table} - سيتم إنشاؤه عند الحاجة")
            
            # فحص الجداول القديمة
            old_tables = ['inventory', 'stock_movements']
            for table in old_tables:
                check = self.db_manager.fetch_all(f"""
                    SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'
                """)
                if check:
                    print(f"⚠️ جدول مخزون قديم موجود: {table} - قد يسبب تضارب")
            
        except Exception as e:
            print(f"❌ خطأ في فحص توافق المخزون: {e}")
    
    def sync_categories_across_system(self):
        """مزامنة التصنيفات عبر النظام"""
        try:
            print("🔄 مزامنة التصنيفات عبر النظام...")
            
            # جمع جميع التصنيفات من جميع المصادر
            all_categories = set()
            
            # من جدول categories
            try:
                categories = self.db_manager.fetch_all("SELECT name FROM categories WHERE is_active = 1")
                for cat in categories:
                    all_categories.add(cat['name'])
            except:
                pass
            
            # من جدول products
            try:
                categories = self.db_manager.fetch_all("SELECT DISTINCT category FROM products WHERE category IS NOT NULL")
                for cat in categories:
                    if cat['category']:
                        all_categories.add(cat['category'])
            except:
                pass
            
            # من جدول unified_products
            try:
                categories = self.db_manager.fetch_all("SELECT DISTINCT category FROM unified_products WHERE category IS NOT NULL")
                for cat in categories:
                    if cat['category']:
                        all_categories.add(cat['category'])
            except:
                pass
            
            # إضافة جميع التصنيفات إلى جدول categories
            for category_name in all_categories:
                try:
                    self.db_manager.execute_query("""
                        INSERT OR IGNORE INTO categories (name, description, created_by)
                        VALUES (?, ?, ?)
                    """, (category_name, f"تصنيف {category_name}", 'system_sync'))
                except:
                    pass
            
            print(f"✅ تم مزامنة {len(all_categories)} تصنيف عبر النظام")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في مزامنة التصنيفات: {e}")
            return False
    
    def migrate_closed_cash_boxes(self):
        """نقل الصناديق المغلقة للنظام الموحد"""
        try:
            print("🔄 فحص ونقل الصناديق المغلقة للنظام الموحد...")

            # فحص الصناديق المغلقة
            closed_boxes = self.db_manager.fetch_all("""
                SELECT id, user_id, net_amount, shift_date, closed_at
                FROM cash_boxes
                WHERE is_closed = 1 AND net_amount > 0
                ORDER BY closed_at
            """)

            if not closed_boxes:
                print("⚠️ لا توجد صناديق مغلقة لنقلها")
                return True

            # إنشاء مدير الخزينة الموحد
            from utils.unified_treasury_manager import UnifiedTreasuryManager
            treasury_manager = UnifiedTreasuryManager(self.db_manager)

            total_migrated = 0
            successful_migrations = 0

            for box in closed_boxes:
                try:
                    user_id = box['user_id']
                    net_amount = box.get('net_amount', 0) or 0
                    shift_date = box.get('shift_date')

                    # استخدام تاريخ الإغلاق إذا لم يكن هناك تاريخ شيفت
                    if not shift_date and box.get('closed_at'):
                        shift_date = box['closed_at'].split()[0]
                    elif not shift_date:
                        from datetime import datetime
                        shift_date = datetime.now().strftime('%Y-%m-%d')

                    if net_amount > 0:
                        # فحص إذا كان المبلغ منقول مسبقاً
                        existing = self.db_manager.fetch_one("""
                            SELECT id FROM transactions
                            WHERE type = 'نقل صندوق مغلق للنظام الموحد'
                            AND description LIKE ?
                        """, (f"%صندوق {box['id']}%",))

                        if not existing:
                            # إضافة المبلغ للخزينة اليومية
                            success = treasury_manager.add_to_daily_treasury(
                                user_id=user_id,
                                currency_type='SYP',
                                amount=net_amount,
                                date=shift_date
                            )

                            if success:
                                total_migrated += net_amount
                                successful_migrations += 1

                                # تسجيل العملية
                                self.db_manager.execute_query("""
                                    INSERT INTO transactions (type, description, amount, user_name, created_at)
                                    VALUES (?, ?, ?, ?, ?)
                                """, (
                                    "نقل صندوق مغلق للنظام الموحد",
                                    f"نقل صندوق {box['id']} للنظام الموحد - صافي: {net_amount:,} ل.س",
                                    net_amount,
                                    f"user_{user_id}",
                                    shift_date + " 23:59:59"
                                ))
                        else:
                            print(f"⚠️ صندوق {box['id']} منقول مسبقاً")

                except Exception as box_error:
                    print(f"❌ خطأ في نقل صندوق {box['id']}: {box_error}")

            if successful_migrations > 0:
                print(f"✅ تم نقل {successful_migrations} صندوق بمبلغ {total_migrated:,} ل.س للنظام الموحد")
            else:
                print("⚠️ لا توجد صناديق جديدة لنقلها")

            return True

        except Exception as e:
            print(f"❌ خطأ في نقل الصناديق المغلقة: {e}")
            return False

    def generate_compatibility_report(self):
        """إنشاء تقرير التوافق"""
        try:
            print("\n📊 تقرير التوافق بين أجزاء النظام:")
            print("=" * 50)

            # فحص الجداول الأساسية
            essential_tables = [
                'categories', 'products', 'unified_products',
                'unified_treasury', 'unified_inventory_movements'
            ]

            for table in essential_tables:
                try:
                    check = self.db_manager.fetch_all(f"""
                        SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'
                    """)
                    if check:
                        count = self.db_manager.fetch_one(f"SELECT COUNT(*) as count FROM {table}")
                        print(f"✅ {table}: موجود ({count['count']} سجل)")
                    else:
                        print(f"❌ {table}: غير موجود")
                except Exception as e:
                    print(f"⚠️ {table}: خطأ في الفحص - {e}")

            # فحص التوافق بين التصنيفات
            try:
                categories_count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM categories")
                products_categories = self.db_manager.fetch_all("SELECT DISTINCT category FROM products WHERE category IS NOT NULL")

                print(f"\n📋 التصنيفات:")
                print(f"  • جدول categories: {categories_count['count']} تصنيف")
                print(f"  • تصنيفات في products: {len(products_categories)} تصنيف")

            except Exception as e:
                print(f"⚠️ خطأ في فحص التصنيفات: {e}")

            # فحص الصناديق المغلقة والخزينة
            try:
                closed_boxes = self.db_manager.fetch_all("SELECT COUNT(*) as count, SUM(net_amount) as total FROM cash_boxes WHERE is_closed = 1 AND net_amount > 0")

                from utils.unified_treasury_manager import UnifiedTreasuryManager
                treasury_manager = UnifiedTreasuryManager(self.db_manager)
                daily_balance = treasury_manager.get_daily_balance(1, 'SYP')

                print(f"\n💰 الخزينة:")
                if closed_boxes and closed_boxes[0]['count']:
                    print(f"  • صناديق مغلقة: {closed_boxes[0]['count']} صندوق بمبلغ {closed_boxes[0]['total']:,} ل.س")
                print(f"  • الخزينة اليومية: {daily_balance:,} ل.س")

                if closed_boxes and closed_boxes[0]['total'] and daily_balance == 0:
                    print("  ⚠️ يوجد صناديق مغلقة لكن الخزينة اليومية فارغة - يحتاج نقل")

            except Exception as e:
                print(f"⚠️ خطأ في فحص الخزينة: {e}")

            print("=" * 50)

        except Exception as e:
            print(f"❌ خطأ في إنشاء تقرير التوافق: {e}")

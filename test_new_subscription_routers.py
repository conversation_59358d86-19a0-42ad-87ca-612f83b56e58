#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحميل الراوترات في واجهة اشتراك جديد
"""

import sys
import os
sys.path.append('src')

def test_routers_in_subscription():
    """اختبار تحميل الراوترات من النظام الموحد في واجهة اشتراك جديد"""
    
    print("🧪 اختبار تحميل الراوترات في واجهة اشتراك جديد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.config_manager import ConfigManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        config_manager = ConfigManager()
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء المكونات الأساسية")
        
        # التحقق من وجود راوترات في النظام الموحد
        routers = inventory_manager.get_products_by_category('راوتر')
        print(f"📊 عدد الراوترات في النظام الموحد: {len(routers)}")
        
        if len(routers) == 0:
            print("⚠️ لا توجد راوترات - سأضيف راوترات تجريبية")
            
            # إضافة راوترات تجريبية
            test_routers = [
                ("راوتر TP-Link AC1200", "راوتر", "كرتونة", "قطعة", 10.0, 500000, 75000, 20, 5),
                ("راوتر D-Link DIR-615", "راوتر", "كرتونة", "قطعة", 12.0, 400000, 60000, 15, 3),
                ("راوتر Tenda AC6", "راوتر", "كرتونة", "قطعة", 8.0, 300000, 45000, 25, 5)
            ]
            
            for name, category, purchase_unit, sale_unit, conversion_factor, purchase_price, sale_price, stock, min_stock in test_routers:
                result = db.execute_query("""
                    INSERT OR IGNORE INTO unified_products 
                    (name, category, purchase_unit, sale_unit, conversion_factor,
                     purchase_price, sale_price, current_stock, min_stock, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (name, category, purchase_unit, sale_unit, conversion_factor,
                      purchase_price, sale_price, stock, min_stock, 1))
                
                if result:
                    print(f"✅ تم إضافة راوتر: {name}")
            
            # إعادة تحميل الراوترات
            routers = inventory_manager.get_products_by_category('راوتر')
            print(f"📊 عدد الراوترات بعد الإضافة: {len(routers)}")
        
        # عرض الراوترات المتوفرة
        print("\n📋 الراوترات المتوفرة:")
        for router in routers:
            price = router.get('sale_price', router.get('unit_price', 0))
            stock = router.get('current_stock', 0)
            print(f"  • {router['name']} - سعر: {price:,} ل.س - مخزون: {stock:.0f}")
        
        # اختبار إنشاء واجهة اشتراك جديد
        print("\n🧪 اختبار إنشاء واجهة اشتراك جديد...")
        
        try:
            from ui.new_subscription_window import NewSubscriptionWindow
            from PyQt5.QtWidgets import QApplication
            
            # إنشاء تطبيق مؤقت للاختبار
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            # إنشاء الواجهة مع inventory_manager
            window = NewSubscriptionWindow(db, inventory_manager, config_manager, current_user)
            
            print("✅ تم إنشاء واجهة اشتراك جديد بنجاح")
            
            # اختبار تحميل الراوترات في الواجهة
            if hasattr(window, 'router_combo') and window.router_combo is not None:
                combo_count = window.router_combo.count()
                print(f"📊 عدد الراوترات في القائمة المنسدلة: {combo_count - 1}")  # -1 للعنصر الأول
                
                # عرض الراوترات في القائمة
                print("\n📋 الراوترات في القائمة المنسدلة:")
                for i in range(1, combo_count):  # تخطي العنصر الأول "اختر نوع الراوتر"
                    item_text = window.router_combo.itemText(i)
                    item_data = window.router_combo.itemData(i)
                    print(f"  • {item_text}")
                    
                    if item_data:
                        print(f"    ID: {item_data['id']}, السعر: {item_data['unit_price']:,} ل.س, المخزون: {item_data.get('current_stock', 0):.0f}")
                
                if combo_count > 1:
                    print("✅ تم تحميل الراوترات في القائمة المنسدلة بنجاح")
                    return True
                else:
                    print("❌ لم يتم تحميل أي راوترات في القائمة المنسدلة")
                    return False
            else:
                print("❌ قائمة الراوترات غير موجودة في الواجهة")
                return False
                
        except Exception as ui_error:
            print(f"❌ خطأ في إنشاء واجهة اشتراك جديد: {ui_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الراوترات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_router_selection():
    """اختبار اختيار راوتر وعرض السعر"""
    
    print("\n🧪 اختبار اختيار راوتر وعرض السعر...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        from utils.config_manager import ConfigManager
        from ui.new_subscription_window import NewSubscriptionWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء المكونات
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        config_manager = ConfigManager()
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = NewSubscriptionWindow(db, inventory_manager, config_manager, current_user)
        
        # اختبار اختيار راوتر
        if window.router_combo.count() > 1:
            # اختيار أول راوتر متوفر
            window.router_combo.setCurrentIndex(1)
            
            # التحقق من تحديث السعر
            selected_router = window.router_combo.currentData()
            if selected_router:
                expected_price = selected_router['unit_price']
                displayed_price = window.router_price_label.text()
                
                print(f"✅ تم اختيار راوتر: {selected_router['name']}")
                print(f"✅ السعر المتوقع: {expected_price:,} ل.س")
                print(f"✅ السعر المعروض: {displayed_price}")
                
                # التحقق من أن السعر يحتوي على القيمة الصحيحة
                if str(int(expected_price)) in displayed_price.replace(',', ''):
                    print("✅ السعر يعرض بشكل صحيح")
                    return True
                else:
                    print("❌ السعر لا يعرض بشكل صحيح")
                    return False
            else:
                print("❌ لم يتم اختيار راوتر")
                return False
        else:
            print("❌ لا توجد راوترات للاختيار")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار الراوتر: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار تحميل الراوترات في واجهة اشتراك جديد")
    print("=" * 60)
    
    # اختبار تحميل الراوترات
    routers_test = test_routers_in_subscription()
    
    # اختبار اختيار الراوتر
    selection_test = test_router_selection()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • تحميل الراوترات: {'✅ نجح' if routers_test else '❌ فشل'}")
    print(f"  • اختيار الراوتر: {'✅ نجح' if selection_test else '❌ فشل'}")
    
    if all([routers_test, selection_test]):
        print("\n🎉 واجهة اشتراك جديد تحمل الراوترات من النظام الموحد بنجاح!")
        
        print("\n📋 المميزات المحققة:")
        print("  ✅ تحميل الراوترات من جدول unified_products")
        print("  ✅ فلترة الراوترات حسب التصنيف 'راوتر'")
        print("  ✅ عرض المخزون المتوفر لكل راوتر")
        print("  ✅ عرض السعر من sale_price")
        print("  ✅ اختيار الراوتر وعرض السعر تلقائياً")
        print("  ✅ التكامل مع إدارة المنتجات")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. أضف راوترات في 'إدارة المنتجات':")
        print("     - الفئة: راوتر")
        print("     - حدد السعر والمخزون")
        print("  3. اضغط 'اشتراك جديد' من القائمة الرئيسية")
        print("  4. ستظهر الراوترات المتوفرة مع المخزون")
        print("  5. اختر راوتر وسيظهر السعر تلقائياً")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقطة البداية الرئيسية للتطبيق
"""

import sys
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont

# إضافة مسار المشروع لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.app_config import AppConfig
from database.database_manager import DatabaseManager
from ui.login_window import LoginWindow
from utils.window_utils import center_window

class ISPManagerApp:
    """التطبيق الرئيسي لإدارة شركات الإنترنت"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        
        # إنشاء التطبيق
        self.app = QApplication(sys.argv)
        self.app.setApplicationName(AppConfig.APP_NAME)
        self.app.setApplicationVersion(AppConfig.APP_VERSION)
        self.app.setOrganizationName(AppConfig.APP_AUTHOR)
        
        # إعداد الترميز
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        
        # متغيرات التطبيق
        self.db_manager = None
        self.splash_screen = None
        self.login_window = None
        self.main_window = None
        
        # إعداد السجلات
        self.setup_logging()
        
        # إنشاء المجلدات المطلوبة
        AppConfig.create_directories()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("بدء تشغيل التطبيق")
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        
        try:
            # إنشاء مجلد السجلات
            os.makedirs(AppConfig.LOGS_DIR, exist_ok=True)
            
            # إعداد السجل
            log_filename = f"app_{datetime.now().strftime('%Y%m%d')}.log"
            log_path = AppConfig.get_logs_path(log_filename)
            
            logging.basicConfig(
                level=getattr(logging, AppConfig.LOGGING_CONFIG['level']),
                format=AppConfig.LOGGING_CONFIG['format'],
                handlers=[
                    logging.FileHandler(log_path, encoding='utf-8'),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            
        except Exception as e:
            print(f"خطأ في إعداد السجلات: {e}")
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        
        if not AppConfig.UI_CONFIG['show_splash']:
            return
        
        try:
            # إنشاء شاشة البداية
            splash_pixmap = self.create_splash_pixmap()
            self.splash_screen = QSplashScreen(splash_pixmap)
            self.splash_screen.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
            
            # عرض الشاشة
            self.splash_screen.show()
            self.app.processEvents()
            
            # رسائل التحميل
            loading_messages = [
                "جاري تحميل التطبيق...",
                "جاري إعداد قاعدة البيانات...",
                "جاري تحميل الواجهات...",
                "جاري الانتهاء من التحميل..."
            ]
            
            for i, message in enumerate(loading_messages):
                self.splash_screen.showMessage(
                    message, 
                    Qt.AlignBottom | Qt.AlignCenter, 
                    QColor(255, 255, 255)
                )
                self.app.processEvents()
                
                # محاكاة وقت التحميل
                QTimer.singleShot(500 * (i + 1), lambda: None)
                self.app.processEvents()
            
            self.logger.info("تم عرض شاشة البداية")
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض شاشة البداية: {e}")
    
    def create_splash_pixmap(self) -> QPixmap:
        """إنشاء صورة شاشة البداية"""
        
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(52, 152, 219))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم الخلفية المتدرجة
        painter.fillRect(pixmap.rect(), QColor(52, 152, 219))
        
        # رسم اللوغو
        logo_size = 80
        logo_x = (pixmap.width() - logo_size) // 2
        logo_y = 50
        
        # دائرة اللوغو
        painter.setBrush(QColor(255, 255, 255, 200))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(logo_x, logo_y, logo_size, logo_size)
        
        # رمز الشبكة
        painter.setBrush(QColor(52, 152, 219))
        center_x = logo_x + logo_size // 2
        center_y = logo_y + logo_size // 2
        
        # نقاط الشبكة
        points = [
            (center_x - 20, center_y - 20),
            (center_x + 20, center_y - 20),
            (center_x, center_y),
            (center_x - 20, center_y + 20),
            (center_x + 20, center_y + 20)
        ]
        
        for x, y in points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)
        
        # خطوط الاتصال
        painter.setPen(QColor(52, 152, 219, 150))
        for i in range(len(points)):
            for j in range(i + 1, len(points)):
                painter.drawLine(points[i][0], points[i][1], points[j][0], points[j][1])
        
        # النصوص
        painter.setPen(QColor(255, 255, 255))
        
        # اسم التطبيق
        title_font = QFont("Arial", 16, QFont.Bold)
        painter.setFont(title_font)
        title_rect = painter.fontMetrics().boundingRect(AppConfig.APP_NAME)
        title_x = (pixmap.width() - title_rect.width()) // 2
        painter.drawText(title_x, 180, AppConfig.APP_NAME)
        
        # الوصف
        desc_font = QFont("Arial", 10)
        painter.setFont(desc_font)
        desc_rect = painter.fontMetrics().boundingRect(AppConfig.APP_DESCRIPTION)
        desc_x = (pixmap.width() - desc_rect.width()) // 2
        painter.drawText(desc_x, 200, AppConfig.APP_DESCRIPTION)
        
        # الإصدار
        version_text = f"الإصدار {AppConfig.APP_VERSION}"
        version_font = QFont("Arial", 8)
        painter.setFont(version_font)
        version_rect = painter.fontMetrics().boundingRect(version_text)
        version_x = (pixmap.width() - version_rect.width()) // 2
        painter.drawText(version_x, 220, version_text)
        
        painter.end()
        
        return pixmap
    
    def initialize_database(self) -> bool:
        """تهيئة قاعدة البيانات"""
        
        try:
            self.logger.info("جاري تهيئة قاعدة البيانات...")
            
            db_path = AppConfig.get_database_path()
            self.db_manager = DatabaseManager(db_path)
            
            self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            
            QMessageBox.critical(
                None, "خطأ في قاعدة البيانات",
                f"فشل في تهيئة قاعدة البيانات:\n{e}\n\n"
                "يرجى التأكد من صلاحيات الكتابة في مجلد التطبيق"
            )
            
            return False
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        
        try:
            self.logger.info("عرض نافذة تسجيل الدخول")
            
            self.login_window = LoginWindow(self.db_manager)
            
            # إخفاء شاشة البداية
            if self.splash_screen:
                self.splash_screen.close()
            
            # عرض نافذة تسجيل الدخول
            result = self.login_window.exec_()
            
            if result != QSplashScreen.Accepted:
                self.logger.info("تم إلغاء تسجيل الدخول")
                self.quit()
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض نافذة تسجيل الدخول: {e}")
            QMessageBox.critical(None, "خطأ", f"خطأ في عرض نافذة تسجيل الدخول: {e}")
            self.quit()
    
    def run(self) -> int:
        """تشغيل التطبيق"""
        
        try:
            self.logger.info("بدء تشغيل التطبيق")
            
            # عرض شاشة البداية
            self.show_splash_screen()
            
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return 1
            
            # عرض نافذة تسجيل الدخول
            self.show_login_window()
            
            # تشغيل حلقة الأحداث
            return self.app.exec_()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            
            QMessageBox.critical(
                None, "خطأ في التطبيق",
                f"حدث خطأ غير متوقع:\n{e}\n\n"
                "يرجى إعادة تشغيل التطبيق"
            )
            
            return 1
        
        finally:
            self.cleanup()
    
    def quit(self):
        """إنهاء التطبيق"""
        
        self.logger.info("إنهاء التطبيق")
        self.app.quit()
    
    def cleanup(self):
        """تنظيف الموارد"""
        
        try:
            # إغلاق قاعدة البيانات
            if self.db_manager:
                self.db_manager.close()
            
            # إغلاق شاشة البداية
            if self.splash_screen:
                self.splash_screen.close()
            
            self.logger.info("تم تنظيف الموارد")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {e}")

def main():
    """الدالة الرئيسية"""
    
    try:
        # إنشاء وتشغيل التطبيق
        app = ISPManagerApp()
        exit_code = app.run()
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
        
    except Exception as e:
        print(f"خطأ فادح في التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

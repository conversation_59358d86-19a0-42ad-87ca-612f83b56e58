#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل بسيط لنظام إدارة شركة الإنترنت
Simple Internet Company Management System Launcher
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "src"))

# تجاهل تحذيرات Qt
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت")
    print("=" * 50)
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox, QWidget, <PERSON><PERSON><PERSON><PERSON>ayout, <PERSON><PERSON><PERSON><PERSON>, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # استيراد الوحدات الأساسية
        from database.database_manager import DatabaseManager
        from utils.config_manager import ConfigManager
        from utils.arabic_support import setup_arabic_support
        
        print("✅ تم تحميل الوحدات الأساسية")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        setup_arabic_support(app)
        
        # إنشاء قاعدة البيانات
        data_dir = current_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة بسيطة
        window = QWidget()
        window.setWindowTitle("نظام إدارة شركة الإنترنت")
        window.setGeometry(300, 300, 600, 400)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("🎉 نظام إدارة شركة الإنترنت جاهز!")
        title.setFont(QFont("Tahoma", 16))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #27ae60; font-weight: bold; padding: 20px;")
        
        # معلومات
        info = QLabel("""
✅ النظام يعمل بشكل صحيح!

📋 بيانات الدخول:
• اسم المستخدم: admin
• كلمة المرور: admin123

🔧 الواجهات المتاحة:
• اشتراك جديد
• تسليم راوتر
• تجديد باقة
• إغلاق الصندوق
• إدارة المشتركين
• إدارة المنتجات
• الإعدادات

📊 قاعدة البيانات:
• المستخدمون: جاهز
• المنتجات: جاهز
• الباقات: جاهز
        """)
        info.setFont(QFont("Tahoma", 10))
        info.setAlignment(Qt.AlignRight)
        info.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px;")
        
        # زر تسجيل الدخول
        login_button = QPushButton("🔐 تسجيل الدخول")
        login_button.setFont(QFont("Tahoma", 12))
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        def show_login():
            """عرض نافذة تسجيل الدخول"""
            try:
                window.hide()
                
                # استيراد نافذة تسجيل الدخول
                from ui.login_window import LoginWindow
                
                login_window = LoginWindow(db_manager)
                
                if login_window.exec_() == login_window.Accepted:
                    current_user = login_window.get_current_user()
                    
                    # استيراد النافذة الرئيسية
                    from ui.main_window import MainWindow
                    
                    main_window = MainWindow(db_manager, config_manager, current_user)
                    main_window.show()
                    
                    print(f"✅ مرحباً {current_user['full_name']}")
                else:
                    window.show()
                    
            except Exception as e:
                print(f"❌ خطأ في تسجيل الدخول: {e}")
                QMessageBox.critical(None, "خطأ", f"خطأ في تسجيل الدخول: {str(e)}")
                window.show()
        
        login_button.clicked.connect(show_login)
        
        # زر اختبار قاعدة البيانات
        test_button = QPushButton("🧪 اختبار قاعدة البيانات")
        test_button.setFont(QFont("Tahoma", 10))
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        def test_database():
            """اختبار قاعدة البيانات"""
            try:
                users = db_manager.fetch_all("SELECT * FROM users")
                products = db_manager.fetch_all("SELECT * FROM products")
                packages = db_manager.fetch_all("SELECT * FROM packages")
                
                QMessageBox.information(None, "نتائج الاختبار", 
                    f"✅ قاعدة البيانات تعمل بشكل صحيح!\n\n"
                    f"المستخدمون: {len(users)}\n"
                    f"المنتجات: {len(products)}\n"
                    f"الباقات: {len(packages)}")
            except Exception as e:
                QMessageBox.critical(None, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
        
        test_button.clicked.connect(test_database)
        
        layout.addWidget(title)
        layout.addWidget(info)
        layout.addWidget(login_button)
        layout.addWidget(test_button)
        
        window.setLayout(layout)
        window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        print("📋 اضغط على 'تسجيل الدخول' للبدء")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

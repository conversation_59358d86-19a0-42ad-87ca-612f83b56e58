#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عرض المشتركين
"""

import sys
import os
sys.path.append('src')

def check_subscribers_table():
    """فحص جدول المشتركين"""
    
    print("🔍 فحص جدول المشتركين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص بنية جدول المشتركين
        columns = db.fetch_all("PRAGMA table_info(subscribers)")
        
        print("\n📋 أعمدة جدول subscribers:")
        for col in columns:
            print(f"  • {col['name']} ({col['type']}) - {'NOT NULL' if col['notnull'] else 'NULL'}")
        
        # عدد المشتركين الإجمالي
        total_count = db.fetch_one("SELECT COUNT(*) as count FROM subscribers")
        print(f"\n📊 إجمالي المشتركين: {total_count['count'] if total_count else 0}")
        
        # المشتركين حسب حالة التسليم
        if 'delivered' in [col['name'] for col in columns]:
            delivered_count = db.fetch_one("SELECT COUNT(*) as count FROM subscribers WHERE delivered = 1")
            undelivered_count = db.fetch_one("SELECT COUNT(*) as count FROM subscribers WHERE delivered = 0")
            
            print(f"📦 المشتركين المسلمين: {delivered_count['count'] if delivered_count else 0}")
            print(f"📋 المشتركين غير المسلمين: {undelivered_count['count'] if undelivered_count else 0}")
            
            # عرض المشتركين غير المسلمين
            undelivered = db.fetch_all("""
                SELECT id, name, phone, address, delivered 
                FROM subscribers 
                WHERE delivered = 0 OR delivered IS NULL
                ORDER BY name
                LIMIT 10
            """)
            
            print(f"\n📋 أول 10 مشتركين غير مسلمين:")
            for sub in undelivered:
                print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']} - حالة التسليم: {sub['delivered']}")
        
        else:
            print("⚠️ عمود 'delivered' غير موجود في جدول المشتركين")
            
            # عرض جميع المشتركين
            all_subscribers = db.fetch_all("""
                SELECT id, name, phone, address 
                FROM subscribers 
                ORDER BY name
                LIMIT 10
            """)
            
            print(f"\n📋 أول 10 مشتركين:")
            for sub in all_subscribers:
                print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول المشتركين: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_router_deliveries_relation():
    """فحص العلاقة بين المشتركين وتسليم الراوترات"""
    
    print("\n🔍 فحص العلاقة بين المشتركين وتسليم الراوترات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص جدول router_deliveries
        deliveries_count = db.fetch_one("SELECT COUNT(*) as count FROM router_deliveries")
        print(f"📦 عدد تسليمات الراوترات: {deliveries_count['count'] if deliveries_count else 0}")
        
        # فحص التسليمات الحديثة
        recent_deliveries = db.fetch_all("""
            SELECT id, subscriber_name, router_name, total_amount, created_at
            FROM router_deliveries 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        print(f"\n📋 آخر 5 تسليمات:")
        for delivery in recent_deliveries:
            print(f"  • {delivery['subscriber_name']} - {delivery['router_name']} - {delivery['total_amount']:,} ل.س")
        
        # فحص المشتركين الذين لم يستلموا (الطريقة القديمة)
        print(f"\n🔍 فحص المشتركين غير المسلمين (الطريقة القديمة):")
        
        try:
            undelivered_old = db.fetch_all("""
                SELECT s.id, s.name, s.phone, s.delivered
                FROM subscribers s
                LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
                WHERE rd.id IS NULL AND (s.delivered = 0 OR s.delivered IS NULL)
                ORDER BY s.name
                LIMIT 10
            """)
            
            print(f"📋 المشتركين غير المسلمين (ربط بـ subscriber_id): {len(undelivered_old)}")
            for sub in undelivered_old:
                print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
                
        except Exception as old_error:
            print(f"❌ خطأ في الطريقة القديمة: {old_error}")
        
        # فحص المشتركين الذين لم يستلموا (الطريقة الجديدة)
        print(f"\n🔍 فحص المشتركين غير المسلمين (الطريقة الجديدة):")
        
        try:
            undelivered_new = db.fetch_all("""
                SELECT s.id, s.name, s.phone, s.delivered
                FROM subscribers s
                LEFT JOIN router_deliveries rd ON s.name = rd.subscriber_name
                WHERE rd.id IS NULL AND (s.delivered = 0 OR s.delivered IS NULL)
                ORDER BY s.name
                LIMIT 10
            """)
            
            print(f"📋 المشتركين غير المسلمين (ربط بـ subscriber_name): {len(undelivered_new)}")
            for sub in undelivered_new:
                print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
                
        except Exception as new_error:
            print(f"❌ خطأ في الطريقة الجديدة: {new_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص العلاقة: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_subscriber_delivery_relation():
    """إصلاح العلاقة بين المشتركين وتسليم الراوترات"""
    
    print("\n🔧 إصلاح العلاقة بين المشتركين وتسليم الراوترات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # 1. إضافة عمود delivered إذا لم يكن موجود
        print("1️⃣ التحقق من عمود delivered...")
        
        columns = db.fetch_all("PRAGMA table_info(subscribers)")
        has_delivered = any(col['name'] == 'delivered' for col in columns)
        
        if not has_delivered:
            print("➕ إضافة عمود delivered...")
            db.execute_query("ALTER TABLE subscribers ADD COLUMN delivered INTEGER DEFAULT 0")
            print("✅ تم إضافة عمود delivered")
        else:
            print("✅ عمود delivered موجود")
        
        # 2. تحديث حالة التسليم للمشتركين الذين استلموا
        print("\n2️⃣ تحديث حالة التسليم...")
        
        # تحديث المشتركين الذين لديهم تسليمات
        updated = db.execute_query("""
            UPDATE subscribers 
            SET delivered = 1 
            WHERE name IN (
                SELECT DISTINCT subscriber_name 
                FROM router_deliveries
            )
        """)
        
        if updated:
            print("✅ تم تحديث حالة التسليم للمشتركين")
        
        # 3. التأكد من أن المشتركين الجدد لديهم delivered = 0
        db.execute_query("""
            UPDATE subscribers 
            SET delivered = 0 
            WHERE delivered IS NULL
        """)
        
        print("✅ تم تحديث المشتركين الجدد")
        
        # 4. إضافة عمود subscriber_id في router_deliveries إذا لم يكن موجود
        print("\n3️⃣ التحقق من عمود subscriber_id في router_deliveries...")
        
        rd_columns = db.fetch_all("PRAGMA table_info(router_deliveries)")
        has_subscriber_id = any(col['name'] == 'subscriber_id' for col in rd_columns)
        
        if not has_subscriber_id:
            print("➕ إضافة عمود subscriber_id...")
            db.execute_query("ALTER TABLE router_deliveries ADD COLUMN subscriber_id INTEGER")
            print("✅ تم إضافة عمود subscriber_id")
            
            # تحديث subscriber_id للتسليمات الموجودة
            print("🔄 تحديث subscriber_id للتسليمات الموجودة...")
            db.execute_query("""
                UPDATE router_deliveries 
                SET subscriber_id = (
                    SELECT s.id 
                    FROM subscribers s 
                    WHERE s.name = router_deliveries.subscriber_name
                )
                WHERE subscriber_id IS NULL
            """)
            print("✅ تم تحديث subscriber_id")
        else:
            print("✅ عمود subscriber_id موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح العلاقة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_relation():
    """اختبار العلاقة المصلحة"""
    
    print("\n🧪 اختبار العلاقة المصلحة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # اختبار الاستعلام المحدث
        undelivered = db.fetch_all("""
            SELECT s.id, s.name, s.phone, s.delivered
            FROM subscribers s
            LEFT JOIN router_deliveries rd ON s.id = rd.subscriber_id
            WHERE rd.id IS NULL AND s.delivered = 0
            ORDER BY s.name
            LIMIT 10
        """)
        
        print(f"📋 المشتركين غير المسلمين (بعد الإصلاح): {len(undelivered)}")
        for sub in undelivered:
            print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
        
        # اختبار الاستعلام البديل
        undelivered_alt = db.fetch_all("""
            SELECT s.id, s.name, s.phone, s.delivered
            FROM subscribers s
            WHERE s.delivered = 0
            ORDER BY s.name
            LIMIT 10
        """)
        
        print(f"\n📋 المشتركين غير المسلمين (استعلام بديل): {len(undelivered_alt)}")
        for sub in undelivered_alt:
            print(f"  • {sub['name']} - ID: {sub['id']} - هاتف: {sub['phone']}")
        
        return len(undelivered) > 0 or len(undelivered_alt) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العلاقة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص وإصلاح مشكلة عرض المشتركين")
    print("=" * 60)
    
    # فحص جدول المشتركين
    subscribers_ok = check_subscribers_table()
    
    # فحص العلاقة مع تسليم الراوترات
    relation_check = check_router_deliveries_relation()
    
    # إصلاح العلاقة
    fix_ok = fix_subscriber_delivery_relation()
    
    # اختبار العلاقة المصلحة
    test_ok = test_fixed_relation()
    
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص والإصلاح:")
    print(f"  • فحص جدول المشتركين: {'✅ سليم' if subscribers_ok else '❌ مشكلة'}")
    print(f"  • فحص العلاقة: {'✅ تم' if relation_check else '❌ فشل'}")
    print(f"  • إصلاح العلاقة: {'✅ تم' if fix_ok else '❌ فشل'}")
    print(f"  • اختبار العلاقة: {'✅ يعمل' if test_ok else '❌ لا يعمل'}")
    
    if all([subscribers_ok, fix_ok, test_ok]):
        print("\n🎉 تم إصلاح مشكلة عرض المشتركين!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إضافة عمود delivered في جدول subscribers")
        print("  ✅ إضافة عمود subscriber_id في جدول router_deliveries")
        print("  ✅ تحديث حالة التسليم للمشتركين")
        print("  ✅ ربط صحيح بين الجدولين")
        
        print("\n🚀 الآن:")
        print("  • المشتركين الذين لم يستلموا سيظهرون في واجهة تسليم الراوتر")
        print("  • الربط يعمل بـ subscriber_id و subscriber_name")
        print("  • حالة التسليم تتحدث تلقائياً")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

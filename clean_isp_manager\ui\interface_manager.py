#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الواجهات القابل للتوسع
"""

import os
import importlib
import logging
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import QWidget, QDialog
from config.app_config import AppConfig

class InterfaceManager:
    """مدير الواجهات القابل للتوسع"""
    
    def __init__(self):
        """تهيئة مدير الواجهات"""
        self.logger = logging.getLogger(__name__)
        self.registered_interfaces = {}
        self.loaded_modules = {}
        
        # تحميل الواجهات المتاحة
        self.load_available_interfaces()
    
    def load_available_interfaces(self):
        """تحميل الواجهات المتاحة من الإعدادات"""
        try:
            interfaces = AppConfig.AVAILABLE_INTERFACES
            
            for category, category_info in interfaces.items():
                self.registered_interfaces[category] = {
                    'info': category_info,
                    'modules': {}
                }
                
                # تحميل وحدات كل فئة
                for module in category_info['modules']:
                    module_name = module['name']
                    class_name = module['class']
                    
                    self.registered_interfaces[category]['modules'][module_name] = {
                        'class_name': class_name,
                        'loaded': False,
                        'instance': None
                    }
            
            self.logger.info(f"تم تحميل {len(self.registered_interfaces)} فئة واجهة")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الواجهات المتاحة: {e}")
    
    def get_interface_categories(self) -> Dict[str, Dict]:
        """الحصول على فئات الواجهات"""
        return {
            category: info['info'] 
            for category, info in self.registered_interfaces.items()
        }
    
    def get_category_modules(self, category: str) -> List[Dict]:
        """الحصول على وحدات فئة معينة"""
        if category not in self.registered_interfaces:
            return []
        
        modules = []
        for module_name, module_info in self.registered_interfaces[category]['modules'].items():
            modules.append({
                'name': module_name,
                'class_name': module_info['class_name'],
                'loaded': module_info['loaded']
            })
        
        return modules
    
    def load_interface_module(self, category: str, module_name: str, *args, **kwargs) -> Optional[QWidget]:
        """تحميل وحدة واجهة معينة"""
        try:
            if category not in self.registered_interfaces:
                self.logger.error(f"فئة الواجهة غير موجودة: {category}")
                return None
            
            if module_name not in self.registered_interfaces[category]['modules']:
                self.logger.error(f"وحدة الواجهة غير موجودة: {module_name}")
                return None
            
            module_info = self.registered_interfaces[category]['modules'][module_name]
            class_name = module_info['class_name']
            
            # محاولة تحميل الوحدة
            interface_instance = self._load_interface_class(category, class_name, *args, **kwargs)
            
            if interface_instance:
                module_info['loaded'] = True
                module_info['instance'] = interface_instance
                self.logger.info(f"تم تحميل واجهة: {module_name}")
                return interface_instance
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل واجهة {module_name}: {e}")
            return None
    
    def _load_interface_class(self, category: str, class_name: str, *args, **kwargs) -> Optional[QWidget]:
        """تحميل فئة الواجهة"""
        try:
            # قائمة المسارات المحتملة للوحدة
            possible_paths = [
                f"ui.{category}.{self._convert_class_to_module_name(class_name)}",
                f"ui.{category}.{class_name.lower()}",
                f"ui.{category}.{class_name}"
            ]

            module = None
            module_path = None

            # محاولة تحميل الوحدة من المسارات المختلفة
            for path in possible_paths:
                try:
                    if path not in self.loaded_modules:
                        module = importlib.import_module(path)
                        self.loaded_modules[path] = module
                        module_path = path
                        break
                    else:
                        module = self.loaded_modules[path]
                        module_path = path
                        break
                except ImportError:
                    continue

            if not module:
                raise ImportError(f"لم يتم العثور على وحدة {class_name} في أي من المسارات المحتملة")

            # الحصول على الفئة
            interface_class = getattr(module, class_name)

            # إنشاء مثيل من الفئة
            instance = interface_class(*args, **kwargs)

            self.logger.info(f"تم تحميل الواجهة {class_name} من {module_path}")
            return instance

        except ImportError as e:
            self.logger.warning(f"لم يتم العثور على وحدة {class_name}: {e}")
            # إنشاء واجهة افتراضية
            return self._create_placeholder_interface(class_name, *args, **kwargs)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل فئة {class_name}: {e}")
            return None

    def _convert_class_to_module_name(self, class_name: str) -> str:
        """تحويل اسم الفئة إلى اسم الوحدة"""
        # تحويل من CamelCase إلى snake_case
        import re
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', class_name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    def _create_placeholder_interface(self, class_name: str, *args, **kwargs) -> QDialog:
        """إنشاء واجهة مؤقتة للوحدات غير المتوفرة"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QMessageBox, QTextEdit
        from PyQt5.QtCore import Qt

        class PlaceholderInterface(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle(f"واجهة {class_name}")
                self.setModal(True)
                self.resize(500, 400)

                layout = QVBoxLayout()
                layout.setSpacing(15)
                layout.setContentsMargins(20, 20, 20, 20)

                # أيقونة تحذير
                warning_label = QLabel("⚠️")
                warning_label.setAlignment(Qt.AlignCenter)
                warning_label.setStyleSheet("font-size: 48px; margin: 10px;")
                layout.addWidget(warning_label)

                # رسالة توضيحية
                title_label = QLabel(f"واجهة {class_name} غير متوفرة")
                title_label.setAlignment(Qt.AlignCenter)
                title_label.setStyleSheet("""
                    QLabel {
                        font-size: 18px;
                        font-weight: bold;
                        color: #e74c3c;
                        margin: 10px;
                    }
                """)
                layout.addWidget(title_label)

                # تفاصيل المشكلة
                details_text = QTextEdit()
                details_text.setReadOnly(True)
                details_text.setMaximumHeight(150)
                details_text.setPlainText(f"""
هذه الواجهة ({class_name}) لم يتم تطويرها بعد أو يوجد خطأ في تحميلها.

الحلول المقترحة:
1. تأكد من وجود ملف الواجهة في المجلد الصحيح
2. تحقق من اسم الفئة والملف
3. راجع ملفات السجلات للحصول على تفاصيل أكثر
4. اتصل بفريق الدعم التقني

مسارات البحث المحتملة:
• ui/{class_name.lower()}.py
• ui/*/{class_name.lower()}.py
                """.strip())
                details_text.setStyleSheet("""
                    QTextEdit {
                        background-color: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                        padding: 10px;
                        font-family: 'Courier New', monospace;
                        font-size: 11px;
                    }
                """)
                layout.addWidget(details_text)

                # أزرار
                buttons_layout = QVBoxLayout()

                # زر إعادة المحاولة
                retry_btn = QPushButton("🔄 إعادة المحاولة")
                retry_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        font-weight: bold;
                        padding: 10px;
                        border-radius: 5px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                retry_btn.clicked.connect(self.retry_loading)
                buttons_layout.addWidget(retry_btn)

                # زر الإغلاق
                close_btn = QPushButton("❌ إغلاق")
                close_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #95a5a6;
                        color: white;
                        font-weight: bold;
                        padding: 10px;
                        border-radius: 5px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #7f8c8d;
                    }
                """)
                close_btn.clicked.connect(self.close)
                buttons_layout.addWidget(close_btn)

                layout.addLayout(buttons_layout)

                self.setLayout(layout)

                # تطبيق تنسيق عام
                self.setStyleSheet("""
                    QDialog {
                        background-color: white;
                        border-radius: 10px;
                    }
                """)

            def retry_loading(self):
                """إعادة محاولة تحميل الواجهة"""
                QMessageBox.information(self, "إعادة المحاولة",
                    "سيتم إعادة تحميل الواجهة في الإصدار القادم.\n"
                    "يرجى التحقق من التحديثات.")

        return PlaceholderInterface(*args, **kwargs)
    
    def register_custom_interface(self, category: str, module_name: str, class_name: str, 
                                 icon: str = None, color: str = None) -> bool:
        """تسجيل واجهة مخصصة جديدة"""
        try:
            if category not in self.registered_interfaces:
                # إنشاء فئة جديدة
                self.registered_interfaces[category] = {
                    'info': {
                        'name': category,
                        'icon': icon or 'default.png',
                        'color': color or '#95a5a6',
                        'modules': []
                    },
                    'modules': {}
                }
            
            # إضافة الوحدة الجديدة
            self.registered_interfaces[category]['modules'][module_name] = {
                'class_name': class_name,
                'loaded': False,
                'instance': None
            }
            
            # تحديث قائمة الوحدات في المعلومات
            self.registered_interfaces[category]['info']['modules'].append({
                'name': module_name,
                'class': class_name
            })
            
            self.logger.info(f"تم تسجيل واجهة مخصصة: {module_name} في فئة {category}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الواجهة المخصصة: {e}")
            return False
    
    def unregister_interface(self, category: str, module_name: str) -> bool:
        """إلغاء تسجيل واجهة"""
        try:
            if category in self.registered_interfaces:
                if module_name in self.registered_interfaces[category]['modules']:
                    del self.registered_interfaces[category]['modules'][module_name]
                    
                    # تحديث قائمة الوحدات في المعلومات
                    modules = self.registered_interfaces[category]['info']['modules']
                    self.registered_interfaces[category]['info']['modules'] = [
                        m for m in modules if m['name'] != module_name
                    ]
                    
                    self.logger.info(f"تم إلغاء تسجيل واجهة: {module_name}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في إلغاء تسجيل الواجهة: {e}")
            return False
    
    def get_interface_info(self, category: str, module_name: str) -> Optional[Dict]:
        """الحصول على معلومات واجهة معينة"""
        if category not in self.registered_interfaces:
            return None
        
        if module_name not in self.registered_interfaces[category]['modules']:
            return None
        
        module_info = self.registered_interfaces[category]['modules'][module_name]
        category_info = self.registered_interfaces[category]['info']
        
        return {
            'category': category,
            'category_name': category_info['name'],
            'category_icon': category_info['icon'],
            'category_color': category_info['color'],
            'module_name': module_name,
            'class_name': module_info['class_name'],
            'loaded': module_info['loaded']
        }
    
    def reload_interfaces(self):
        """إعادة تحميل جميع الواجهات"""
        try:
            # مسح الوحدات المحملة
            self.loaded_modules.clear()
            
            # إعادة تعيين حالة التحميل
            for category in self.registered_interfaces:
                for module_name in self.registered_interfaces[category]['modules']:
                    self.registered_interfaces[category]['modules'][module_name]['loaded'] = False
                    self.registered_interfaces[category]['modules'][module_name]['instance'] = None
            
            self.logger.info("تم إعادة تحميل جميع الواجهات")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعادة تحميل الواجهات: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الواجهات"""
        total_categories = len(self.registered_interfaces)
        total_modules = sum(
            len(info['modules']) 
            for info in self.registered_interfaces.values()
        )
        loaded_modules = sum(
            sum(1 for module in info['modules'].values() if module['loaded'])
            for info in self.registered_interfaces.values()
        )
        
        return {
            'total_categories': total_categories,
            'total_modules': total_modules,
            'loaded_modules': loaded_modules,
            'load_percentage': (loaded_modules / total_modules * 100) if total_modules > 0 else 0
        }

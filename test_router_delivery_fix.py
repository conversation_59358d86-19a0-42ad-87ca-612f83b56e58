#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات واجهة تسليم الراوتر
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from database.database_manager import DatabaseManager

# إنشاء config manager مبسط
class SimpleConfigManager:
    def get(self, section, default=None):
        return default or {}

def test_router_delivery_window():
    """اختبار واجهة تسليم الراوتر"""
    print("=== اختبار واجهة تسليم الراوتر ===")
    
    app = QApplication(sys.argv)
    
    # إعداد قاعدة البيانات والإعدادات
    db_manager = DatabaseManager()
    db_manager.connect()
    
    config_manager = SimpleConfigManager()
    
    current_user = {
        'id': 1,
        'username': 'admin',
        'full_name': 'المدير العام'
    }
    
    try:
        # استيراد واجهة تسليم الراوتر
        from ui.router_delivery_window import RouterDeliveryWindow
        
        print("✅ تم استيراد واجهة تسليم الراوتر بنجاح")
        
        # إنشاء النافذة
        window = RouterDeliveryWindow(db_manager, config_manager, current_user)
        print("✅ تم إنشاء نافذة تسليم الراوتر بنجاح")
        
        # اختبار تحميل المشتركين
        print("\n--- اختبار تحميل المشتركين ---")
        window.load_undelivered_subscribers()
        
        if hasattr(window, 'undelivered_subscribers'):
            print(f"✅ تم تحميل {len(window.undelivered_subscribers)} مشترك")
            for i, sub in enumerate(window.undelivered_subscribers[:3]):  # أول 3 مشتركين
                print(f"  {i+1}. {sub.get('name', 'غير محدد')} - ID: {sub.get('id', 'غير محدد')}")
        else:
            print("⚠️ لم يتم تحميل المشتركين")
        
        # اختبار عدد العناصر في القائمة المنسدلة
        combo_count = window.subscriber_combo.count()
        print(f"عدد العناصر في القائمة المنسدلة: {combo_count}")
        
        # اختبار اختيار مشترك (محاكاة)
        print("\n--- اختبار اختيار مشترك ---")
        if combo_count > 1:  # أكثر من العنصر الافتراضي
            try:
                # اختيار المشترك الثاني (الفهرس 1)
                window.subscriber_combo.setCurrentIndex(1)
                selected_data = window.subscriber_combo.currentData()
                
                if selected_data:
                    print(f"✅ تم اختيار المشترك: {selected_data.get('name', 'غير محدد')}")
                    
                    # اختبار تعيين البيانات
                    window.set_subscriber_data(selected_data)
                    print("✅ تم تعيين بيانات المشترك بنجاح")
                    
                    # اختبار تحديث الإجمالي
                    window.update_total()
                    print("✅ تم تحديث الإجمالي بنجاح")
                    
                else:
                    print("⚠️ لا توجد بيانات للمشترك المختار")
                    
            except Exception as e:
                print(f"❌ خطأ في اختبار اختيار المشترك: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("⚠️ لا توجد مشتركين للاختبار")
        
        # اختبار البحث
        print("\n--- اختبار البحث ---")
        try:
            # محاكاة كتابة في حقل البحث
            window.subscriber_combo.lineEdit().setText("محمد")
            window.search_subscribers()
            print("✅ تم اختبار البحث بنجاح")
        except Exception as e:
            print(f"❌ خطأ في اختبار البحث: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n=== انتهاء الاختبار ===")
        print("✅ جميع الاختبارات تمت بنجاح!")
        
        # عرض النافذة للاختبار اليدوي
        window.show()
        print("\n🎯 النافذة مفتوحة الآن للاختبار اليدوي")
        print("جرب اختيار مشترك من القائمة المنسدلة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة تسليم الراوتر: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_router_delivery_window())

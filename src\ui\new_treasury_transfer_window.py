#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نقل الخزينة الجديدة - تعمل مع نظام الشيفتات
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, 
                            QTableWidgetItem, QMessageBox, QHeaderView,
                            QTextEdit, QFrame, QGroupBox, QDoubleSpinBox,
                            QComboBox, QDateEdit, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class NewTreasuryTransferWindow(QDialog):
    """واجهة نقل الخزينة الجديدة"""
    
    transfer_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user = current_user
        
        self.setWindowTitle(reshape_arabic_text("نقل الخزينة"))
        self.setModal(True)
        self.resize(1000, 700)
        
        # تطبيق الخط العربي
        font = create_arabic_font(11)
        self.setFont(font)
        
        self.setup_ui()
        self.load_data()
        
        print("✅ تم إنشاء واجهة نقل الخزينة الجديدة")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel(reshape_arabic_text("💰 نقل الخزينة اليومية"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الخزينة اليومية
        treasury_info_group = self.create_treasury_info_section()
        layout.addWidget(treasury_info_group)
        
        # جدول الشيفتات المغلقة
        shifts_group = self.create_shifts_table_section()
        layout.addWidget(shifts_group)
        
        # معلومات النقل
        transfer_group = self.create_transfer_section()
        layout.addWidget(transfer_group)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)

    def create_treasury_info_section(self):
        """إنشاء قسم معلومات الخزينة"""
        group = QGroupBox(reshape_arabic_text("📊 معلومات الخزينة اليومية"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # التاريخ
        layout.addWidget(QLabel(reshape_arabic_text("التاريخ:")), 0, 0)
        self.date_label = QLabel(datetime.now().strftime('%Y-%m-%d'))
        self.date_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(self.date_label, 0, 1)
        
        # إجمالي المبيعات
        layout.addWidget(QLabel(reshape_arabic_text("إجمالي المبيعات:")), 0, 2)
        self.total_sales_label = QLabel("0 ل.س")
        self.total_sales_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 14px;")
        layout.addWidget(self.total_sales_label, 0, 3)
        
        # إجمالي المصاريف
        layout.addWidget(QLabel(reshape_arabic_text("إجمالي المصاريف:")), 1, 0)
        self.total_expenses_label = QLabel("0 ل.س")
        self.total_expenses_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
        layout.addWidget(self.total_expenses_label, 1, 1)
        
        # الصافي
        layout.addWidget(QLabel(reshape_arabic_text("الصافي:")), 1, 2)
        self.net_amount_label = QLabel("0 ل.س")
        self.net_amount_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 16px;")
        layout.addWidget(self.net_amount_label, 1, 3)
        
        # عدد الشيفتات
        layout.addWidget(QLabel(reshape_arabic_text("عدد الشيفتات المغلقة:")), 2, 0)
        self.shifts_count_label = QLabel("0")
        self.shifts_count_label.setStyleSheet("font-weight: bold; color: #3498db; font-size: 14px;")
        layout.addWidget(self.shifts_count_label, 2, 1)
        
        # عدد المستخدمين
        layout.addWidget(QLabel(reshape_arabic_text("عدد المستخدمين:")), 2, 2)
        self.users_count_label = QLabel("0")
        self.users_count_label.setStyleSheet("font-weight: bold; color: #9b59b6; font-size: 14px;")
        layout.addWidget(self.users_count_label, 2, 3)
        
        return group

    def create_shifts_table_section(self):
        """إنشاء قسم جدول الشيفتات"""
        group = QGroupBox(reshape_arabic_text("📋 تفصيل الشيفتات المغلقة"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QVBoxLayout(group)
        
        # جدول الشيفتات
        self.shifts_table = QTableWidget()
        self.shifts_table.setFont(create_arabic_font(10))
        self.shifts_table.setAlternatingRowColors(True)
        self.shifts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.shifts_table.setMaximumHeight(200)
        self.shifts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # تحديد الأعمدة
        headers = ["المستخدم", "وقت البداية", "وقت الإغلاق", "المبيعات", "المصاريف", "الصافي", "النقدية الفعلية", "الفرق"]
        self.shifts_table.setColumnCount(len(headers))
        self.shifts_table.setHorizontalHeaderLabels([reshape_arabic_text(h) for h in headers])
        
        layout.addWidget(self.shifts_table)
        
        return group

    def create_transfer_section(self):
        """إنشاء قسم معلومات النقل"""
        group = QGroupBox(reshape_arabic_text("📤 معلومات النقل"))
        group.setFont(create_arabic_font(12, bold=True))
        layout = QGridLayout(group)
        
        # المبلغ المنقول
        layout.addWidget(QLabel(reshape_arabic_text("المبلغ المنقول:")), 0, 0)
        self.transfer_amount_spin = QDoubleSpinBox()
        self.transfer_amount_spin.setRange(0, 999999999)
        self.transfer_amount_spin.setDecimals(0)
        self.transfer_amount_spin.setSuffix(" ل.س")
        self.transfer_amount_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.transfer_amount_spin, 0, 1)
        
        # نوع النقل
        layout.addWidget(QLabel(reshape_arabic_text("نوع النقل:")), 0, 2)
        self.transfer_type_combo = QComboBox()
        self.transfer_type_combo.addItems([
            reshape_arabic_text("نقل للخزينة الرئيسية"),
            reshape_arabic_text("إيداع بنكي"),
            reshape_arabic_text("نقل لفرع آخر"),
            reshape_arabic_text("أخرى")
        ])
        self.transfer_type_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #27ae60;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.transfer_type_combo, 0, 3)
        
        # تاريخ النقل
        layout.addWidget(QLabel(reshape_arabic_text("تاريخ النقل:")), 1, 0)
        self.transfer_date_edit = QDateEdit()
        self.transfer_date_edit.setDate(QDate.currentDate())
        self.transfer_date_edit.setCalendarPopup(True)
        self.transfer_date_edit.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #f39c12;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.transfer_date_edit, 1, 1)
        
        # المستلم
        layout.addWidget(QLabel(reshape_arabic_text("المستلم:")), 1, 2)
        self.receiver_edit = QLineEdit()
        self.receiver_edit.setPlaceholderText(reshape_arabic_text("اسم المستلم أو الجهة"))
        self.receiver_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #9b59b6;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.receiver_edit, 1, 3)
        
        # ملاحظات
        layout.addWidget(QLabel(reshape_arabic_text("ملاحظات:")), 2, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText(reshape_arabic_text("أدخل أي ملاحظات حول عملية النقل..."))
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #34495e;
                border-radius: 5px;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.notes_edit, 2, 1, 1, 3)
        
        return group

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر تحديث
        refresh_btn = QPushButton(reshape_arabic_text("🔄 تحديث"))
        refresh_btn.setFont(create_arabic_font(11))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_data)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        
        # زر تنفيذ النقل
        transfer_btn = QPushButton(reshape_arabic_text("💰 تنفيذ النقل"))
        transfer_btn.setFont(create_arabic_font(11, bold=True))
        transfer_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        transfer_btn.clicked.connect(self.execute_transfer)
        layout.addWidget(transfer_btn)
        
        # زر إلغاء
        cancel_btn = QPushButton(reshape_arabic_text("❌ إلغاء"))
        cancel_btn.setFont(create_arabic_font(11))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        return layout

    def load_data(self):
        """تحميل البيانات من الشيفتات المغلقة"""
        try:
            self.load_daily_treasury()
            self.load_closed_shifts()
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تحميل البيانات: {e}"))

    def load_daily_treasury(self):
        """تحميل معلومات الخزينة اليومية من النظام الموحد"""
        try:
            print("🔄 تحميل بيانات الخزينة من النظام الموحد...")

            # استخدام النظام الموحد للحصول على الأرصدة
            daily_syp_balance = self.treasury_manager.get_daily_balance(self.current_user['id'], 'SYP')
            daily_usd_balance = self.treasury_manager.get_daily_balance(self.current_user['id'], 'USD')
            daily_eur_balance = self.treasury_manager.get_daily_balance(self.current_user['id'], 'EUR')

            main_syp_balance = self.treasury_manager.get_main_balance('SYP')
            main_usd_balance = self.treasury_manager.get_main_balance('USD')
            main_eur_balance = self.treasury_manager.get_main_balance('EUR')

            print(f"💰 الخزينة اليومية - ليرة: {daily_syp_balance:,} ل.س، دولار: ${daily_usd_balance:.2f}، يورو: €{daily_eur_balance:.2f}")
            print(f"💰 الخزينة الرئيسية - ليرة: {main_syp_balance:,} ل.س، دولار: ${main_usd_balance:.2f}، يورو: €{main_eur_balance:.2f}")

            # إنشاء بيانات موحدة للعرض
            treasury_data = {
                'total_sales': daily_syp_balance,
                'total_expenses': 0,  # المصاريف منفصلة
                'shifts_count': 1,
                'users_count': 1
            }

            # إنشاء بيانات العملات
            currencies_data = [
                {'currency_type': 'SYP', 'balance': daily_syp_balance},
                {'currency_type': 'USD', 'balance': daily_usd_balance},
                {'currency_type': 'EUR', 'balance': daily_eur_balance}
            ]

            # معلومات الليرة السورية
            syp_sales = treasury_data['total_sales'] if treasury_data else 0
            syp_expenses = treasury_data['total_expenses'] if treasury_data else 0
            syp_net = syp_sales - syp_expenses
            shifts_count = treasury_data['shifts_count'] if treasury_data else 0
            users_count = treasury_data['users_count'] if treasury_data else 0

            # تحديث التسميات الأساسية
            self.total_sales_label.setText(format_currency(syp_sales))
            self.total_expenses_label.setText(format_currency(syp_expenses))
            self.shifts_count_label.setText(str(shifts_count))
            self.users_count_label.setText(str(users_count))

            # إنشاء نص شامل للصافي يتضمن جميع العملات
            net_text = f"{format_currency(syp_net)}"

            # إضافة العملات الأخرى
            other_currencies = []
            for currency in currencies_data:
                if currency['currency_type'] != 'SYP' and currency['balance'] > 0:
                    if currency['currency_type'] == 'USD':
                        other_currencies.append(f"${currency['balance']:,.2f}")
                    elif currency['currency_type'] == 'EUR':
                        other_currencies.append(f"€{currency['balance']:,.2f}")
                    else:
                        other_currencies.append(f"{currency['balance']:,.2f} {currency['currency_type']}")

            if other_currencies:
                net_text += f"\n+ {' + '.join(other_currencies)}"

            self.net_amount_label.setText(net_text)

            # تحديد المبلغ المتاح للنقل (الليرة السورية فقط)
            self.transfer_amount_spin.setMaximum(max(0, syp_net))
            self.transfer_amount_spin.setValue(max(0, syp_net))

            # تحديث لون الصافي
            if syp_net > 0:
                self.net_amount_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 14px;")
            elif syp_net < 0:
                self.net_amount_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
            else:
                self.net_amount_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")

            # إضافة معلومات العملات في الملخص
            self.update_currencies_summary(currencies_data)

            print(f"📊 الخزينة اليومية: مبيعات={syp_sales:,} ل.س, مصاريف={syp_expenses:,} ل.س, صافي={syp_net:,} ل.س")
            if other_currencies:
                print(f"💱 عملات أخرى: {', '.join(other_currencies)}")

        except Exception as e:
            print(f"❌ خطأ في تحميل الخزينة اليومية: {e}")

    def update_currencies_summary(self, currencies_data):
        """تحديث ملخص العملات في الواجهة"""
        try:
            # إضافة معلومات العملات الأخرى للملخص إذا كانت موجودة
            if hasattr(self, 'currencies_info_label'):
                currencies_text = "العملات المتوفرة:\n"
                for currency in currencies_data:
                    if currency['balance'] > 0:
                        if currency['currency_type'] == 'SYP':
                            currencies_text += f"• الليرة السورية: {format_currency(currency['balance'])}\n"
                        elif currency['currency_type'] == 'USD':
                            currencies_text += f"• الدولار الأمريكي: ${currency['balance']:,.2f}\n"
                        elif currency['currency_type'] == 'EUR':
                            currencies_text += f"• اليورو: €{currency['balance']:,.2f}\n"
                        else:
                            currencies_text += f"• {currency['currency_type']}: {currency['balance']:,.2f}\n"

                self.currencies_info_label.setText(currencies_text)
            else:
                # إنشاء تسمية العملات إذا لم تكن موجودة
                self.create_currencies_info_section(currencies_data)

        except Exception as e:
            print(f"❌ خطأ في تحديث ملخص العملات: {e}")

    def create_currencies_info_section(self, currencies_data):
        """إنشاء قسم معلومات العملات"""
        try:
            # البحث عن المجموعة الرئيسية للملخص
            summary_group = None
            for child in self.findChildren(QGroupBox):
                if "معلومات الخزينة اليومية" in child.title():
                    summary_group = child
                    break

            if summary_group:
                layout = summary_group.layout()

                # إضافة تسمية العملات
                currencies_text = "العملات المتوفرة:\n"
                for currency in currencies_data:
                    if currency['balance'] > 0:
                        if currency['currency_type'] == 'SYP':
                            currencies_text += f"• الليرة السورية: {format_currency(currency['balance'])}\n"
                        elif currency['currency_type'] == 'USD':
                            currencies_text += f"• الدولار الأمريكي: ${currency['balance']:,.2f}\n"
                        elif currency['currency_type'] == 'EUR':
                            currencies_text += f"• اليورو: €{currency['balance']:,.2f}\n"
                        else:
                            currencies_text += f"• {currency['currency_type']}: {currency['balance']:,.2f}\n"

                # إضافة تسمية العملات في صف جديد
                layout.addWidget(QLabel(reshape_arabic_text("العملات المتوفرة:")), 3, 0)
                self.currencies_info_label = QLabel(currencies_text)
                self.currencies_info_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px; background-color: #f8f9fa; padding: 8px; border-radius: 5px;")
                self.currencies_info_label.setWordWrap(True)
                layout.addWidget(self.currencies_info_label, 3, 1, 1, 3)

        except Exception as e:
            print(f"❌ خطأ في إنشاء قسم العملات: {e}")

    def load_closed_shifts(self):
        """تحميل تفصيل الشيفتات المغلقة"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')

            shifts = self.db_manager.fetch_all("""
                SELECT user_name, start_time, end_time, total_sales, total_expenses,
                       closing_balance, cash_difference
                FROM shifts
                WHERE shift_date = ? AND status = 'closed'
                ORDER BY end_time DESC
            """, (today,))

            # إعداد الجدول
            self.shifts_table.setRowCount(len(shifts))

            for row, shift in enumerate(shifts):
                # المستخدم
                user_item = QTableWidgetItem(str(shift['user_name']))
                user_item.setFont(create_arabic_font(10))
                self.shifts_table.setItem(row, 0, user_item)

                # وقت البداية
                start_time = shift['start_time'].split(' ')[1][:8] if shift['start_time'] else '--:--:--'
                start_item = QTableWidgetItem(start_time)
                start_item.setFont(create_arabic_font(10))
                start_item.setTextAlignment(Qt.AlignCenter)
                self.shifts_table.setItem(row, 1, start_item)

                # وقت الإغلاق
                end_time = shift['end_time'].split(' ')[1][:8] if shift['end_time'] else '--:--:--'
                end_item = QTableWidgetItem(end_time)
                end_item.setFont(create_arabic_font(10))
                end_item.setTextAlignment(Qt.AlignCenter)
                self.shifts_table.setItem(row, 2, end_item)

                # المبيعات
                sales_item = QTableWidgetItem(format_currency(shift['total_sales']))
                sales_item.setFont(create_arabic_font(10))
                sales_item.setTextAlignment(Qt.AlignCenter)
                self.shifts_table.setItem(row, 3, sales_item)

                # المصاريف
                expenses_item = QTableWidgetItem(format_currency(shift['total_expenses']))
                expenses_item.setFont(create_arabic_font(10))
                expenses_item.setTextAlignment(Qt.AlignCenter)
                self.shifts_table.setItem(row, 4, expenses_item)

                # الصافي
                net = shift['total_sales'] - shift['total_expenses']
                net_item = QTableWidgetItem(format_currency(net))
                net_item.setFont(create_arabic_font(10))
                net_item.setTextAlignment(Qt.AlignCenter)
                if net > 0:
                    net_item.setBackground(Qt.green)
                    net_item.setForeground(Qt.black)
                elif net < 0:
                    net_item.setBackground(Qt.red)
                    net_item.setForeground(Qt.black)
                self.shifts_table.setItem(row, 5, net_item)

                # النقدية الفعلية
                cash_item = QTableWidgetItem(format_currency(shift['closing_balance']))
                cash_item.setFont(create_arabic_font(10))
                cash_item.setTextAlignment(Qt.AlignCenter)
                self.shifts_table.setItem(row, 6, cash_item)

                # الفرق
                diff_item = QTableWidgetItem(format_currency(shift['cash_difference']))
                diff_item.setFont(create_arabic_font(10))
                diff_item.setTextAlignment(Qt.AlignCenter)
                if shift['cash_difference'] > 0:
                    diff_item.setBackground(Qt.green)
                    diff_item.setForeground(Qt.black)
                elif shift['cash_difference'] < 0:
                    diff_item.setBackground(Qt.red)
                    diff_item.setForeground(Qt.black)
                self.shifts_table.setItem(row, 7, diff_item)

            # تنسيق الجدول
            header = self.shifts_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # المستخدم
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # وقت البداية
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # وقت الإغلاق
            header.setSectionResizeMode(3, QHeaderView.Stretch)           # المبيعات
            header.setSectionResizeMode(4, QHeaderView.Stretch)           # المصاريف
            header.setSectionResizeMode(5, QHeaderView.Stretch)           # الصافي
            header.setSectionResizeMode(6, QHeaderView.Stretch)           # النقدية الفعلية
            header.setSectionResizeMode(7, QHeaderView.Stretch)           # الفرق

            print(f"📋 تم تحميل {len(shifts)} شيفت مغلق")

        except Exception as e:
            print(f"❌ خطأ في تحميل الشيفتات: {e}")

    def execute_transfer(self):
        """تنفيذ عملية النقل"""
        try:
            transfer_amount = self.transfer_amount_spin.value()
            if transfer_amount <= 0:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال مبلغ صحيح للنقل"))
                return

            transfer_type = self.transfer_type_combo.currentText()
            transfer_date = self.transfer_date_edit.date().toString('yyyy-MM-dd')
            receiver = self.receiver_edit.text().strip()
            notes = self.notes_edit.toPlainText().strip()

            if not receiver:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("يرجى إدخال اسم المستلم"))
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self,
                reshape_arabic_text("تأكيد النقل"),
                reshape_arabic_text(f"هل أنت متأكد من نقل {format_currency(transfer_amount)}؟\n\nنوع النقل: {transfer_type}\nالمستلم: {receiver}"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # استخدام النظام الموحد لتنفيذ النقل
            if transfer_type == "نقل للخزينة الرئيسية":
                # نقل للخزينة الرئيسية
                success = self.treasury_manager.transfer_to_main_treasury(
                    user_id=self.current_user['id'],
                    currency_type='SYP',
                    amount=transfer_amount
                )

                if not success:
                    QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                                       reshape_arabic_text("فشل في نقل المبلغ للخزينة الرئيسية"))
                    return
            else:
                # خصم من الخزينة اليومية للأنواع الأخرى
                success = self.treasury_manager.subtract_from_daily_treasury(
                    user_id=self.current_user['id'],
                    currency_type='SYP',
                    amount=transfer_amount
                )

                if not success:
                    QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                                       reshape_arabic_text("فشل في خصم المبلغ من الخزينة اليومية"))
                    return

            # تسجيل عملية النقل في السجل
            self.db_manager.execute_query("""
                INSERT INTO transactions (type, amount, description, user_name, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                f"نقل خزينة - {transfer_type}",
                transfer_amount,
                f"نقل {format_currency(transfer_amount)} - {transfer_type} - المستلم: {receiver}",
                self.current_user['username'],
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))

            # إرسال إشارة النجاح
            transfer_data = {
                'amount': transfer_amount,
                'type': transfer_type,
                'receiver': receiver,
                'date': transfer_date
            }
            self.transfer_completed.emit(transfer_data)

            QMessageBox.information(self, reshape_arabic_text("نجح"),
                                  reshape_arabic_text(f"تم نقل {format_currency(transfer_amount)} بنجاح"))

            self.accept()

        except Exception as e:
            print(f"❌ خطأ في تنفيذ النقل: {e}")
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في تنفيذ النقل: {e}"))

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مفصل لمشكلة إغلاق التطبيق في تسليم الراوتر
"""

import sys
import os
import traceback
sys.path.append('src')

def test_imports():
    """اختبار الاستيرادات خطوة بخطوة"""
    
    print("🔍 اختبار الاستيرادات خطوة بخطوة...")
    
    try:
        print("1️⃣ استيراد PyQt5...")
        from PyQt5.QtWidgets import QApplication, QDialog, QMessageBox
        from PyQt5.QtCore import pyqtSignal
        print("✅ PyQt5")
        
        print("2️⃣ استيراد قاعدة البيانات...")
        from database.database_manager import DatabaseManager
        print("✅ DatabaseManager")
        
        print("3️⃣ استيراد الدعم العربي...")
        from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
        print("✅ Arabic Support")
        
        print("4️⃣ استيراد النظام الموحد...")
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        print("✅ UnifiedTreasuryManager")
        
        print("5️⃣ استيراد واجهة تسليم الراوتر...")
        from ui.router_delivery_window import RouterDeliveryWindow
        print("✅ RouterDeliveryWindow")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    
    print("\n🔍 اختبار عمليات قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        print("✅ اتصال قاعدة البيانات")
        
        # اختبار جدول shifts
        print("1️⃣ اختبار جدول shifts...")
        try:
            shifts = db.fetch_all("SELECT COUNT(*) as count FROM shifts")
            print(f"✅ جدول shifts: {shifts[0]['count']} سجل")
        except Exception as e:
            print(f"❌ مشكلة في جدول shifts: {e}")
        
        # اختبار جدول products
        print("2️⃣ اختبار جدول products...")
        try:
            routers = db.fetch_all("SELECT COUNT(*) as count FROM products WHERE category = 'router'")
            print(f"✅ جدول products (راوترات): {routers[0]['count']} سجل")
        except Exception as e:
            print(f"❌ مشكلة في جدول products: {e}")
        
        # اختبار جدول subscribers
        print("3️⃣ اختبار جدول subscribers...")
        try:
            subscribers = db.fetch_all("SELECT COUNT(*) as count FROM subscribers WHERE delivered = 0")
            print(f"✅ جدول subscribers (غير مسلمين): {subscribers[0]['count']} سجل")
        except Exception as e:
            print(f"❌ مشكلة في جدول subscribers: {e}")
        
        # اختبار جدول packages
        print("4️⃣ اختبار جدول packages...")
        try:
            packages = db.fetch_all("SELECT COUNT(*) as count FROM packages")
            print(f"✅ جدول packages: {packages[0]['count']} سجل")
        except Exception as e:
            print(f"❌ مشكلة في جدول packages: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_shift_function():
    """اختبار دالة get_current_shift"""
    
    print("\n🔍 اختبار دالة get_current_shift...")
    
    try:
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import get_current_shift
        
        db = DatabaseManager('data/company_system.db')
        user_id = 1
        user_name = 'admin'
        
        print(f"📊 اختبار الشيفت للمستخدم: {user_name} (ID: {user_id})")
        
        # محاولة الحصول على الشيفت
        shift_id = get_current_shift(db, user_id, user_name)
        
        if shift_id:
            print(f"✅ تم الحصول على الشيفت: {shift_id}")
            return True
        else:
            print("❌ فشل في الحصول على الشيفت")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في دالة get_current_shift: {e}")
        traceback.print_exc()
        return False

def test_window_creation():
    """اختبار إنشاء واجهة تسليم الراوتر"""
    
    print("\n🔍 اختبار إنشاء واجهة تسليم الراوتر...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ تطبيق Qt")
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        print("✅ قاعدة البيانات")
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        print("✅ بيانات المستخدم")
        
        # إنشاء الواجهة
        print("🔄 إنشاء واجهة تسليم الراوتر...")
        window = RouterDeliveryWindow(db, current_user)
        print("✅ تم إنشاء الواجهة بنجاح")
        
        # اختبار عرض الواجهة
        print("🔄 عرض الواجهة...")
        window.show()
        print("✅ تم عرض الواجهة")
        
        # إغلاق الواجهة
        window.close()
        print("✅ تم إغلاق الواجهة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الواجهة: {e}")
        traceback.print_exc()
        return False

def test_save_function():
    """اختبار دالة الحفظ بشكل منفصل"""
    
    print("\n🔍 اختبار دالة الحفظ...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        print("1️⃣ فتح الصندوق...")
        treasury_manager.open_cash_box(user_id=user_id)
        
        print("2️⃣ إضافة مبلغ للخزينة...")
        success = treasury_manager.add_to_daily_treasury(
            user_id=user_id,
            currency_type='SYP',
            amount=100000
        )
        
        if success:
            print("✅ تم تحديث الخزينة")
        else:
            print("❌ فشل في تحديث الخزينة")
        
        print("3️⃣ محاكاة حفظ التسليم...")
        
        # محاكاة بيانات التسليم
        delivery_data = {
            'subscriber_name': 'مشترك تجريبي',
            'phone': '0123456789',
            'address': 'عنوان تجريبي',
            'router_id': 1,
            'package_id': 1,
            'subscription_fee': 50000,
            'router_price': 100000,
            'cable_cost': 25000,
            'total': 175000
        }
        
        # محاولة حفظ البيانات
        try:
            delivery_id = db.execute_query("""
                INSERT INTO router_deliveries 
                (subscriber_name, phone, address, router_id, package_id, 
                 subscription_fee, router_price, cable_cost, total_amount, 
                 created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                delivery_data['subscriber_name'],
                delivery_data['phone'], 
                delivery_data['address'],
                delivery_data['router_id'],
                delivery_data['package_id'],
                delivery_data['subscription_fee'],
                delivery_data['router_price'],
                delivery_data['cable_cost'],
                delivery_data['total'],
                'admin'
            ))
            
            print(f"✅ تم حفظ التسليم: ID {delivery_id}")
            
        except Exception as save_error:
            print(f"❌ خطأ في حفظ التسليم: {save_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحفظ: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    
    print("🚨 تشخيص مفصل لمشكلة إغلاق التطبيق في تسليم الراوتر")
    print("=" * 70)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار قاعدة البيانات
    database_ok = test_database_operations()
    
    # اختبار دالة الشيفت
    shift_ok = test_shift_function()
    
    # اختبار إنشاء الواجهة
    window_ok = test_window_creation()
    
    # اختبار دالة الحفظ
    save_ok = test_save_function()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج التشخيص:")
    print(f"  • الاستيرادات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"  • قاعدة البيانات: {'✅ نجح' if database_ok else '❌ فشل'}")
    print(f"  • دالة الشيفت: {'✅ نجح' if shift_ok else '❌ فشل'}")
    print(f"  • إنشاء الواجهة: {'✅ نجح' if window_ok else '❌ فشل'}")
    print(f"  • دالة الحفظ: {'✅ نجح' if save_ok else '❌ فشل'}")
    
    if all([imports_ok, database_ok, shift_ok, window_ok, save_ok]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 المشكلة قد تكون في تفاعل المستخدم أو بيانات محددة")
        print("\n🔧 اقتراحات:")
        print("  1. تأكد من ملء جميع الحقول المطلوبة")
        print("  2. تأكد من وجود راوترات في المخزون")
        print("  3. تأكد من وجود مشتركين غير مسلمين")
        print("  4. جرب مع بيانات مختلفة")
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")
        
        if not imports_ok:
            print("  🔧 إصلاح مشكلة الاستيرادات")
        if not database_ok:
            print("  🔧 إصلاح مشكلة قاعدة البيانات")
        if not shift_ok:
            print("  🔧 إصلاح مشكلة الشيفت")
        if not window_ok:
            print("  🔧 إصلاح مشكلة إنشاء الواجهة")
        if not save_ok:
            print("  🔧 إصلاح مشكلة الحفظ")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 خطأ حرج في التشخيص: {e}")
        traceback.print_exc()
        print("\n🚨 هذا الخطأ قد يكون سبب إغلاق التطبيق!")

# 🔧 تشخيص مشكلة المصاريف في إغلاق الصندوق!

## 🔍 **المشكلة:**
**المصاريف لا تظهر في إغلاق الصندوق - تبقى بقيمة صفر**

## 🔧 **التشخيص المضاف:**

### 1️⃣ **تشخيص إضافة المصاريف:**
```python
# في expenses_window.py - دالة add_expense():
print(f"=== إضافة مصروف جديد ===")
print(f"النوع: {expense_data['expense_type']}")
print(f"المبلغ: {expense_data['amount']}")
print(f"التاريخ: {expense_data['expense_date']}")
print(f"الوصف: {expense_data['description']}")
print(f"المستخدم: {expense_data['user_name']}")

self.db_manager.execute_query("""
    INSERT INTO expenses (expense_type, amount, expense_date, description, notes, user_name)
    VALUES (?, ?, ?, ?, ?, ?)
""", (...))

print(f"تم حفظ المصروف في جدول expenses بنجاح")
```

### 2️⃣ **تشخيص شامل في إغلاق الصندوق:**
```python
# في cash_close_window.py - دالة calculate_totals():
print(f"استعلام إجمالي المصاريف للتاريخ: {today}")
print(f"تنسيق التاريخ المستخدم: '{today}' (نوع: {type(today)})")

print("=== اختبار استعلامات التاريخ ===")

# استعلام 1: بالتاريخ الحالي مع DATE function
expenses_result_1 = self.db_manager.fetch_one("""
    SELECT SUM(amount) as total FROM expenses
    WHERE DATE(expense_date) = ? AND expense_type != 'رواتب'
""", (today,))
print(f"استعلام 1 (DATE(expense_date) = '{today}'): {expenses_result_1}")

# استعلام 2: بدون DATE function
expenses_result_2 = self.db_manager.fetch_one("""
    SELECT SUM(amount) as total FROM expenses
    WHERE expense_date = ? AND expense_type != 'رواتب'
""", (today,))
print(f"استعلام 2 (expense_date = '{today}'): {expenses_result_2}")

# استعلام 3: جميع المصاريف اليوم مع التفاصيل
today_expenses_detailed = self.db_manager.fetch_all("""
    SELECT expense_type, amount, expense_date, description FROM expenses
    WHERE DATE(expense_date) = ?
""", (today,))
print(f"استعلام 3 (تفاصيل مصاريف اليوم): {len(today_expenses_detailed)} مصروف")
for exp in today_expenses_detailed:
    print(f"  - {exp['expense_type']}: {exp['amount']} في {exp['expense_date']}")

# استعلام 4: جميع التواريخ الموجودة
all_dates = self.db_manager.fetch_all("""
    SELECT DISTINCT expense_date FROM expenses ORDER BY expense_date DESC LIMIT 5
""")
print(f"آخر 5 تواريخ في جدول expenses:")
for date_row in all_dates:
    print(f"  - {date_row['expense_date']}")
```

---

## 🎯 **التشخيص المتوقع:**

### ✅ **عند إضافة مصروف:**
```
=== إضافة مصروف جديد ===
النوع: مكتبية
المبلغ: 25000.0
التاريخ: 2024-01-15
الوصف: أوراق وأقلام
المستخدم: admin
تم حفظ المصروف في جدول expenses بنجاح
تم خصم مصروف مكتبية بمبلغ 25000.0 ل.س من الصندوق
```

### ✅ **عند فتح إغلاق الصندوق:**
```
=== تشخيص المصاريف لتاريخ 2024-01-15 ===
إجمالي المصاريف في قاعدة البيانات: 3
آخر 10 مصاريف في قاعدة البيانات:
  - مكتبية: 25000.0 ل.س في 2024-01-15 - أوراق وأقلام
  - صيانة: 15000.0 ل.س في 2024-01-14 - إصلاح جهاز
مصاريف اليوم (2024-01-15):
  - مكتبية: 25000.0 ل.س - أوراق وأقلام (مُضاف للصندوق)
استعلام إجمالي المصاريف للتاريخ: 2024-01-15
تنسيق التاريخ المستخدم: '2024-01-15' (نوع: <class 'str'>)
=== اختبار استعلامات التاريخ ===
استعلام 1 (DATE(expense_date) = '2024-01-15'): {'total': 25000.0}
استعلام 2 (expense_date = '2024-01-15'): {'total': 25000.0}
استعلام 3 (تفاصيل مصاريف اليوم): 1 مصروف
  - مكتبية: 25000.0 في 2024-01-15
آخر 5 تواريخ في جدول expenses:
  - 2024-01-15
  - 2024-01-14
إجمالي المصاريف في الصندوق: 25000.0 ل.س
```

---

## 📋 **خطوات الاختبار:**

### 1️⃣ **أضف مصروف:**
- **افتح واجهة المصاريف**
- **اختر نوع المصروف** (مثل: مكتبية)
- **أدخل المبلغ** (مثل: 25000)
- **أدخل الوصف** (مثل: أوراق وأقلام)
- **اضغط "إضافة مصروف"**
- **راجع وحدة التحكم** لرؤية:
  ```
  === إضافة مصروف جديد ===
  النوع: مكتبية
  المبلغ: 25000.0
  التاريخ: 2024-01-15
  الوصف: أوراق وأقلام
  المستخدم: admin
  تم حفظ المصروف في جدول expenses بنجاح
  ```

### 2️⃣ **افتح إغلاق الصندوق:**
- **اذهب لإغلاق الصندوق**
- **راجع وحدة التحكم** لرؤية التشخيص المفصل:
  ```
  === تشخيص المصاريف لتاريخ YYYY-MM-DD ===
  إجمالي المصاريف في قاعدة البيانات: X
  آخر 10 مصاريف في قاعدة البيانات:
    - [نوع]: [مبلغ] ل.س في [تاريخ] - [وصف]
  مصاريف اليوم:
    - [نوع]: [مبلغ] ل.س - [وصف] (مُضاف للصندوق)
  === اختبار استعلامات التاريخ ===
  استعلام 1: {result}
  استعلام 2: {result}
  استعلام 3: X مصروف
  آخر 5 تواريخ في جدول expenses:
    - [تاريخ]
  إجمالي المصاريف في الصندوق: [مبلغ] ل.س
  ```

### 3️⃣ **تحليل النتائج:**
- **إذا كانت النتيجة 0:** تحقق من التواريخ والاستعلامات
- **إذا كانت النتيجة صحيحة:** المشكلة محلولة
- **إذا كان هناك خطأ:** راجع رسائل الخطأ في وحدة التحكم

---

## 🔍 **الأسباب المحتملة للمشكلة:**

### 1️⃣ **مشكلة التاريخ:**
- **تنسيق مختلف:** التاريخ في قاعدة البيانات بتنسيق مختلف عن المتوقع
- **منطقة زمنية:** اختلاف في المنطقة الزمنية
- **نوع البيانات:** التاريخ محفوظ كـ string بدلاً من date

### 2️⃣ **مشكلة الاستعلام:**
- **DATE function:** قد لا تعمل بشكل صحيح مع SQLite
- **مقارنة التاريخ:** مشكلة في مقارنة التواريخ
- **فلتر النوع:** مشكلة في استبعاد الرواتب

### 3️⃣ **مشكلة البيانات:**
- **جدول فارغ:** لا توجد مصاريف في قاعدة البيانات
- **نوع خاطئ:** جميع المصاريف من نوع "رواتب" فتُستبعد
- **تاريخ خاطئ:** المصاريف محفوظة بتاريخ مختلف

---

## 🎯 **الحلول المتوقعة:**

### ✅ **إذا كانت المشكلة في التاريخ:**
```python
# استخدام استعلام بدون DATE function
expenses_result = self.db_manager.fetch_one("""
    SELECT SUM(amount) as total FROM expenses
    WHERE expense_date = ? AND expense_type != 'رواتب'
""", (today,))
```

### ✅ **إذا كانت المشكلة في التنسيق:**
```python
# تحويل التاريخ لتنسيق صحيح
from datetime import date
today = date.today().strftime('%Y-%m-%d')
```

### ✅ **إذا كانت المشكلة في الاستعلام:**
```python
# استعلام أكثر مرونة
expenses_result = self.db_manager.fetch_one("""
    SELECT SUM(amount) as total FROM expenses
    WHERE expense_date LIKE ? AND expense_type != 'رواتب'
""", (f"{today}%",))
```

---

## 🎉 **الهدف:**

### ✅ **بعد التشخيص:**
- **معرفة السبب الدقيق** لعدم ظهور المصاريف
- **إصلاح المشكلة** بناءً على التشخيص
- **ضمان عمل المصاريف** في إغلاق الصندوق
- **تحسين الاستعلامات** للحصول على نتائج دقيقة

### 🚀 **النتيجة المطلوبة:**
- **المصاريف تظهر بشكل صحيح** في إغلاق الصندوق
- **الحسابات دقيقة** ومطابقة للواقع
- **تشخيص مفصل** لأي مشاكل مستقبلية
- **استقرار كامل** في النظام

**🎉 الآن مع التشخيص المفصل سنتمكن من معرفة السبب الدقيق وإصلاح المشكلة نهائياً! 🚀**

---

## 💡 **تعليمات الاختبار:**

### 🔧 **للمطور:**
1. **أضف مصروف** من واجهة المصاريف
2. **راجع وحدة التحكم** لرؤية رسائل الحفظ
3. **افتح إغلاق الصندوق**
4. **راجع وحدة التحكم** لرؤية التشخيص المفصل
5. **حلل النتائج** وحدد السبب
6. **طبق الحل المناسب** بناءً على التشخيص

### 📊 **للمستخدم:**
- **راجع وحدة التحكم دائماً** للرسائل التشخيصية
- **تأكد من إضافة المصاريف** قبل إغلاق الصندوق
- **تحقق من التواريخ** للتأكد من التطابق
- **أبلغ عن أي رسائل خطأ** تظهر في وحدة التحكم

**💡 هذا التشخيص سيكشف السبب الدقيق ويساعد في إصلاح المشكلة نهائياً!**

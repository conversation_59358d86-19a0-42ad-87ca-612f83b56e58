#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام المخزون الموحد
"""

import sys
import os
sys.path.append('src')

def test_unified_inventory_system():
    """اختبار النظام الموحد للمخزون"""
    
    print("🧪 اختبار نظام المخزون الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        print("✅ تم إنشاء مدير المخزون الموحد")
        
        # اختبار 1: نقل البيانات الموجودة
        print("\n=== 1. نقل البيانات الموجودة ===")
        inventory_manager.migrate_existing_data()
        
        # اختبار 2: إضافة منتج جديد للاختبار
        print("\n=== 2. إضافة منتج تجريبي ===")
        
        # إضافة منتج تجريبي
        db.execute_query("""
            INSERT OR IGNORE INTO unified_products 
            (name, category, unit_type, purchase_price, sale_price, current_stock, min_stock)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, ("راوتر تجريبي", "راوتر", "قطعة", 50000, 75000, 0, 5))
        
        # جلب معرف المنتج
        product = db.fetch_one("""
            SELECT id FROM unified_products WHERE name = 'راوتر تجريبي'
        """)
        
        if product:
            product_id = product['id']
            print(f"✅ تم إنشاء منتج تجريبي - ID: {product_id}")
            
            # اختبار 3: شراء من مورد (إضافة للمخزون)
            print("\n=== 3. شراء من مورد ===")
            success = inventory_manager.add_stock(
                product_id=product_id,
                quantity=10,
                operation_type="purchase",
                reference_id=1,
                notes="شراء تجريبي",
                user_id=1,
                from_location="supplier_1"
            )
            
            if success:
                stock = inventory_manager.get_product_stock(product_id)
                print(f"✅ تم شراء 10 قطع - المخزون الحالي: {stock}")
            
            # اختبار 4: تسليم للعامل
            print("\n=== 4. تسليم للعامل ===")
            success = inventory_manager.transfer_to_worker(
                product_id=product_id,
                worker_id=1,
                quantity=3,
                operation_type="delivery",
                reference_id=1,
                notes="تسليم تجريبي للعامل",
                user_id=1
            )
            
            if success:
                main_stock = inventory_manager.get_product_stock(product_id)
                worker_stock = inventory_manager.get_worker_stock(1, product_id)
                print(f"✅ تم تسليم 3 قطع للعامل")
                print(f"   المخزون الرئيسي: {main_stock}")
                print(f"   مخزون العامل: {worker_stock}")
            
            # اختبار 5: تسليم راوتر (خصم من العامل)
            print("\n=== 5. تسليم راوتر للعميل ===")
            success = inventory_manager.remove_from_worker(
                product_id=product_id,
                worker_id=1,
                quantity=1,
                operation_type="router_delivery",
                reference_id=1,
                notes="تسليم راوتر للعميل",
                user_id=1
            )
            
            if success:
                worker_stock = inventory_manager.get_worker_stock(1, product_id)
                print(f"✅ تم تسليم راوتر للعميل")
                print(f"   مخزون العامل بعد التسليم: {worker_stock}")
            
            # اختبار 6: أمر صيانة (خصم من المخزون الرئيسي)
            print("\n=== 6. أمر صيانة ===")
            success = inventory_manager.remove_stock(
                product_id=product_id,
                quantity=2,
                operation_type="maintenance",
                reference_id=1,
                notes="أمر صيانة تجريبي",
                user_id=1,
                to_location="maintenance"
            )
            
            if success:
                main_stock = inventory_manager.get_product_stock(product_id)
                print(f"✅ تم استخدام 2 قطع في الصيانة")
                print(f"   المخزون الرئيسي: {main_stock}")
            
            # اختبار 7: عرض الحركات
            print("\n=== 7. حركات المخزون ===")
            movements = inventory_manager.get_inventory_movements(product_id, limit=10)
            
            print(f"📊 عدد الحركات: {len(movements)}")
            for movement in movements:
                movement_type = "دخول" if movement['movement_type'] == 'in' else "خروج"
                print(f"  • {movement['created_at'][:16]} - {movement_type} - {movement['quantity']} - {movement['operation_type']}")
            
            # اختبار 8: الأرصدة النهائية
            print("\n=== 8. الأرصدة النهائية ===")
            main_stock = inventory_manager.get_product_stock(product_id)
            worker_stock = inventory_manager.get_worker_stock(1, product_id)
            
            print(f"📊 المخزون الرئيسي: {main_stock}")
            print(f"📊 مخزون العامل 1: {worker_stock}")
            print(f"📊 إجمالي المخزون: {main_stock + worker_stock}")
            
            # التحقق من صحة الحسابات
            expected_total = 10 - 2  # 10 مشتراة - 2 صيانة = 8 (1 عند العامل + 7 في المخزون الرئيسي)
            actual_total = main_stock + worker_stock
            
            if abs(actual_total - expected_total) < 0.1:
                print("✅ الحسابات صحيحة!")
                return True
            else:
                print(f"❌ خطأ في الحسابات: متوقع {expected_total}, فعلي {actual_total}")
                return False
        else:
            print("❌ فشل في إنشاء المنتج التجريبي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_window():
    """اختبار واجهة المخزون الموحد"""
    
    print("\n🧪 اختبار واجهة المخزون الموحد...")
    
    try:
        from database.database_manager import DatabaseManager
        from ui.unified_inventory_window import UnifiedInventoryWindow
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # إنشاء الواجهة
        window = UnifiedInventoryWindow(db, current_user)
        
        print("✅ تم إنشاء واجهة المخزون الموحد بنجاح")
        
        # لا نعرض الواجهة في الاختبار التلقائي
        # window.show()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار نظام المخزون الموحد")
    print("=" * 60)
    
    # اختبار النظام الأساسي
    system_test = test_unified_inventory_system()
    
    # اختبار الواجهة
    window_test = test_inventory_window()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • نظام المخزون الموحد: {'✅ نجح' if system_test else '❌ فشل'}")
    print(f"  • واجهة المخزون الموحد: {'✅ نجح' if window_test else '❌ فشل'}")
    
    if all([system_test, window_test]):
        print("\n🎉 نظام المخزون الموحد يعمل بشكل مثالي!")
        
        print("\n📋 المميزات المحققة:")
        print("  ✅ جدول واحد للمنتجات مع المخزون")
        print("  ✅ تتبع دقيق لحركات المخزون")
        print("  ✅ المشتريات تضاف للمخزون الرئيسي")
        print("  ✅ تسليم العمال ينقل من الرئيسي للعامل")
        print("  ✅ تسليم الراوتر يخصم من مخزون العامل")
        print("  ✅ أمر الصيانة يخصم من المخزون الرئيسي")
        print("  ✅ واجهة موحدة لعرض جميع المخزون")
        print("  ✅ تتبع مخزون كل عامل منفصل")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. اضغط 'المخزون الموحد' من قائمة الإدارة")
        print("  3. تصفح المخزون الرئيسي ومخزون العمال")
        print("  4. تتبع جميع حركات المخزون")
        print("  5. جميع العمليات تحدث تلقائياً:")
        print("     • المشتريات → المخزون الرئيسي")
        print("     • تسليم العمال → من الرئيسي للعامل")
        print("     • تسليم الراوتر → من العامل للعميل")
        print("     • أمر الصيانة → من المخزون الرئيسي")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

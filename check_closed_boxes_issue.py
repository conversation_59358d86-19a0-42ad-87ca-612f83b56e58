#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مشكلة عدم قراءة الصناديق المغلقة
"""

import sys
import os
sys.path.append('src')

def check_closed_cash_boxes():
    """فحص الصناديق المغلقة الموجودة"""
    
    print("🔍 فحص الصناديق المغلقة الموجودة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('.')
        
        # فحص جدول cash_boxes
        print("\n📦 فحص جدول cash_boxes:")
        try:
            cash_boxes = db.fetch_all("""
                SELECT id, user_id, opening_balance, total_sales, total_expenses, 
                       closing_balance, is_closed, created_at, closed_at
                FROM cash_boxes 
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            print(f"  • تم العثور على {len(cash_boxes)} صندوق:")
            total_closed = 0
            total_amount = 0
            
            for box in cash_boxes:
                status = "مغلق" if box['is_closed'] else "مفتوح"
                closing_balance = box.get('closing_balance', 0) or 0
                if box['is_closed']:
                    total_closed += 1
                    total_amount += closing_balance
                
                print(f"    - صندوق {box['id']}: مستخدم {box['user_id']} - {status} - رصيد إغلاق: {closing_balance:,} ل.س")
            
            print(f"\n📊 ملخص الصناديق:")
            print(f"  • إجمالي الصناديق المغلقة: {total_closed}")
            print(f"  • إجمالي أرصدة الإغلاق: {total_amount:,} ل.س")
            
            return cash_boxes, total_amount
            
        except Exception as e:
            print(f"❌ خطأ في قراءة جدول cash_boxes: {e}")
            
            # فحص جدول shifts كبديل
            print("\n📦 فحص جدول shifts كبديل:")
            try:
                shifts = db.fetch_all("""
                    SELECT id, user_id, total_sales, total_expenses, 
                           (total_sales - total_expenses) as net_amount,
                           status, shift_date, closed_at
                    FROM shifts 
                    WHERE status = 'closed'
                    ORDER BY shift_date DESC
                    LIMIT 10
                """)
                
                print(f"  • تم العثور على {len(shifts)} شيفت مغلق:")
                total_net = 0
                
                for shift in shifts:
                    net = shift.get('net_amount', 0) or 0
                    total_net += net
                    print(f"    - شيفت {shift['id']}: مستخدم {shift['user_id']} - صافي: {net:,} ل.س - تاريخ: {shift.get('shift_date', 'غير محدد')}")
                
                print(f"\n📊 ملخص الشيفتات:")
                print(f"  • إجمالي الشيفتات المغلقة: {len(shifts)}")
                print(f"  • إجمالي الصافي: {total_net:,} ل.س")
                
                return shifts, total_net
                
            except Exception as shifts_error:
                print(f"❌ خطأ في قراءة جدول shifts: {shifts_error}")
                return [], 0
        
    except Exception as e:
        print(f"❌ خطأ عام في فحص الصناديق: {e}")
        return [], 0

def check_unified_treasury_content():
    """فحص محتوى النظام الموحد للخزينة"""
    
    print("\n🔍 فحص محتوى النظام الموحد للخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        # فحص جدول unified_treasury
        print("\n📊 فحص جدول unified_treasury:")
        records = db.fetch_all("""
            SELECT user_id, session_date, currency_type, daily_balance, main_balance, 
                   is_session_active, last_updated, created_at
            FROM unified_treasury 
            ORDER BY session_date DESC, last_updated DESC
        """)
        
        print(f"  • إجمالي السجلات: {len(records)}")
        
        # تجميع الأرصدة حسب المستخدم والعملة
        user_balances = {}
        for record in records:
            user_id = record['user_id']
            currency = record['currency_type']
            
            if user_id not in user_balances:
                user_balances[user_id] = {}
            if currency not in user_balances[user_id]:
                user_balances[user_id][currency] = {'daily': 0, 'main': 0, 'sessions': 0}
            
            user_balances[user_id][currency]['daily'] = max(user_balances[user_id][currency]['daily'], record['daily_balance'])
            user_balances[user_id][currency]['main'] = max(user_balances[user_id][currency]['main'], record['main_balance'])
            user_balances[user_id][currency]['sessions'] += 1
        
        print("\n💰 الأرصدة حسب المستخدم:")
        for user_id, currencies in user_balances.items():
            print(f"  • المستخدم {user_id}:")
            for currency, balances in currencies.items():
                symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")
                print(f"    - {currency}: يومي {balances['daily']:,.2f} {symbol} - رئيسي {balances['main']:,.2f} {symbol} ({balances['sessions']} جلسة)")
        
        # فحص الجلسات النشطة
        active_sessions = db.fetch_all("""
            SELECT user_id, currency_type, daily_balance, session_date
            FROM unified_treasury 
            WHERE is_session_active = 1
            ORDER BY user_id, currency_type
        """)
        
        print(f"\n🔄 الجلسات النشطة: {len(active_sessions)}")
        for session in active_sessions:
            symbol = "ل.س" if session['currency_type'] == "SYP" else ("$" if session['currency_type'] == "USD" else "€")
            print(f"  • مستخدم {session['user_id']} - {session['currency_type']}: {session['daily_balance']:,.2f} {symbol} - تاريخ: {session['session_date']}")
        
        return user_balances, active_sessions
        
    except Exception as e:
        print(f"❌ خطأ في فحص النظام الموحد: {e}")
        import traceback
        traceback.print_exc()
        return {}, []

def check_treasury_calculation_logic():
    """فحص منطق حساب الخزينة"""
    
    print("\n🔍 فحص منطق حساب الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1}
        
        print("\n🧮 اختبار دوال الحساب:")
        
        # اختبار get_daily_balance
        daily_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"  • get_daily_balance(user_1, SYP): {daily_balance:,} ل.س")
        
        # اختبار get_main_balance
        main_balance = treasury_manager.get_main_balance('SYP')
        print(f"  • get_main_balance(SYP): {main_balance:,} ل.س")
        
        # فحص كيف يتم حساب daily_balance
        print("\n🔍 فحص حساب daily_balance:")
        manual_daily = db.fetch_one("""
            SELECT daily_balance FROM unified_treasury 
            WHERE user_id = ? AND currency_type = 'SYP' AND is_session_active = 1
            ORDER BY session_date DESC, last_updated DESC
            LIMIT 1
        """, (current_user['id'],))
        
        if manual_daily:
            print(f"  • من الجدول مباشرة: {manual_daily['daily_balance']:,} ل.س")
            
            if abs(daily_balance - manual_daily['daily_balance']) < 1:
                print("  ✅ الحساب صحيح")
            else:
                print(f"  ❌ خطأ في الحساب: دالة={daily_balance:,} جدول={manual_daily['daily_balance']:,}")
        else:
            print("  ⚠️ لا توجد جلسة نشطة")
        
        # فحص كيف يتم حساب main_balance
        print("\n🔍 فحص حساب main_balance:")
        manual_main = db.fetch_one("""
            SELECT SUM(main_balance) as total_main FROM (
                SELECT DISTINCT user_id, main_balance FROM unified_treasury 
                WHERE currency_type = 'SYP'
                ORDER BY user_id, session_date DESC, last_updated DESC
            ) GROUP BY user_id
        """)
        
        if manual_main and manual_main['total_main']:
            print(f"  • من الجدول مباشرة: {manual_main['total_main']:,} ل.س")
            
            if abs(main_balance - manual_main['total_main']) < 1:
                print("  ✅ الحساب صحيح")
            else:
                print(f"  ❌ خطأ في الحساب: دالة={main_balance:,} جدول={manual_main['total_main']:,}")
        else:
            print("  ⚠️ لا توجد بيانات في الخزينة الرئيسية")
        
        return daily_balance, main_balance
        
    except Exception as e:
        print(f"❌ خطأ في فحص منطق الحساب: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

def main():
    """الدالة الرئيسية"""
    
    print("🚨 فحص مشكلة عدم قراءة الصناديق المغلقة")
    print("=" * 70)
    
    # 1. فحص الصناديق المغلقة
    closed_boxes, total_amount = check_closed_cash_boxes()
    
    # 2. فحص النظام الموحد
    user_balances, active_sessions = check_unified_treasury_content()
    
    # 3. فحص منطق الحساب
    daily_balance, main_balance = check_treasury_calculation_logic()
    
    print("\n" + "=" * 70)
    print("📊 ملخص التشخيص:")
    
    if closed_boxes and total_amount > 0:
        print(f"  • الصناديق المغلقة: {len(closed_boxes)} صندوق بمجموع {total_amount:,} ل.س")
    else:
        print("  • الصناديق المغلقة: لا توجد أو لا يمكن قراءتها")
    
    if user_balances:
        print(f"  • النظام الموحد: يحتوي على بيانات {len(user_balances)} مستخدم")
    else:
        print("  • النظام الموحد: فارغ أو به مشكلة")
    
    print(f"  • الخزينة اليومية الحالية: {daily_balance:,} ل.س")
    print(f"  • الخزينة الرئيسية الحالية: {main_balance:,} ل.س")
    
    # التشخيص
    if total_amount > 0 and daily_balance == 0:
        print("\n❌ المشكلة المحددة:")
        print("  • يوجد صناديق مغلقة لكن الخزينة اليومية فارغة")
        print("  • الصناديق المغلقة لم يتم نقلها للنظام الموحد")
        
        print("\n💡 الحل:")
        print("  • تشغيل: python migrate_closed_boxes.py")
        print("  • أو إضافة نقل تلقائي للصناديق المغلقة")
        
    elif daily_balance > 0:
        print("\n✅ لا توجد مشكلة:")
        print("  • الخزينة اليومية تحتوي على رصيد")
        print("  • النظام يعمل بشكل صحيح")
        
    else:
        print("\n⚠️ حالة غير واضحة:")
        print("  • لا توجد صناديق مغلقة ولا رصيد في الخزينة اليومية")
        print("  • قد تحتاج لفتح صندوق جديد أو إضافة رصيد")

if __name__ == "__main__":
    main()

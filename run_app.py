#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة شركة الإنترنت
Run Internet Company Management System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    
    from src.database.database_manager import DatabaseManager
    from src.ui.login_window import LoginWindow
    from src.ui.main_window import MainWindow
    from src.utils.config_manager import ConfigManager
    from src.utils.arabic_support import setup_arabic_support
    
    def main():
        """الدالة الرئيسية"""
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        
        # اختيار مجلد المشروع
        dialog = QFileDialog()
        dialog.setFileMode(QFileDialog.Directory)
        dialog.setWindowTitle("اختر مجلد حفظ بيانات النظام")
        
        if not dialog.exec_():
            QMessageBox.critical(None, "خطأ", "يجب اختيار مجلد لحفظ بيانات النظام")
            return 1
            
        project_dir = dialog.selectedFiles()[0]
        
        # إنشاء مدير الإعدادات
        config_manager = ConfigManager(project_dir)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager(project_dir)
        
        # تهيئة قاعدة البيانات
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
            
        # عرض نافذة تسجيل الدخول
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() != login_window.Accepted:
            return 0
            
        current_user = login_window.get_current_user()
        
        # عرض النافذة الرئيسية
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        # تشغيل حلقة الأحداث
        return app.exec_()
        
    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من تثبيت جميع المتطلبات")
    input("اضغط Enter للخروج...")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ في تشغيل البرنامج: {e}")
    input("اضغط Enter للخروج...")
    sys.exit(1)

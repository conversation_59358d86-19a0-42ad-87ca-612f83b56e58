#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل المبسط لسند القبض
"""

import sys
import os
sys.path.append('src')

def test_receipt_to_daily_treasury():
    """اختبار إضافة سند قبض للخزينة اليومية مباشرة"""
    
    print("🧪 اختبار إضافة سند قبض للخزينة اليومية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # 1. فتح جلسة جديدة
        print("\n1️⃣ فتح جلسة جديدة...")
        treasury_manager.open_cash_box(user_id=current_user['id'])
        
        # 2. فحص الخزينة اليومية قبل إضافة سند القبض
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"💰 الخزينة اليومية قبل سند القبض: {daily_balance_before:,} ل.س")
        
        # 3. إضافة سند قبض للخزينة اليومية
        print("\n2️⃣ إضافة سند قبض للخزينة اليومية...")
        receipt_amount = 125000
        receipt_success = treasury_manager.add_to_daily_treasury(
            user_id=current_user['id'],
            currency_type='SYP',
            amount=receipt_amount
        )
        
        if receipt_success:
            print(f"✅ تم إضافة سند قبض بقيمة {receipt_amount:,} ل.س للخزينة اليومية")
            
            # فحص الخزينة اليومية بعد إضافة سند القبض
            daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
            print(f"💰 الخزينة اليومية بعد سند القبض: {daily_balance_after:,} ل.س")
            
            # التحقق من الزيادة
            expected_increase = receipt_amount
            actual_increase = daily_balance_after - daily_balance_before
            
            if abs(actual_increase - expected_increase) < 1:
                print(f"✅ تم إضافة المبلغ بشكل صحيح: زيادة {actual_increase:,} ل.س")
                return True
            else:
                print(f"❌ خطأ في الإضافة: متوقع {expected_increase:,} ل.س، فعلي {actual_increase:,} ل.س")
                return False
        else:
            print("❌ فشل في إضافة سند القبض")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سند القبض: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voucher_from_daily_treasury():
    """اختبار خصم سند دفع من الخزينة اليومية مباشرة"""
    
    print("\n🧪 اختبار خصم سند دفع من الخزينة اليومية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الخزينة اليومية قبل خصم سند الدفع
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"💰 الخزينة اليومية قبل سند الدفع: {daily_balance_before:,} ل.س")
        
        if daily_balance_before > 0:
            # خصم سند دفع
            voucher_amount = min(50000, daily_balance_before)
            voucher_success = treasury_manager.subtract_from_daily_treasury(
                user_id=current_user['id'],
                currency_type='SYP',
                amount=voucher_amount
            )
            
            if voucher_success:
                print(f"✅ تم خصم سند دفع بقيمة {voucher_amount:,} ل.س من الخزينة اليومية")
                
                # فحص الخزينة اليومية بعد خصم سند الدفع
                daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                print(f"💰 الخزينة اليومية بعد سند الدفع: {daily_balance_after:,} ل.س")
                
                # التحقق من النقصان
                expected_decrease = voucher_amount
                actual_decrease = daily_balance_before - daily_balance_after
                
                if abs(actual_decrease - expected_decrease) < 1:
                    print(f"✅ تم خصم المبلغ بشكل صحيح: نقصان {actual_decrease:,} ل.س")
                    return True
                else:
                    print(f"❌ خطأ في الخصم: متوقع {expected_decrease:,} ل.س، فعلي {actual_decrease:,} ل.س")
                    return False
            else:
                print("❌ فشل في خصم سند الدفع")
                return False
        else:
            print("⚠️ لا يوجد رصيد في الخزينة اليومية للخصم")
            return True  # لا نعتبرها فشل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سند الدفع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_with_receipts():
    """اختبار نقل الخزينة مع السندات"""
    
    print("\n🧪 اختبار نقل الخزينة مع السندات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة قبل النقل
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_balance_before = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الخزينة اليومية قبل النقل: {daily_balance_before:,} ل.س")
        print(f"💰 الخزينة الرئيسية قبل النقل: {main_balance_before:,} ل.س")
        
        if daily_balance_before > 0:
            # نقل جزء من المبلغ
            transfer_amount = min(75000, daily_balance_before)
            
            print(f"\n3️⃣ نقل {transfer_amount:,} ل.س من اليومية للرئيسية...")
            
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=current_user['id'],
                currency_type='SYP',
                amount=transfer_amount
            )
            
            if transfer_success:
                print("✅ تم النقل بنجاح")
                
                # فحص الأرصدة بعد النقل
                daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                main_balance_after = treasury_manager.get_main_balance('SYP')
                
                print(f"💰 الخزينة اليومية بعد النقل: {daily_balance_after:,} ل.س")
                print(f"💰 الخزينة الرئيسية بعد النقل: {main_balance_after:,} ل.س")
                
                # التحقق من صحة النقل
                daily_decrease = daily_balance_before - daily_balance_after
                main_increase = main_balance_after - main_balance_before
                
                if (abs(daily_decrease - transfer_amount) < 1 and
                    abs(main_increase - transfer_amount) < 1):
                    print(f"✅ النقل صحيح - خصم: {daily_decrease:,} ل.س، إضافة: {main_increase:,} ل.س")
                    return True
                else:
                    print(f"❌ خطأ في النقل")
                    return False
            else:
                print("❌ فشل في النقل")
                return False
        else:
            print("⚠️ لا يوجد رصيد في الخزينة اليومية للنقل")
            return True  # لا نعتبرها فشل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار الحل المبسط لسند القبض والدفع")
    print("=" * 70)
    
    # اختبار إضافة سند قبض للخزينة اليومية
    receipt_test = test_receipt_to_daily_treasury()
    
    # اختبار خصم سند دفع من الخزينة اليومية
    voucher_test = test_voucher_from_daily_treasury()
    
    # اختبار نقل الخزينة مع السندات
    transfer_test = test_treasury_transfer_with_receipts()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • إضافة سند قبض للخزينة اليومية: {'✅ يعمل' if receipt_test else '❌ لا يعمل'}")
    print(f"  • خصم سند دفع من الخزينة اليومية: {'✅ يعمل' if voucher_test else '❌ لا يعمل'}")
    print(f"  • نقل الخزينة مع السندات: {'✅ يعمل' if transfer_test else '❌ لا يعمل'}")
    
    if all([receipt_test, voucher_test, transfer_test]):
        print("\n🎉 تم إصلاح مشكلة السندات بالحل المبسط!")
        
        print("\n📋 الحل المطبق:")
        print("  ✅ الحل المبسط والأمثل")
        print("    • سند القبض يضيف مباشرة للخزينة اليومية")
        print("    • سند الدفع يخصم مباشرة من الخزينة اليومية")
        print("    • لا حاجة لتعقيد جدول cash_boxes منفصل")
        print("    • تكامل كامل مع النظام الموحد")
        
        print("  ✅ المميزات")
        print("    • السندات تظهر فوراً في واجهة نقل الخزينة")
        print("    • تعمل مع شراء الدولار من الخزينة اليومية")
        print("    • تنقل مع الخزينة من اليومية للرئيسية")
        print("    • بساطة في التصميم والصيانة")
        
        print("\n🚀 النظام الآن:")
        print("  • سند القبض → يضيف للخزينة اليومية مباشرة")
        print("  • سند الدفع → يخصم من الخزينة اليومية مباشرة")
        print("  • شراء الدولار → من الخزينة اليومية")
        print("  • نقل الخزينة → من اليومية للرئيسية")
        print("  • جميع الأرصدة تظهر بشكل صحيح")
        
        print("\n🎯 للاستخدام:")
        print("  1. أضف سند قبض → يظهر في الخزينة اليومية")
        print("  2. أضف سند دفع → يخصم من الخزينة اليومية")
        print("  3. اشتري دولار → من الخزينة اليومية")
        print("  4. انقل الخزينة → من اليومية للرئيسية")
        print("  5. جميع العمليات متكاملة ومترابطة")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح إضافي!")

if __name__ == "__main__":
    main()

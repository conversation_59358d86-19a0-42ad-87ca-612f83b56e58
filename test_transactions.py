#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل المعاملات المالية
"""

import sqlite3
from datetime import datetime

def test_transactions():
    """اختبار تسجيل المعاملات"""
    try:
        conn = sqlite3.connect('data/company_system.db')
        cursor = conn.cursor()
        
        print("🧪 اختبار تسجيل المعاملات المالية...")
        
        # تسجيل معاملة تجريبية
        test_transactions = [
            ("اشتراك جديد", "اشتراك تجريبي - أحمد محمد", 150000, "SYP", "نقدي", 1, "admin"),
            ("تسليم راوتر", "تسليم راوتر تجريبي - سارة أحمد", 200000, "SYP", "نقدي", 2, "admin"),
            ("تجديد باقة", "تجديد باقة تجريبي - محمد علي", 75000, "SYP", "نقدي", 3, "admin"),
        ]
        
        for trans_type, description, amount, currency, payment_method, ref_id, user_name in test_transactions:
            cursor.execute("""
                INSERT INTO transactions (type, description, amount, currency, payment_method, reference_id, user_name)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (trans_type, description, amount, currency, payment_method, ref_id, user_name))
            
            print(f"✅ تم تسجيل: {trans_type} - {amount:,} ل.س")
        
        conn.commit()
        
        # التحقق من التسجيل
        today = datetime.now().strftime('%Y-%m-%d')
        transactions = cursor.execute("""
            SELECT type, description, amount, user_name, created_at
            FROM transactions 
            WHERE DATE(created_at) = ?
            ORDER BY created_at DESC
        """, (today,)).fetchall()
        
        print(f"\n📊 المعاملات المسجلة لليوم ({len(transactions)} معاملة):")
        total_amount = 0
        for trans in transactions:
            print(f"  • {trans[0]}: {trans[2]:,} ل.س - {trans[1]} - {trans[3]}")
            total_amount += trans[2]
        
        print(f"\n💰 إجمالي المعاملات: {total_amount:,} ل.س")
        
        # حساب المبيعات حسب النوع
        sales_by_type = cursor.execute("""
            SELECT type, COUNT(*) as count, SUM(amount) as total
            FROM transactions 
            WHERE DATE(created_at) = ? AND user_name = 'admin'
            GROUP BY type
        """, (today,)).fetchall()
        
        print(f"\n📈 المبيعات حسب النوع:")
        for sale_type, count, total in sales_by_type:
            print(f"  • {sale_type}: {count} عملية - {total:,} ل.س")
        
        conn.close()
        print("\n✅ تم الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transactions()

# -*- coding: utf-8 -*-
"""
نافذة أمر الصيانة
Maintenance Order Window

ملاحظة: المنتجات في هذه الواجهة تأتي من جدول المنتجات
الذي يتم إدارته من خلال واجهة "إدارة المنتجات"
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QSpinBox, QGroupBox, 
                            QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from utils.arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency
except ImportError:
    from arabic_support import apply_arabic_style, reshape_arabic_text, create_arabic_font, format_currency


class MaintenanceOrderWindow(QDialog):
    """نافذة أمر الصيانة"""
    
    order_added = pyqtSignal(dict)
    
    def __init__(self, db_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.current_user = current_user
        self.selected_items = []  # قائمة المواد المختارة

        # إنشاء مدير المخزون الموحد
        try:
            from utils.unified_inventory_manager import UnifiedInventoryManager
            self.inventory_manager = UnifiedInventoryManager(db_manager)
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم تحميل مدير المخزون الموحد: {e}")
            self.inventory_manager = None

        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("أمر صيانة")
        self.setGeometry(100, 100, 1200, 800)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("أمر صيانة")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #fef9e7;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - معلومات الأمر
        left_side = self.create_order_info()
        
        # الجانب الأيمن - اختيار المواد
        right_side = self.create_items_selection()
        
        content_layout.addWidget(left_side, 1)
        content_layout.addWidget(right_side, 1)
        
        # جدول أوامر الصيانة
        self.orders_table = self.create_orders_table()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        layout.addWidget(title_label)
        layout.addLayout(content_layout)
        layout.addWidget(self.orders_table)
        layout.addLayout(buttons_layout)
        
    def create_order_info(self):
        """إنشاء مجموعة معلومات الأمر"""
        group = QGroupBox("معلومات أمر الصيانة")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # رقم الأمر
        order_number_label = QLabel("رقم الأمر:")
        apply_arabic_style(order_number_label, 10)
        self.order_number_edit = QLineEdit()
        apply_arabic_style(self.order_number_edit, 10)
        self.order_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        self.order_number_edit.setReadOnly(True)
        
        # اسم العميل
        customer_name_label = QLabel("اسم العميل:")
        apply_arabic_style(customer_name_label, 10)
        self.customer_name_edit = QLineEdit()
        apply_arabic_style(self.customer_name_edit, 10)
        self.customer_name_edit.setPlaceholderText("اسم العميل...")
        
        # رقم الهاتف
        customer_phone_label = QLabel("رقم الهاتف:")
        apply_arabic_style(customer_phone_label, 10)
        self.customer_phone_edit = QLineEdit()
        apply_arabic_style(self.customer_phone_edit, 10)
        self.customer_phone_edit.setPlaceholderText("رقم هاتف العميل...")
        
        # التاريخ
        date_label = QLabel("تاريخ الأمر:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # الفني المسؤول
        technician_label = QLabel("الفني المسؤول:")
        apply_arabic_style(technician_label, 10)
        self.technician_combo = QComboBox()
        apply_arabic_style(self.technician_combo, 10)
        
        # حالة الأمر
        status_label = QLabel("حالة الأمر:")
        apply_arabic_style(status_label, 10)
        self.status_combo = QComboBox()
        apply_arabic_style(self.status_combo, 10)
        self.status_combo.addItems(["جديد", "قيد التنفيذ", "مكتمل", "ملغي"])
        
        # وصف المشكلة
        description_label = QLabel("وصف المشكلة:")
        apply_arabic_style(description_label, 10)
        self.description_edit = QTextEdit()
        apply_arabic_style(self.description_edit, 10)
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("وصف تفصيلي للمشكلة...")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        
        layout.addWidget(order_number_label, 0, 0)
        layout.addWidget(self.order_number_edit, 0, 1)
        layout.addWidget(customer_name_label, 1, 0)
        layout.addWidget(self.customer_name_edit, 1, 1)
        layout.addWidget(customer_phone_label, 2, 0)
        layout.addWidget(self.customer_phone_edit, 2, 1)
        layout.addWidget(date_label, 3, 0)
        layout.addWidget(self.date_edit, 3, 1)
        layout.addWidget(technician_label, 4, 0)
        layout.addWidget(self.technician_combo, 4, 1)
        layout.addWidget(status_label, 5, 0)
        layout.addWidget(self.status_combo, 5, 1)
        layout.addWidget(description_label, 6, 0, 1, 2)
        layout.addWidget(self.description_edit, 7, 0, 1, 2)
        layout.addWidget(notes_label, 8, 0, 1, 2)
        layout.addWidget(self.notes_edit, 9, 0, 1, 2)
        
        return group
        
    def create_items_selection(self):
        """إنشاء مجموعة اختيار المواد"""
        group = QGroupBox("المواد المطلوبة للصيانة")
        apply_arabic_style(group, 12, bold=True)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # اختيار المنتج
        product_layout = QHBoxLayout()
        product_label = QLabel("المنتج:")
        apply_arabic_style(product_label, 10)
        self.product_combo = QComboBox()
        apply_arabic_style(self.product_combo, 10)
        
        quantity_label = QLabel("الكمية:")
        apply_arabic_style(quantity_label, 10)
        self.quantity_spin = QSpinBox()
        apply_arabic_style(self.quantity_spin, 10)
        self.quantity_spin.setRange(1, 9999)
        
        add_item_button = QPushButton("إضافة")
        apply_arabic_style(add_item_button, 10)
        add_item_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        product_layout.addWidget(product_label)
        product_layout.addWidget(self.product_combo, 2)
        product_layout.addWidget(quantity_label)
        product_layout.addWidget(self.quantity_spin)
        product_layout.addWidget(add_item_button)
        
        # قائمة المواد المختارة
        selected_label = QLabel("المواد المختارة:")
        apply_arabic_style(selected_label, 10, bold=True)
        
        self.selected_items_list = QListWidget()
        apply_arabic_style(self.selected_items_list, 10)
        self.selected_items_list.setMaximumHeight(200)
        
        # زر حذف المادة
        remove_item_button = QPushButton("حذف المادة المختارة")
        apply_arabic_style(remove_item_button, 10)
        remove_item_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر إنشاء الأمر
        create_order_button = QPushButton("إنشاء أمر الصيانة")
        apply_arabic_style(create_order_button, 10)
        create_order_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        layout.addLayout(product_layout)
        layout.addWidget(selected_label)
        layout.addWidget(self.selected_items_list)
        layout.addWidget(remove_item_button)
        layout.addWidget(create_order_button)
        
        # ربط الأحداث
        add_item_button.clicked.connect(self.add_item_to_list)
        remove_item_button.clicked.connect(self.remove_item_from_list)
        create_order_button.clicked.connect(self.create_maintenance_order)
        
        return group
        
    def create_orders_table(self):
        """إنشاء جدول أوامر الصيانة"""
        table = QTableWidget()
        apply_arabic_style(table, 10)
        
        # إعداد الأعمدة
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "الرقم", "رقم الأمر", "العميل", "الهاتف", "التاريخ", 
            "الفني", "الحالة", "الوصف"
        ])
        
        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(7, header.Stretch)  # عمود الوصف
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        return table
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر تحديث حالة الأمر
        update_status_button = QPushButton("تحديث حالة الأمر")
        apply_arabic_style(update_status_button, 10)
        update_status_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر حذف أمر
        delete_button = QPushButton("حذف أمر")
        apply_arabic_style(delete_button, 10)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر طباعة
        print_button = QPushButton("طباعة أمر")
        apply_arabic_style(print_button, 10)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        
        # زر تحديث
        refresh_button = QPushButton("تحديث")
        apply_arabic_style(refresh_button, 10)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        
        # زر إغلاق
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(update_status_button)
        layout.addWidget(delete_button)
        layout.addWidget(print_button)
        layout.addWidget(refresh_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        update_status_button.clicked.connect(self.update_order_status)
        delete_button.clicked.connect(self.delete_order)
        print_button.clicked.connect(self.print_order)
        refresh_button.clicked.connect(self.load_data)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_products()
        self.load_technicians()
        self.load_orders_data()
        self.generate_order_number()
        
    def load_products(self):
        """تحميل قائمة المنتجات من جدول إدارة المنتجات"""
        try:
            # جلب المنتجات مباشرة من جدول products
            products = self.db_manager.fetch_all("""
                SELECT id, name, category, unit_price, unit_type, stock_quantity, min_stock_level
                FROM products
                WHERE stock_quantity > 0
                ORDER BY category, name
            """)

            self.product_combo.clear()
            if not products:
                self.product_combo.addItem("لا توجد منتجات متاحة في المخزون", None)
                return

            for product in products:
                # عرض المنتج لأمر الصيانة
                unit_type = product['unit_type'] if product['unit_type'] else 'قطعة'
                display_text = f"{product['name']} ({product['category']}) - متوفر: {product['stock_quantity']} {unit_type}"
                self.product_combo.addItem(display_text, product)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المنتجات من إدارة المنتجات: {e}")
            # في حالة الخطأ، أضف خيار افتراضي
            self.product_combo.clear()
            self.product_combo.addItem("خطأ في تحميل المنتجات", None)
            
    def load_technicians(self):
        """تحميل قائمة الفنيين"""
        try:
            technicians = self.db_manager.fetch_all("""
                SELECT id, name FROM workers 
                WHERE work_type LIKE '%فني%' OR work_type LIKE '%صيانة%'
                AND is_active = 1 ORDER BY name
            """)
            
            self.technician_combo.clear()
            self.technician_combo.addItem("غير محدد", None)
            for technician in technicians:
                self.technician_combo.addItem(technician['name'], technician)
                
        except Exception as e:
            # في حالة عدم وجود فنيين، أضف خيارات افتراضية
            self.technician_combo.clear()
            self.technician_combo.addItems(["غير محدد", "فني عام", "فني شبكات"])
            
    def load_orders_data(self):
        """تحميل بيانات أوامر الصيانة"""
        try:
            orders = self.db_manager.fetch_all("""
                SELECT id, order_number, customer_name, customer_phone, 
                       order_date, technician_name, status, description, created_at
                FROM maintenance_orders 
                ORDER BY order_date DESC, created_at DESC
            """)
            
            self.orders_table.setRowCount(len(orders))
            
            for row, order in enumerate(orders):
                self.orders_table.setItem(row, 0, QTableWidgetItem(str(order['id'])))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order['order_number']))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order['customer_name']))
                self.orders_table.setItem(row, 3, QTableWidgetItem(order['customer_phone'] or ''))
                self.orders_table.setItem(row, 4, QTableWidgetItem(order['order_date']))
                self.orders_table.setItem(row, 5, QTableWidgetItem(order['technician_name'] or 'غير محدد'))
                self.orders_table.setItem(row, 6, QTableWidgetItem(order['status']))
                self.orders_table.setItem(row, 7, QTableWidgetItem(order['description'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات أوامر الصيانة: {e}")
            
    def generate_order_number(self):
        """إنشاء رقم أمر جديد"""
        try:
            # جلب آخر رقم أمر
            last_order = self.db_manager.fetch_one("""
                SELECT order_number FROM maintenance_orders 
                ORDER BY id DESC LIMIT 1
            """)
            
            if last_order and last_order['order_number']:
                # استخراج الرقم من آخر أمر
                last_number = int(last_order['order_number'].replace('MNT-', ''))
                new_number = last_number + 1
            else:
                new_number = 1
                
            order_number = f"MNT-{new_number:06d}"
            self.order_number_edit.setText(order_number)
            
        except Exception as e:
            # في حالة الخطأ، استخدم رقم افتراضي
            self.order_number_edit.setText("MNT-000001")
            
    def add_item_to_list(self):
        """إضافة مادة للقائمة من المنتجات المدارة"""
        product_data = self.product_combo.currentData()
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج من قائمة المنتجات")
            return

        quantity = self.quantity_spin.value()
        if quantity <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
            return

        # التحقق من المخزون المتاح (استخدام الحقل الصحيح)
        available_stock = product_data['stock_quantity']
        if quantity > available_stock:
            QMessageBox.warning(self, "تحذير",
                              f"الكمية المطلوبة ({quantity}) أكبر من المخزون المتاح ({available_stock} {product_data['unit_type']})")
            return

        # إضافة للقائمة مع معلومات مفصلة
        item_text = f"{product_data['name']} ({product_data['category']}) - الكمية: {quantity} {product_data['unit_type']}"
        item_data = {
            'product_id': product_data['id'],
            'product_name': product_data['name'],
            'product_category': product_data['category'],
            'unit_type': product_data['unit_type'],
            'quantity': quantity
        }
        
        list_item = QListWidgetItem(item_text)
        list_item.setData(Qt.UserRole, item_data)
        self.selected_items_list.addItem(list_item)
        
        # إعادة تعيين الكمية
        self.quantity_spin.setValue(1)
        
    def remove_item_from_list(self):
        """حذف مادة من القائمة"""
        current_item = self.selected_items_list.currentItem()
        if current_item:
            self.selected_items_list.takeItem(self.selected_items_list.row(current_item))
            
    def create_maintenance_order(self):
        """إنشاء أمر صيانة"""
        QMessageBox.information(self, "قريباً", "ميزة إنشاء أمر الصيانة قيد التطوير")
        
    def update_order_status(self):
        """تحديث حالة الأمر"""
        QMessageBox.information(self, "قريباً", "ميزة تحديث حالة الأمر قيد التطوير")
        
    def delete_order(self):
        """حذف أمر صيانة"""
        QMessageBox.information(self, "قريباً", "ميزة حذف أمر الصيانة قيد التطوير")
        
    def print_order(self):
        """طباعة أمر الصيانة"""
        QMessageBox.information(self, "قريباً", "ميزة طباعة أمر الصيانة قيد التطوير")

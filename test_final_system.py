#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للنظام المحدث
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.unified_treasury_manager import UnifiedTreasuryManager

def test_final_system():
    """اختبار نهائي للنظام"""
    
    print("🧪 اختبار نهائي للنظام المحدث...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = UnifiedTreasuryManager(db)
    
    user_id = 1
    
    try:
        print("\n=== اختبار سريع ===")
        
        # 1. فتح الصندوق
        print("1️⃣ فتح الصندوق...")
        treasury_manager.open_cash_box(user_id=user_id)
        
        # 2. إضافة مبيعات
        print("2️⃣ إضافة مبيعات...")
        treasury_manager.add_to_daily_treasury(user_id, 'SYP', 200000)
        
        # 3. فحص الرصيد
        balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"3️⃣ الرصيد اليومي: {balance:,} ل.س")
        
        # 4. إغلاق الصندوق
        print("4️⃣ إغلاق الصندوق...")
        close_result = treasury_manager.close_cash_box(user_id=user_id)
        
        # 5. فحص الرصيد الرئيسي
        main_balance = treasury_manager.get_main_balance(user_id, 'SYP')
        print(f"5️⃣ الرصيد الرئيسي: {main_balance:,} ل.س")
        
        print(f"\n✅ النتائج:")
        print(f"  • فتح الصندوق: ✅")
        print(f"  • إضافة المبيعات: ✅")
        print(f"  • الرصيد اليومي: {balance:,} ل.س")
        print(f"  • إغلاق الصندوق: {'✅' if close_result else '❌'}")
        print(f"  • الرصيد الرئيسي: {main_balance:,} ل.س")
        
        if close_result and main_balance > 0:
            print(f"\n🎉 النظام يعمل بشكل صحيح!")
            return True
        else:
            print(f"\n❌ هناك مشكلة في النظام")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_final_system()

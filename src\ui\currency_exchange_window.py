# -*- coding: utf-8 -*-
"""
نافذة صرف العملات (شراء الدولار)
Currency Exchange Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit, QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency

# دالة الحصول على الشيفت الحالي
def get_current_shift(db_manager, user_id, user_name):
    """الحصول على الشيفت الحالي للمستخدم أو إنشاء واحد جديد"""
    from datetime import datetime

    today = datetime.now().strftime('%Y-%m-%d')

    # البحث عن شيفت مفتوح لليوم
    current_shift = db_manager.fetch_one("""
        SELECT id FROM shifts
        WHERE user_id = ? AND shift_date = ? AND status = 'open'
    """, (user_id, today))

    if current_shift:
        return current_shift['id']

    # إنشاء شيفت جديد
    try:
        cursor = db_manager.execute_query("""
            INSERT INTO shifts (user_id, user_name, shift_date, start_time, status)
            VALUES (?, ?, ?, ?, 'open')
        """, (user_id, user_name, today, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

        return cursor.lastrowid if hasattr(cursor, 'lastrowid') else 1
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشيفت: {e}")
        return None

class CurrencyExchangeWindow(QDialog):
    """نافذة صرف العملات"""
    
    exchange_completed = pyqtSignal(dict)
    
    def __init__(self, db_manager, treasury_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.treasury_manager = treasury_manager
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("صرف العملات - شراء الدولار")
        self.setGeometry(100, 100, 700, 600)
        self.setModal(True)
        
        apply_arabic_style(self, 10)
        
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("صرف العملات - شراء الدولار من الخزينة اليومية")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        
        # معلومات الخزينة الحالية
        treasury_info_group = self.create_treasury_info_group()
        
        # معلومات الصرف
        exchange_info_group = self.create_exchange_info_group()
        
        # ملخص العملية
        summary_group = self.create_summary_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(treasury_info_group)
        main_layout.addWidget(exchange_info_group)
        main_layout.addWidget(summary_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_treasury_info_group(self):
        """إنشاء مجموعة معلومات الخزينة"""
        group = QGroupBox("الخزينة اليومية (من الشيفتات المغلقة)")
        apply_arabic_style(group, 10, bold=True)

        layout = QGridLayout(group)
        layout.setSpacing(10)

        # رصيد الليرة السورية من الخزينة اليومية
        syp_label = QLabel("الرصيد المتاح للصرف:")
        apply_arabic_style(syp_label, 10)
        self.syp_balance_label = QLabel("0 ل.س")
        apply_arabic_style(self.syp_balance_label, 12, bold=True)
        self.syp_balance_label.setStyleSheet("color: #27ae60;")

        # رصيد الدولار في الخزينة العامة
        usd_label = QLabel("رصيد الدولار الحالي:")
        apply_arabic_style(usd_label, 10)
        self.usd_balance_label = QLabel("$0")
        apply_arabic_style(self.usd_balance_label, 12, bold=True)
        self.usd_balance_label.setStyleSheet("color: #3498db;")
        
        layout.addWidget(syp_label, 0, 0)
        layout.addWidget(self.syp_balance_label, 0, 1)
        layout.addWidget(usd_label, 0, 2)
        layout.addWidget(self.usd_balance_label, 0, 3)
        
        return group
        
    def create_exchange_info_group(self):
        """إنشاء مجموعة معلومات الصرف"""
        group = QGroupBox("تفاصيل عملية الصرف")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # سعر الصرف
        rate_label = QLabel("سعر صرف الدولار:")
        apply_arabic_style(rate_label, 10)
        self.exchange_rate_spin = QDoubleSpinBox()
        apply_arabic_style(self.exchange_rate_spin, 10)
        self.exchange_rate_spin.setRange(1000, 50000)
        self.exchange_rate_spin.setValue(15000)
        self.exchange_rate_spin.setSuffix(" ل.س")
        
        # المبلغ بالليرة السورية
        syp_amount_label = QLabel("المبلغ بالليرة السورية:")
        apply_arabic_style(syp_amount_label, 10)
        self.syp_amount_spin = QDoubleSpinBox()
        apply_arabic_style(self.syp_amount_spin, 10)
        self.syp_amount_spin.setRange(15000, 10000000)
        self.syp_amount_spin.setSuffix(" ل.س")
        self.syp_amount_spin.setValue(150000)
        
        # المبلغ بالدولار (محسوب تلقائياً)
        usd_amount_label = QLabel("المبلغ بالدولار:")
        apply_arabic_style(usd_amount_label, 10)
        self.usd_amount_label = QLabel("$10")
        apply_arabic_style(self.usd_amount_label, 12, bold=True)
        self.usd_amount_label.setStyleSheet("color: #e74c3c;")
        
        # تاريخ العملية
        date_label = QLabel("تاريخ العملية:")
        apply_arabic_style(date_label, 10)
        self.date_edit = QDateEdit()
        apply_arabic_style(self.date_edit, 10)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        layout.addWidget(rate_label, 0, 0)
        layout.addWidget(self.exchange_rate_spin, 0, 1)
        layout.addWidget(syp_amount_label, 1, 0)
        layout.addWidget(self.syp_amount_spin, 1, 1)
        layout.addWidget(usd_amount_label, 1, 2)
        layout.addWidget(self.usd_amount_label, 1, 3)
        layout.addWidget(date_label, 2, 0)
        layout.addWidget(self.date_edit, 2, 1, 1, 3)
        
        return group
        
    def create_summary_group(self):
        """إنشاء مجموعة الملخص"""
        group = QGroupBox("ملخص العملية")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # الرصيد الجديد بالليرة
        new_syp_label = QLabel("الرصيد الجديد بالليرة:")
        apply_arabic_style(new_syp_label, 10)
        self.new_syp_balance_label = QLabel("0 ل.س")
        apply_arabic_style(self.new_syp_balance_label, 11, bold=True)
        self.new_syp_balance_label.setStyleSheet("color: #f39c12;")
        
        # الرصيد الجديد بالدولار
        new_usd_label = QLabel("الرصيد الجديد بالدولار:")
        apply_arabic_style(new_usd_label, 10)
        self.new_usd_balance_label = QLabel("$0")
        apply_arabic_style(self.new_usd_balance_label, 11, bold=True)
        self.new_usd_balance_label.setStyleSheet("color: #f39c12;")
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        apply_arabic_style(notes_label, 10)
        self.notes_edit = QTextEdit()
        apply_arabic_style(self.notes_edit, 10)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(60)
        
        layout.addWidget(new_syp_label, 0, 0)
        layout.addWidget(self.new_syp_balance_label, 0, 1)
        layout.addWidget(new_usd_label, 0, 2)
        layout.addWidget(self.new_usd_balance_label, 0, 3)
        layout.addWidget(notes_label, 1, 0)
        layout.addWidget(self.notes_edit, 1, 1, 1, 3)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        exchange_button = QPushButton("تنفيذ عملية الصرف")
        apply_arabic_style(exchange_button, 10, bold=True)
        exchange_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        apply_arabic_style(cancel_button, 10)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addStretch()
        layout.addWidget(cancel_button)
        layout.addWidget(exchange_button)
        
        exchange_button.clicked.connect(self.execute_exchange)
        cancel_button.clicked.connect(self.reject)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات من الخزينة الرئيسية (الصناديق المغلقة)"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            # قراءة رصيد الليرة السورية من الخزينة اليومية الإجمالية (مجموع جميع المستخدمين)
            syp_balance = self.treasury_manager.get_total_daily_balance(currency_type='SYP')

            print(f"📊 رصيد الليرة السورية في الخزينة اليومية: {syp_balance:,} ل.س")

            # تحميل رصيد الدولار من الخزينة اليومية
            usd_balance = self.treasury_manager.get_daily_balance(
                user_id=self.current_user['id'],
                currency_type='USD'
            )

            # تحديث التسميات
            self.syp_balance_label.setText(format_currency(max(0, syp_balance)))
            self.usd_balance_label.setText(f"${usd_balance:,.2f}")

            # تحديد الحد الأقصى للمبلغ المتاح
            self.syp_amount_spin.setMaximum(max(0, syp_balance))

            # تحديث لون الرصيد حسب المبلغ المتاح
            if syp_balance > 0:
                self.syp_balance_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            elif syp_balance < 0:
                self.syp_balance_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.syp_balance_label.setStyleSheet("color: #f39c12; font-weight: bold;")

            # تحميل سعر الصرف من الإعدادات
            exchange_rate_setting = self.db_manager.fetch_one("""
                SELECT value FROM settings WHERE key = 'exchange_rate_usd'
            """)

            if exchange_rate_setting:
                self.exchange_rate_spin.setValue(float(exchange_rate_setting['value']))

            # حساب القيم الأولية
            self.calculate_amounts()

            print(f"💰 الرصيد المتاح للصرف من الخزينة اليومية: {syp_balance:,} ل.س")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {e}")
            
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.exchange_rate_spin.valueChanged.connect(self.calculate_amounts)
        self.syp_amount_spin.valueChanged.connect(self.calculate_amounts)
        

    def force_refresh_interface(self):
        """تحديث قوي للواجهة"""
        try:
            print("🔄 تحديث قوي لواجهة شراء الدولار...")
            
            # إعادة تحميل البيانات من قاعدة البيانات
            self.load_data()
            
            # تحديث الحسابات
            self.calculate_amounts()
            
            # إجبار إعادة رسم الواجهة
            self.repaint()
            self.update()
            
            # تحديث النافذة الأب إذا كانت موجودة
            if self.parent():
                self.parent().repaint()
                self.parent().update()
            
            print("✅ تم التحديث القوي لواجهة شراء الدولار")
            
        except Exception as e:
            print(f"❌ خطأ في التحديث القوي: {e}")

    def calculate_amounts(self):
        """حساب المبالغ والأرصدة الجديدة"""
        try:
            exchange_rate = self.exchange_rate_spin.value()
            syp_amount = self.syp_amount_spin.value()
            
            # حساب المبلغ بالدولار
            usd_amount = syp_amount / exchange_rate
            self.usd_amount_label.setText(f"${usd_amount:.2f}")
            
            # حساب الأرصدة الجديدة
            current_syp = float(self.syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())
            current_usd = float(self.usd_balance_label.text().replace("$", "").replace(",", "").strip())
            
            new_syp_balance = current_syp - syp_amount
            new_usd_balance = current_usd + usd_amount
            
            self.new_syp_balance_label.setText(format_currency(new_syp_balance))
            self.new_usd_balance_label.setText(f"${new_usd_balance:,.2f}")
            
        except:
            pass
            
    def execute_exchange(self):
        """تنفيذ عملية الصرف من الخزينة اليومية"""
        try:
            # التحقق من صحة البيانات
            syp_amount = self.syp_amount_spin.value()
            current_syp = float(self.syp_balance_label.text().replace("ل.س", "").replace(",", "").strip())

            if current_syp <= 0:
                QMessageBox.warning(self, "تحذير", "لا يوجد رصيد متاح في الخزينة اليومية\nيجب إغلاق الصناديق أولاً لتكوين الخزينة اليومية")
                return

            if syp_amount > current_syp:
                QMessageBox.warning(self, "تحذير", "المبلغ المطلوب أكبر من الرصيد المتاح في الخزينة اليومية")
                return

            if syp_amount <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return
            
            # تأكيد العملية
            exchange_rate = self.exchange_rate_spin.value()
            usd_amount = syp_amount / exchange_rate
            
            reply = QMessageBox.question(
                self,
                "تأكيد عملية الصرف",
                f"هل تريد تنفيذ عملية صرف العملة؟\n\n"
                f"المبلغ: {format_currency(syp_amount)}\n"
                f"سعر الصرف: {format_currency(exchange_rate)}\n"
                f"ستحصل على: ${usd_amount:.2f}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # تنفيذ العملية باستخدام النظام الموحد
                try:
                    # تنفيذ تحويل العملة من الخزينة اليومية الإجمالية
                    exchange_success = self.treasury_manager.exchange_currency_from_total_daily(
                        from_currency='SYP',
                        to_currency='USD',
                        amount_from=syp_amount,
                        exchange_rate=exchange_rate
                    )

                    if not exchange_success:
                        raise Exception("فشل في تنفيذ عملية تحويل العملة")

                    print(f"✅ تم تحويل {syp_amount:,} ل.س إلى ${usd_amount:.2f} في الخزينة اليومية")
                    
                    # إعداد بيانات النتيجة
                    exchange_data = {
                        'syp_amount': syp_amount,
                        'usd_amount': usd_amount,
                        'exchange_rate': exchange_rate,
                        'date': self.date_edit.date().toString("yyyy-MM-dd"),
                        'notes': self.notes_edit.toPlainText().strip()
                    }
                    
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم تنفيذ عملية الصرف بنجاح\n\n"
                        f"تم صرف: {format_currency(syp_amount)}\n"
                        f"تم الحصول على: ${usd_amount:.2f}"
                    )

                    # تحديث قوي للواجهة
                    self.force_refresh_interface()

                    # إرسال إشارة
                    self.exchange_completed.emit(exchange_data)

                    # لا نغلق النافذة فوراً لنرى التحديث
                    # self.accept()
                    
                except Exception as e:
                    print(f"❌ خطأ في تنفيذ عملية تحويل العملة: {e}")
                    QMessageBox.critical(self, "خطأ", f"فشل في تنفيذ عملية الصرف:\n{e}")
                    return
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ عملية الصرف: {e}")

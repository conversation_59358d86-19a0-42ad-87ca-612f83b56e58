#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جدول المستخدمين من واجهة إدارة المستخدمين
"""

import sys
import os
import hashlib
sys.path.append('src')

def check_user_management_users():
    """فحص المستخدمين من واجهة إدارة المستخدمين"""
    
    print("🔍 فحص المستخدمين من واجهة إدارة المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # فحص جميع الجداول الموجودة
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table'")
        print(f"📊 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  • {table[0]}")
        
        # فحص جدول المستخدمين
        print(f"\n🔍 فحص جدول users:")
        
        # فحص بنية الجدول
        columns = db.fetch_all("PRAGMA table_info(users)")
        print(f"📋 أعمدة جدول users ({len(columns)}):")
        for col in columns:
            print(f"  • {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # فحص المستخدمين الموجودين
        users = db.fetch_all("SELECT * FROM users")
        print(f"\n👥 المستخدمين في جدول users ({len(users)}):")
        
        if users:
            for user in users:
                print(f"  • ID: {user[0]}")
                print(f"    اسم المستخدم: '{user[1]}'")
                print(f"    كلمة المرور المشفرة: {user[2][:20] if user[2] else 'None'}...")
                print(f"    الاسم الكامل: '{user[3] if len(user) > 3 else 'غير محدد'}'")
                print(f"    البريد الإلكتروني: '{user[4] if len(user) > 4 and user[4] else 'غير محدد'}'")
                print(f"    الدور: '{user[5] if len(user) > 5 else 'غير محدد'}'")
                print(f"    نشط: {user[6] if len(user) > 6 else 'غير محدد'}")
                print()
        else:
            print("  ❌ لا يوجد مستخدمين!")
        
        return users
        
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_user_management_interface():
    """اختبار واجهة إدارة المستخدمين"""
    
    print("\n🧪 اختبار واجهة إدارة المستخدمين...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.users_management_window import UsersManagementWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم الحالي
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء واجهة إدارة المستخدمين
        print("🔄 إنشاء واجهة إدارة المستخدمين...")
        window = UsersManagementWindow(db, current_user)
        
        # تحميل المستخدمين
        print("📋 تحميل المستخدمين من الواجهة...")
        window.load_users()
        
        # فحص عدد المستخدمين في الجدول
        row_count = window.users_table.rowCount()
        print(f"📊 عدد المستخدمين في واجهة الإدارة: {row_count}")
        
        # عرض المستخدمين من الواجهة
        if row_count > 0:
            print("👥 المستخدمين في واجهة الإدارة:")
            for row in range(row_count):
                user_id = window.users_table.item(row, 0).text() if window.users_table.item(row, 0) else 'N/A'
                username = window.users_table.item(row, 1).text() if window.users_table.item(row, 1) else 'N/A'
                full_name = window.users_table.item(row, 2).text() if window.users_table.item(row, 2) else 'N/A'
                role = window.users_table.item(row, 3).text() if window.users_table.item(row, 3) else 'N/A'
                status = window.users_table.item(row, 5).text() if window.users_table.item(row, 5) else 'N/A'
                
                print(f"  • ID: {user_id}, اسم المستخدم: '{username}', الاسم: '{full_name}', الدور: '{role}', الحالة: {status}")
        else:
            print("❌ لا يوجد مستخدمين في واجهة الإدارة!")
        
        return row_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إدارة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_test_user_via_management():
    """إضافة مستخدم تجريبي عبر واجهة الإدارة"""
    
    print("\n👤 إضافة مستخدم تجريبي عبر واجهة الإدارة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم التجريبي
        username = 'test_admin'
        password = '123'
        full_name = 'مدير تجريبي'
        email = '<EMAIL>'
        role = 'admin'
        is_active = 1
        
        print(f"📝 بيانات المستخدم التجريبي:")
        print(f"  • اسم المستخدم: {username}")
        print(f"  • كلمة المرور: {password}")
        print(f"  • الاسم الكامل: {full_name}")
        print(f"  • البريد الإلكتروني: {email}")
        print(f"  • الدور: {role}")
        
        # تشفير كلمة المرور (نفس الطريقة في واجهة الإدارة)
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        print(f"  • كلمة المرور المشفرة: {password_hash}")
        
        # حذف المستخدم إذا كان موجوداً
        db.execute_query("DELETE FROM users WHERE username = ?", (username,))
        
        # إضافة المستخدم (نفس الاستعلام في واجهة الإدارة)
        result = db.execute_query("""
            INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        """, (username, password_hash, full_name, email, role, is_active))
        
        if result:
            print("✅ تم إضافة المستخدم التجريبي بنجاح")
            
            # التحقق من المستخدم
            added_user = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
            
            if added_user:
                print("✅ تم العثور على المستخدم في قاعدة البيانات")
                return True
            else:
                print("❌ لم يتم العثور على المستخدم بعد الإضافة")
                return False
        else:
            print("❌ فشل في إضافة المستخدم التجريبي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدم التجريبي: {e}")
        return False

def test_login_with_management_users():
    """اختبار تسجيل الدخول مع مستخدمي واجهة الإدارة"""
    
    print("\n🧪 اختبار تسجيل الدخول مع مستخدمي واجهة الإدارة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # الحصول على جميع المستخدمين من الجدول
        users = db.fetch_all("SELECT username, password_hash, is_active FROM users WHERE is_active = 1")
        
        print(f"👥 اختبار {len(users)} مستخدم من جدول users...")
        
        success_count = 0
        
        for user in users:
            username = user['username']
            stored_hash = user['password_hash']
            
            print(f"\n🔍 اختبار المستخدم: {username}")
            
            # اختبار بكلمة المرور الشائعة
            test_passwords = ['123', 'admin', 'password']
            found_password = False
            
            for password in test_passwords:
                test_hash = hashlib.sha256(password.encode()).hexdigest()
                
                if test_hash == stored_hash:
                    print(f"  ✅ كلمة المرور الصحيحة: '{password}'")
                    
                    # اختبار تسجيل الدخول (محاكاة واجهة تسجيل الدخول)
                    login_test = db.fetch_one("""
                        SELECT * FROM users 
                        WHERE username = ? AND password_hash = ? AND is_active = 1
                    """, (username, test_hash))
                    
                    if login_test:
                        print(f"  ✅ تسجيل الدخول نجح")
                        success_count += 1
                    else:
                        print(f"  ❌ تسجيل الدخول فشل")
                    
                    found_password = True
                    break
            
            if not found_password:
                print(f"  ❌ لم يتم العثور على كلمة المرور")
        
        print(f"\n📊 النتيجة: {success_count}/{len(users)} مستخدمين يمكنهم تسجيل الدخول")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 فحص جدول المستخدمين من واجهة إدارة المستخدمين")
    print("=" * 70)
    
    # فحص المستخدمين الموجودين
    users = check_user_management_users()
    
    # اختبار واجهة إدارة المستخدمين
    management_test = test_user_management_interface()
    
    # إضافة مستخدم تجريبي إذا لم يكن هناك مستخدمين
    if len(users) == 0:
        print("\n⚠️ لا يوجد مستخدمين - سيتم إضافة مستخدم تجريبي")
        user_added = add_test_user_via_management()
    else:
        user_added = True
    
    # اختبار تسجيل الدخول
    login_test = test_login_with_management_users()
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"  • فحص المستخدمين: {'✅ نجح' if len(users) > 0 or user_added else '❌ فشل'}")
    print(f"  • اختبار واجهة الإدارة: {'✅ نجح' if management_test else '❌ فشل'}")
    print(f"  • إضافة/وجود المستخدمين: {'✅ نجح' if user_added else '❌ فشل'}")
    print(f"  • اختبار تسجيل الدخول: {'✅ نجح' if login_test else '❌ فشل'}")
    
    if len(users) > 0 or user_added:
        print("\n📋 المستخدمين المتاحين:")
        final_users = check_user_management_users()
        
        print("\n💡 للاستخدام:")
        print("  1. افتح واجهة إدارة المستخدمين")
        print("  2. أضف مستخدمين جدد أو استخدم الموجودين")
        print("  3. استخدم نفس اسم المستخدم وكلمة المرور في تسجيل الدخول")
        
    else:
        print("\n❌ لا يوجد مستخدمين في الجدول!")
        print("💡 يرجى إضافة مستخدمين من واجهة إدارة المستخدمين أولاً")

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نافذة تسليم للعمال
Worker Delivery Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton, 
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from utils.arabic_support import create_arabic_font, apply_arabic_style, format_currency
except ImportError:
    # في حالة عدم وجود الملف، استخدم دوال بديلة
    def create_arabic_font(size, bold=False):
        from PyQt5.QtGui import QFont
        font = QFont("Arial", size)
        font.setBold(bold)
        return font
    def apply_arabic_style(widget, size, bold=False):
        widget.setFont(create_arabic_font(size, bold))
    def format_currency(amount):
        return f"{amount:,.0f} ل.س"

class WorkerDeliveryWindow(QDialog):
    """نافذة تسليم للعمال"""
    
    def __init__(self, db_manager, inventory_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = inventory_manager
        self.config_manager = config_manager
        self.current_user = current_user

        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسليم للعمال")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel("تسليم مواد للعمال")
        apply_arabic_style(title_label, 16, bold=True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # مجموعة اختيار العامل والمنتج
        selection_group = self.create_selection_group()
        layout.addWidget(selection_group)
        
        # جدول المواد المسلمة
        self.create_delivery_table()
        layout.addWidget(self.delivery_table)
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
        
    def create_selection_group(self):
        """إنشاء مجموعة اختيار العامل والمنتج"""
        group = QGroupBox("تسليم مواد")
        apply_arabic_style(group, 10, bold=True)
        
        layout = QGridLayout(group)
        
        # اختيار العامل
        worker_label = QLabel("العامل:")
        apply_arabic_style(worker_label, 10)
        self.worker_combo = QComboBox()
        apply_arabic_style(self.worker_combo, 10)
        
        # اختيار المنتج
        product_label = QLabel("المنتج:")
        apply_arabic_style(product_label, 10)
        self.product_combo = QComboBox()
        apply_arabic_style(self.product_combo, 10)
        
        # الكمية
        quantity_label = QLabel("الكمية:")
        apply_arabic_style(quantity_label, 10)
        self.quantity_spin = QSpinBox()
        apply_arabic_style(self.quantity_spin, 10)
        self.quantity_spin.setRange(1, 9999)
        
        # زر إضافة
        add_button = QPushButton("إضافة للقائمة")
        apply_arabic_style(add_button, 10, bold=True)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_button.clicked.connect(self.add_item_to_delivery)
        
        layout.addWidget(worker_label, 0, 0)
        layout.addWidget(self.worker_combo, 0, 1)
        layout.addWidget(product_label, 0, 2)
        layout.addWidget(self.product_combo, 0, 3)
        layout.addWidget(quantity_label, 1, 0)
        layout.addWidget(self.quantity_spin, 1, 1)
        layout.addWidget(add_button, 1, 2, 1, 2)
        
        return group
        
    def create_delivery_table(self):
        """إنشاء جدول المواد المسلمة"""
        self.delivery_table = QTableWidget()
        apply_arabic_style(self.delivery_table, 10)
        
        # إعداد الأعمدة
        headers = ["العامل", "المنتج", "الكمية", "الوحدة", "التاريخ"]
        self.delivery_table.setColumnCount(len(headers))
        self.delivery_table.setHorizontalHeaderLabels(headers)
        
        # إعداد خصائص الجدول
        self.delivery_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.delivery_table.setAlternatingRowColors(True)
        self.delivery_table.horizontalHeader().setStretchLastSection(True)
        self.delivery_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ التسليم")
        apply_arabic_style(save_button, 10, bold=True)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        clear_button = QPushButton("مسح القائمة")
        apply_arabic_style(clear_button, 10)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        apply_arabic_style(close_button, 10)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        layout.addWidget(save_button)
        layout.addWidget(clear_button)
        layout.addStretch()
        layout.addWidget(close_button)
        
        # ربط الأزرار
        save_button.clicked.connect(self.save_delivery)
        clear_button.clicked.connect(self.clear_delivery_list)
        close_button.clicked.connect(self.accept)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات"""
        self.load_workers()
        self.load_products()
        
    def load_workers(self):
        """تحميل قائمة العمال"""
        try:
            workers = self.db_manager.fetch_all("""
                SELECT id, name FROM workers 
                WHERE is_active = 1 OR is_active IS NULL
                ORDER BY name
            """)
            
            self.worker_combo.clear()
            self.worker_combo.addItem("اختر العامل...", None)
            
            for worker in workers:
                self.worker_combo.addItem(worker['name'], worker['id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العمال: {e}")
            
    def load_products(self):
        """تحميل قائمة المنتجات من النظام الموحد"""
        try:
            # جلب المنتجات من النظام الموحد
            if self.inventory_manager:
                products = self.inventory_manager.get_all_products()
                # فلترة المنتجات التي لها مخزون متوفر
                products = [p for p in products if p.get('current_stock', 0) > 0]
            else:
                # الطريقة القديمة كبديل
                products = self.db_manager.fetch_all("""
                    SELECT id, name, category, sale_price as unit_price, unit_type,
                           current_stock as stock_quantity, min_stock as min_stock_level
                    FROM unified_products
                    WHERE current_stock > 0 AND is_active = 1
                    ORDER BY category, name
                """)

            self.product_combo.clear()
            self.product_combo.addItem("اختر المنتج...", None)

            for product in products:
                # عرض المنتج للتسليم للعمال من النظام الموحد
                unit_type = product.get('unit_type', 'قطعة')
                current_stock = product.get('current_stock', product.get('stock_quantity', 0))
                category = product.get('category', '')

                display_text = f"{product['name']} ({category}) - متوفر: {current_stock:.1f} {unit_type}"

                # تحديث بيانات المنتج للتوافق
                product_data = {
                    'id': product['id'],
                    'name': product['name'],
                    'category': category,
                    'unit_type': unit_type,
                    'stock_quantity': current_stock,
                    'unit_price': product.get('sale_price', product.get('unit_price', 0))
                }

                self.product_combo.addItem(display_text, product_data)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المنتجات: {e}")
            
    def add_item_to_delivery(self):
        """إضافة مادة لقائمة التسليم"""
        worker_id = self.worker_combo.currentData()
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        
        if not worker_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل")
            return
            
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج")
            return
            
        if quantity > product_data['stock_quantity']:
            QMessageBox.warning(self, "تحذير", 
                              f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({product_data['stock_quantity']})")
            return
            
        # إضافة للجدول
        row = self.delivery_table.rowCount()
        self.delivery_table.insertRow(row)
        
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        self.delivery_table.setItem(row, 0, QTableWidgetItem(self.worker_combo.currentText()))
        self.delivery_table.setItem(row, 1, QTableWidgetItem(product_data['name']))
        self.delivery_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
        self.delivery_table.setItem(row, 3, QTableWidgetItem(product_data['unit_type']))
        self.delivery_table.setItem(row, 4, QTableWidgetItem(current_date))
        
        # حفظ البيانات في الصف
        self.delivery_table.item(row, 0).setData(Qt.UserRole, {
            'worker_id': worker_id,
            'product_id': product_data['id'],
            'quantity': quantity
        })
        
    def clear_delivery_list(self):
        """مسح قائمة التسليم"""
        self.delivery_table.setRowCount(0)
        
    def save_delivery(self):
        """حفظ التسليم باستخدام النظام الموحد"""
        if self.delivery_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد مواد للتسليم")
            return

        try:
            delivery_success = True

            # حفظ كل عنصر في قائمة التسليم
            for row in range(self.delivery_table.rowCount()):
                item_data = self.delivery_table.item(row, 0).data(Qt.UserRole)

                print(f"🚚 تسليم {item_data['quantity']} من المنتج {item_data['product_id']} للعامل {item_data['worker_id']}")

                # حفظ سجل التسليم
                delivery_result = self.db_manager.execute_query("""
                    INSERT INTO worker_deliveries (worker_id, product_id, quantity, delivery_date, user_id)
                    VALUES (?, ?, ?, DATE('now'), ?)
                """, (
                    item_data['worker_id'],
                    item_data['product_id'],
                    item_data['quantity'],
                    self.current_user['id']
                ))

                if delivery_result:
                    delivery_id = delivery_result.lastrowid

                    # استخدام النظام الموحد لنقل المخزون
                    if self.inventory_manager:
                        transfer_success = self.inventory_manager.transfer_to_worker(
                            product_id=item_data['product_id'],
                            worker_id=item_data['worker_id'],
                            quantity=item_data['quantity'],
                            operation_type="delivery",
                            reference_id=delivery_id,
                            notes=f"تسليم مواد للعامل - سجل رقم {delivery_id}",
                            user_id=self.current_user['id']
                        )

                        if transfer_success:
                            print(f"✅ تم نقل {item_data['quantity']} للعامل باستخدام النظام الموحد")
                        else:
                            print(f"❌ فشل في نقل المخزون للعامل")
                            delivery_success = False
                            break
                    else:
                        # الطريقة القديمة كبديل
                        print("⚠️ استخدام الطريقة القديمة لنقل المخزون")

                        # إضافة أو تحديث مخزون العامل
                        existing_stock = self.db_manager.fetch_one("""
                            SELECT quantity FROM worker_inventory
                            WHERE worker_id = ? AND product_id = ?
                        """, (item_data['worker_id'], item_data['product_id']))

                        if existing_stock:
                            # تحديث الكمية الموجودة
                            new_quantity = existing_stock['quantity'] + item_data['quantity']
                            self.db_manager.execute_query("""
                                UPDATE worker_inventory SET quantity = ?
                                WHERE worker_id = ? AND product_id = ?
                            """, (new_quantity, item_data['worker_id'], item_data['product_id']))
                        else:
                            # إضافة مخزون جديد للعامل
                            self.db_manager.execute_query("""
                                INSERT INTO worker_inventory (worker_id, product_id, quantity)
                                VALUES (?, ?, ?)
                            """, (item_data['worker_id'], item_data['product_id'], item_data['quantity']))

                        # خصم من المخزون الرئيسي
                        self.db_manager.execute_query("""
                            UPDATE unified_products
                            SET current_stock = current_stock - ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (item_data['quantity'], item_data['product_id']))
                else:
                    print(f"❌ فشل في حفظ سجل التسليم")
                    delivery_success = False
                    break

            if delivery_success:
                QMessageBox.information(self, "نجح", "تم حفظ التسليم بنجاح")
                self.clear_delivery_list()
                self.load_products()  # تحديث قائمة المنتجات
                print("🎉 تم حفظ جميع التسليمات بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ بعض التسليمات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التسليم: {e}")
            print(f"❌ خطأ في حفظ التسليم: {e}")
            import traceback
            traceback.print_exc()

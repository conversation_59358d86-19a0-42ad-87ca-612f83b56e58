#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تحويل الوحدات
"""

import sys
import os
sys.path.append('src')

def test_units_conversion_system():
    """اختبار نظام تحويل الوحدات"""
    
    print("🧪 اختبار نظام تحويل الوحدات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_inventory_manager import UnifiedInventoryManager
        
        db = DatabaseManager('.')
        inventory_manager = UnifiedInventoryManager(db)
        
        print("✅ تم إنشاء مدير المخزون الموحد")
        
        # إعداد البيانات التجريبية
        print("\n=== إعداد البيانات التجريبية ===")
        
        # إضافة منتجات بوحدات مختلفة
        test_products = [
            {
                'name': 'كبل شبكة Cat6 تجريبي',
                'category': 'كبل',
                'purchase_unit': 'بكرة',
                'sale_unit': 'متر',
                'conversion_factor': 100.0,  # بكرة واحدة = 100 متر
                'purchase_price': 50000,  # سعر البكرة
                'sale_price': 750,  # سعر المتر
                'stock': 0
            },
            {
                'name': 'راوتر TP-Link تجريبي',
                'category': 'راوتر',
                'purchase_unit': 'كرتونة',
                'sale_unit': 'قطعة',
                'conversion_factor': 10.0,  # كرتونة واحدة = 10 قطع
                'purchase_price': 500000,  # سعر الكرتونة
                'sale_price': 75000,  # سعر القطعة
                'stock': 0
            }
        ]
        
        product_ids = []
        for product in test_products:
            result = db.execute_query("""
                INSERT INTO unified_products 
                (name, category, purchase_unit, sale_unit, conversion_factor,
                 purchase_price, sale_price, current_stock, min_stock, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                product['name'], product['category'], product['purchase_unit'], 
                product['sale_unit'], product['conversion_factor'],
                product['purchase_price'], product['sale_price'], 
                product['stock'], 5, 1
            ))
            
            if result:
                product_id = result.lastrowid
                product_ids.append(product_id)
                print(f"✅ تم إضافة منتج: {product['name']} - ID: {product_id}")
                print(f"   وحدة الشراء: {product['purchase_unit']}")
                print(f"   وحدة البيع: {product['sale_unit']}")
                print(f"   معامل التحويل: 1 {product['purchase_unit']} = {product['conversion_factor']} {product['sale_unit']}")
        
        if len(product_ids) >= 2:
            cable_id, router_id = product_ids[:2]
            
            # اختبار 1: شراء كبل بالبكرة
            print("\n=== 1. شراء كبل بالبكرة ===")
            
            cable_purchase_success = inventory_manager.add_stock_with_unit(
                product_id=cable_id,
                quantity=5,  # 5 بكرات
                unit='بكرة',
                operation_type="purchase",
                notes="شراء كبل بالبكرة",
                user_id=1
            )
            
            if cable_purchase_success:
                cable_stock = inventory_manager.get_product_stock(cable_id)
                print(f"✅ تم شراء 5 بكرات كبل")
                print(f"   المخزون بالمتر: {cable_stock} متر")
                print(f"   المتوقع: {5 * 100} متر")
                
                if abs(cable_stock - 500) < 0.1:
                    print("✅ تحويل الوحدة صحيح!")
                else:
                    print(f"❌ خطأ في التحويل: متوقع 500، فعلي {cable_stock}")
                    return False
            else:
                print("❌ فشل في شراء الكبل")
                return False
            
            # اختبار 2: شراء راوتر بالكرتونة
            print("\n=== 2. شراء راوتر بالكرتونة ===")
            
            router_purchase_success = inventory_manager.add_stock_with_unit(
                product_id=router_id,
                quantity=3,  # 3 كراتين
                unit='كرتونة',
                operation_type="purchase",
                notes="شراء راوتر بالكرتونة",
                user_id=1
            )
            
            if router_purchase_success:
                router_stock = inventory_manager.get_product_stock(router_id)
                print(f"✅ تم شراء 3 كراتين راوتر")
                print(f"   المخزون بالقطعة: {router_stock} قطعة")
                print(f"   المتوقع: {3 * 10} قطعة")
                
                if abs(router_stock - 30) < 0.1:
                    print("✅ تحويل الوحدة صحيح!")
                else:
                    print(f"❌ خطأ في التحويل: متوقع 30، فعلي {router_stock}")
                    return False
            else:
                print("❌ فشل في شراء الراوتر")
                return False
            
            # اختبار 3: بيع كبل بالمتر
            print("\n=== 3. بيع كبل بالمتر ===")
            
            cable_sale_success = inventory_manager.remove_stock_with_unit(
                product_id=cable_id,
                quantity=150,  # 150 متر
                unit='متر',
                operation_type="sale",
                notes="بيع كبل بالمتر",
                user_id=1
            )
            
            if cable_sale_success:
                remaining_cable = inventory_manager.get_product_stock(cable_id)
                print(f"✅ تم بيع 150 متر كبل")
                print(f"   المخزون المتبقي: {remaining_cable} متر")
                print(f"   المتوقع: {500 - 150} متر")
                
                if abs(remaining_cable - 350) < 0.1:
                    print("✅ خصم الوحدة صحيح!")
                else:
                    print(f"❌ خطأ في الخصم: متوقع 350، فعلي {remaining_cable}")
                    return False
            else:
                print("❌ فشل في بيع الكبل")
                return False
            
            # اختبار 4: بيع راوتر بالقطعة
            print("\n=== 4. بيع راوتر بالقطعة ===")
            
            router_sale_success = inventory_manager.remove_stock_with_unit(
                product_id=router_id,
                quantity=12,  # 12 قطعة
                unit='قطعة',
                operation_type="sale",
                notes="بيع راوتر بالقطعة",
                user_id=1
            )
            
            if router_sale_success:
                remaining_router = inventory_manager.get_product_stock(router_id)
                print(f"✅ تم بيع 12 قطعة راوتر")
                print(f"   المخزون المتبقي: {remaining_router} قطعة")
                print(f"   المتوقع: {30 - 12} قطعة")
                
                if abs(remaining_router - 18) < 0.1:
                    print("✅ خصم الوحدة صحيح!")
                else:
                    print(f"❌ خطأ في الخصم: متوقع 18، فعلي {remaining_router}")
                    return False
            else:
                print("❌ فشل في بيع الراوتر")
                return False
            
            # اختبار 5: تسليم للعامل بوحدة البيع
            print("\n=== 5. تسليم للعامل بوحدة البيع ===")
            
            # إضافة عامل تجريبي
            worker_result = db.execute_query("""
                INSERT OR IGNORE INTO workers (name, phone, is_active)
                VALUES (?, ?, ?)
            """, ("عامل الوحدات التجريبي", "555666777", 1))
            
            worker_id = db.fetch_one("SELECT id FROM workers WHERE name = 'عامل الوحدات التجريبي'")['id']
            
            # تسليم كبل للعامل بالمتر
            cable_delivery_success = inventory_manager.transfer_to_worker(
                product_id=cable_id,
                worker_id=worker_id,
                quantity=100,  # 100 متر
                operation_type="delivery",
                notes="تسليم كبل للعامل بالمتر",
                user_id=1
            )
            
            if cable_delivery_success:
                remaining_main_cable = inventory_manager.get_product_stock(cable_id)
                worker_cable = inventory_manager.get_worker_stock(worker_id, cable_id)
                
                print(f"✅ تم تسليم 100 متر كبل للعامل")
                print(f"   المخزون الرئيسي: {remaining_main_cable} متر")
                print(f"   مخزون العامل: {worker_cable} متر")
                
                if abs(remaining_main_cable - 250) < 0.1 and abs(worker_cable - 100) < 0.1:
                    print("✅ تسليم العامل صحيح!")
                else:
                    print(f"❌ خطأ في التسليم")
                    return False
            else:
                print("❌ فشل في تسليم الكبل للعامل")
                return False
            
            # اختبار 6: عرض معلومات المنتج مع الوحدات
            print("\n=== 6. معلومات المنتج مع الوحدات ===")
            
            cable_info = inventory_manager.get_product_info_with_units(cable_id)
            router_info = inventory_manager.get_product_info_with_units(router_id)
            
            if cable_info and router_info:
                print(f"📊 معلومات الكبل:")
                print(f"   الاسم: {cable_info['name']}")
                print(f"   وحدة الشراء: {cable_info['purchase_unit']}")
                print(f"   وحدة البيع: {cable_info['sale_unit']}")
                print(f"   معامل التحويل: {cable_info['conversion_factor']}")
                print(f"   المخزون الحالي: {cable_info['current_stock']} {cable_info['sale_unit']}")
                
                print(f"\n📊 معلومات الراوتر:")
                print(f"   الاسم: {router_info['name']}")
                print(f"   وحدة الشراء: {router_info['purchase_unit']}")
                print(f"   وحدة البيع: {router_info['sale_unit']}")
                print(f"   معامل التحويل: {router_info['conversion_factor']}")
                print(f"   المخزون الحالي: {router_info['current_stock']} {router_info['sale_unit']}")
            
            # تنظيف البيانات التجريبية
            print("\n=== تنظيف البيانات التجريبية ===")
            
            # حذف الحركات
            for product_id in product_ids:
                db.execute_query("DELETE FROM unified_inventory_movements WHERE product_id = ?", (product_id,))
            
            # حذف مخزون العامل
            db.execute_query("DELETE FROM worker_inventory WHERE worker_id = ?", (worker_id,))
            
            # حذف المنتجات
            for product_id in product_ids:
                db.execute_query("DELETE FROM unified_products WHERE id = ?", (product_id,))
            
            # حذف العامل
            db.execute_query("DELETE FROM workers WHERE id = ?", (worker_id,))
            
            print("🗑️ تم تنظيف البيانات التجريبية")
            
            return True
        else:
            print("❌ فشل في إعداد المنتجات التجريبية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار نظام تحويل الوحدات")
    print("=" * 60)
    
    # اختبار نظام الوحدات
    units_test = test_units_conversion_system()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • نظام تحويل الوحدات: {'✅ نجح' if units_test else '❌ فشل'}")
    
    if units_test:
        print("\n🎉 نظام تحويل الوحدات يعمل بشكل مثالي!")
        
        print("\n📋 المميزات المحققة:")
        print("  ✅ دعم وحدات شراء وبيع مختلفة")
        print("  ✅ تحويل تلقائي بين الوحدات")
        print("  ✅ شراء بالبكرة/الكرتونة، بيع بالمتر/القطعة")
        print("  ✅ تسليم للعمال بوحدة البيع")
        print("  ✅ حسابات دقيقة للمخزون")
        print("  ✅ تتبع الحركات مع الوحدات الأصلية")
        
        print("\n🔧 أمثلة الاستخدام:")
        print("  📦 الكبل:")
        print("     • الشراء: بالبكرة (1 بكرة = 100 متر)")
        print("     • البيع: بالمتر")
        print("     • التسليم للعامل: بالمتر")
        print("     • الخصم من العامل: بالمتر")
        
        print("  📦 الراوتر:")
        print("     • الشراء: بالكرتونة (1 كرتونة = 10 قطع)")
        print("     • البيع: بالقطعة")
        print("     • التسليم للعامل: بالقطعة")
        print("     • الخصم من العامل: بالقطعة")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. في المشتريات: اشتر بوحدة الشراء (بكرة/كرتونة)")
        print("  3. في تسليم العمال: سلم بوحدة البيع (متر/قطعة)")
        print("  4. في تسليم الراوتر: اخصم بوحدة البيع")
        print("  5. النظام يحول تلقائياً بين الوحدات")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

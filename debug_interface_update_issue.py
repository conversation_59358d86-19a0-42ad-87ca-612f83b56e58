#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة تحديث الواجهات الحقيقية
"""

import sys
import os
sys.path.append('src')

def test_real_interface_behavior():
    """اختبار سلوك الواجهة الحقيقي"""
    
    print("🔍 اختبار سلوك الواجهة الحقيقي...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import QTimer
        from database.database_manager import DatabaseManager
        from ui.enhanced_treasury_transfer_window import EnhancedTreasuryTransferWindow
        
        # إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        db = DatabaseManager('.')
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # تنظيف الأرصدة السالبة
        db.execute_query("UPDATE unified_treasury SET daily_balance = 0 WHERE daily_balance < 0")
        
        # إنشاء الواجهة
        print("🖥️ إنشاء واجهة نقل الخزينة...")
        window = EnhancedTreasuryTransferWindow(db, current_user)
        
        # عرض الواجهة
        window.show()
        
        # فحص الأرصدة الأولية
        initial_daily = window.daily_syp_label.text()
        initial_main = window.main_syp_label.text()
        
        print(f"📺 الأرصدة الأولية في الواجهة:")
        print(f"  • يومي: {initial_daily}")
        print(f"  • رئيسي: {initial_main}")
        
        # إعداد النقل
        window.amount_spin.setValue(30000)
        window.receiver_edit.setText("اختبار تحديث الواجهة")
        
        print(f"\n🔄 محاكاة الضغط على زر تنفيذ النقل...")
        
        # تنفيذ النقل مباشرة (تجاوز رسائل التأكيد)
        try:
            # استخراج البيانات
            selected_currency = window.currency_combo.currentData()
            amount = window.amount_spin.value()
            receiver = window.receiver_edit.text().strip()
            notes = window.notes_edit.toPlainText().strip()
            transfer_date = window.date_edit.date().toString('yyyy-MM-dd')
            
            print(f"  • العملة: {selected_currency}")
            print(f"  • المبلغ: {amount:,}")
            print(f"  • المستلم: {receiver}")
            
            # تنفيذ النقل
            transfer_success = window.treasury_manager.transfer_from_total_daily_to_main(
                currency_type=selected_currency,
                amount=amount,
                date=transfer_date
            )
            
            if transfer_success:
                print("✅ تم النقل في النظام")
                
                # تحديث الواجهة (كما يحدث في execute_transfer)
                print("🔄 تحديث الواجهة...")
                window.load_treasury_data()
                window.update_balance_labels()
                window.update_max_amount()
                window.update_summary()
                
                # فحص الأرصدة بعد التحديث
                updated_daily = window.daily_syp_label.text()
                updated_main = window.main_syp_label.text()
                
                print(f"📺 الأرصدة بعد التحديث:")
                print(f"  • يومي: {updated_daily}")
                print(f"  • رئيسي: {updated_main}")
                
                # التحقق من التحديث
                daily_changed = updated_daily != initial_daily
                main_changed = updated_main != initial_main
                
                print(f"\n✅ نتائج التحديث:")
                print(f"  • تغيير الخزينة اليومية: {'✅ تم' if daily_changed else '❌ لم يتم'}")
                print(f"  • تغيير الخزينة الرئيسية: {'✅ تم' if main_changed else '❌ لم يتم'}")
                
                if daily_changed and main_changed:
                    print("🎉 الواجهة تتحدث بشكل صحيح!")
                    return True
                else:
                    print("❌ الواجهة لا تتحدث")
                    return False
            else:
                print("❌ فشل النقل في النظام")
                return False
                
        except Exception as transfer_error:
            print(f"❌ خطأ في تنفيذ النقل: {transfer_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_refresh_signal_to_interfaces():
    """إضافة إشارة تحديث للواجهات"""
    
    print("\n🔧 إضافة إشارة تحديث للواجهات...")
    
    try:
        # إضافة إشارة تحديث لواجهة نقل الخزينة المحسنة
        enhanced_treasury_code = '''
        # إضافة في نهاية execute_transfer بعد النقل الناجح
        # تحديث الواجهة فوراً
        self.load_treasury_data()
        self.update_balance_labels()
        self.update_max_amount()
        self.update_summary()
        
        # إعادة تعيين النموذج
        self.amount_spin.setValue(0)
        self.receiver_edit.clear()
        self.notes_edit.clear()
        '''
        
        print("✅ تم تحديد كود التحديث لواجهة نقل الخزينة المحسنة")
        
        # إضافة إشارة تحديث لواجهة شراء الدولار
        currency_exchange_code = '''
        # إضافة في نهاية execute_exchange بعد النقل الناجح
        # تحديث الأرصدة في الواجهة
        self.load_data()
        
        # إعادة تعيين النموذج
        self.syp_amount_spin.setValue(0)
        self.notes_edit.clear()
        '''
        
        print("✅ تم تحديد كود التحديث لواجهة شراء الدولار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة إشارات التحديث: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص مشكلة تحديث الواجهات الحقيقية")
    print("=" * 70)
    
    # 1. اختبار سلوك الواجهة الحقيقي
    interface_test = test_real_interface_behavior()
    
    # 2. إضافة إشارات التحديث
    signals_added = add_refresh_signal_to_interfaces()
    
    print("\n" + "=" * 70)
    print("📊 ملخص التشخيص:")
    print(f"  • اختبار الواجهة الحقيقي: {'✅ تعمل' if interface_test else '❌ لا تعمل'}")
    print(f"  • إضافة إشارات التحديث: {'✅ تم' if signals_added else '❌ فشل'}")
    
    if not interface_test:
        print("\n❌ المشكلة الحقيقية:")
        print("  • الواجهة لا تتحدث بعد النقل")
        print("  • قد تكون المشكلة في:")
        print("    - عدم استدعاء دوال التحديث")
        print("    - مشكلة في دوال التحديث نفسها")
        print("    - مشكلة في ربط الإشارات")
        
        print("\n💡 الحلول المقترحة:")
        print("  1. إضافة استدعاء صريح لدوال التحديث")
        print("  2. إضافة إعادة تحميل البيانات")
        print("  3. إضافة إعادة تعيين النموذج")
        print("  4. فحص دوال التحديث للتأكد من عملها")
    else:
        print("\n✅ الواجهة تعمل بشكل صحيح في الاختبار")
        print("💡 قد تكون المشكلة في الاستخدام الفعلي")

if __name__ == "__main__":
    main()

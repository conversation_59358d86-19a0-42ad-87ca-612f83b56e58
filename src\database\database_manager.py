# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path
import hashlib

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, project_dir=None):
        if project_dir:
            self.project_dir = Path(project_dir)
            self.db_path = self.project_dir / "data" / "company_system.db"
        else:
            self.project_dir = Path(".")
            self.db_path = self.project_dir / "data" / "company_system.db"
        self.connection = None

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        try:
            if not self.db_path.parent.exists():
                self.db_path.parent.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"تحذير: لا يمكن إنشاء مجلد البيانات: {e}")
            # استخدام المسار الحالي كبديل
            self.db_path = Path("company_system.db")
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(str(self.db_path))
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
            
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
            
    def execute_query(self, query, params=None, silent=False):
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            self.connection.commit()
            return cursor
        except Exception as e:
            if not silent:
                print(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return None

    def add_column_if_not_exists(self, table_name, column_name, column_type):
        """إضافة عمود إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود العمود
            columns_info = self.execute_query(f"PRAGMA table_info({table_name})", silent=True)
            if columns_info:
                existing_columns = [col[1] for col in columns_info.fetchall()]
                if column_name not in existing_columns:
                    self.execute_query(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}", silent=True)
        except:
            pass
            
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return []

    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            return cursor.fetchone()
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return None
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            if not self.connect():
                return False
                
            # إنشاء جداول النظام
            self.create_users_table()
            self.create_subscribers_table()
            self.create_products_table()
            self.create_packages_table()
            self.create_suppliers_table()
            self.create_distributors_table()
            self.create_workers_table()
            self.create_transactions_table()
            self.create_inventory_table()
            self.create_worker_inventory_table()
            self.create_settings_table()
            self.create_company_info_table()
            self.create_user_permissions_table()
            # تم حذف create_treasury_table() - نستخدم unified_treasury بدلاً منه
            self.create_currency_exchange_table()
            self.create_treasury_transfers_table()
            self.create_inventory_movements_table()
            self.create_router_deliveries_table()
            self.create_expenses_table()
            self.create_salary_payments_table()
            self.create_receipts_table()
            self.create_vouchers_table()
            self.create_maintenance_orders_table()
            self.create_worker_deliveries_table()
            self.create_purchases_table()
            self.create_balance_charges_table()
            self.create_purchase_items_table()
            self.create_stock_movements_table()
            self.create_cash_boxes_table()
            self.create_new_subscriptions_table()

            # إدراج البيانات الافتراضية
            self.insert_default_data()
            
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
            
    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        query = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'employee', 'accountant')),
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        """
        self.execute_query(query)
        print("تم إنشاء جدول المستخدمين")

        # تحديث بنية جدول المستخدمين الموجود
        self.update_users_table_structure()

    def update_users_table_structure(self):
        """تحديث بنية جدول المستخدمين الموجود"""
        try:
            print("=== تحديث بنية جدول المستخدمين ===")

            # التحقق من الأعمدة الموجودة
            columns_info = self.execute_query("PRAGMA table_info(users)")
            existing_columns = [col[1] for col in columns_info] if columns_info else []
            print(f"الأعمدة الموجودة: {existing_columns}")

            # إضافة عمود email إذا لم يكن موجوداً
            if 'email' not in existing_columns:
                print("إضافة عمود email...")
                self.execute_query("ALTER TABLE users ADD COLUMN email TEXT")
                print("تم إضافة عمود email")

            # إضافة عمود password إذا لم يكن موجوداً (للتوافق مع النظام القديم)
            if 'password' not in existing_columns:
                print("إضافة عمود password...")
                self.execute_query("ALTER TABLE users ADD COLUMN password TEXT")
                print("تم إضافة عمود password")

            # تحديث عمود password إلى password_hash إذا لزم الأمر
            if 'password' in existing_columns and 'password_hash' not in existing_columns:
                print("تحديث عمود password إلى password_hash...")
                # إنشاء جدول مؤقت بالبنية الجديدة
                self.execute_query("""
                    CREATE TABLE users_temp (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        full_name TEXT NOT NULL,
                        email TEXT,
                        role TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP
                    )
                """)

                # نسخ البيانات من الجدول القديم
                self.execute_query("""
                    INSERT INTO users_temp (id, username, password_hash, full_name, role, is_active, created_at, last_login)
                    SELECT id, username, password, full_name, role, is_active, created_at, last_login
                    FROM users
                """)

                # حذف الجدول القديم
                self.execute_query("DROP TABLE users")

                # إعادة تسمية الجدول الجديد
                self.execute_query("ALTER TABLE users_temp RENAME TO users")

                print("تم تحديث بنية جدول المستخدمين")

            print("=== انتهاء تحديث بنية جدول المستخدمين ===")

        except Exception as e:
            print(f"خطأ في تحديث بنية جدول المستخدمين: {e}")

    def create_subscribers_table(self):
        """إنشاء جدول المشتركين"""
        query = """
        CREATE TABLE IF NOT EXISTS subscribers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            subscription_fee REAL DEFAULT 0,
            subscription_paid BOOLEAN DEFAULT 0,
            router_type TEXT,
            router_paid BOOLEAN DEFAULT 0,
            package_type TEXT,
            cable_type TEXT,
            cable_meters INTEGER DEFAULT 0,
            installation_worker TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by TEXT
        )
        """
        self.execute_query(query)

        # إضافة عمود router_type_id إذا لم يكن موجوداً
        try:
            self.execute_query("""
                ALTER TABLE subscribers ADD COLUMN router_type_id INTEGER
            """)
            print("تم إضافة عمود router_type_id لجدول المشتركين")
        except Exception as e:
            if "duplicate column name" not in str(e).lower():
                print(f"تحذير في إضافة عمود router_type_id: {e}")

    def create_products_table(self):
        """إنشاء جدول المنتجات"""
        query = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            unit_price REAL NOT NULL,
            unit_type TEXT NOT NULL,
            stock_quantity INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)
        
    def create_packages_table(self):
        """إنشاء جدول الباقات"""
        query = """
        CREATE TABLE IF NOT EXISTS packages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            price REAL NOT NULL,
            speed TEXT,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)
        
    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        query = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            company_name TEXT,
            supplier_type TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة للجداول الموجودة (للتوافق مع قواعد البيانات القديمة)
        self.add_missing_columns()
        
    def create_distributors_table(self):
        """إنشاء جدول الموزعين"""
        query = """
        CREATE TABLE IF NOT EXISTS distributors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            area TEXT,
            balance REAL DEFAULT 0,
            commission_rate REAL DEFAULT 0,
            address TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة للجداول الموجودة (للتوافق مع قواعد البيانات القديمة)
        self.add_column_if_not_exists("distributors", "company_name", "TEXT")
        self.add_column_if_not_exists("distributors", "supplier_type", "TEXT")
        self.add_column_if_not_exists("distributors", "email", "TEXT")
        self.add_column_if_not_exists("distributors", "area", "TEXT")
        self.add_column_if_not_exists("distributors", "commission_rate", "REAL DEFAULT 0")
        self.add_column_if_not_exists("distributors", "address", "TEXT")
        self.add_column_if_not_exists("distributors", "is_active", "BOOLEAN DEFAULT 1")
        self.add_column_if_not_exists("distributors", "created_by", "TEXT")
        
    def create_workers_table(self):
        """إنشاء جدول العمال"""
        query = """
        CREATE TABLE IF NOT EXISTS workers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            area TEXT,
            work_type TEXT DEFAULT 'تركيب راوترات',
            salary REAL DEFAULT 0,
            commission_rate REAL DEFAULT 0,
            address TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة للجداول الموجودة (للتوافق مع قواعد البيانات القديمة)
        self.add_column_if_not_exists("workers", "area", "TEXT")
        self.add_column_if_not_exists("workers", "work_type", "TEXT DEFAULT 'تركيب راوترات'")
        self.add_column_if_not_exists("workers", "salary", "REAL DEFAULT 0")
        self.add_column_if_not_exists("workers", "commission_rate", "REAL DEFAULT 0")
        self.add_column_if_not_exists("workers", "is_active", "BOOLEAN DEFAULT 1")
        self.add_column_if_not_exists("workers", "created_by", "TEXT")
        
    def create_transactions_table(self):
        """إنشاء جدول العمليات"""
        query = """
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            description TEXT,
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'SYP',
            payment_method TEXT,
            reference_id INTEGER,
            user_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)
        
    def create_inventory_table(self):
        """إنشاء جدول المخزون الرئيسي"""
        query = """
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER,
            quantity INTEGER NOT NULL,
            unit_type TEXT NOT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """
        self.execute_query(query)
        
    def create_worker_inventory_table(self):
        """إنشاء جدول مخزون العمال"""
        query = """
        CREATE TABLE IF NOT EXISTS worker_inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            worker_id INTEGER,
            product_id INTEGER,
            quantity INTEGER NOT NULL,
            delivered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            delivered_by TEXT,
            FOREIGN KEY (worker_id) REFERENCES workers (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """
        self.execute_query(query)
        
    def create_settings_table(self):
        """إنشاء جدول الإعدادات"""
        query = """
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)
        
    def create_company_info_table(self):
        """إنشاء جدول بيانات الشركة"""
        query = """
        CREATE TABLE IF NOT EXISTS company_info (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            email TEXT,
            logo_path TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

    def create_user_permissions_table(self):
        """إنشاء جدول صلاحيات المستخدمين"""
        query = """
        CREATE TABLE IF NOT EXISTS user_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            permission_name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            UNIQUE(user_id, permission_name)
        )
        """
        self.execute_query(query)

    # تم حذف create_treasury_table() - نستخدم unified_treasury بدلاً منه

    def create_currency_exchange_table(self):
        """إنشاء جدول صرف العملات"""
        query = """
        CREATE TABLE IF NOT EXISTS currency_exchange (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_currency TEXT NOT NULL,
            to_currency TEXT NOT NULL,
            exchange_rate REAL NOT NULL,
            amount_from REAL NOT NULL,
            amount_to REAL NOT NULL,
            exchange_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)

    def create_treasury_transfers_table(self):
        """إنشاء جدول تحويلات الخزينة"""
        query = """
        CREATE TABLE IF NOT EXISTS treasury_transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            amount REAL NOT NULL,
            currency_type TEXT NOT NULL,
            transfer_type TEXT NOT NULL,
            description TEXT,
            from_treasury TEXT,
            to_treasury TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        try:
            self.execute_query("ALTER TABLE treasury_transfers ADD COLUMN transfer_type TEXT")
        except:
            pass
        try:
            self.execute_query("ALTER TABLE treasury_transfers ADD COLUMN description TEXT")
        except:
            pass
        try:
            self.execute_query("ALTER TABLE treasury_transfers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        except:
            pass

    def create_inventory_movements_table(self):
        """إنشاء جدول حركة المخزون"""
        query = """
        CREATE TABLE IF NOT EXISTS inventory_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            movement_type TEXT NOT NULL,
            quantity_before INTEGER NOT NULL DEFAULT 0,
            quantity_after INTEGER NOT NULL DEFAULT 0,
            quantity_change INTEGER NOT NULL DEFAULT 0,
            movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            notes TEXT,
            FOREIGN KEY (product_id) REFERENCES products (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)

    def create_router_deliveries_table(self):
        """إنشاء جدول تسليم الراوترات"""
        query = """
        CREATE TABLE IF NOT EXISTS router_deliveries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscriber_id INTEGER NOT NULL,
            subscriber_name TEXT NOT NULL,
            router_type TEXT NOT NULL,
            router_paid BOOLEAN DEFAULT 0,
            subscription_fee REAL DEFAULT 0,
            subscription_paid BOOLEAN DEFAULT 0,
            package_type TEXT,
            cable_type TEXT,
            cable_meters INTEGER DEFAULT 0,
            worker_id INTEGER,
            worker_name TEXT,
            commission REAL DEFAULT 0,
            total_amount REAL DEFAULT 0,
            delivery_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            notes TEXT,
            FOREIGN KEY (subscriber_id) REFERENCES subscribers (id),
            FOREIGN KEY (worker_id) REFERENCES workers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)

    def create_expenses_table(self):
        """إنشاء جدول المصاريف"""
        query = """
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            expense_type TEXT NOT NULL,
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'SYP',
            expense_date DATE NOT NULL,
            description TEXT,
            notes TEXT,
            user_name TEXT,
            deducted_from_daily INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        try:
            self.execute_query("ALTER TABLE expenses ADD COLUMN user_id INTEGER")
        except:
            pass
        try:
            self.execute_query("ALTER TABLE expenses ADD COLUMN currency TEXT DEFAULT 'SYP'")
        except:
            pass
        try:
            self.execute_query("ALTER TABLE expenses ADD COLUMN deducted_from_daily INTEGER DEFAULT 0")
        except:
            pass

    def create_salary_payments_table(self):
        """إنشاء جدول سداد الرواتب"""
        query = """
        CREATE TABLE IF NOT EXISTS salary_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            worker_id INTEGER NOT NULL,
            payment_month INTEGER NOT NULL,
            payment_year INTEGER NOT NULL,
            basic_salary REAL NOT NULL,
            commissions REAL DEFAULT 0,
            bonus REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            user_name TEXT,
            FOREIGN KEY (worker_id) REFERENCES workers (id)
        )
        """
        self.execute_query(query)

    def create_receipts_table(self):
        """إنشاء جدول سندات القبض"""
        query = """
        CREATE TABLE IF NOT EXISTS receipts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            receipt_number TEXT UNIQUE NOT NULL,
            distributor_id INTEGER,
            distributor_name TEXT NOT NULL,
            amount REAL NOT NULL,
            receipt_date DATE NOT NULL,
            description TEXT,
            notes TEXT,
            user_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (distributor_id) REFERENCES distributors (id)
        )
        """
        self.execute_query(query)

    def create_vouchers_table(self):
        """إنشاء جدول سندات الدفع"""
        query = """
        CREATE TABLE IF NOT EXISTS vouchers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            voucher_number TEXT UNIQUE NOT NULL,
            supplier_name TEXT NOT NULL,
            amount REAL NOT NULL,
            voucher_date DATE NOT NULL,
            description TEXT,
            notes TEXT,
            user_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

    def create_maintenance_orders_table(self):
        """إنشاء جدول أوامر الصيانة"""
        query = """
        CREATE TABLE IF NOT EXISTS maintenance_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_number TEXT UNIQUE NOT NULL,
            customer_name TEXT NOT NULL,
            customer_phone TEXT,
            description TEXT NOT NULL,
            order_date DATE NOT NULL,
            technician_name TEXT,
            status TEXT DEFAULT 'جديد',
            notes TEXT,
            user_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)

    def create_worker_deliveries_table(self):
        """إنشاء جدول تسليم المواد للعمال"""
        query = """
        CREATE TABLE IF NOT EXISTS worker_deliveries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            worker_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            delivery_date DATE NOT NULL,
            user_id INTEGER,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (worker_id) REFERENCES workers (id),
            FOREIGN KEY (product_id) REFERENCES products (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)
        print("تم إنشاء جدول تسليم المواد للعمال")

    def create_purchases_table(self):
        """إنشاء جدول المشتريات"""
        query = """
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            supplier_name TEXT NOT NULL,
            invoice_number TEXT UNIQUE NOT NULL,
            purchase_date DATE NOT NULL,
            payment_method TEXT,
            subtotal REAL NOT NULL,
            tax_rate REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            discount REAL DEFAULT 0,
            total REAL NOT NULL,
            notes TEXT,
            user_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """
        self.execute_query(query)

    def create_purchase_items_table(self):
        """إنشاء جدول تفاصيل المشتريات"""
        query = """
        CREATE TABLE IF NOT EXISTS purchase_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (purchase_id) REFERENCES purchases (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """
        self.execute_query(query)

    def create_stock_movements_table(self):
        """إنشاء جدول حركات المخزون"""
        query = """
        CREATE TABLE IF NOT EXISTS stock_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            quantity_change REAL NOT NULL,
            operation_type TEXT NOT NULL,
            new_stock REAL NOT NULL,
            reference_id INTEGER,
            notes TEXT,
            user_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """
        self.execute_query(query)

    def add_missing_columns(self):
        """إضافة الأعمدة المفقودة للجداول الموجودة"""
        # قائمة الأعمدة المطلوب إضافتها
        columns_to_add = [
            # جدول suppliers
            ("suppliers", "company_name", "TEXT"),
            ("suppliers", "supplier_type", "TEXT"),
            ("suppliers", "email", "TEXT"),
            ("suppliers", "is_active", "BOOLEAN DEFAULT 1"),

            # جدول distributors
            ("distributors", "area", "TEXT"),
            ("distributors", "commission_rate", "REAL DEFAULT 0"),
            ("distributors", "address", "TEXT"),
            ("distributors", "is_active", "BOOLEAN DEFAULT 1"),
            ("distributors", "created_by", "INTEGER"),

            # جدول workers
            ("workers", "area", "TEXT"),
            ("workers", "work_type", "TEXT"),
            ("workers", "salary", "REAL DEFAULT 0"),
            ("workers", "commission_rate", "REAL DEFAULT 0"),
            ("workers", "is_active", "BOOLEAN DEFAULT 1"),
            ("workers", "created_by", "INTEGER"),

            # جدول subscribers
            ("subscribers", "email", "TEXT"),
            ("subscribers", "subscription_end_date", "DATE"),

            # جدول users
            ("users", "email", "TEXT"),
            ("users", "password_hash", "TEXT"),

            # جدول products - وحدات الشراء والبيع
            ("products", "purchase_unit", "TEXT DEFAULT 'قطعة'"),
            ("products", "sale_unit", "TEXT DEFAULT 'قطعة'"),
            ("products", "conversion_rate", "REAL DEFAULT 1"),
            ("products", "supplier_id", "INTEGER"),
        ]

        for table, column, column_type in columns_to_add:
            self.add_column_if_not_exists(table, column, column_type)

    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        # إنشاء مستخدم افتراضي (admin)
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        self.execute_query(
            "INSERT OR IGNORE INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
            ("admin", admin_password, "المدير العام", "admin")
        )
        
        # إعدادات افتراضية
        default_settings = [
            ("subscription_fee", "50000", "رسم الاشتراك الافتراضي"),
            ("exchange_rate_usd", "15000", "سعر صرف الدولار"),
            ("company_name", "شركة الإنترنت", "اسم الشركة"),
            ("printer_name", "", "اسم الطابعة الافتراضية"),
            ("print_copies", "1", "عدد النسخ للطباعة"),
            ("paper_size", "A4", "حجم الورق"),
        ]
        
        for key, value, desc in default_settings:
            self.execute_query(
                "INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)",
                (key, value, desc)
            )
            
        # منتجات افتراضية (مناسبة لأوامر الصيانة أيضاً)
        default_products = [
            ("راوتر TP-Link", "راوتر", 150000, "قطعة"),
            ("كبل شبكة", "كبل", 2000, "متر"),
            ("موصل RJ45", "موصلات", 500, "قطعة"),
            ("محول كهرباء", "قطع غيار", 25000, "قطعة"),
            ("هوائي واي فاي", "قطع غيار", 15000, "قطعة"),
            ("كبل طاقة", "كبل", 3000, "قطعة"),
            ("مفتاح شبكة", "أجهزة شبكة", 75000, "قطعة"),
            ("قطعة تمديد", "موصلات", 1000, "قطعة"),
        ]
        
        for name, category, price, unit in default_products:
            self.execute_query(
                "INSERT OR IGNORE INTO products (name, category, unit_price, unit_type, stock_quantity, min_stock_level) VALUES (?, ?, ?, ?, ?, ?)",
                (name, category, price, unit, 100, 10)  # مخزون افتراضي 100 وحد أدنى 10
            )
            
        # باقات افتراضية
        default_packages = [
            ("باقة 10 ميجا", 25000, "10 Mbps", "باقة إنترنت سرعة 10 ميجا"),
            ("باقة 20 ميجا", 40000, "20 Mbps", "باقة إنترنت سرعة 20 ميجا"),
            ("باقة 50 ميجا", 75000, "50 Mbps", "باقة إنترنت سرعة 50 ميجا"),
        ]
        
        for name, price, speed, desc in default_packages:
            self.execute_query(
                "INSERT OR IGNORE INTO packages (name, price, speed, description) VALUES (?, ?, ?, ?)",
                (name, price, speed, desc)
            )

        # خزائن افتراضية
        default_treasuries = [
            ("SYP", 0, "الخزينة الرئيسية بالليرة السورية"),
            ("USD", 0, "الخزينة الرئيسية بالدولار الأمريكي"),
        ]

        for currency, balance, notes in default_treasuries:
            self.execute_query(
                "INSERT OR IGNORE INTO treasury (currency_type, balance, notes) VALUES (?, ?, ?)",
                (currency, balance, notes)
            )

    def create_balance_charges_table(self):
        """إنشاء جدول شحن أرصدة الموزعين"""
        query = """
        CREATE TABLE IF NOT EXISTS balance_charges (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            distributor_id INTEGER NOT NULL,
            charge_amount REAL NOT NULL,
            previous_balance REAL DEFAULT 0,
            new_balance REAL NOT NULL,
            payment_method TEXT DEFAULT 'نقدي',
            reference_number TEXT,
            bank TEXT,
            commission_rate REAL DEFAULT 0,
            commission_amount REAL DEFAULT 0,
            charge_date DATE NOT NULL,
            notes TEXT,
            user_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (distributor_id) REFERENCES distributors (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        self.execute_query(query)
        print("تم إنشاء جدول شحن أرصدة الموزعين")

    def create_new_subscriptions_table(self):
        """إنشاء جدول الاشتراكات الجديدة"""
        query = """
        CREATE TABLE IF NOT EXISTS new_subscriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscriber_name TEXT NOT NULL,
            subscription_fee REAL NOT NULL DEFAULT 0,
            router_type TEXT,
            router_price REAL DEFAULT 0,
            package_type TEXT,
            cable_type TEXT,
            cable_meters INTEGER DEFAULT 0,
            cable_price_per_meter REAL DEFAULT 0,
            cable_cost REAL DEFAULT 0,
            installation_worker TEXT,
            total_amount REAL DEFAULT 0,
            subscription_paid BOOLEAN DEFAULT 0,
            router_paid BOOLEAN DEFAULT 0,
            cable_paid BOOLEAN DEFAULT 0,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
        """
        self.execute_query(query)
        print("تم إنشاء جدول الاشتراكات الجديدة")

    def get_or_create_cash_box(self, user_id, date_str=None):
        """الحصول على صندوق اليوم أو إنشاؤه - ضمان عدم الاختلاط"""
        from datetime import date

        if date_str is None:
            date_str = date.today().strftime('%Y-%m-%d')

        print(f"=== البحث عن صندوق للمستخدم {user_id} في تاريخ {date_str} ===")

        # البحث عن صندوق موجود لهذا المستخدم في هذا التاريخ
        cash_box = self.fetch_one("""
            SELECT * FROM cash_boxes
            WHERE date = ? AND user_id = ?
        """, (date_str, user_id))

        if cash_box:
            print(f"تم العثور على صندوق موجود: ID={cash_box['id']}")
            return cash_box

        print("لم يتم العثور على صندوق - سيتم إنشاء صندوق جديد")

        # إنشاء صندوق جديد
        query = """
        INSERT INTO cash_boxes (date, user_id, opening_balance, total_receipts, total_expenses, closing_balance, is_closed)
        VALUES (?, ?, 0, 0, 0, 0, 0)
        """

        result = self.execute_query(query, (date_str, user_id))

        if result is not None:
            # إرجاع الصندوق الجديد
            new_cash_box = self.fetch_one("""
                SELECT * FROM cash_boxes
                WHERE date = ? AND user_id = ?
            """, (date_str, user_id))

            if new_cash_box:
                print(f"تم إنشاء صندوق جديد: ID={new_cash_box['id']}")
                return new_cash_box

        print("فشل في إنشاء الصندوق")
        return None

    def update_cash_box_totals(self, cash_box_id):
        """تحديث إجماليات الصندوق"""
        print(f"=== تحديث إجماليات الصندوق {cash_box_id} ===")

        # الحصول على معلومات الصندوق
        cash_box = self.fetch_one("SELECT * FROM cash_boxes WHERE id = ?", (cash_box_id,))
        if not cash_box:
            print("الصندوق غير موجود")
            return None

        user_id = cash_box['user_id']
        date_str = cash_box['date']

        # حساب إجمالي المقبوضات
        receipts_result = self.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total
            FROM receipts
            WHERE DATE(date) = ? AND created_by = ?
        """, (date_str, user_id))

        total_receipts = receipts_result['total'] if receipts_result else 0

        # حساب إجمالي المصاريف (باستثناء الرواتب)
        expenses_result = self.fetch_one("""
            SELECT COALESCE(SUM(amount), 0) as total
            FROM expenses
            WHERE DATE(expense_date) = ? AND created_by = ? AND expense_type != 'رواتب'
        """, (date_str, user_id))

        total_expenses = expenses_result['total'] if expenses_result else 0

        # حساب الرصيد الختامي
        opening_balance = cash_box['opening_balance'] or 0
        closing_balance = opening_balance + total_receipts - total_expenses

        print(f"الرصيد الافتتاحي: {opening_balance}")
        print(f"إجمالي المقبوضات: {total_receipts}")
        print(f"إجمالي المصاريف: {total_expenses}")
        print(f"الرصيد الختامي: {closing_balance}")

        # تحديث الصندوق
        self.execute_query("""
            UPDATE cash_boxes
            SET total_receipts = ?, total_expenses = ?, closing_balance = ?
            WHERE id = ?
        """, (total_receipts, total_expenses, closing_balance, cash_box_id))

        return {
            'total_receipts': total_receipts,
            'total_expenses': total_expenses,
            'closing_balance': closing_balance
        }

    def create_cash_boxes_table(self):
        """إنشاء جدول الصناديق - مع ضمان عدم الاختلاط"""
        query = """
        CREATE TABLE IF NOT EXISTS cash_boxes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATE NOT NULL,
            user_id INTEGER NOT NULL,
            opening_balance DECIMAL(10,2) DEFAULT 0,
            total_receipts DECIMAL(10,2) DEFAULT 0,
            total_expenses DECIMAL(10,2) DEFAULT 0,
            closing_balance DECIMAL(10,2) DEFAULT 0,
            actual_amount DECIMAL(10,2) DEFAULT 0,
            difference DECIMAL(10,2) DEFAULT 0,
            is_closed BOOLEAN DEFAULT 0,
            closed_at TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            UNIQUE(date, user_id)  -- ضمان صندوق واحد لكل مستخدم في اليوم
        )
        """
        self.execute_query(query)

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        self.add_column_if_not_exists("cash_boxes", "actual_amount", "DECIMAL(10,2) DEFAULT 0")
        self.add_column_if_not_exists("cash_boxes", "difference", "DECIMAL(10,2) DEFAULT 0")
        self.add_column_if_not_exists("cash_boxes", "user_name", "TEXT")
        self.add_column_if_not_exists("cash_boxes", "status", "TEXT DEFAULT 'مفتوح'")

        print("✅ تم إنشاء جدول الصناديق")

    def get_or_create_cash_box(self, user_id, user_name, date):
        """الحصول على صندوق المستخدم لتاريخ معين أو إنشاؤه"""
        try:
            # البحث عن صندوق موجود
            existing_box = self.fetch_one("""
                SELECT * FROM cash_boxes
                WHERE user_id = ? AND date = ?
            """, (user_id, date))

            if existing_box:
                print(f"تم العثور على صندوق موجود للمستخدم {user_name} في {date}")
                return existing_box

            # إنشاء صندوق جديد
            print(f"إنشاء صندوق جديد للمستخدم {user_name} في {date}")
            self.execute_query("""
                INSERT INTO cash_boxes (user_id, user_name, date, opening_balance, total_receipts, total_expenses, closing_balance, status)
                VALUES (?, ?, ?, 0, 0, 0, 0, 'مفتوح')
            """, (user_id, user_name, date))

            # إرجاع الصندوق الجديد
            new_box = self.fetch_one("""
                SELECT * FROM cash_boxes
                WHERE user_id = ? AND date = ?
            """, (user_id, date))

            print(f"تم إنشاء صندوق جديد بنجاح: ID {new_box['id'] if new_box else 'غير محدد'}")
            return new_box

        except Exception as e:
            print(f"خطأ في إنشاء/جلب الصندوق: {e}")
            return None

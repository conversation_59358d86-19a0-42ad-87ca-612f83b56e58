#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام إغلاق الصندوق
"""

import sys
import os
sys.path.append('src')

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    
    print("🧪 اختبار الوظائف الأساسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        print("1️⃣ فتح الصندوق...")
        treasury_manager.open_cash_box(user_id=user_id)
        print("✅ تم فتح الصندوق")
        
        print("2️⃣ إضافة مبيعات...")
        treasury_manager.add_to_daily_treasury(user_id, 'SYP', 100000)
        balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"✅ الرصيد: {balance:,} ل.س")
        
        print("3️⃣ إغلاق الصندوق...")
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        print(f"✅ إغلاق الصندوق: {'نجح' if close_success else 'فشل'}")
        
        print("4️⃣ فحص الجلسة...")
        is_active = treasury_manager.is_session_active(user_id=user_id)
        print(f"✅ الجلسة: {'مفتوحة' if is_active else 'مغلقة'}")
        
        print("5️⃣ فتح صندوق جديد...")
        treasury_manager.open_cash_box(user_id=user_id)
        new_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"✅ الرصيد الجديد: {new_balance:,} ل.س")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_cash_box_window():
    """اختبار واجهة إغلاق الصندوق"""
    
    print("\n🧪 اختبار واجهة إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from ui.cash_box_closure_window import CashBoxClosureWindow
        
        db = DatabaseManager('data/company_system.db')
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير'
        }
        
        # إنشاء الواجهة (بدون عرض)
        window = CashBoxClosureWindow(db, current_user)
        
        # فحص الدوال الجديدة
        if hasattr(window, 'check_cash_box_status'):
            print("✅ دالة check_cash_box_status موجودة")
        else:
            print("❌ دالة check_cash_box_status مفقودة")
        
        if hasattr(window, 'reset_all_values'):
            print("✅ دالة reset_all_values موجودة")
        else:
            print("❌ دالة reset_all_values مفقودة")
        
        # اختبار إعادة تعيين القيم
        window.reset_all_values()
        print("✅ تم اختبار إعادة تعيين القيم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_main_window_stats():
    """اختبار إحصائيات الواجهة الرئيسية"""
    
    print("\n🧪 اختبار إحصائيات الواجهة الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('data/company_system.db')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        # فتح الصندوق وإضافة مبيعات
        treasury_manager.open_cash_box(user_id=user_id)
        treasury_manager.add_to_daily_treasury(user_id, 'SYP', 200000)
        
        # قراءة الرصيد (هذا ما ستعرضه الواجهة الرئيسية)
        daily_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        
        print(f"📊 إجمالي المبيعات (الواجهة الرئيسية): {daily_balance:,} ل.س")
        
        if daily_balance > 0:
            print("✅ الواجهة الرئيسية ستعرض المبيعات بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في عرض المبيعات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار بسيط لنظام إغلاق الصندوق المحدث")
    print("=" * 60)
    
    # اختبار الوظائف الأساسية
    basic_test = test_basic_functionality()
    
    # اختبار واجهة إغلاق الصندوق
    window_test = test_cash_box_window()
    
    # اختبار إحصائيات الواجهة الرئيسية
    stats_test = test_main_window_stats()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"  • الوظائف الأساسية: {'✅ نجح' if basic_test else '❌ فشل'}")
    print(f"  • واجهة إغلاق الصندوق: {'✅ نجح' if window_test else '❌ فشل'}")
    print(f"  • إحصائيات الواجهة الرئيسية: {'✅ نجح' if stats_test else '❌ فشل'}")
    
    if all([basic_test, window_test, stats_test]):
        print("\n🎉 جميع الاختبارات نجحت!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("  ✅ إعادة تعيين القيم إلى صفر بعد الإغلاق")
        print("  ✅ إضافة قيمة الإغلاق للخزينة اليومية الجديدة")
        print("  ✅ عرض إجمالي المبيعات في الواجهة الرئيسية")
        print("  ✅ تكامل مع النظام الموحد")
        print("  ✅ واجهة نقل الخزينة تعرض الخزينة اليومية")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. سجل دخول: admin / 123")
        print("  3. قم بعمليات مبيعات")
        print("  4. افتح 'إغلاق الصندوق' - ستجد القيم محدثة")
        print("  5. اضغط 'إغلاق الصندوق'")
        print("  6. افتح 'إغلاق الصندوق' مرة أخرى - ستجد القيم صفر")
        print("  7. الواجهة الرئيسية تعرض إجمالي المبيعات")
        
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        
        if not basic_test:
            print("  🔧 مراجعة النظام الموحد")
        if not window_test:
            print("  🔧 مراجعة واجهة إغلاق الصندوق")
        if not stats_test:
            print("  🔧 مراجعة إحصائيات الواجهة الرئيسية")

if __name__ == "__main__":
    main()

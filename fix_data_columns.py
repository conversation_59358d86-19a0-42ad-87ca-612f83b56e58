#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة الأعمدة المفقودة في الجداول
"""

import sys
import os
sys.path.append('src')

def fix_database_columns():
    """إصلاح الأعمدة المفقودة"""
    
    print("🔧 إصلاح الأعمدة المفقودة في قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        print("✅ اتصال قاعدة البيانات")
        
        # 1. إصلاح جدول subscribers
        print("1️⃣ إصلاح جدول subscribers...")
        
        # إضافة العمود المفقود subscription_paid
        try:
            db.execute_query("ALTER TABLE subscribers ADD COLUMN subscription_paid INTEGER DEFAULT 0")
            print("✅ تم إضافة عمود subscription_paid")
        except:
            print("ℹ️ عمود subscription_paid موجود بالفعل")
        
        # إضافة العمود المفقود router_paid
        try:
            db.execute_query("ALTER TABLE subscribers ADD COLUMN router_paid INTEGER DEFAULT 0")
            print("✅ تم إضافة عمود router_paid")
        except:
            print("ℹ️ عمود router_paid موجود بالفعل")
        
        # 2. إصلاح جدول workers
        print("2️⃣ إصلاح جدول workers...")
        
        # إضافة العمود المفقود is_active
        try:
            db.execute_query("ALTER TABLE workers ADD COLUMN is_active INTEGER DEFAULT 1")
            print("✅ تم إضافة عمود is_active")
        except:
            print("ℹ️ عمود is_active موجود بالفعل")
        
        # 3. إصلاح جدول products
        print("3️⃣ إصلاح جدول products...")
        
        # إضافة العمود المفقود price
        try:
            db.execute_query("ALTER TABLE products ADD COLUMN price REAL DEFAULT 0")
            print("✅ تم إضافة عمود price")
        except:
            print("ℹ️ عمود price موجود بالفعل")
        
        # إضافة العمود المفقود category
        try:
            db.execute_query("ALTER TABLE products ADD COLUMN category TEXT DEFAULT 'general'")
            print("✅ تم إضافة عمود category")
        except:
            print("ℹ️ عمود category موجود بالفعل")
        
        print("✅ تم إصلاح جميع الأعمدة المفقودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأعمدة: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_existing_data():
    """تحديث البيانات الموجودة"""
    
    print("\n📊 تحديث البيانات الموجودة...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # 1. تحديث جدول products
        print("1️⃣ تحديث جدول products...")
        
        # إضافة راوتر تجريبي
        db.execute_query("""
            INSERT OR REPLACE INTO products (id, name, price, category, stock_quantity)
            VALUES (1, 'راوتر TP-Link', 150000, 'router', 10)
        """)
        
        db.execute_query("""
            INSERT OR IGNORE INTO products (name, price, category, stock_quantity)
            VALUES ('راوتر D-Link', 120000, 'router', 5)
        """)
        
        db.execute_query("""
            INSERT OR IGNORE INTO products (name, price, category, stock_quantity)
            VALUES ('كبل شبكة', 5000, 'cable', 100)
        """)
        
        print("✅ تم تحديث جدول products")
        
        # 2. تحديث جدول workers
        print("2️⃣ تحديث جدول workers...")
        
        db.execute_query("UPDATE workers SET is_active = 1 WHERE is_active IS NULL")
        print("✅ تم تحديث جدول workers")
        
        # 3. تحديث جدول subscribers
        print("3️⃣ تحديث جدول subscribers...")
        
        db.execute_query("UPDATE subscribers SET subscription_paid = 0 WHERE subscription_paid IS NULL")
        db.execute_query("UPDATE subscribers SET router_paid = 0 WHERE router_paid IS NULL")
        print("✅ تم تحديث جدول subscribers")
        
        print("✅ تم تحديث جميع البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_data():
    """التحقق من البيانات"""
    
    print("\n🔍 التحقق من البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # فحص المشتركين
        subscribers = db.fetch_all("""
            SELECT s.id, s.name, s.phone, s.delivered, 
                   COALESCE(s.subscription_paid, 0) as subscription_paid,
                   COALESCE(s.router_paid, 0) as router_paid
            FROM subscribers s 
            WHERE s.delivered = 0 
            LIMIT 5
        """)
        
        print(f"👥 المشتركين غير المسلمين ({len(subscribers)}):")
        for sub in subscribers:
            print(f"  • {sub[1]} ({sub[2]}) - اشتراك: {sub[4]}, راوتر: {sub[5]}")
        
        # فحص الراوترات
        routers = db.fetch_all("""
            SELECT name, COALESCE(price, 0) as price, 
                   COALESCE(category, 'general') as category,
                   stock_quantity
            FROM products 
            WHERE category = 'router'
            LIMIT 5
        """)
        
        print(f"📦 الراوترات ({len(routers)}):")
        for router in routers:
            print(f"  • {router[0]}: {router[1]:,} ل.س (مخزون: {router[3]})")
        
        # فحص العمال
        workers = db.fetch_all("""
            SELECT name, role, COALESCE(is_active, 1) as is_active
            FROM workers 
            WHERE is_active = 1
            LIMIT 5
        """)
        
        print(f"👷 العمال النشطين ({len(workers)}):")
        for worker in workers:
            print(f"  • {worker[0]} ({worker[1]})")
        
        # فحص الباقات
        packages = db.fetch_all("SELECT name, price FROM packages LIMIT 5")
        print(f"📋 الباقات ({len(packages)}):")
        for pkg in packages:
            print(f"  • {pkg[0]}: {pkg[1]:,} ل.س")
        
        return len(subscribers) > 0 and len(routers) > 0 and len(workers) > 0 and len(packages) > 0
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_router_delivery_after_fix():
    """اختبار تسليم الراوتر بعد الإصلاح"""
    
    print("\n🧪 اختبار تسليم الراوتر بعد الإصلاح...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء الواجهة
        print("🔄 إنشاء واجهة تسليم الراوتر...")
        window = RouterDeliveryWindow(db, current_user)
        print("✅ تم إنشاء الواجهة")
        
        # فحص البيانات المحملة
        subscriber_count = window.subscriber_combo.count() if hasattr(window, 'subscriber_combo') else 0
        router_count = window.router_combo.count() if hasattr(window, 'router_combo') else 0
        package_count = window.package_combo.count() if hasattr(window, 'package_combo') else 0
        worker_count = window.worker_combo.count() if hasattr(window, 'worker_combo') else 0
        
        print(f"📊 البيانات المحملة في الواجهة:")
        print(f"  • المشتركين: {subscriber_count}")
        print(f"  • الراوترات: {router_count}")
        print(f"  • الباقات: {package_count}")
        print(f"  • العمال: {worker_count}")
        
        if all([subscriber_count > 0, router_count > 0, package_count > 0, worker_count > 0]):
            print("✅ جميع البيانات متوفرة - الواجهة جاهزة للاستخدام")
            return True
        else:
            print("❌ بعض البيانات مفقودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح مشكلة البيانات المفقودة في تسليم الراوتر")
    print("=" * 70)
    
    # إصلاح الأعمدة
    columns_fixed = fix_database_columns()
    
    # تحديث البيانات
    data_updated = update_existing_data()
    
    # التحقق من البيانات
    data_verified = verify_data()
    
    # اختبار الواجهة
    interface_tested = test_router_delivery_after_fix()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الإصلاح:")
    print(f"  • إصلاح الأعمدة: {'✅ نجح' if columns_fixed else '❌ فشل'}")
    print(f"  • تحديث البيانات: {'✅ نجح' if data_updated else '❌ فشل'}")
    print(f"  • التحقق من البيانات: {'✅ نجح' if data_verified else '❌ فشل'}")
    print(f"  • اختبار الواجهة: {'✅ نجح' if interface_tested else '❌ فشل'}")
    
    if all([columns_fixed, data_updated, data_verified, interface_tested]):
        print("\n🎉 تم إصلاح جميع مشاكل البيانات!")
        print("\n📋 الآن جرب:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. سجل دخول: admin / 123")
        print("  3. افتح 'تسليم راوتر'")
        print("  4. ستجد البيانات متوفرة:")
        print("     • مشتركين في القائمة المنسدلة")
        print("     • راوترات متاحة")
        print("     • باقات متنوعة")
        print("     • عمال تركيب")
        print("  5. املأ البيانات واضغط 'حفظ وتسليم'")
        print("  6. ✅ يجب أن يعمل بدون إغلاق التطبيق!")
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

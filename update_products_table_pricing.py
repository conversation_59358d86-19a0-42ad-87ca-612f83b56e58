#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث جدول المنتجات لدعم أسعار الشراء والبيع والوحدات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append('src')

from database.database_manager import DatabaseManager

def update_products_table():
    """تحديث جدول المنتجات"""
    
    print("🔄 تحديث جدول المنتجات لدعم أسعار الشراء والبيع...")
    
    # الاتصال بقاعدة البيانات
    db_path = Path("data/company_system.db")
    if not db_path.exists():
        db_path = Path("company_system.db")
    
    db_manager = DatabaseManager(str(db_path))
    
    try:
        # التحقق من الأعمدة الموجودة
        columns = db_manager.fetch_all("PRAGMA table_info(products)")
        column_names = [col[1] for col in columns]
        
        print(f"📊 الأعمدة الحالية: {column_names}")
        
        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        new_columns = [
            ('purchase_price', 'REAL DEFAULT 0', 'سعر الشراء'),
            ('sale_price', 'REAL DEFAULT 0', 'سعر البيع'),
            ('purchase_unit', 'TEXT', 'وحدة الشراء'),
            ('sale_unit', 'TEXT', 'وحدة البيع'),
            ('conversion_factor', 'REAL DEFAULT 1', 'معامل التحويل بين وحدة الشراء والبيع'),
            ('supplier_id', 'INTEGER', 'معرف المورد'),
            ('barcode', 'TEXT', 'الباركود'),
            ('profit_margin', 'REAL DEFAULT 0', 'هامش الربح'),
            ('last_purchase_date', 'DATE', 'تاريخ آخر شراء'),
            ('last_purchase_price', 'REAL', 'آخر سعر شراء')
        ]
        
        for col_name, col_type, description in new_columns:
            if col_name not in column_names:
                try:
                    db_manager.execute_query(f"ALTER TABLE products ADD COLUMN {col_name} {col_type}")
                    print(f"✅ تم إضافة عمود {col_name} - {description}")
                except Exception as e:
                    print(f"⚠️ تحذير في إضافة عمود {col_name}: {e}")
            else:
                print(f"✅ عمود {col_name} موجود بالفعل")
        
        # تحديث الأعمدة الموجودة
        try:
            # تغيير اسم unit_price إلى sale_price إذا لم يكن موجود
            if 'unit_price' in column_names and 'sale_price' not in column_names:
                # نسخ البيانات من unit_price إلى sale_price
                db_manager.execute_query("UPDATE products SET sale_price = unit_price WHERE sale_price IS NULL OR sale_price = 0")
                print("✅ تم نسخ unit_price إلى sale_price")
            
            # تحديث وحدات البيع والشراء الافتراضية
            db_manager.execute_query("""
                UPDATE products 
                SET purchase_unit = COALESCE(unit_type, 'قطعة'),
                    sale_unit = COALESCE(unit_type, 'قطعة')
                WHERE purchase_unit IS NULL OR sale_unit IS NULL
            """)
            print("✅ تم تحديث الوحدات الافتراضية")
            
            # تحديث أسعار الشراء الافتراضية (80% من سعر البيع)
            db_manager.execute_query("""
                UPDATE products 
                SET purchase_price = COALESCE(sale_price * 0.8, unit_price * 0.8, 0)
                WHERE purchase_price IS NULL OR purchase_price = 0
            """)
            print("✅ تم تحديث أسعار الشراء الافتراضية")
            
            # حساب هامش الربح
            db_manager.execute_query("""
                UPDATE products 
                SET profit_margin = CASE 
                    WHEN purchase_price > 0 THEN ((sale_price - purchase_price) / purchase_price) * 100
                    ELSE 0 
                END
                WHERE profit_margin IS NULL OR profit_margin = 0
            """)
            print("✅ تم حساب هامش الربح")
            
        except Exception as e:
            print(f"⚠️ تحذير في تحديث البيانات: {e}")
        
        # إنشاء فهارس للأداء
        indexes = [
            ("idx_products_purchase_unit", "products", "purchase_unit"),
            ("idx_products_sale_unit", "products", "sale_unit"),
            ("idx_products_supplier", "products", "supplier_id"),
            ("idx_products_barcode", "products", "barcode"),
            ("idx_products_category_price", "products", "category, sale_price")
        ]
        
        for idx_name, table_name, columns in indexes:
            try:
                db_manager.execute_query(f"""
                    CREATE INDEX IF NOT EXISTS {idx_name} ON {table_name}({columns})
                """)
                print(f"✅ تم إنشاء فهرس {idx_name}")
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس {idx_name}: {e}")
        
        # عرض بنية الجدول المحدثة
        updated_columns = db_manager.fetch_all("PRAGMA table_info(products)")
        print(f"\n📊 بنية جدول المنتجات المحدثة:")
        for col in updated_columns:
            default_value = f" - Default: {col[4]}" if col[4] else ""
            print(f"  • {col[1]} - {col[2]}{default_value}")
        
        # عرض عينة من البيانات
        sample_products = db_manager.fetch_all("""
            SELECT id, name, purchase_price, sale_price, purchase_unit, sale_unit, profit_margin 
            FROM products 
            LIMIT 3
        """)
        
        if sample_products:
            print(f"\n📝 عينة من المنتجات المحدثة:")
            for product in sample_products:
                print(f"  • {product[1]}: شراء {product[2]} {product[4]} | بيع {product[3]} {product[5]} | ربح {product[6]:.1f}%")
        
        print("\n🎉 تم تحديث جدول المنتجات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول المنتجات: {e}")
        return False
    
    finally:
        db_manager.close()
    
    return True

if __name__ == "__main__":
    update_products_table()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة شركة إنترنت
Internet Company Management System

الملف الرئيسي لتشغيل البرنامج
Main application entry point
"""

import sys
import os
import json
from pathlib import Path

# التحقق من وجود PyQt5
try:
    from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog
    from PyQt5.QtCore import Qt, QTranslator, QLocale
    from PyQt5.QtGui import QFont, QIcon
except ImportError as e:
    print("❌ خطأ: PyQt5 غير مثبت")
    print("يرجى تثبيت PyQt5 باستخدام الأمر التالي:")
    print("pip install PyQt5")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# التحقق من وجود الوحدات المطلوبة
try:
    from src.database.database_manager import DatabaseManager
    from src.ui.login_window import LoginWindow
    from src.ui.main_window import MainWindow
    from src.utils.config_manager import ConfigManager
    from src.utils.arabic_support import setup_arabic_support
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    input("اضغط Enter للخروج...")
    sys.exit(1)



class InternetCompanyApp:
    """الكلاس الرئيسي للتطبيق"""
    
    def __init__(self):
        self.app = None
        self.config_manager = None
        self.db_manager = None
        self.main_window = None
        self.current_user = None
        
    def setup_application(self):
        """إعداد التطبيق الأساسي"""
        # إنشاء تطبيق PyQt5
        self.app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(self.app)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        font.setFamily("Tahoma")
        self.app.setFont(font)
        
        # إعداد اتجاه النص من اليمين إلى اليسار
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        return True
        
    def select_project_directory(self):
        """اختيار مجلد المشروع"""
        dialog = QFileDialog()
        dialog.setFileMode(QFileDialog.Directory)
        dialog.setWindowTitle("اختر مجلد حفظ بيانات النظام")
        
        if dialog.exec_():
            selected_dir = dialog.selectedFiles()[0]
            return selected_dir
        return None
        
    def initialize_system(self):
        """تهيئة النظام"""
        try:
            # اختيار مجلد المشروع
            project_dir = self.select_project_directory()
            if not project_dir:
                QMessageBox.critical(None, "خطأ", "يجب اختيار مجلد لحفظ بيانات النظام")
                return False
                
            # إنشاء مدير الإعدادات
            self.config_manager = ConfigManager(project_dir)
            
            # إنشاء مدير قاعدة البيانات
            self.db_manager = DatabaseManager(project_dir)
            
            # تهيئة قاعدة البيانات
            if not self.db_manager.initialize_database():
                QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
                return False
                
            return True
            
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"خطأ في تهيئة النظام: {str(e)}")
            return False
            
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow(self.db_manager)
        
        if login_window.exec_() == login_window.Accepted:
            self.current_user = login_window.get_current_user()
            return True
        return False
        
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.main_window = MainWindow(
            self.db_manager, 
            self.config_manager, 
            self.current_user
        )
        self.main_window.show()
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد التطبيق
            if not self.setup_application():
                return 1
                
            # تهيئة النظام
            if not self.initialize_system():
                return 1
                
            # عرض نافذة تسجيل الدخول
            if not self.show_login():
                return 0
                
            # عرض النافذة الرئيسية
            self.show_main_window()
            
            # تشغيل حلقة الأحداث
            return self.app.exec_()
            
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"خطأ في تشغيل التطبيق: {str(e)}")
            return 1

def main():
    """الدالة الرئيسية"""
    app = InternetCompanyApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())

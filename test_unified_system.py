#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الموحد للخزينة
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager
from utils.unified_treasury_manager import UnifiedTreasuryManager

def test_unified_system():
    """اختبار النظام الموحد للخزينة"""
    
    print("🧪 اختبار النظام الموحد للخزينة...")
    
    db = DatabaseManager('data/company_system.db')
    treasury_manager = UnifiedTreasuryManager(db)
    
    user_id = 1  # المستخدم الحالي
    
    try:
        print("\n=== 1. فتح الصندوق ===")
        
        # فتح الصندوق
        open_success = treasury_manager.open_cash_box(user_id=user_id)
        if open_success:
            print(f"✅ تم فتح الصندوق للمستخدم {user_id}")
        else:
            print(f"❌ فشل في فتح الصندوق")
            return
        
        # التحقق من الجلسة
        is_active = treasury_manager.is_session_active(user_id=user_id)
        print(f"📊 حالة الجلسة: {'مفتوحة' if is_active else 'مغلقة'}")
        
        print("\n=== 2. عرض الأرصدة الأولية ===")
        
        # عرض الأرصدة
        syp_daily = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily = treasury_manager.get_daily_balance(user_id, 'USD')
        syp_main = treasury_manager.get_main_balance(user_id, 'SYP')
        usd_main = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"📊 الخزينة اليومية:")
        print(f"  • الليرة السورية: {syp_daily:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_daily:.2f}")
        
        print(f"📊 الخزينة الرئيسية:")
        print(f"  • الليرة السورية: {syp_main:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_main:.2f}")
        
        print("\n=== 3. محاكاة المبيعات والمصاريف ===")
        
        # إضافة مبيعات
        sales_amount = 750000
        treasury_manager.add_to_daily_treasury(user_id, 'SYP', sales_amount)
        print(f"✅ تم إضافة مبيعات: {sales_amount:,} ل.س")
        
        # إضافة مصاريف
        expenses_amount = 80000
        treasury_manager.subtract_from_daily_treasury(user_id, 'SYP', expenses_amount)
        print(f"✅ تم خصم مصاريف: {expenses_amount:,} ل.س")
        
        # عرض الرصيد بعد العمليات
        syp_after_ops = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"📊 الرصيد بعد العمليات: {syp_after_ops:,} ل.س")
        
        print("\n=== 4. محاكاة صرف العملة ===")
        
        # صرف عملة
        exchange_amount = 150000
        exchange_rate = 15000
        usd_amount = exchange_amount / exchange_rate
        
        exchange_success = treasury_manager.exchange_currency(
            user_id=user_id,
            from_currency='SYP',
            to_currency='USD',
            amount_from=exchange_amount,
            exchange_rate=exchange_rate
        )
        
        if exchange_success:
            print(f"✅ تم صرف العملة: {exchange_amount:,} ل.س → ${usd_amount:.2f}")
        else:
            print(f"❌ فشل في صرف العملة")
        
        # عرض الأرصدة بعد الصرف
        syp_after_exchange = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_after_exchange = treasury_manager.get_daily_balance(user_id, 'USD')
        
        print(f"📊 الأرصدة بعد صرف العملة:")
        print(f"  • الليرة السورية: {syp_after_exchange:,} ل.س")
        print(f"  • الدولار الأمريكي: ${usd_after_exchange:.2f}")
        
        print("\n=== 5. محاكاة نقل للخزينة الرئيسية ===")
        
        # نقل جزء من الليرة السورية
        transfer_syp = 200000
        if syp_after_exchange >= transfer_syp:
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=user_id,
                currency_type='SYP',
                amount=transfer_syp
            )
            
            if transfer_success:
                print(f"✅ تم نقل {transfer_syp:,} ل.س للخزينة الرئيسية")
            else:
                print(f"❌ فشل في النقل")
        else:
            print(f"❌ الرصيد غير كافي للنقل")
        
        # نقل كامل الدولار
        if usd_after_exchange > 0:
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=user_id,
                currency_type='USD',
                amount=usd_after_exchange
            )
            
            if transfer_success:
                print(f"✅ تم نقل ${usd_after_exchange:.2f} للخزينة الرئيسية")
            else:
                print(f"❌ فشل في نقل الدولار")
        
        # عرض الأرصدة بعد النقل
        print(f"\n📊 الأرصدة بعد النقل:")
        
        syp_daily_final = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily_final = treasury_manager.get_daily_balance(user_id, 'USD')
        syp_main_final = treasury_manager.get_main_balance(user_id, 'SYP')
        usd_main_final = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"  الخزينة اليومية:")
        print(f"    • الليرة السورية: {syp_daily_final:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_daily_final:.2f}")
        
        print(f"  الخزينة الرئيسية:")
        print(f"    • الليرة السورية: {syp_main_final:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_main_final:.2f}")
        
        print("\n=== 6. محاكاة إغلاق الصندوق ===")
        
        # إغلاق الصندوق
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        
        if close_success:
            print(f"✅ تم إغلاق الصندوق بنجاح")
            print(f"💾 تم إنشاء نسخة احتياطية")
        else:
            print(f"❌ فشل في إغلاق الصندوق")
        
        # التحقق من الجلسة بعد الإغلاق
        is_active_after = treasury_manager.is_session_active(user_id=user_id)
        print(f"📊 حالة الجلسة بعد الإغلاق: {'مفتوحة' if is_active_after else 'مغلقة'}")
        
        # عرض الأرصدة النهائية
        print(f"\n📊 الأرصدة النهائية بعد إغلاق الصندوق:")
        
        syp_daily_closed = treasury_manager.get_daily_balance(user_id, 'SYP')
        usd_daily_closed = treasury_manager.get_daily_balance(user_id, 'USD')
        syp_main_closed = treasury_manager.get_main_balance(user_id, 'SYP')
        usd_main_closed = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"  الخزينة اليومية:")
        print(f"    • الليرة السورية: {syp_daily_closed:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_daily_closed:.2f}")
        
        print(f"  الخزينة الرئيسية:")
        print(f"    • الليرة السورية: {syp_main_closed:,} ل.س")
        print(f"    • الدولار الأمريكي: ${usd_main_closed:.2f}")
        
        print("\n=== 7. فحص النسخة الاحتياطية ===")
        
        # فحص وجود النسخة الاحتياطية
        backup_dir = "backups"
        if os.path.exists(backup_dir):
            backups = [d for d in os.listdir(backup_dir) if d.startswith('backup_')]
            if backups:
                latest_backup = sorted(backups)[-1]
                backup_path = os.path.join(backup_dir, latest_backup)
                
                print(f"📁 آخر نسخة احتياطية: {latest_backup}")
                
                # فحص محتويات النسخة الاحتياطية
                if os.path.exists(backup_path):
                    files = os.listdir(backup_path)
                    print(f"📋 محتويات النسخة الاحتياطية:")
                    for file in files:
                        file_path = os.path.join(backup_path, file)
                        if os.path.isfile(file_path):
                            size = os.path.getsize(file_path)
                            print(f"  • {file}: {size:,} بايت")
                        else:
                            print(f"  • {file}: مجلد")
            else:
                print(f"⚠️ لا توجد نسخ احتياطية")
        else:
            print(f"⚠️ مجلد النسخ الاحتياطية غير موجود")
        
        print(f"\n🎉 انتهى اختبار النظام الموحد بنجاح!")
        
        print(f"\n📋 ملخص النظام الموحد:")
        print(f"  ✅ جدول واحد للخزينة (unified_treasury)")
        print(f"  ✅ فتح الصندوق عند تسجيل الدخول")
        print(f"  ✅ إغلاق الصندوق ينقل الأموال ويُنشئ نسخة احتياطية")
        print(f"  ✅ صرف العملة يحول الأموال في الخزينة اليومية")
        print(f"  ✅ نقل الخزينة من اليومية للرئيسية")
        print(f"  ✅ جميع العمليات مربوطة بالمستخدم المسجل")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_unified_system()

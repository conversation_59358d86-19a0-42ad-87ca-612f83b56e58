#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإصلاح واجهات الخزينة
"""

import sys
import os
sys.path.append('src')

def test_currency_exchange_interface():
    """اختبار واجهة شراء الدولار"""
    
    print("🧪 اختبار واجهة شراء الدولار...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة
        syp_balance = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        usd_balance = treasury_manager.get_daily_balance(current_user['id'], 'USD')
        
        print(f"💰 رصيد الليرة السورية: {syp_balance:,} ل.س")
        print(f"💰 رصيد الدولار: ${usd_balance:.2f}")
        
        if syp_balance > 0:
            print("✅ واجهة شراء الدولار تعمل - يوجد رصيد متاح")
            
            # محاكاة عملية شراء دولار
            if syp_balance >= 100000:
                print("\n🔄 محاكاة شراء دولار...")
                exchange_rate = 15000  # سعر الصرف
                syp_amount = 100000
                usd_amount = syp_amount / exchange_rate
                
                # خصم الليرة
                subtract_success = treasury_manager.subtract_from_daily_treasury(
                    user_id=current_user['id'],
                    currency_type='SYP',
                    amount=syp_amount
                )
                
                if subtract_success:
                    # إضافة الدولار
                    add_success = treasury_manager.add_to_daily_treasury(
                        user_id=current_user['id'],
                        currency_type='USD',
                        amount=usd_amount
                    )
                    
                    if add_success:
                        new_syp = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                        new_usd = treasury_manager.get_daily_balance(current_user['id'], 'USD')
                        
                        print(f"  ✅ تم شراء ${usd_amount:.2f} مقابل {syp_amount:,} ل.س")
                        print(f"  💰 الأرصدة الجديدة: {new_syp:,} ل.س، ${new_usd:.2f}")
                        return True
                    else:
                        print("  ❌ فشل في إضافة الدولار")
                        return False
                else:
                    print("  ❌ فشل في خصم الليرة")
                    return False
            else:
                print("⚠️ الرصيد أقل من 100,000 ل.س لاختبار الشراء")
                return True
        else:
            print("❌ واجهة شراء الدولار لا تعمل - لا يوجد رصيد")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة شراء الدولار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_interface():
    """اختبار واجهة نقل الخزينة"""
    
    print("\n🧪 اختبار واجهة نقل الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة
        daily_syp = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_syp = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الخزينة اليومية: {daily_syp:,} ل.س")
        print(f"💰 الخزينة الرئيسية: {main_syp:,} ل.س")
        
        if daily_syp > 0:
            print("✅ واجهة نقل الخزينة تعمل - يوجد رصيد متاح")
            
            # محاكاة عملية نقل
            if daily_syp >= 50000:
                print("\n🔄 محاكاة نقل للخزينة الرئيسية...")
                transfer_amount = 50000
                
                # تنفيذ النقل
                transfer_success = treasury_manager.transfer_to_main_treasury(
                    user_id=current_user['id'],
                    currency_type='SYP',
                    amount=transfer_amount
                )
                
                if transfer_success:
                    new_daily = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                    new_main = treasury_manager.get_main_balance('SYP')
                    
                    print(f"  ✅ تم نقل {transfer_amount:,} ل.س للخزينة الرئيسية")
                    print(f"  💰 الأرصدة الجديدة: يومي {new_daily:,} ل.س، رئيسي {new_main:,} ل.س")
                    return True
                else:
                    print("  ❌ فشل في النقل")
                    return False
            else:
                print("⚠️ الرصيد أقل من 50,000 ل.س لاختبار النقل")
                return True
        else:
            print("❌ واجهة نقل الخزينة لا تعمل - لا يوجد رصيد")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_final_status():
    """عرض الحالة النهائية"""
    
    print("\n📊 الحالة النهائية للنظام:")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1}
        
        # عرض جميع الأرصدة
        currencies = ['SYP', 'USD', 'EUR']
        
        print("\n💰 الأرصدة الحالية:")
        for currency in currencies:
            daily_balance = treasury_manager.get_daily_balance(current_user['id'], currency)
            main_balance = treasury_manager.get_main_balance(currency)
            
            symbol = "ل.س" if currency == "SYP" else ("$" if currency == "USD" else "€")
            print(f"  • {currency}: يومي {daily_balance:,.2f} {symbol} - رئيسي {main_balance:,.2f} {symbol}")
        
        # فحص الجداول
        print("\n📋 حالة الجداول:")
        
        # فحص unified_treasury
        unified_records = db.fetch_all("SELECT COUNT(*) as count FROM unified_treasury")
        print(f"  • unified_treasury: {unified_records[0]['count']} سجل")
        
        # فحص transactions
        transactions = db.fetch_all("SELECT COUNT(*) as count FROM transactions WHERE type LIKE '%خزينة%'")
        print(f"  • معاملات الخزينة: {transactions[0]['count']} معاملة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض الحالة النهائية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار نهائي لإصلاح واجهات الخزينة")
    print("=" * 60)
    
    # اختبار واجهة شراء الدولار
    currency_exchange_test = test_currency_exchange_interface()
    
    # اختبار واجهة نقل الخزينة
    treasury_transfer_test = test_treasury_transfer_interface()
    
    # عرض الحالة النهائية
    status_display = show_final_status()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار النهائي:")
    print(f"  • واجهة شراء الدولار: {'✅ تعمل' if currency_exchange_test else '❌ لا تعمل'}")
    print(f"  • واجهة نقل الخزينة: {'✅ تعمل' if treasury_transfer_test else '❌ لا تعمل'}")
    print(f"  • عرض الحالة: {'✅ يعمل' if status_display else '❌ لا يعمل'}")
    
    if all([currency_exchange_test, treasury_transfer_test, status_display]):
        print("\n🎉 تم إصلاح جميع مشاكل واجهات الخزينة!")
        
        print("\n📋 النتائج:")
        print("  ✅ واجهة شراء الدولار تعمل بشكل طبيعي")
        print("  ✅ واجهة نقل الخزينة تعمل بشكل طبيعي")
        print("  ✅ جميع الأرصدة متوفرة ومتوافقة")
        print("  ✅ النظام الموحد للخزينة يعمل بالكامل")
        
        print("\n🚀 للاستخدام:")
        print("  1. شغل النظام بـ: python system_launcher.py")
        print("  2. سجل دخول بـ: admin / admin123")
        print("  3. افتح واجهة شراء الدولار - ستجد الرصيد متوفر")
        print("  4. افتح واجهة نقل الخزينة - ستجد الرصيد متوفر")
        print("  5. جميع العمليات ستعمل بشكل طبيعي")
        
        print("\n💡 ملاحظة:")
        print("  • الأرصدة الآن في النظام الموحد")
        print("  • جميع الواجهات تقرأ من نفس المصدر")
        print("  • لا يوجد تضارب بين الجداول")
        print("  • العملات المتعددة مدعومة")
        
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إصلاح!")
        
        if not currency_exchange_test:
            print("  • واجهة شراء الدولار تحتاج فحص إضافي")
        if not treasury_transfer_test:
            print("  • واجهة نقل الخزينة تحتاج فحص إضافي")

if __name__ == "__main__":
    main()

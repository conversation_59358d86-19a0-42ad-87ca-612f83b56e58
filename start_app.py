#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة شركة الإنترنت
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل التطبيق"""
    try:
        # إعداد المسارات
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        
        sys.path.insert(0, str(project_root))
        sys.path.insert(0, str(src_path))
        
        # تجاهل تحذيرات Qt
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
        
        print("🌟 نظام إدارة شركة الإنترنت - النسخة النهائية")
        print("=" * 70)
        
        # استيراد الوحدات
        from PyQt5.QtWidgets import QApplication
        from src.database.database_manager import DatabaseManager
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        
        print("✅ تم تحميل جميع الوحدات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        setup_arabic_support(app)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        config_manager = ConfigManager()
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # فتح نافذة تسجيل الدخول
        login_window = LoginWindow(db_manager, config_manager)
        
        print("📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123")
        print("")
        
        if login_window.exec_() == login_window.Accepted:
            current_user = login_window.get_current_user()
            print(f"✅ تم تسجيل الدخول: {current_user['full_name']}")
            
            # فتح النافذة الرئيسية
            main_window = MainWindow(db_manager, config_manager, current_user)
            main_window.show()
            
            print("✅ تم فتح النافذة الرئيسية")
            print("🎉 النظام جاهز للاستخدام!")
            
            # تشغيل التطبيق
            sys.exit(app.exec_())
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جدول transactions
"""

import sqlite3
from datetime import date

def main():
    conn = sqlite3.connect('data/company_system.db')
    cursor = conn.cursor()

    today = date.today().strftime('%Y-%m-%d')
    print(f'=== جميع المعاملات في transactions لتاريخ {today} ===')

    cursor.execute('SELECT * FROM transactions WHERE DATE(created_at) = ? ORDER BY created_at DESC', (today,))
    transactions = cursor.fetchall()

    print(f'عدد المعاملات: {len(transactions)}')
    
    for t in transactions:
        print(f'ID: {t[0]}, النوع: {t[1]}, المبلغ: {t[3]}, المستخدم: {t[7]}, التاريخ: {t[8]}')

    print(f'\n=== معاملات المبيعات فقط ===')
    cursor.execute("""
        SELECT * FROM transactions 
        WHERE DATE(created_at) = ? AND type IN ('اشتراك جديد', 'تسليم راوتر', 'تجديد باقة')
        ORDER BY created_at DESC
    """, (today,))
    
    sales = cursor.fetchall()
    print(f'عدد معاملات المبيعات: {len(sales)}')
    
    for s in sales:
        print(f'النوع: {s[1]}, المبلغ: {s[3]}, المستخدم: {s[7]}')

    conn.close()

if __name__ == "__main__":
    main()

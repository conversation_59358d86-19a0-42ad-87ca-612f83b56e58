#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التدفق المالي الصحيح
"""

import sys
import os
sys.path.append('src')

def test_cash_box_to_daily_treasury():
    """اختبار نقل رصيد الصندوق للخزينة اليومية"""
    
    print("🧪 اختبار نقل رصيد الصندوق للخزينة اليومية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # 1. فتح شيفت/صندوق جديد
        print("\n🔓 فتح شيفت/صندوق جديد...")
        shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
        
        if shift_opened:
            print("✅ تم فتح شيفت/صندوق جديد")
        else:
            print("⚠️ الشيفت/الصندوق مفتوح مسبقاً")
        
        # 2. إضافة مبيعات للصندوق
        print("\n💵 إضافة مبيعات للصندوق...")
        sales_added = treasury_manager.add_to_cash_box(
            user_id=current_user['id'],
            amount=250000,
            description="مبيعات تجريبية - اختبار التدفق",
            transaction_type="sale"
        )
        
        if sales_added:
            print("✅ تم إضافة مبيعات للصندوق")
            
            # فحص رصيد الصندوق
            cash_box_balance = treasury_manager.get_cash_box_balance(current_user['id'])
            print(f"💰 رصيد الصندوق: {cash_box_balance:,} ل.س")
        else:
            print("❌ فشل في إضافة المبيعات للصندوق")
            return False
        
        # 3. فحص الخزينة اليومية قبل الإغلاق
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        print(f"💰 الخزينة اليومية قبل الإغلاق: {daily_balance_before:,} ل.س")
        
        # 4. إغلاق الصندوق (نقل للخزينة اليومية)
        print("\n🔒 إغلاق الصندوق ونقل للخزينة اليومية...")
        closure_success = treasury_manager.close_cash_box_to_daily_treasury(
            user_id=current_user['id'],
            actual_cash=cash_box_balance,
            notes="اختبار التدفق المالي الصحيح"
        )
        
        if closure_success:
            print("✅ تم إغلاق الصندوق ونقل الرصيد للخزينة اليومية")
            
            # فحص الخزينة اليومية بعد الإغلاق
            daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
            print(f"💰 الخزينة اليومية بعد الإغلاق: {daily_balance_after:,} ل.س")
            
            # التحقق من النقل
            expected_increase = cash_box_balance
            actual_increase = daily_balance_after - daily_balance_before
            
            if abs(actual_increase - expected_increase) < 1:  # تسامح في الفروق الصغيرة
                print(f"✅ تم النقل بشكل صحيح: زيادة {actual_increase:,} ل.س")
                return True
            else:
                print(f"❌ خطأ في النقل: متوقع {expected_increase:,} ل.س، فعلي {actual_increase:,} ل.س")
                return False
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نقل الصندوق للخزينة اليومية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_exchange_from_daily_treasury():
    """اختبار شراء الدولار من الخزينة اليومية"""
    
    print("\n🧪 اختبار شراء الدولار من الخزينة اليومية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة قبل التحويل
        syp_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        usd_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'USD')
        
        print(f"💰 رصيد الليرة قبل التحويل: {syp_balance_before:,} ل.س")
        print(f"💰 رصيد الدولار قبل التحويل: ${usd_balance_before:.2f}")
        
        if syp_balance_before >= 150000:  # نحتاج على الأقل 150000 ل.س
            # تحويل العملة
            syp_amount = 150000
            exchange_rate = 15000
            
            print(f"\n💱 تحويل {syp_amount:,} ل.س إلى دولار بسعر {exchange_rate:,}...")
            
            exchange_success = treasury_manager.exchange_currency(
                user_id=current_user['id'],
                from_currency='SYP',
                to_currency='USD',
                amount_from=syp_amount,
                exchange_rate=exchange_rate
            )
            
            if exchange_success:
                print("✅ تم تحويل العملة بنجاح")
                
                # فحص الأرصدة بعد التحويل
                syp_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                usd_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'USD')
                
                expected_usd = syp_amount / exchange_rate
                
                print(f"💰 رصيد الليرة بعد التحويل: {syp_balance_after:,} ل.س")
                print(f"💰 رصيد الدولار بعد التحويل: ${usd_balance_after:.2f}")
                
                # التحقق من صحة التحويل
                syp_decrease = syp_balance_before - syp_balance_after
                usd_increase = usd_balance_after - usd_balance_before
                
                if (abs(syp_decrease - syp_amount) < 1 and 
                    abs(usd_increase - expected_usd) < 0.01):
                    print(f"✅ التحويل صحيح: خصم {syp_decrease:,} ل.س، إضافة ${usd_increase:.2f}")
                    return True
                else:
                    print(f"❌ خطأ في التحويل")
                    return False
            else:
                print("❌ فشل في تحويل العملة")
                return False
        else:
            print("⚠️ لا يوجد رصيد كافي للتحويل")
            return True  # لا نعتبرها فشل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار شراء الدولار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_daily_to_main():
    """اختبار نقل الخزينة من اليومية للرئيسية"""
    
    print("\n🧪 اختبار نقل الخزينة من اليومية للرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # فحص الأرصدة قبل النقل
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_balance_before = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الخزينة اليومية قبل النقل: {daily_balance_before:,} ل.س")
        print(f"💰 الخزينة الرئيسية قبل النقل: {main_balance_before:,} ل.س")
        
        if daily_balance_before > 0:
            # نقل جزء من المبلغ
            transfer_amount = min(100000, daily_balance_before)
            
            print(f"\n🔄 نقل {transfer_amount:,} ل.س من اليومية للرئيسية...")
            
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=current_user['id'],
                currency_type='SYP',
                amount=transfer_amount
            )
            
            if transfer_success:
                print("✅ تم النقل بنجاح")
                
                # فحص الأرصدة بعد النقل
                daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                main_balance_after = treasury_manager.get_main_balance('SYP')
                
                print(f"💰 الخزينة اليومية بعد النقل: {daily_balance_after:,} ل.س")
                print(f"💰 الخزينة الرئيسية بعد النقل: {main_balance_after:,} ل.س")
                
                # التحقق من صحة النقل
                daily_decrease = daily_balance_before - daily_balance_after
                main_increase = main_balance_after - main_balance_before
                
                if (abs(daily_decrease - transfer_amount) < 1 and
                    abs(main_increase - transfer_amount) < 1):
                    print(f"✅ النقل صحيح: خصم {daily_decrease:,} ل.س، إضافة {main_increase:,} ل.س")
                    return True
                else:
                    print(f"❌ خطأ في النقل")
                    return False
            else:
                print("❌ فشل في النقل")
                return False
        else:
            print("⚠️ لا يوجد رصيد في الخزينة اليومية للنقل")
            return True  # لا نعتبرها فشل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار التدفق المالي الصحيح")
    print("=" * 70)
    
    # اختبار نقل الصندوق للخزينة اليومية
    cash_box_test = test_cash_box_to_daily_treasury()
    
    # اختبار شراء الدولار من الخزينة اليومية
    currency_exchange_test = test_currency_exchange_from_daily_treasury()
    
    # اختبار نقل الخزينة من اليومية للرئيسية
    treasury_transfer_test = test_treasury_transfer_daily_to_main()
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • نقل الصندوق للخزينة اليومية: {'✅ يعمل' if cash_box_test else '❌ لا يعمل'}")
    print(f"  • شراء الدولار من الخزينة اليومية: {'✅ يعمل' if currency_exchange_test else '❌ لا يعمل'}")
    print(f"  • نقل الخزينة من اليومية للرئيسية: {'✅ يعمل' if treasury_transfer_test else '❌ لا يعمل'}")
    
    if all([cash_box_test, currency_exchange_test, treasury_transfer_test]):
        print("\n🎉 التدفق المالي الصحيح يعمل بشكل مثالي!")
        
        print("\n📋 التدفق المالي الصحيح:")
        print("  1️⃣ الصندوق المفتوح ← المبيعات والمصاريف اليومية")
        print("  2️⃣ إغلاق الصندوق → نقل رصيد الصندوق إلى الخزينة اليومية")
        print("  3️⃣ شراء الدولار ← يتعامل مع الخزينة اليومية (ليرة ← دولار)")
        print("  4️⃣ نقل الخزينة → من الخزينة اليومية إلى الخزينة الرئيسية")
        
        print("\n🎯 المميزات:")
        print("  ✅ الصندوق منفصل عن الخزينة اليومية")
        print("  ✅ إغلاق الصندوق ينقل للخزينة اليومية")
        print("  ✅ شراء الدولار من الخزينة اليومية")
        print("  ✅ نقل الخزينة من اليومية للرئيسية")
        print("  ✅ كل خطوة منطقية ومنفصلة")
        
        print("\n🚀 للاستخدام:")
        print("  • المبيعات تُسجل في الصندوق")
        print("  • إغلاق الصندوق ينقل للخزينة اليومية")
        print("  • شراء الدولار من الخزينة اليومية")
        print("  • نقل الخزينة من اليومية للرئيسية")
        
    else:
        print("\n❌ هناك مشاكل في التدفق المالي!")

if __name__ == "__main__":
    main()

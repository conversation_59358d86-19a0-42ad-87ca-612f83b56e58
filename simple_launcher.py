#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل مبسط لنظام إدارة شركة الإنترنت
Simple Launcher for Internet Company Management System
"""

import sys
import os
from pathlib import Path

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة شركة الإنترنت")
    print("=" * 50)
    
    # إعداد المسارات
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    
    sys.path.insert(0, str(project_root))
    sys.path.insert(0, str(src_path))
    
    # تجاهل تحذيرات Qt
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    try:
        print("📦 تحميل المكتبات...")
        
        # استيراد PyQt5
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        print("✅ PyQt5")
        
        # استيراد وحدات النظام
        from src.database.database_manager import DatabaseManager
        from src.utils.config_manager import ConfigManager
        from src.utils.arabic_support import setup_arabic_support
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        print("✅ وحدات النظام")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الدعم العربي
        setup_arabic_support(app)
        print("✅ الدعم العربي")
        
        # إنشاء مجلد البيانات
        data_dir = project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager(str(data_dir))
        config_manager = ConfigManager(str(data_dir))
        
        print("📊 تهيئة قاعدة البيانات...")
        if not db_manager.initialize_database():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ قاعدة البيانات جاهزة")
        
        # عرض نافذة تسجيل الدخول
        print("🔐 فتح نافذة تسجيل الدخول...")
        login_window = LoginWindow(db_manager)
        
        if login_window.exec_() != login_window.Accepted:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
        
        current_user = login_window.get_current_user()
        print(f"✅ مرحباً {current_user['full_name']}")
        
        # عرض النافذة الرئيسية
        print("🖥️ فتح النافذة الرئيسية...")
        main_window = MainWindow(db_manager, config_manager, current_user)
        main_window.show()
        
        print("🎉 تم تشغيل النظام بنجاح!")
        print("=" * 50)
        print("📋 بيانات الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("=" * 50)
        
        # تشغيل التطبيق
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

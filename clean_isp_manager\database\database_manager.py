#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات النظيف والمحسن
"""

import sqlite3
import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

class DatabaseManager:
    """مدير قاعدة البيانات المحسن"""
    
    def __init__(self, db_path: str):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.logger = logging.getLogger(__name__)
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        self.connect()
        
        # إنشاء الجداول
        self.create_tables()
        
        # إدراج البيانات الافتراضية
        self.insert_default_data()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30
            )
            self.connection.row_factory = sqlite3.Row
            self.connection.execute("PRAGMA foreign_keys = ON")
            self.logger.info("تم الاتصال بقاعدة البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                permissions TEXT DEFAULT '{}',
                settings TEXT DEFAULT '{}'
            )
        """)
        
        # جدول الباقات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS packages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                speed TEXT NOT NULL,
                price REAL NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول المنتجات (راوترات، كابلات، إلخ)
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                cost_price REAL DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول العمال
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                salary REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول المشتركين
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS subscribers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                package_id INTEGER,
                router_delivered BOOLEAN DEFAULT 0,
                router_type TEXT,
                cable_meters REAL DEFAULT 0,
                installation_worker_id INTEGER,
                subscription_fee_paid BOOLEAN DEFAULT 0,
                router_price_paid BOOLEAN DEFAULT 0,
                cable_cost_paid BOOLEAN DEFAULT 0,
                total_amount REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (package_id) REFERENCES packages (id),
                FOREIGN KEY (installation_worker_id) REFERENCES workers (id)
            )
        """)
        
        # جدول المخزون الرئيسي
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS main_inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                UNIQUE(product_id)
            )
        """)
        
        # جدول مخزون العمال
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS worker_inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES workers (id),
                FOREIGN KEY (product_id) REFERENCES products (id),
                UNIQUE(worker_id, product_id)
            )
        """)
        
        # جدول الخزينة الموحدة
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS treasury (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                currency TEXT NOT NULL DEFAULT 'SYP',
                daily_balance REAL NOT NULL DEFAULT 0,
                main_balance REAL NOT NULL DEFAULT 0,
                session_date DATE NOT NULL,
                is_session_active BOOLEAN DEFAULT 1,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # جدول المعاملات المالية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                type TEXT NOT NULL,
                category TEXT NOT NULL,
                amount REAL NOT NULL,
                currency TEXT DEFAULT 'SYP',
                description TEXT,
                reference_id INTEGER,
                reference_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # جدول المصروفات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                category TEXT NOT NULL,
                amount REAL NOT NULL,
                currency TEXT DEFAULT 'SYP',
                description TEXT NOT NULL,
                recipient TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # جدول الإعدادات المالية
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS financial_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                description TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول سجل العمليات
        self.execute_query("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # إنشاء الفهارس للأداء
        self.create_indexes()
        
        self.logger.info("تم إنشاء جداول قاعدة البيانات بنجاح")
    
    def create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_subscribers_name ON subscribers(name)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, created_at)",
            "CREATE INDEX IF NOT EXISTS idx_treasury_user_date ON treasury(user_id, session_date)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_user_date ON activity_log(user_id, created_at)"
        ]
        
        for index in indexes:
            self.execute_query(index)
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        
        # إنشاء المستخدم الافتراضي
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = 'admin'")
        if not admin_exists:
            self.execute_query("""
                INSERT INTO users (username, password, full_name, role, permissions)
                VALUES ('admin', 'admin123', 'المدير العام', 'admin', '{"all": true}')
            """)
        
        # إدراج الباقات الافتراضية
        default_packages = [
            ('باقة 10 ميجا', '10 Mbps', 25000, 'باقة إنترنت سرعة 10 ميجا'),
            ('باقة 20 ميجا', '20 Mbps', 35000, 'باقة إنترنت سرعة 20 ميجا'),
            ('باقة 50 ميجا', '50 Mbps', 50000, 'باقة إنترنت سرعة 50 ميجا')
        ]
        
        for package in default_packages:
            exists = self.fetch_one("SELECT id FROM packages WHERE name = ?", (package[0],))
            if not exists:
                self.execute_query("""
                    INSERT INTO packages (name, speed, price, description)
                    VALUES (?, ?, ?, ?)
                """, package)
        
        # إدراج المنتجات الافتراضية
        default_products = [
            ('راوتر TP-Link', 'router', 75000, 60000, 'راوتر TP-Link للإنترنت'),
            ('راوتر Huawei', 'router', 85000, 70000, 'راوتر Huawei للإنترنت'),
            ('كابل شبكة', 'cable', 1000, 800, 'كابل شبكة للمتر الواحد')
        ]
        
        for product in default_products:
            exists = self.fetch_one("SELECT id FROM products WHERE name = ?", (product[0],))
            if not exists:
                self.execute_query("""
                    INSERT INTO products (name, category, price, cost_price, description)
                    VALUES (?, ?, ?, ?, ?)
                """, product)
        
        # إدراج الإعدادات المالية الافتراضية
        default_settings = [
            ('subscription_fee', '100000', 'رسم الاشتراك الافتراضي'),
            ('exchange_rate_usd', '15000', 'سعر صرف الدولار مقابل الليرة'),
            ('company_name', 'شركة الإنترنت المتطورة', 'اسم الشركة'),
            ('company_phone', '+963-XXX-XXXX', 'هاتف الشركة')
        ]
        
        for setting in default_settings:
            exists = self.fetch_one("SELECT id FROM financial_settings WHERE setting_name = ?", (setting[0],))
            if not exists:
                self.execute_query("""
                    INSERT INTO financial_settings (setting_name, setting_value, description)
                    VALUES (?, ?, ?)
                """, setting)
        
        self.logger.info("تم إدراج البيانات الافتراضية بنجاح")
    
    def execute_query(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            self.connection.rollback()
            return False
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[sqlite3.Row]:
        """جلب سجل واحد"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"خطأ في جلب السجل: {e}")
            return None
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """جلب جميع السجلات"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"خطأ في جلب السجلات: {e}")
            return []
    
    def close(self):
        """إغلاق الاتصال"""
        if self.connection:
            self.connection.close()
            self.logger.info("تم إغلاق الاتصال بقاعدة البيانات")
    
    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نقل الخزينة المتجاوبة والكاملة
"""

import logging
from datetime import datetime, date
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QGridLayout, QPushButton, QLabel, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QGroupBox, QFrame, QMessageBox,
                             QDateEdit, QProgressBar, QSizePolicy, QScrollArea)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from database.database_manager import DatabaseManager
from utils.treasury_manager import TreasuryManager
from utils.window_utils import center_window, apply_modern_style
from config.app_config import AppConfig

class TreasuryTransferWindow(QDialog):
    """واجهة نقل الخزينة المتجاوبة والكاملة"""
    
    # إشارات مخصصة
    transfer_completed = pyqtSignal(dict)
    balance_updated = pyqtSignal()
    
    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """تهيئة واجهة نقل الخزينة"""
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.current_user = current_user
        self.treasury_manager = TreasuryManager(db_manager)
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الحالة
        self.current_balances = {}
        self.is_mobile_view = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.setup_responsive_design()
        
        # توسيط النافذة
        center_window(self)
        
        # تطبيق التنسيق الحديث
        apply_modern_style(self)
        
        # بدء المؤقتات
        self.start_timers()
        
        self.logger.info("تم فتح واجهة نقل الخزينة")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المتجاوبة"""
        
        # إعداد النافذة
        self.setWindowTitle("نقل الخزينة")
        self.setModal(True)
        self.setMinimumSize(600, 500)
        self.resize(900, 700)
        
        # إنشاء منطقة التمرير للتجاوب مع الشاشات الصغيرة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # الويدجت الرئيسي
        main_widget = QFrame()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الأقسام
        header_section = self.create_header_section()
        main_layout.addWidget(header_section)
        
        balances_section = self.create_balances_section()
        main_layout.addWidget(balances_section)
        
        transfer_section = self.create_transfer_section()
        main_layout.addWidget(transfer_section)
        
        summary_section = self.create_summary_section()
        main_layout.addWidget(summary_section)
        
        buttons_section = self.create_buttons_section()
        main_layout.addWidget(buttons_section)
        
        # إعداد التخطيط النهائي
        dialog_layout = QVBoxLayout()
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(scroll_area)
        self.setLayout(dialog_layout)
    
    def create_header_section(self) -> QFrame:
        """إنشاء قسم الهيدر المتجاوب"""
        
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # أيقونة نقل الخزينة
        icon_label = QLabel("💰")
        icon_label.setStyleSheet("font-size: 28px; margin-right: 10px;")
        layout.addWidget(icon_label)
        
        # معلومات الهيدر
        info_layout = QVBoxLayout()
        
        title_label = QLabel("نقل الخزينة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        info_layout.addWidget(title_label)
        
        subtitle_label = QLabel("نقل الأموال من الخزينة اليومية إلى الرئيسية")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #ecf0f1;
                margin: 0;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # معلومات المستخدم والوقت
        user_info_layout = QVBoxLayout()
        
        user_label = QLabel(f"المستخدم: {self.current_user['full_name']}")
        user_label.setStyleSheet("font-size: 12px; color: #ecf0f1;")
        user_info_layout.addWidget(user_label)
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 11px; color: #bdc3c7;")
        user_info_layout.addWidget(self.time_label)
        
        layout.addLayout(user_info_layout)
        
        return frame
    
    def create_balances_section(self) -> QGroupBox:
        """إنشاء قسم عرض الأرصدة المتجاوب"""
        
        group = QGroupBox("الأرصدة الحالية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #3498db;
                background-color: white;
            }
        """)
        
        # استخدام Grid Layout للتجاوب
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # الخزينة اليومية - الليرة السورية
        daily_syp_frame = self.create_balance_card("الخزينة اليومية", "SYP", "#27ae60")
        self.daily_syp_label = daily_syp_frame.findChild(QLabel, "balance_label")
        layout.addWidget(daily_syp_frame, 0, 0)
        
        # الخزينة اليومية - الدولار
        daily_usd_frame = self.create_balance_card("الخزينة اليومية", "USD", "#f39c12")
        self.daily_usd_label = daily_usd_frame.findChild(QLabel, "balance_label")
        layout.addWidget(daily_usd_frame, 0, 1)
        
        # الخزينة الرئيسية - الليرة السورية
        main_syp_frame = self.create_balance_card("الخزينة الرئيسية", "SYP", "#8e44ad")
        self.main_syp_label = main_syp_frame.findChild(QLabel, "balance_label")
        layout.addWidget(main_syp_frame, 1, 0)
        
        # الخزينة الرئيسية - الدولار
        main_usd_frame = self.create_balance_card("الخزينة الرئيسية", "USD", "#e74c3c")
        self.main_usd_label = main_usd_frame.findChild(QLabel, "balance_label")
        layout.addWidget(main_usd_frame, 1, 1)
        
        return group
    
    def create_balance_card(self, title: str, currency: str, color: str) -> QFrame:
        """إنشاء بطاقة رصيد متجاوبة"""
        
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                color: white;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 80px;
            }}
            QFrame:hover {{
                background-color: {self.darken_color(color)};
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # عنوان البطاقة
        title_label = QLabel(f"{title} ({currency})")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        layout.addWidget(title_label)
        
        # قيمة الرصيد
        balance_label = QLabel("0")
        balance_label.setObjectName("balance_label")
        balance_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                margin: 5px 0;
            }
        """)
        layout.addWidget(balance_label)
        
        # رمز العملة
        symbol = "ل.س" if currency == "SYP" else "$"
        symbol_label = QLabel(symbol)
        symbol_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
        """)
        layout.addWidget(symbol_label)
        
        return frame
    
    def create_transfer_section(self) -> QGroupBox:
        """إنشاء قسم النقل المتجاوب"""
        
        group = QGroupBox("تفاصيل النقل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(15)
        layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        
        # نوع العملة
        self.currency_combo = QComboBox()
        self.currency_combo.addItem("الليرة السورية", "SYP")
        self.currency_combo.addItem("الدولار الأمريكي", "USD")
        self.currency_combo.setMinimumHeight(40)
        layout.addRow("نوع العملة:", self.currency_combo)
        
        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 999999999)
        self.amount_spin.setDecimals(0)
        self.amount_spin.setSuffix(" ل.س")
        self.amount_spin.setMinimumHeight(40)
        self.amount_spin.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addRow("المبلغ:", self.amount_spin)
        
        # الحد الأقصى المتاح
        self.max_amount_label = QLabel("الحد الأقصى: 0 ل.س")
        self.max_amount_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        layout.addRow("", self.max_amount_label)
        
        # المستلم
        self.receiver_edit = QLineEdit()
        self.receiver_edit.setPlaceholderText("أدخل اسم المستلم")
        self.receiver_edit.setMinimumHeight(40)
        layout.addRow("المستلم:", self.receiver_edit)
        
        # تاريخ النقل
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setMinimumHeight(40)
        layout.addRow("تاريخ النقل:", self.date_edit)
        
        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(80)
        layout.addRow("الملاحظات:", self.notes_edit)
        
        return group
    
    def create_summary_section(self) -> QGroupBox:
        """إنشاء قسم الملخص"""
        
        group = QGroupBox("ملخص العملية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #fef9e7;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #f39c12;
                background-color: #fef9e7;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        self.summary_label = QLabel("اختر المبلغ لعرض ملخص العملية")
        self.summary_label.setStyleSheet("""
            QLabel {
                color: #8e44ad;
                font-size: 13px;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)
        self.summary_label.setWordWrap(True)
        layout.addWidget(self.summary_label)
        
        return group
    
    def create_buttons_section(self) -> QFrame:
        """إنشاء قسم الأزرار المتجاوب"""
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(10)
        
        # زر تنفيذ النقل
        self.transfer_button = QPushButton("💸 تنفيذ النقل")
        self.transfer_button.setMinimumHeight(45)
        self.transfer_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.transfer_button)
        
        # زر تحديث الأرصدة
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setMinimumHeight(45)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_button.clicked.connect(self.refresh_balances)
        layout.addWidget(refresh_button)
        
        layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setMinimumHeight(45)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)
        
        return frame
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        
        # ربط الأزرار
        self.transfer_button.clicked.connect(self.execute_transfer)
        
        # ربط تغيير العملة
        self.currency_combo.currentTextChanged.connect(self.on_currency_changed)
        
        # ربط تغيير المبلغ
        self.amount_spin.valueChanged.connect(self.update_summary)
        
        # ربط الإشارات المخصصة
        self.transfer_completed.connect(self.on_transfer_completed)
        self.balance_updated.connect(self.refresh_balances)
    
    def setup_responsive_design(self):
        """إعداد التصميم المتجاوب"""
        
        # تحديد حجم الشاشة
        screen_size = self.size()
        
        if screen_size.width() < 800 or screen_size.height() < 600:
            self.is_mobile_view = True
            self.apply_mobile_styles()
        else:
            self.is_mobile_view = False
            self.apply_desktop_styles()
    
    def apply_mobile_styles(self):
        """تطبيق أنماط الجوال"""
        
        # تقليل المسافات
        self.layout().setContentsMargins(10, 10, 10, 10)
        
        # تقليل حجم الخط
        mobile_style = """
            QLabel { font-size: 12px; }
            QPushButton { font-size: 12px; padding: 8px 15px; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { 
                font-size: 12px; 
                min-height: 35px; 
            }
        """
        self.setStyleSheet(self.styleSheet() + mobile_style)
    
    def apply_desktop_styles(self):
        """تطبيق أنماط سطح المكتب"""
        
        # استخدام المسافات الافتراضية
        desktop_style = """
            QLabel { font-size: 11pt; }
            QPushButton { font-size: 11pt; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { 
                font-size: 11pt; 
            }
        """
        self.setStyleSheet(self.styleSheet() + desktop_style)
    
    def darken_color(self, color: str, factor: float = 0.2) -> str:
        """تغميق لون"""
        color_obj = QColor(color)
        h, s, v, a = color_obj.getHsv()
        v = max(0, int(v * (1 - factor)))
        color_obj.setHsv(h, s, v, a)
        return color_obj.name()
    
    def start_timers(self):
        """بدء المؤقتات"""
        
        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        
        # مؤقت تحديث الأرصدة
        self.balance_timer = QTimer()
        self.balance_timer.timeout.connect(self.refresh_balances)
        self.balance_timer.start(30000)  # كل 30 ثانية
        
        # تحديث أولي
        self.update_time()
    
    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime('%H:%M:%S')
        self.time_label.setText(f"الوقت: {current_time}")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.refresh_balances()
            self.update_summary()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
    
    def refresh_balances(self):
        """تحديث عرض الأرصدة"""
        try:
            # الحصول على الأرصدة
            daily_syp = self.treasury_manager.get_total_daily_balance('SYP')
            daily_usd = self.treasury_manager.get_total_daily_balance('USD')
            main_syp = self.treasury_manager.get_main_balance('SYP')
            main_usd = self.treasury_manager.get_main_balance('USD')
            
            # تحديث العرض
            self.daily_syp_label.setText(f"{daily_syp:,.0f}")
            self.daily_usd_label.setText(f"{daily_usd:.2f}")
            self.main_syp_label.setText(f"{main_syp:,.0f}")
            self.main_usd_label.setText(f"{main_usd:.2f}")
            
            # حفظ الأرصدة الحالية
            self.current_balances = {
                'daily_syp': daily_syp,
                'daily_usd': daily_usd,
                'main_syp': main_syp,
                'main_usd': main_usd
            }
            
            # تحديث الحد الأقصى
            self.update_max_amount()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الأرصدة: {e}")
    
    def update_max_amount(self):
        """تحديث الحد الأقصى المتاح للنقل"""
        try:
            currency = self.currency_combo.currentData()
            
            if currency == 'SYP':
                max_amount = self.current_balances.get('daily_syp', 0)
                self.max_amount_label.setText(f"الحد الأقصى: {max_amount:,.0f} ل.س")
                self.amount_spin.setMaximum(max_amount)
                self.amount_spin.setSuffix(" ل.س")
            else:
                max_amount = self.current_balances.get('daily_usd', 0)
                self.max_amount_label.setText(f"الحد الأقصى: ${max_amount:.2f}")
                self.amount_spin.setMaximum(max_amount)
                self.amount_spin.setSuffix(" $")
            
            # تفعيل/تعطيل زر النقل
            self.transfer_button.setEnabled(max_amount > 0)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الحد الأقصى: {e}")
    
    def on_currency_changed(self):
        """معالجة تغيير نوع العملة"""
        self.update_max_amount()
        self.update_summary()
    
    def update_summary(self):
        """تحديث ملخص العملية"""
        try:
            amount = self.amount_spin.value()
            currency = self.currency_combo.currentData()
            receiver = self.receiver_edit.text().strip()
            
            if amount <= 0:
                self.summary_label.setText("اختر المبلغ لعرض ملخص العملية")
                return
            
            currency_symbol = "ل.س" if currency == "SYP" else "$"
            
            summary_text = f"""
📋 ملخص عملية النقل:

💰 المبلغ: {amount:,.0f if currency == 'SYP' else amount:.2f} {currency_symbol}
🏦 من: الخزينة اليومية ({currency})
🏛️ إلى: الخزينة الرئيسية ({currency})
👤 المستلم: {receiver if receiver else 'غير محدد'}
📅 التاريخ: {self.date_edit.date().toString('yyyy-MM-dd')}

⚠️ تأكد من صحة البيانات قبل التنفيذ
            """
            
            self.summary_label.setText(summary_text.strip())
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الملخص: {e}")
    
    def execute_transfer(self):
        """تنفيذ عملية النقل"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_transfer_data():
                return
            
            # جمع بيانات النقل
            amount = self.amount_spin.value()
            currency = self.currency_combo.currentData()
            receiver = self.receiver_edit.text().strip()
            notes = self.notes_edit.toPlainText().strip()
            transfer_date = self.date_edit.date().toString('yyyy-MM-dd')
            
            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد النقل",
                f"هل أنت متأكد من نقل {amount:,.0f if currency == 'SYP' else amount:.2f} "
                f"{'ل.س' if currency == 'SYP' else '$'} إلى الخزينة الرئيسية؟",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تعطيل الزر أثناء المعالجة
            self.transfer_button.setEnabled(False)
            self.transfer_button.setText("جاري النقل...")
            
            # تنفيذ النقل
            success = self.treasury_manager.transfer_to_main_treasury(
                user_id=self.current_user['id'],
                amount=amount,
                currency=currency,
                receiver=receiver,
                notes=notes
            )
            
            if success:
                # إرسال إشارة النجاح
                transfer_data = {
                    'amount': amount,
                    'currency': currency,
                    'receiver': receiver,
                    'notes': notes,
                    'date': transfer_date
                }
                self.transfer_completed.emit(transfer_data)
                
                QMessageBox.information(
                    self, "نجح النقل",
                    f"تم نقل {amount:,.0f if currency == 'SYP' else amount:.2f} "
                    f"{'ل.س' if currency == 'SYP' else '$'} إلى الخزينة الرئيسية بنجاح"
                )
                
                # مسح النموذج
                self.clear_form()
                
            else:
                QMessageBox.critical(
                    self, "فشل النقل",
                    "فشل في تنفيذ عملية النقل. يرجى المحاولة مرة أخرى."
                )
            
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ النقل: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تنفيذ النقل: {e}")
            
        finally:
            # إعادة تفعيل الزر
            self.transfer_button.setEnabled(True)
            self.transfer_button.setText("💸 تنفيذ النقل")
    
    def validate_transfer_data(self) -> bool:
        """التحقق من صحة بيانات النقل"""
        
        amount = self.amount_spin.value()
        currency = self.currency_combo.currentData()
        receiver = self.receiver_edit.text().strip()
        
        # التحقق من المبلغ
        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            self.amount_spin.setFocus()
            return False
        
        # التحقق من توفر الرصيد
        max_amount = self.current_balances.get(f'daily_{currency.lower()}', 0)
        if amount > max_amount:
            QMessageBox.warning(
                self, "تحذير", 
                f"المبلغ المطلوب أكبر من الرصيد المتاح\n"
                f"الرصيد المتاح: {max_amount:,.0f if currency == 'SYP' else max_amount:.2f} "
                f"{'ل.س' if currency == 'SYP' else '$'}"
            )
            self.amount_spin.setFocus()
            return False
        
        # التحقق من المستلم
        if not receiver:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستلم")
            self.receiver_edit.setFocus()
            return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.amount_spin.setValue(0)
        self.receiver_edit.clear()
        self.notes_edit.clear()
        self.date_edit.setDate(QDate.currentDate())
        
        # تحديث الأرصدة
        self.refresh_balances()
    
    def on_transfer_completed(self, transfer_data: dict):
        """معالجة اكتمال النقل"""
        self.logger.info(f"تم نقل {transfer_data['amount']} {transfer_data['currency']} بنجاح")
        
        # تحديث الأرصدة
        self.refresh_balances()
    
    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للتجاوب"""
        super().resizeEvent(event)
        
        # إعادة تطبيق التصميم المتجاوب
        self.setup_responsive_design()
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        # إيقاف المؤقتات
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()
        if hasattr(self, 'balance_timer'):
            self.balance_timer.stop()
        
        event.accept()

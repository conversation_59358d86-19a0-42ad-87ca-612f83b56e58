# -*- coding: utf-8 -*-
"""
نافذة الاشتراك الجديد
New Subscription Window
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QComboBox, QPushButton,
                            QMessageBox, QFrame, QCheckBox, QSpinBox,
                            QDoubleSpinBox, QGroupBox, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime, QTime
from PyQt5.QtGui import QFont

try:
    from ..utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from ..utils.shift_manager import get_current_shift
except ImportError:
    from utils.arabic_support import reshape_arabic_text, create_arabic_font, format_currency
    from utils.shift_manager import get_current_shift

class NewSubscriptionWindow(QDialog):
    """نافذة الاشتراك الجديد"""

    subscription_added = pyqtSignal(dict)

    def __init__(self, db_manager, inventory_manager, config_manager, current_user, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = inventory_manager
        self.config_manager = config_manager
        self.current_user = current_user

        self.setup_ui()
        self.load_data()
        self.setup_connections()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(reshape_arabic_text("اشتراك جديد"))
        self.setFixedSize(600, 700)
        self.setModal(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel(reshape_arabic_text("تسجيل اشتراك جديد"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)

        # معلومات المشترك
        subscriber_group = self.create_subscriber_group()

        # معلومات الاشتراك
        subscription_group = self.create_subscription_group()

        # معلومات الراوتر
        router_group = self.create_router_group()

        # الإجمالي
        total_group = self.create_total_group()

        # أزرار التحكم
        buttons_layout = self.create_buttons()

        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addWidget(subscriber_group)
        main_layout.addWidget(subscription_group)
        main_layout.addWidget(router_group)
        main_layout.addWidget(total_group)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def create_subscriber_group(self):
        """إنشاء مجموعة معلومات المشترك"""
        group = QGroupBox(reshape_arabic_text("معلومات المشترك"))
        group.setFont(create_arabic_font(10, bold=True))

        layout = QGridLayout(group)
        layout.setSpacing(10)

        # اسم المشترك
        name_label = QLabel(reshape_arabic_text("اسم المشترك:"))
        name_label.setFont(create_arabic_font(10))
        self.name_edit = QLineEdit()
        self.name_edit.setFont(create_arabic_font(10))
        self.name_edit.setPlaceholderText(reshape_arabic_text("أدخل اسم المشترك"))

        # رقم الهاتف
        phone_label = QLabel(reshape_arabic_text("رقم الهاتف:"))
        phone_label.setFont(create_arabic_font(10))
        self.phone_edit = QLineEdit()
        self.phone_edit.setFont(create_arabic_font(10))
        self.phone_edit.setPlaceholderText(reshape_arabic_text("أدخل رقم الهاتف"))

        # العنوان
        address_label = QLabel(reshape_arabic_text("العنوان:"))
        address_label.setFont(create_arabic_font(10))
        self.address_edit = QTextEdit()
        self.address_edit.setFont(create_arabic_font(10))
        self.address_edit.setMaximumHeight(60)
        self.address_edit.setPlaceholderText(reshape_arabic_text("أدخل العنوان"))

        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.name_edit, 0, 1)
        layout.addWidget(phone_label, 1, 0)
        layout.addWidget(self.phone_edit, 1, 1)
        layout.addWidget(address_label, 2, 0)
        layout.addWidget(self.address_edit, 2, 1)

        return group

    def create_subscription_group(self):
        """إنشاء مجموعة معلومات الاشتراك"""
        group = QGroupBox(reshape_arabic_text("معلومات الاشتراك"))
        group.setFont(create_arabic_font(10, bold=True))

        layout = QGridLayout(group)
        layout.setSpacing(10)

        # رسم الاشتراك
        fee_label = QLabel(reshape_arabic_text("رسم الاشتراك:"))
        fee_label.setFont(create_arabic_font(10))
        self.subscription_fee_spin = QDoubleSpinBox()
        self.subscription_fee_spin.setFont(create_arabic_font(10))
        self.subscription_fee_spin.setRange(0, 999999999)
        self.subscription_fee_spin.setSuffix(" ل.س")

        # حالة دفع رسم الاشتراك
        self.subscription_paid_check = QCheckBox(reshape_arabic_text("تم دفع رسم الاشتراك"))
        self.subscription_paid_check.setFont(create_arabic_font(10))

        layout.addWidget(fee_label, 0, 0)
        layout.addWidget(self.subscription_fee_spin, 0, 1)
        layout.addWidget(self.subscription_paid_check, 1, 0, 1, 2)

        return group

    def create_router_group(self):
        """إنشاء مجموعة معلومات الراوتر"""
        group = QGroupBox(reshape_arabic_text("معلومات الراوتر"))
        group.setFont(create_arabic_font(10, bold=True))

        layout = QGridLayout(group)
        layout.setSpacing(10)

        # نوع الراوتر
        router_label = QLabel(reshape_arabic_text("نوع الراوتر:"))
        router_label.setFont(create_arabic_font(10))
        self.router_combo = QComboBox()
        self.router_combo.setFont(create_arabic_font(10))

        # سعر الراوتر
        router_price_label = QLabel(reshape_arabic_text("سعر الراوتر:"))
        router_price_label.setFont(create_arabic_font(10))
        self.router_price_label = QLabel("0 ل.س")
        self.router_price_label.setFont(create_arabic_font(10, bold=True))
        self.router_price_label.setStyleSheet("color: #27ae60;")

        # حالة دفع الراوتر
        self.router_paid_check = QCheckBox(reshape_arabic_text("تم دفع ثمن الراوتر"))
        self.router_paid_check.setFont(create_arabic_font(10))

        layout.addWidget(router_label, 0, 0)
        layout.addWidget(self.router_combo, 0, 1)
        layout.addWidget(router_price_label, 1, 0)
        layout.addWidget(self.router_price_label, 1, 1)
        layout.addWidget(self.router_paid_check, 2, 0, 1, 2)

        return group

    def create_total_group(self):
        """إنشاء مجموعة الإجمالي"""
        group = QGroupBox(reshape_arabic_text("الإجمالي"))
        group.setFont(create_arabic_font(10, bold=True))

        layout = QHBoxLayout(group)

        total_label = QLabel(reshape_arabic_text("إجمالي المبلغ:"))
        total_label.setFont(create_arabic_font(12))

        self.total_amount_label = QLabel("0 ل.س")
        self.total_amount_label.setFont(create_arabic_font(14, bold=True))
        self.total_amount_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: #f8f9fa;
                padding: 10px;
                border: 2px solid #e74c3c;
                border-radius: 5px;
            }
        """)

        layout.addWidget(total_label)
        layout.addStretch()
        layout.addWidget(self.total_amount_label)

        return group

    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر الإلغاء
        self.cancel_button = QPushButton(reshape_arabic_text("إلغاء"))
        self.cancel_button.setFont(create_arabic_font(10))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        # زر الطباعة
        self.print_button = QPushButton(reshape_arabic_text("طباعة"))
        self.print_button.setFont(create_arabic_font(10))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        # زر الحفظ
        self.save_button = QPushButton(reshape_arabic_text("حفظ"))
        self.save_button.setFont(create_arabic_font(10, bold=True))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        layout.addWidget(self.cancel_button)
        layout.addStretch()
        layout.addWidget(self.print_button)
        layout.addWidget(self.save_button)

        return layout

    def load_data(self):
        """تحميل البيانات"""
        # تحميل رسم الاشتراك الافتراضي
        default_fee = self.config_manager.get("financial.subscription_fee", 50000)
        self.subscription_fee_spin.setValue(default_fee)

        # تحميل أنواع الراوترات
        self.load_routers()

        # تحديث الإجمالي
        self.update_total()

    def load_routers(self):
        """تحميل أنواع الراوترات من النظام الموحد"""
        try:
            # تحميل الراوترات من النظام الموحد
            if hasattr(self, 'inventory_manager') and self.inventory_manager:
                routers = self.inventory_manager.get_products_by_category('راوتر')
            else:
                # الطريقة القديمة كبديل
                routers = self.db_manager.fetch_all("""
                    SELECT id, name, category, sale_price as unit_price, current_stock
                    FROM unified_products
                    WHERE category = 'راوتر' AND is_active = 1
                    ORDER BY name
                """)

            self.router_combo.clear()
            self.router_combo.addItem(reshape_arabic_text("اختر نوع الراوتر"), None)

            for router in routers:
                # استخدام sale_price أو unit_price حسب المتوفر
                price = router.get('sale_price', router.get('unit_price', 0))
                current_stock = router.get('current_stock', 0)

                # عرض المخزون المتوفر
                stock_info = f" (متوفر: {current_stock:.0f})" if current_stock > 0 else " (غير متوفر)"
                display_text = f"{router['name']} - {format_currency(price)}{stock_info}"

                # تحديث بيانات الراوتر للتوافق
                router_data = {
                    'id': router['id'],
                    'name': router['name'],
                    'category': router['category'],
                    'unit_price': price,
                    'current_stock': current_stock
                }

                self.router_combo.addItem(reshape_arabic_text(display_text), router_data)

            print(f"✅ تم تحميل {len(routers)} نوع راوتر من النظام الموحد")

        except Exception as e:
            print(f"❌ خطأ في تحميل الراوترات: {e}")
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text(f"خطأ في تحميل الراوترات: {e}"))

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.subscription_fee_spin.valueChanged.connect(self.update_total)
        self.router_combo.currentIndexChanged.connect(self.on_router_changed)

        # ربط تغيير حالة التسديد بتحديث الإجمالي
        self.subscription_paid_check.toggled.connect(self.update_total)
        self.router_paid_check.toggled.connect(self.update_total)

        self.save_button.clicked.connect(self.save_subscription)
        self.print_button.clicked.connect(self.print_invoice)
        self.cancel_button.clicked.connect(self.reject)

    def on_router_changed(self):
        """معالجة تغيير نوع الراوتر"""
        router_data = self.router_combo.currentData()
        if router_data:
            price_text = format_currency(router_data['unit_price'])
            self.router_price_label.setText(price_text)
        else:
            self.router_price_label.setText("0 ل.س")

        self.update_total()

    def update_total(self):
        """تحديث الإجمالي بناءً على حالة التسديد"""
        total = 0

        # إضافة رسم الاشتراك إذا تم تسديده
        if self.subscription_paid_check.isChecked():
            total += self.subscription_fee_spin.value()

        # إضافة ثمن الراوتر إذا تم تسديده
        if self.router_paid_check.isChecked():
            router_data = self.router_combo.currentData()
            if router_data:
                total += router_data['unit_price']

        self.total_amount_label.setText(format_currency(total))

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text("يرجى إدخال اسم المشترك"))
            self.name_edit.setFocus()
            return False

        if self.router_combo.currentData() is None:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text("يرجى اختيار نوع الراوتر"))
            self.router_combo.setFocus()
            return False

        return True

    def save_subscription(self):
        """حفظ الاشتراك"""
        if not self.validate_data():
            return

        try:
            # الحصول على الشيفت الحالي
            try:
                current_shift_id = get_current_shift(self.db_manager, self.current_user['id'], self.current_user['username'])
                if not current_shift_id:
                    raise ValueError("فشل في الحصول على الشيفت الحالي")
                print(f"📂 الشيفت الحالي: {current_shift_id}")
            except Exception as shift_error:
                print(f"❌ خطأ في الحصول على الشيفت: {shift_error}")
                current_shift_id = 1

            # بيانات المشترك
            router_data = self.router_combo.currentData()
            router_type_id = router_data['id'] if router_data else None

            subscriber_data = {
                'name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip(),
                'subscription_fee': self.subscription_fee_spin.value(),
                'subscription_paid': self.subscription_paid_check.isChecked(),
                'router_type': self.router_combo.currentText(),
                'router_type_id': router_type_id,
                'router_paid': self.router_paid_check.isChecked(),
                'created_by': self.current_user['username']
            }

            # حفظ في قاعدة البيانات
            cursor = self.db_manager.execute_query("""
                INSERT INTO subscribers (name, phone, address, subscription_fee,
                                       subscription_paid, router_type, router_type_id, router_paid, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                subscriber_data['name'], subscriber_data['phone'], subscriber_data['address'],
                subscriber_data['subscription_fee'], subscriber_data['subscription_paid'],
                subscriber_data['router_type'], subscriber_data['router_type_id'], subscriber_data['router_paid'],
                subscriber_data['created_by']
            ))

            if cursor:
                subscriber_id = cursor.lastrowid

                # تسجيل العملية المالية
                total_amount = self.subscription_fee_spin.value()
                router_data = self.router_combo.currentData()
                if router_data:
                    total_amount += router_data['unit_price']

                self.db_manager.execute_query("""
                    INSERT INTO transactions (type, description, amount, currency, payment_method, reference_id, user_name, shift_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    "اشتراك جديد",
                    f"اشتراك جديد للمشترك: {subscriber_data['name']} - راوتر: {subscriber_data['router_type']}",
                    float(total_amount),
                    "SYP",
                    "نقدي",
                    subscriber_id,
                    self.current_user['username'],
                    current_shift_id
                ))

                # إرسال إشارة نجح الحفظ
                self.subscription_added.emit(subscriber_data)

                QMessageBox.information(self, reshape_arabic_text("نجح"),
                                      reshape_arabic_text("تم حفظ الاشتراك بنجاح"))

                # سؤال المستخدم عن الطباعة
                reply = QMessageBox.question(
                    self,
                    reshape_arabic_text("طباعة"),
                    reshape_arabic_text("هل تريد طباعة فاتورة الاشتراك؟"),
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.print_invoice()

                self.accept()

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في حفظ الاشتراك: {e}"))

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # جمع بيانات الفاتورة
            invoice_data = {
                'customer_name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip(),
                'package_name': self.package_combo.currentText(),
                'router_type': self.router_combo.currentText(),
                'amount': self.amount_spin.value(),
                'invoice_number': f"SUB-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}",
                'description': f"اشتراك جديد - باقة {self.package_combo.currentText()}",
                'date': QDate.currentDate().toString("yyyy-MM-dd"),
                'time': QTime.currentTime().toString("hh:mm:ss")
            }

            # طباعة الفاتورة
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_invoice(invoice_data)

            if success:
                QMessageBox.information(self, reshape_arabic_text("تم"),
                                      reshape_arabic_text("تم إرسال الفاتورة للطباعة بنجاح"))
            else:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("تم إلغاء عملية الطباعة"))

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في طباعة الفاتورة: {e}"))
        title_label = QLabel(reshape_arabic_text("تسجيل اشتراك جديد"))
        title_label.setFont(create_arabic_font(16, bold=True))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        
        # معلومات المشترك
        subscriber_group = self.create_subscriber_group()
        
        # معلومات الاشتراك
        subscription_group = self.create_subscription_group()
        
        # معلومات الراوتر
        router_group = self.create_router_group()
        
        # الإجمالي
        total_group = self.create_total_group()
        
        # أزرار التحكم
        buttons_layout = self.create_buttons()
        
        # تجميع العناصر
        main_layout.addWidget(title_label)
        main_layout.addWidget(subscriber_group)
        main_layout.addWidget(subscription_group)
        main_layout.addWidget(router_group)
        main_layout.addWidget(total_group)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_subscriber_group(self):
        """إنشاء مجموعة معلومات المشترك"""
        group = QGroupBox(reshape_arabic_text("معلومات المشترك"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # اسم المشترك
        name_label = QLabel(reshape_arabic_text("اسم المشترك:"))
        name_label.setFont(create_arabic_font(10))
        self.name_edit = QLineEdit()
        self.name_edit.setFont(create_arabic_font(10))
        self.name_edit.setPlaceholderText(reshape_arabic_text("أدخل اسم المشترك"))
        
        # رقم الهاتف
        phone_label = QLabel(reshape_arabic_text("رقم الهاتف:"))
        phone_label.setFont(create_arabic_font(10))
        self.phone_edit = QLineEdit()
        self.phone_edit.setFont(create_arabic_font(10))
        self.phone_edit.setPlaceholderText(reshape_arabic_text("أدخل رقم الهاتف"))
        
        # العنوان
        address_label = QLabel(reshape_arabic_text("العنوان:"))
        address_label.setFont(create_arabic_font(10))
        self.address_edit = QTextEdit()
        self.address_edit.setFont(create_arabic_font(10))
        self.address_edit.setMaximumHeight(60)
        self.address_edit.setPlaceholderText(reshape_arabic_text("أدخل العنوان"))
        
        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.name_edit, 0, 1)
        layout.addWidget(phone_label, 1, 0)
        layout.addWidget(self.phone_edit, 1, 1)
        layout.addWidget(address_label, 2, 0)
        layout.addWidget(self.address_edit, 2, 1)
        
        return group
        
    def create_subscription_group(self):
        """إنشاء مجموعة معلومات الاشتراك"""
        group = QGroupBox(reshape_arabic_text("معلومات الاشتراك"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # رسم الاشتراك
        fee_label = QLabel(reshape_arabic_text("رسم الاشتراك:"))
        fee_label.setFont(create_arabic_font(10))
        self.subscription_fee_spin = QDoubleSpinBox()
        self.subscription_fee_spin.setFont(create_arabic_font(10))
        self.subscription_fee_spin.setRange(0, 999999999)
        self.subscription_fee_spin.setSuffix(" ل.س")
        
        # حالة دفع رسم الاشتراك
        self.subscription_paid_check = QCheckBox(reshape_arabic_text("تم دفع رسم الاشتراك"))
        self.subscription_paid_check.setFont(create_arabic_font(10))
        
        layout.addWidget(fee_label, 0, 0)
        layout.addWidget(self.subscription_fee_spin, 0, 1)
        layout.addWidget(self.subscription_paid_check, 1, 0, 1, 2)
        
        return group
        
    def create_router_group(self):
        """إنشاء مجموعة معلومات الراوتر"""
        group = QGroupBox(reshape_arabic_text("معلومات الراوتر"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # نوع الراوتر
        router_label = QLabel(reshape_arabic_text("نوع الراوتر:"))
        router_label.setFont(create_arabic_font(10))
        self.router_combo = QComboBox()
        self.router_combo.setFont(create_arabic_font(10))
        
        # سعر الراوتر
        router_price_label = QLabel(reshape_arabic_text("سعر الراوتر:"))
        router_price_label.setFont(create_arabic_font(10))
        self.router_price_label = QLabel("0 ل.س")
        self.router_price_label.setFont(create_arabic_font(10, bold=True))
        self.router_price_label.setStyleSheet("color: #27ae60;")
        
        # حالة دفع الراوتر
        self.router_paid_check = QCheckBox(reshape_arabic_text("تم دفع ثمن الراوتر"))
        self.router_paid_check.setFont(create_arabic_font(10))
        
        layout.addWidget(router_label, 0, 0)
        layout.addWidget(self.router_combo, 0, 1)
        layout.addWidget(router_price_label, 1, 0)
        layout.addWidget(self.router_price_label, 1, 1)
        layout.addWidget(self.router_paid_check, 2, 0, 1, 2)
        
        return group
        
    def create_total_group(self):
        """إنشاء مجموعة الإجمالي"""
        group = QGroupBox(reshape_arabic_text("الإجمالي"))
        group.setFont(create_arabic_font(10, bold=True))
        
        layout = QHBoxLayout(group)
        
        total_label = QLabel(reshape_arabic_text("إجمالي المبلغ:"))
        total_label.setFont(create_arabic_font(12))
        
        self.total_amount_label = QLabel("0 ل.س")
        self.total_amount_label.setFont(create_arabic_font(14, bold=True))
        self.total_amount_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: #f8f9fa;
                padding: 10px;
                border: 2px solid #e74c3c;
                border-radius: 5px;
            }
        """)
        
        layout.addWidget(total_label)
        layout.addStretch()
        layout.addWidget(self.total_amount_label)
        
        return group
        
    def create_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الإلغاء
        self.cancel_button = QPushButton(reshape_arabic_text("إلغاء"))
        self.cancel_button.setFont(create_arabic_font(10))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        # زر الطباعة
        self.print_button = QPushButton(reshape_arabic_text("طباعة"))
        self.print_button.setFont(create_arabic_font(10))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        # زر الحفظ
        self.save_button = QPushButton(reshape_arabic_text("حفظ"))
        self.save_button.setFont(create_arabic_font(10, bold=True))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        layout.addWidget(self.cancel_button)
        layout.addStretch()
        layout.addWidget(self.print_button)
        layout.addWidget(self.save_button)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات"""
        # تحميل رسم الاشتراك الافتراضي
        default_fee = self.config_manager.get("financial.subscription_fee", 50000)
        self.subscription_fee_spin.setValue(default_fee)
        
        # تحميل أنواع الراوترات
        self.load_routers()
        
        # تحديث الإجمالي
        self.update_total()
        
    def load_routers(self):
        """تحميل أنواع الراوترات من النظام الموحد"""
        try:
            # تحميل الراوترات من النظام الموحد
            if hasattr(self, 'inventory_manager') and self.inventory_manager:
                routers = self.inventory_manager.get_products_by_category('راوتر')
            else:
                # الطريقة القديمة كبديل
                routers = self.db_manager.fetch_all("""
                    SELECT id, name, category, sale_price as unit_price, current_stock
                    FROM unified_products
                    WHERE category = 'راوتر' AND is_active = 1
                    ORDER BY name
                """)

            self.router_combo.clear()
            self.router_combo.addItem(reshape_arabic_text("اختر نوع الراوتر"), None)

            for router in routers:
                # استخدام sale_price أو unit_price حسب المتوفر
                price = router.get('sale_price', router.get('unit_price', 0))
                current_stock = router.get('current_stock', 0)

                # عرض المخزون المتوفر
                stock_info = f" (متوفر: {current_stock:.0f})" if current_stock > 0 else " (غير متوفر)"
                display_text = f"{router['name']} - {format_currency(price)}{stock_info}"

                # تحديث بيانات الراوتر للتوافق
                router_data = {
                    'id': router['id'],
                    'name': router['name'],
                    'category': router['category'],
                    'unit_price': price,
                    'current_stock': current_stock
                }

                self.router_combo.addItem(reshape_arabic_text(display_text), router_data)

            print(f"✅ تم تحميل {len(routers)} نوع راوتر من النظام الموحد")

        except Exception as e:
            print(f"❌ خطأ في تحميل الراوترات: {e}")
            QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                              reshape_arabic_text(f"خطأ في تحميل الراوترات: {e}"))

    def on_router_changed(self):
        """معالجة تغيير نوع الراوتر"""
        router_data = self.router_combo.currentData()
        if router_data:
            price_text = format_currency(router_data['unit_price'])
            self.router_price_label.setText(price_text)
        else:
            self.router_price_label.setText("0 ل.س")
            
        self.update_total()
        
    def update_total(self):
        """تحديث الإجمالي بناءً على حالة التسديد"""
        total = 0

        # إضافة رسم الاشتراك إذا تم تسديده
        if self.subscription_paid_check.isChecked():
            total += self.subscription_fee_spin.value()

        # إضافة ثمن الراوتر إذا تم تسديده
        if self.router_paid_check.isChecked():
            router_data = self.router_combo.currentData()
            if router_data:
                total += router_data['unit_price']

        self.total_amount_label.setText(format_currency(total))
        
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, reshape_arabic_text("تحذير"), 
                              reshape_arabic_text("يرجى إدخال اسم المشترك"))
            self.name_edit.setFocus()
            return False
            
        if self.router_combo.currentData() is None:
            QMessageBox.warning(self, reshape_arabic_text("تحذير"), 
                              reshape_arabic_text("يرجى اختيار نوع الراوتر"))
            self.router_combo.setFocus()
            return False
            
        return True

    def save_subscription(self):
        """حفظ الاشتراك الجديد"""
        if not self.validate_data():
            return

        try:
            # الحصول على الشيفت الحالي
            try:
                current_shift_id = get_current_shift(self.db_manager, self.current_user['id'], self.current_user['username'])
                if not current_shift_id:
                    raise ValueError("فشل في الحصول على الشيفت الحالي")
                print(f"📂 الشيفت الحالي: {current_shift_id}")
            except Exception as shift_error:
                print(f"❌ خطأ في الحصول على الشيفت: {shift_error}")
                current_shift_id = 1

            # بيانات المشترك
            router_data = self.router_combo.currentData()
            router_type_id = router_data['id'] if router_data else None

            subscriber_data = {
                'name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip(),
                'subscription_fee': self.subscription_fee_spin.value(),
                'subscription_paid': self.subscription_paid_check.isChecked(),
                'router_type': self.router_combo.currentText(),
                'router_type_id': router_type_id,
                'router_paid': self.router_paid_check.isChecked(),
                'created_by': self.current_user['username']
            }

            # حفظ في قاعدة البيانات
            cursor = self.db_manager.execute_query("""
                INSERT INTO subscribers (name, phone, address, subscription_fee,
                                       subscription_paid, router_type, router_type_id, router_paid, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                subscriber_data['name'], subscriber_data['phone'], subscriber_data['address'],
                subscriber_data['subscription_fee'], subscriber_data['subscription_paid'],
                subscriber_data['router_type'], subscriber_data['router_type_id'], subscriber_data['router_paid'],
                subscriber_data['created_by']
            ))

            if cursor:
                subscriber_id = cursor.lastrowid
                print(f"✅ تم حفظ المشترك برقم: {subscriber_id}")

                # حساب المبلغ الإجمالي
                total_amount = self.subscription_fee_spin.value()
                router_data = self.router_combo.currentData()
                if router_data:
                    total_amount += router_data['unit_price']

                # حفظ في جدول الاشتراكات الجديدة
                self.db_manager.execute_query("""
                    INSERT INTO new_subscriptions (subscriber_name, subscription_fee, router_type,
                                                 router_price, total_amount, subscription_paid,
                                                 router_paid, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    subscriber_data['name'], subscriber_data['subscription_fee'],
                    subscriber_data['router_type'], router_data['unit_price'] if router_data else 0,
                    total_amount, subscriber_data['subscription_paid'],
                    subscriber_data['router_paid'], subscriber_data['created_by']
                ))

                # تسجيل العملية المالية
                self.db_manager.execute_query("""
                    INSERT INTO transactions (type, description, amount, currency, payment_method, reference_id, user_name, shift_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    "اشتراك جديد",
                    f"اشتراك جديد للمشترك: {subscriber_data['name']} - راوتر: {subscriber_data['router_type']}",
                    float(total_amount),
                    "SYP",
                    "نقدي",
                    subscriber_id,
                    self.current_user['username'],
                    current_shift_id
                ))

                # إرسال إشارة نجح الحفظ
                self.subscription_added.emit(subscriber_data)

                QMessageBox.information(self, reshape_arabic_text("نجح"),
                                      reshape_arabic_text("تم حفظ الاشتراك بنجاح"))

                # سؤال المستخدم عن الطباعة
                reply = QMessageBox.question(
                    self,
                    reshape_arabic_text("طباعة"),
                    reshape_arabic_text("هل تريد طباعة فاتورة الاشتراك؟"),
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.print_invoice()

                self.accept()

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في حفظ الاشتراك: {e}"))

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # جمع بيانات الفاتورة
            invoice_data = {
                'customer_name': self.name_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip(),
                'package_name': self.package_combo.currentText(),
                'router_type': self.router_combo.currentText(),
                'amount': self.amount_spin.value(),
                'invoice_number': f"SUB-{QDateTime.currentDateTime().toString('yyyyMMddhhmmss')}",
                'description': f"اشتراك جديد - باقة {self.package_combo.currentText()}",
                'date': QDate.currentDate().toString("yyyy-MM-dd"),
                'time': QTime.currentTime().toString("hh:mm:ss")
            }

            # طباعة الفاتورة
            from utils.print_manager import PrintManager
            print_manager = PrintManager(self.db_manager, self.config_manager)

            success = print_manager.print_invoice(invoice_data)

            if success:
                QMessageBox.information(self, reshape_arabic_text("تم"),
                                      reshape_arabic_text("تم إرسال الفاتورة للطباعة بنجاح"))
            else:
                QMessageBox.warning(self, reshape_arabic_text("تحذير"),
                                  reshape_arabic_text("تم إلغاء عملية الطباعة"))

        except Exception as e:
            QMessageBox.critical(self, reshape_arabic_text("خطأ"),
                               reshape_arabic_text(f"خطأ في طباعة الفاتورة: {e}"))

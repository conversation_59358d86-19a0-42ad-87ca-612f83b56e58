#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المتكامل للخزينة
"""

import sys
import os
sys.path.append('src')

def test_cash_box_cycle():
    """اختبار دورة إغلاق الصندوق المحدثة"""
    
    print("🧪 اختبار دورة إغلاق الصندوق المحدثة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        print("\n=== 1. فتح صندوق جديد ===")
        
        # فتح صندوق جديد
        treasury_manager.open_cash_box(user_id=user_id)
        
        # فحص الرصيد الأولي
        initial_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد الأولي: {initial_balance:,} ل.س")
        
        print("\n=== 2. محاكاة المبيعات ===")
        
        # محاكاة مبيعات متنوعة
        sales_operations = [
            ("اشتراك جديد", 75000),
            ("تسليم راوتر", 150000),
            ("تجديد باقة", 50000)
        ]
        
        total_sales = 0
        for operation, amount in sales_operations:
            treasury_manager.add_to_daily_treasury(user_id, 'SYP', amount)
            total_sales += amount
            print(f"✅ {operation}: {amount:,} ل.س")
        
        # فحص الرصيد بعد المبيعات
        balance_after_sales = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد بعد المبيعات: {balance_after_sales:,} ل.س")
        
        print("\n=== 3. إغلاق الصندوق ===")
        
        # إغلاق الصندوق
        close_success = treasury_manager.close_cash_box(user_id=user_id)
        
        if close_success:
            print("✅ تم إغلاق الصندوق بنجاح")
            
            # فحص الأرصدة بعد الإغلاق
            daily_after_close = treasury_manager.get_daily_balance(user_id, 'SYP')
            main_after_close = treasury_manager.get_main_balance(user_id, 'SYP')
            
            print(f"💰 الخزينة اليومية بعد الإغلاق: {daily_after_close:,} ل.س (يجب أن تكون 0)")
            print(f"💰 الخزينة الرئيسية بعد الإغلاق: {main_after_close:,} ل.س")
            
            # التحقق من تصفير القيم
            if daily_after_close == 0:
                print("✅ تم تصفير الخزينة اليومية بنجاح")
            else:
                print("❌ لم يتم تصفير الخزينة اليومية")
                return False
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
        print("\n=== 4. فتح صندوق جديد ===")
        
        # فتح صندوق جديد
        treasury_manager.open_cash_box(user_id=user_id)
        
        # فحص الرصيد الجديد (يجب أن يكون صفر)
        new_daily_balance = treasury_manager.get_daily_balance(user_id, 'SYP')
        print(f"💰 الرصيد اليومي الجديد: {new_daily_balance:,} ل.س (يجب أن يكون 0)")
        
        if new_daily_balance == 0:
            print("✅ الصندوق الجديد يبدأ بقيم صفر")
            return True
        else:
            print("❌ الصندوق الجديد لا يبدأ بقيم صفر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دورة الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_exchange():
    """اختبار شراء الدولار من الخزينة الرئيسية"""
    
    print("\n🧪 اختبار شراء الدولار من الخزينة الرئيسية...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        # التأكد من وجود رصيد في الخزينة الرئيسية
        main_syp_before = treasury_manager.get_main_balance(user_id, 'SYP')
        main_usd_before = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"📊 الخزينة الرئيسية قبل الصرف:")
        print(f"  • SYP: {main_syp_before:,} ل.س")
        print(f"  • USD: ${main_usd_before:.2f}")
        
        if main_syp_before < 100000:
            print("⚠️ الرصيد غير كافي للاختبار - إضافة رصيد")
            # إضافة رصيد للاختبار
            treasury_manager.open_cash_box(user_id=user_id)
            treasury_manager.add_to_daily_treasury(user_id, 'SYP', 500000)
            treasury_manager.close_cash_box(user_id=user_id)
            main_syp_before = treasury_manager.get_main_balance(user_id, 'SYP')
            print(f"✅ تم إضافة رصيد: {main_syp_before:,} ل.س")
        
        # محاولة شراء دولار
        syp_amount = 150000  # 150,000 ل.س
        exchange_rate = 15000  # 15,000 ل.س للدولار الواحد
        expected_usd = syp_amount / exchange_rate  # 10 دولار
        
        print(f"\n💱 محاولة شراء دولار:")
        print(f"  • المبلغ بالليرة: {syp_amount:,} ل.س")
        print(f"  • سعر الصرف: {exchange_rate:,} ل.س/دولار")
        print(f"  • المبلغ المتوقع بالدولار: ${expected_usd:.2f}")
        
        # تنفيذ عملية الصرف
        exchange_success = treasury_manager.exchange_currency_in_main_treasury(
            user_id=user_id,
            from_currency='SYP',
            to_currency='USD',
            amount_from=syp_amount,
            exchange_rate=exchange_rate
        )
        
        if exchange_success:
            print("✅ تم شراء الدولار بنجاح")
            
            # فحص الأرصدة بعد الصرف
            main_syp_after = treasury_manager.get_main_balance(user_id, 'SYP')
            main_usd_after = treasury_manager.get_main_balance(user_id, 'USD')
            
            print(f"📊 الخزينة الرئيسية بعد الصرف:")
            print(f"  • SYP: {main_syp_after:,} ل.س")
            print(f"  • USD: ${main_usd_after:.2f}")
            
            # التحقق من صحة العملية
            expected_syp_after = main_syp_before - syp_amount
            expected_usd_after = main_usd_before + expected_usd
            
            if abs(main_syp_after - expected_syp_after) < 1 and abs(main_usd_after - expected_usd_after) < 0.01:
                print("✅ عملية الصرف تمت بشكل صحيح")
                return True
            else:
                print("❌ خطأ في حساب عملية الصرف")
                return False
        else:
            print("❌ فشل في شراء الدولار")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار شراء الدولار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer_integration():
    """اختبار تكامل واجهة نقل الخزينة"""
    
    print("\n🧪 اختبار تكامل واجهة نقل الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        
        user_id = 1
        
        # فحص الأرصدة الحالية
        daily_syp = treasury_manager.get_daily_balance(user_id, 'SYP')
        daily_usd = treasury_manager.get_daily_balance(user_id, 'USD')
        main_syp = treasury_manager.get_main_balance(user_id, 'SYP')
        main_usd = treasury_manager.get_main_balance(user_id, 'USD')
        
        print(f"📊 الأرصدة الحالية:")
        print(f"  الخزينة اليومية: SYP={daily_syp:,}, USD=${daily_usd:.2f}")
        print(f"  الخزينة الرئيسية: SYP={main_syp:,}, USD=${main_usd:.2f}")
        
        # اختبار نقل من اليومية للرئيسية
        if daily_syp > 0:
            transfer_amount = min(daily_syp, 50000)
            
            print(f"\n💸 اختبار نقل {transfer_amount:,} ل.س من اليومية للرئيسية...")
            
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=user_id,
                currency_type='SYP',
                amount=transfer_amount
            )
            
            if transfer_success:
                print("✅ تم النقل بنجاح")
                
                # فحص الأرصدة بعد النقل
                new_daily_syp = treasury_manager.get_daily_balance(user_id, 'SYP')
                new_main_syp = treasury_manager.get_main_balance(user_id, 'SYP')
                
                print(f"📊 الأرصدة بعد النقل:")
                print(f"  الخزينة اليومية: {new_daily_syp:,} ل.س")
                print(f"  الخزينة الرئيسية: {new_main_syp:,} ل.س")
                
                return True
            else:
                print("❌ فشل في النقل")
                return False
        else:
            print("ℹ️ لا يوجد رصيد في الخزينة اليومية للنقل")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار النظام المتكامل للخزينة")
    print("=" * 60)
    
    # اختبار دورة إغلاق الصندوق
    cash_box_test = test_cash_box_cycle()
    
    # اختبار شراء الدولار
    currency_test = test_currency_exchange()
    
    # اختبار نقل الخزينة
    transfer_test = test_treasury_transfer_integration()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • دورة إغلاق الصندوق: {'✅ نجح' if cash_box_test else '❌ فشل'}")
    print(f"  • شراء الدولار: {'✅ نجح' if currency_test else '❌ فشل'}")
    print(f"  • نقل الخزينة: {'✅ نجح' if transfer_test else '❌ فشل'}")
    
    if all([cash_box_test, currency_test, transfer_test]):
        print("\n🎉 النظام المتكامل للخزينة يعمل بشكل مثالي!")
        
        print("\n📋 المميزات المحققة:")
        print("  ✅ إغلاق الصندوق يصفر القيم")
        print("  ✅ فتح صندوق جديد بقيم صفر")
        print("  ✅ نقل الأموال للخزينة الرئيسية")
        print("  ✅ شراء الدولار من الخزينة الرئيسية")
        print("  ✅ واجهة نقل الخزينة تعرض الأرصدة الصحيحة")
        print("  ✅ تكامل كامل بين جميع الواجهات")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python run_system.py")
        print("  2. قم بعمليات مبيعات")
        print("  3. اغلق الصندوق - ستجد القيم تصفر")
        print("  4. استخدم 'شراء دولار' من الخزينة الرئيسية")
        print("  5. استخدم 'نقل الخزينة' لإدارة الأموال")
        
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح!")

if __name__ == "__main__":
    main()

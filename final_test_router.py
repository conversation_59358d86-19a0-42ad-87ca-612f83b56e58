#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لتسليم الراوتر
"""

import sys
import os
sys.path.append('src')

def test_router_delivery_final():
    """اختبار نهائي لتسليم الراوتر"""
    
    print("🧪 اختبار نهائي لتسليم الراوتر...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager('data/company_system.db')
        
        # بيانات المستخدم
        current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'المدير',
            'role': 'admin'
        }
        
        # إنشاء الواجهة
        print("🔄 إنشاء واجهة تسليم الراوتر...")
        window = RouterDeliveryWindow(db, current_user)
        print("✅ تم إنشاء الواجهة")
        
        # فحص وجود دالة save_delivery
        if hasattr(window, 'save_delivery'):
            print("✅ دالة save_delivery موجودة")
        else:
            print("❌ دالة save_delivery مفقودة")
            return False
        
        # تعيين مشترك تجريبي
        window.selected_subscriber = {
            'id': 1,
            'name': 'مشترك تجريبي',
            'phone': '0123456789'
        }
        print("✅ تم تعيين مشترك تجريبي")
        
        # محاولة الحفظ
        print("🚨 محاولة الحفظ...")
        try:
            window.save_delivery()
            print("✅ تم استدعاء دالة الحفظ بدون إغلاق التطبيق!")
            return True
        except Exception as save_error:
            print(f"❌ خطأ في دالة الحفظ: {save_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test_app():
    """إنشاء تطبيق اختبار بسيط"""
    
    print("\n🚀 إنشاء تطبيق اختبار تفاعلي...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
        from PyQt5.QtCore import QTimer
        from database.database_manager import DatabaseManager
        from ui.router_delivery_window import RouterDeliveryWindow
        
        class TestApp(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("اختبار تسليم الراوتر النهائي")
                self.setGeometry(100, 100, 500, 400)
                
                # إعداد قاعدة البيانات
                self.db = DatabaseManager('data/company_system.db')
                self.current_user = {
                    'id': 1,
                    'username': 'admin',
                    'full_name': 'المدير',
                    'role': 'admin'
                }
                
                # إعداد الواجهة
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                layout = QVBoxLayout()
                central_widget.setLayout(layout)
                
                # تسمية
                title_label = QLabel("اختبار تسليم الراوتر النهائي")
                title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
                layout.addWidget(title_label)
                
                # معلومات
                info_label = QLabel("هذا الاختبار سيفتح واجهة تسليم الراوتر\nويختبر ما إذا كان الحفظ يغلق التطبيق أم لا")
                info_label.setStyleSheet("margin: 10px;")
                layout.addWidget(info_label)
                
                # زر فتح تسليم الراوتر
                self.open_btn = QPushButton("فتح واجهة تسليم الراوتر")
                self.open_btn.setStyleSheet("padding: 10px; font-size: 14px;")
                self.open_btn.clicked.connect(self.open_router_delivery)
                layout.addWidget(self.open_btn)
                
                # زر اختبار الحفظ المباشر
                self.test_btn = QPushButton("اختبار الحفظ المباشر")
                self.test_btn.setStyleSheet("padding: 10px; font-size: 14px;")
                self.test_btn.clicked.connect(self.test_save_directly)
                layout.addWidget(self.test_btn)
                
                # زر الإغلاق
                close_btn = QPushButton("إغلاق")
                close_btn.setStyleSheet("padding: 10px; font-size: 14px;")
                close_btn.clicked.connect(self.close)
                layout.addWidget(close_btn)
                
                # حالة
                self.status_label = QLabel("جاهز للاختبار")
                self.status_label.setStyleSheet("margin: 10px; padding: 10px; background-color: #f0f0f0;")
                layout.addWidget(self.status_label)
                
                # متغير للواجهة
                self.router_window = None
            
            def open_router_delivery(self):
                """فتح واجهة تسليم الراوتر"""
                try:
                    self.status_label.setText("فتح واجهة تسليم الراوتر...")
                    
                    self.router_window = RouterDeliveryWindow(self.db, self.current_user, self)
                    self.router_window.show()
                    
                    self.status_label.setText("تم فتح واجهة تسليم الراوتر - جرب الحفظ الآن!")
                    
                except Exception as e:
                    self.status_label.setText(f"خطأ: {str(e)}")
                    print(f"❌ خطأ في فتح الواجهة: {e}")
                    import traceback
                    traceback.print_exc()
            
            def test_save_directly(self):
                """اختبار الحفظ مباشرة"""
                try:
                    self.status_label.setText("اختبار الحفظ المباشر...")
                    
                    # إنشاء واجهة جديدة
                    test_window = RouterDeliveryWindow(self.db, self.current_user)
                    
                    # تعيين مشترك تجريبي
                    test_window.selected_subscriber = {
                        'id': 1,
                        'name': 'مشترك تجريبي',
                        'phone': '0123456789'
                    }
                    
                    # محاولة الحفظ
                    test_window.save_delivery()
                    
                    self.status_label.setText("✅ تم الحفظ بنجاح - التطبيق لم يغلق!")
                    
                except Exception as e:
                    self.status_label.setText(f"❌ خطأ في الحفظ: {str(e)}")
                    print(f"❌ خطأ في الحفظ المباشر: {e}")
                    import traceback
                    traceback.print_exc()
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء النافذة الرئيسية
        main_window = TestApp()
        main_window.show()
        
        print("✅ تم إنشاء تطبيق الاختبار")
        print("\n📋 تعليمات الاختبار:")
        print("  1. اضغط على 'فتح واجهة تسليم الراوتر'")
        print("  2. في الواجهة الجديدة:")
        print("     • اختر مشترك من القائمة")
        print("     • اختر راوتر وباقة")
        print("     • اضغط 'حفظ وتسليم'")
        print("  3. أو اضغط 'اختبار الحفظ المباشر' للاختبار السريع")
        print("  4. راقب ما إذا كان التطبيق يغلق أم لا")
        
        # إغلاق التطبيق بعد 60 ثانية
        QTimer.singleShot(60000, app.quit)
        
        # تشغيل التطبيق
        result = app.exec_()
        print(f"📊 انتهى التطبيق بالكود: {result}")
        
        return result == 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تطبيق الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 الاختبار النهائي لإصلاح مشكلة إغلاق التطبيق")
    print("=" * 70)
    
    # اختبار أساسي
    basic_test = test_router_delivery_final()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الاختبار الأساسي:")
    print(f"  • الاختبار الأساسي: {'✅ نجح' if basic_test else '❌ فشل'}")
    
    if basic_test:
        print("\n🎉 الاختبار الأساسي نجح!")
        print("✅ دالة الحفظ تعمل بدون إغلاق التطبيق")
        
        print("\n🚀 تشغيل اختبار تفاعلي...")
        create_simple_test_app()
        
        print("\n📋 للاستخدام الفعلي:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. سجل دخول: admin / 123")
        print("  3. افتح 'تسليم راوتر'")
        print("  4. املأ البيانات واضغط 'حفظ وتسليم'")
        print("  5. ✅ التطبيق لن يغلق!")
        
    else:
        print("\n❌ الاختبار الأساسي فشل!")
        print("💡 قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()

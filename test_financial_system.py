#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المالي الجديد
"""

import sys
import os
sys.path.append('src')

def test_shift_system():
    """اختبار نظام الشيفتات"""
    
    print("🧪 اختبار نظام الشيفتات...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        print("✅ تم إنشاء مدير الخزينة")
        
        # اختبار فتح شيفت (تسجيل دخول)
        print("\n🔓 اختبار فتح شيفت...")
        shift_opened = treasury_manager.open_cash_box(user_id=current_user['id'])
        if shift_opened:
            print("✅ تم فتح شيفت جديد")
        else:
            print("⚠️ الشيفت مفتوح مسبقاً")
        
        # اختبار الحصول على رصيد الصندوق
        cash_box_balance = treasury_manager.get_cash_box_balance(current_user['id'])
        print(f"💰 رصيد الصندوق: {cash_box_balance:,} ل.س")
        
        # اختبار إضافة مبيعات للصندوق
        print("\n💵 اختبار إضافة مبيعات...")
        sale_added = treasury_manager.add_to_cash_box(
            user_id=current_user['id'],
            amount=100000,
            description="مبيعات تجريبية",
            transaction_type="sale"
        )
        
        if sale_added:
            print("✅ تم إضافة مبيعات للصندوق")
            new_balance = treasury_manager.get_cash_box_balance(current_user['id'])
            print(f"💰 الرصيد الجديد: {new_balance:,} ل.س")
        else:
            print("❌ فشل في إضافة المبيعات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الشيفتات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cash_box_closure():
    """اختبار إغلاق الصندوق"""
    
    print("\n🧪 اختبار إغلاق الصندوق...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # الحصول على رصيد الصندوق قبل الإغلاق
        cash_balance_before = treasury_manager.get_cash_box_balance(current_user['id'])
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        
        print(f"💰 رصيد الصندوق قبل الإغلاق: {cash_balance_before:,} ل.س")
        print(f"💰 رصيد الخزينة اليومية قبل الإغلاق: {daily_balance_before:,} ل.س")
        
        # اختبار إغلاق الصندوق
        print("\n🔒 اختبار إغلاق الصندوق...")
        closure_success = treasury_manager.close_cash_box(
            user_id=current_user['id'],
            actual_cash=cash_balance_before,
            notes="إغلاق تجريبي"
        )
        
        if closure_success:
            print("✅ تم إغلاق الصندوق بنجاح")
            
            # التحقق من النقل للخزينة اليومية
            daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
            print(f"💰 رصيد الخزينة اليومية بعد الإغلاق: {daily_balance_after:,} ل.س")
            
            if daily_balance_after > daily_balance_before:
                print("✅ تم نقل المبلغ للخزينة اليومية")
            else:
                print("⚠️ لم يتم نقل المبلغ للخزينة اليومية")
        else:
            print("❌ فشل في إغلاق الصندوق")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إغلاق الصندوق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_treasury_transfer():
    """اختبار نقل الخزينة"""
    
    print("\n🧪 اختبار نقل الخزينة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # الحصول على الأرصدة قبل النقل
        daily_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        main_balance_before = treasury_manager.get_main_balance('SYP')
        
        print(f"💰 الخزينة اليومية قبل النقل: {daily_balance_before:,} ل.س")
        print(f"💰 الخزينة الرئيسية قبل النقل: {main_balance_before:,} ل.س")
        
        if daily_balance_before > 0:
            # اختبار نقل جزء من المبلغ
            transfer_amount = min(50000, daily_balance_before)
            
            print(f"\n🔄 اختبار نقل {transfer_amount:,} ل.س...")
            transfer_success = treasury_manager.transfer_to_main_treasury(
                user_id=current_user['id'],
                currency='SYP',
                amount=transfer_amount,
                notes="نقل تجريبي"
            )
            
            if transfer_success:
                print("✅ تم النقل بنجاح")
                
                # التحقق من الأرصدة بعد النقل
                daily_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                main_balance_after = treasury_manager.get_main_balance('SYP')
                
                print(f"💰 الخزينة اليومية بعد النقل: {daily_balance_after:,} ل.س")
                print(f"💰 الخزينة الرئيسية بعد النقل: {main_balance_after:,} ل.س")
                
                if (daily_balance_after == daily_balance_before - transfer_amount and 
                    main_balance_after == main_balance_before + transfer_amount):
                    print("✅ النقل تم بشكل صحيح")
                else:
                    print("❌ خطأ في حسابات النقل")
                    return False
            else:
                print("❌ فشل في النقل")
                return False
        else:
            print("⚠️ لا يوجد رصيد في الخزينة اليومية للنقل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نقل الخزينة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_exchange():
    """اختبار تحويل العملة"""
    
    print("\n🧪 اختبار تحويل العملة...")
    
    try:
        from database.database_manager import DatabaseManager
        from utils.unified_treasury_manager import UnifiedTreasuryManager
        
        db = DatabaseManager('.')
        treasury_manager = UnifiedTreasuryManager(db)
        current_user = {'id': 1, 'username': 'admin', 'full_name': 'المدير'}
        
        # الحصول على الأرصدة قبل التحويل
        syp_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
        usd_balance_before = treasury_manager.get_daily_balance(current_user['id'], 'USD')
        
        print(f"💰 رصيد الليرة قبل التحويل: {syp_balance_before:,} ل.س")
        print(f"💰 رصيد الدولار قبل التحويل: {usd_balance_before:.2f} $")
        
        if syp_balance_before >= 15000:  # نحتاج على الأقل 15000 ل.س لشراء 1$
            # اختبار تحويل العملة
            syp_amount = 15000
            exchange_rate = 15000
            
            print(f"\n💱 اختبار تحويل {syp_amount:,} ل.س إلى دولار...")
            exchange_success = treasury_manager.exchange_currency(
                user_id=current_user['id'],
                from_currency='SYP',
                to_currency='USD',
                from_amount=syp_amount,
                exchange_rate=exchange_rate,
                notes="تحويل تجريبي"
            )
            
            if exchange_success:
                print("✅ تم التحويل بنجاح")
                
                # التحقق من الأرصدة بعد التحويل
                syp_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'SYP')
                usd_balance_after = treasury_manager.get_daily_balance(current_user['id'], 'USD')
                
                expected_usd = syp_amount / exchange_rate
                
                print(f"💰 رصيد الليرة بعد التحويل: {syp_balance_after:,} ل.س")
                print(f"💰 رصيد الدولار بعد التحويل: {usd_balance_after:.2f} $")
                
                if (abs(syp_balance_after - (syp_balance_before - syp_amount)) < 1 and
                    abs(usd_balance_after - (usd_balance_before + expected_usd)) < 0.01):
                    print("✅ التحويل تم بشكل صحيح")
                else:
                    print("❌ خطأ في حسابات التحويل")
                    return False
            else:
                print("❌ فشل في التحويل")
                return False
        else:
            print("⚠️ لا يوجد رصيد كافي للتحويل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحويل العملة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 اختبار النظام المالي الجديد")
    print("=" * 60)
    
    # اختبار نظام الشيفتات
    shift_test = test_shift_system()
    
    # اختبار إغلاق الصندوق
    closure_test = test_cash_box_closure()
    
    # اختبار نقل الخزينة
    transfer_test = test_treasury_transfer()
    
    # اختبار تحويل العملة
    exchange_test = test_currency_exchange()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"  • نظام الشيفتات: {'✅ يعمل' if shift_test else '❌ لا يعمل'}")
    print(f"  • إغلاق الصندوق: {'✅ يعمل' if closure_test else '❌ لا يعمل'}")
    print(f"  • نقل الخزينة: {'✅ يعمل' if transfer_test else '❌ لا يعمل'}")
    print(f"  • تحويل العملة: {'✅ يعمل' if exchange_test else '❌ لا يعمل'}")
    
    if all([shift_test, closure_test, transfer_test, exchange_test]):
        print("\n🎉 النظام المالي الجديد يعمل بشكل مثالي!")
        
        print("\n📋 التدفق المالي:")
        print("  1️⃣ تسجيل الدخول → فتح شيفت/صندوق")
        print("  2️⃣ المبيعات والمصاريف → تُسجل في الصندوق")
        print("  3️⃣ إغلاق الصندوق → نقل المبلغ للخزينة اليومية")
        print("  4️⃣ نقل الخزينة → من اليومية للرئيسية")
        print("  5️⃣ شراء الدولار → تحويل داخلي في الخزينة اليومية")
        
        print("\n🔧 للاستخدام:")
        print("  • تسجيل الدخول يفتح شيفت تلقائياً")
        print("  • المبيعات تُضاف للصندوق")
        print("  • إغلاق الصندوق ينقل للخزينة اليومية")
        print("  • استخدم 'نقل الخزينة' لنقل للرئيسية")
        print("  • استخدم 'شراء الدولار' لتحويل العملة")
        
    else:
        print("\n❌ هناك مشاكل في النظام المالي!")

if __name__ == "__main__":
    main()

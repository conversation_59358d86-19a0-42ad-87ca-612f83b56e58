#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة المستخدمين المفقودين
"""

import sys
import os
import hashlib
sys.path.append('src')

def check_users_table():
    """فحص جدول المستخدمين"""
    
    print("🔍 فحص جدول المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # فحص وجود الجدول
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        
        if not tables:
            print("❌ جدول المستخدمين غير موجود!")
            return False, []
        
        print("✅ جدول المستخدمين موجود")
        
        # فحص المستخدمين الموجودين
        users = db.fetch_all("SELECT id, username, full_name, role, is_active FROM users")
        print(f"👥 المستخدمين الموجودين ({len(users)}):")
        
        if users:
            for user in users:
                status = "نشط" if user[4] == 1 else "غير نشط"
                print(f"  • ID: {user[0]}, اسم المستخدم: '{user[1]}', الاسم: '{user[2]}', الدور: '{user[3]}', الحالة: {status}")
        else:
            print("  ❌ لا يوجد مستخدمين!")
        
        return True, users
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def create_default_users():
    """إنشاء المستخدمين الافتراضيين"""
    
    print("\n👥 إنشاء المستخدمين الافتراضيين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # المستخدمين الافتراضيين
        default_users = [
            ('admin', '123', 'المدير العام', '<EMAIL>', 'admin'),
            ('manager', '123', 'المدير', '<EMAIL>', 'manager'),
            ('employee', '123', 'الموظف', '<EMAIL>', 'employee'),
            ('accountant', '123', 'المحاسب', '<EMAIL>', 'accountant')
        ]
        
        created_count = 0
        
        for username, password, full_name, email, role in default_users:
            # تشفير كلمة المرور
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # التحقق من عدم وجود المستخدم
            existing = db.fetch_one("SELECT id FROM users WHERE username = ?", (username,))
            
            if existing:
                print(f"  ⚠️ المستخدم '{username}' موجود بالفعل")
                continue
            
            # إنشاء المستخدم
            result = db.execute_query("""
                INSERT INTO users (username, password_hash, full_name, email, role, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
            """, (username, password_hash, full_name, email, role))
            
            if result:
                print(f"  ✅ تم إنشاء المستخدم: {username} / {password} ({role})")
                created_count += 1
            else:
                print(f"  ❌ فشل في إنشاء المستخدم: {username}")
        
        print(f"\n📊 تم إنشاء {created_count} مستخدم جديد")
        return created_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين الافتراضيين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_for_all_users():
    """اختبار تسجيل الدخول لجميع المستخدمين"""
    
    print("\n🧪 اختبار تسجيل الدخول لجميع المستخدمين...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # الحصول على جميع المستخدمين النشطين
        users = db.fetch_all("SELECT username, full_name FROM users WHERE is_active = 1")
        
        if not users:
            print("❌ لا يوجد مستخدمين نشطين!")
            return False
        
        print(f"👥 اختبار {len(users)} مستخدم...")
        
        success_count = 0
        
        for user in users:
            username = user['username']
            
            # اختبار بكلمة المرور الافتراضية
            password = '123'
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # محاولة تسجيل الدخول
            login_test = db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, hashed_password))
            
            if login_test:
                print(f"  ✅ {username} / {password} - نجح")
                success_count += 1
            else:
                print(f"  ❌ {username} / {password} - فشل")
                
                # محاولة إصلاح كلمة المرور
                print(f"    🔧 إصلاح كلمة المرور للمستخدم {username}...")
                fix_result = db.execute_query("""
                    UPDATE users 
                    SET password_hash = ? 
                    WHERE username = ?
                """, (hashed_password, username))
                
                if fix_result:
                    print(f"    ✅ تم إصلاح كلمة المرور")
                    success_count += 1
                else:
                    print(f"    ❌ فشل في إصلاح كلمة المرور")
        
        print(f"\n📊 النتيجة: {success_count}/{len(users)} مستخدمين يعملون")
        
        return success_count == len(users)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def verify_login_window_compatibility():
    """التحقق من توافق واجهة تسجيل الدخول"""
    
    print("\n🔍 التحقق من توافق واجهة تسجيل الدخول...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager('data/company_system.db')
        
        # محاكاة عملية تسجيل الدخول كما تحدث في الواجهة
        username = 'admin'
        password = '123'
        
        print(f"📝 محاكاة تسجيل الدخول:")
        print(f"  • اسم المستخدم: '{username}'")
        print(f"  • كلمة المرور: '{password}'")
        
        # تشفير كلمة المرور (نفس الطريقة في login_window.py)
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        print(f"  • كلمة المرور المشفرة: {hashed_password}")
        
        # البحث عن المستخدم بالاسم فقط (نفس الطريقة في login_window.py)
        user_check = db.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
        
        if user_check:
            print("✅ المستخدم موجود")
            print(f"  • كلمة المرور في قاعدة البيانات: {user_check['password_hash']}")
            print(f"  • المستخدم نشط: {user_check['is_active']}")
            
            if user_check['password_hash'] == hashed_password:
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور خاطئة")
                print(f"  المتوقع: {user_check['password_hash']}")
                print(f"  المدخل: {hashed_password}")
        else:
            print("❌ المستخدم غير موجود")
        
        # البحث الكامل (نفس الطريقة في login_window.py)
        user = db.fetch_one("""
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        """, (username, hashed_password))
        
        if user:
            print("✅ تسجيل الدخول سينجح في الواجهة!")
            return True
        else:
            print("❌ تسجيل الدخول سيفشل في الواجهة!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من التوافق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚨 إصلاح مشكلة المستخدمين المفقودين")
    print("=" * 50)
    
    # فحص جدول المستخدمين
    table_exists, existing_users = check_users_table()
    
    if not table_exists:
        print("❌ جدول المستخدمين غير موجود!")
        return
    
    # إنشاء المستخدمين الافتراضيين إذا لم يكونوا موجودين
    if len(existing_users) == 0:
        print("\n⚠️ لا يوجد مستخدمين - سيتم إنشاء المستخدمين الافتراضيين")
        users_created = create_default_users()
    else:
        print(f"\n✅ يوجد {len(existing_users)} مستخدم")
        users_created = True
    
    # اختبار تسجيل الدخول لجميع المستخدمين
    if users_created:
        login_test = test_login_for_all_users()
    else:
        login_test = False
    
    # التحقق من توافق واجهة تسجيل الدخول
    compatibility_test = verify_login_window_compatibility()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"  • وجود جدول المستخدمين: {'✅ نجح' if table_exists else '❌ فشل'}")
    print(f"  • إنشاء/وجود المستخدمين: {'✅ نجح' if users_created else '❌ فشل'}")
    print(f"  • اختبار تسجيل الدخول: {'✅ نجح' if login_test else '❌ فشل'}")
    print(f"  • توافق واجهة تسجيل الدخول: {'✅ نجح' if compatibility_test else '❌ فشل'}")
    
    if all([table_exists, users_created, login_test, compatibility_test]):
        print("\n🎉 تم إصلاح مشكلة المستخدمين المفقودين!")
        
        print("\n📋 المستخدمين المتاحين الآن:")
        print("  • admin / 123 (المدير العام)")
        print("  • manager / 123 (المدير)")
        print("  • employee / 123 (الموظف)")
        print("  • accountant / 123 (المحاسب)")
        
        print("\n🔧 للاستخدام:")
        print("  1. شغل التطبيق: python system_launcher.py")
        print("  2. استخدم: admin / 123")
        print("  3. تسجيل الدخول يجب أن يعمل الآن!")
        
    else:
        print("\n❌ لا تزال هناك مشاكل!")
        
        if not table_exists:
            print("  🔧 إنشاء جدول المستخدمين")
        if not users_created:
            print("  🔧 إنشاء المستخدمين الافتراضيين")
        if not login_test:
            print("  🔧 إصلاح كلمات مرور المستخدمين")
        if not compatibility_test:
            print("  🔧 مراجعة واجهة تسجيل الدخول")

if __name__ == "__main__":
    main()

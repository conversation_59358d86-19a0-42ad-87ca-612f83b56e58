#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف جداول الخزينة المكررة
"""

import sys
import os
sys.path.append('src')

from database.database_manager import DatabaseManager

def cleanup_treasury_tables():
    """تنظيف جداول الخزينة المكررة"""
    
    print("🧹 تنظيف جداول الخزينة المكررة...")
    
    db = DatabaseManager('data/company_system.db')
    
    try:
        # عرض الجداول الموجودة
        tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%treasury%' ORDER BY name")
        print(f"\n📋 جداول الخزينة الموجودة:")
        for table in tables:
            print(f"  • {table[0]}")
        
        # فحص محتوى كل جدول
        for table in tables:
            table_name = table[0]
            try:
                count = db.fetch_one(f"SELECT COUNT(*) as count FROM {table_name}")
                columns = db.fetch_all(f"PRAGMA table_info({table_name})")
                
                print(f"\n📊 جدول {table_name}:")
                print(f"  • عدد السجلات: {count['count'] if count else 0}")
                print(f"  • الأعمدة:")
                for col in columns:
                    print(f"    - {col[1]} ({col[2]})")
                
                # عرض عينة من البيانات
                if count and count['count'] > 0:
                    sample = db.fetch_all(f"SELECT * FROM {table_name} LIMIT 3")
                    print(f"  • عينة من البيانات:")
                    for row in sample:
                        print(f"    - {dict(row)}")
                        
            except Exception as e:
                print(f"  ❌ خطأ في فحص {table_name}: {e}")
        
        # تحديد الجدول الرئيسي (treasury)
        print(f"\n🎯 تحديد الجدول الرئيسي:")
        
        # فحص جدول treasury الرئيسي
        treasury_exists = any(table[0] == 'treasury' for table in tables)
        
        if treasury_exists:
            print(f"  ✅ جدول treasury موجود")
            
            # فحص البنية
            treasury_columns = db.fetch_all("PRAGMA table_info(treasury)")
            treasury_data = db.fetch_all("SELECT * FROM treasury")
            
            print(f"  📊 بنية جدول treasury:")
            for col in treasury_columns:
                print(f"    • {col[1]} - {col[2]}")
            
            print(f"  📊 بيانات جدول treasury ({len(treasury_data)} سجل):")
            for row in treasury_data:
                currency_type = row[1] if len(row) > 1 else 'N/A'
                balance = row[2] if len(row) > 2 else 0
                print(f"    • {currency_type}: {balance}")
            
            # التحقق من وجود جداول أخرى مشابهة
            other_treasury_tables = [t[0] for t in tables if t[0] != 'treasury' and 'treasury' in t[0]]
            
            if other_treasury_tables:
                print(f"\n⚠️ جداول خزينة إضافية موجودة:")
                for table_name in other_treasury_tables:
                    print(f"  • {table_name}")
                
                # اقتراح الحذف
                print(f"\n🗑️ اقتراح حذف الجداول الإضافية:")
                for table_name in other_treasury_tables:
                    try:
                        # فحص البيانات قبل الحذف
                        data = db.fetch_all(f"SELECT * FROM {table_name}")
                        if data:
                            print(f"  ⚠️ {table_name} يحتوي على {len(data)} سجل - يحتاج مراجعة")
                            for row in data:
                                print(f"    - {dict(row)}")
                        else:
                            print(f"  ✅ {table_name} فارغ - يمكن حذفه")
                            # حذف الجدول الفارغ
                            db.execute_query(f"DROP TABLE IF EXISTS {table_name}")
                            print(f"  🗑️ تم حذف {table_name}")
                    except Exception as e:
                        print(f"  ❌ خطأ في فحص {table_name}: {e}")
            else:
                print(f"  ✅ لا توجد جداول خزينة إضافية")
        
        else:
            print(f"  ❌ جدول treasury غير موجود")
            
            # إنشاء جدول treasury الرئيسي
            print(f"  🔧 إنشاء جدول treasury الرئيسي...")
            db.execute_query("""
                CREATE TABLE treasury (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    currency_type TEXT UNIQUE NOT NULL DEFAULT 'SYP',
                    balance REAL DEFAULT 0,
                    exchange_rate REAL DEFAULT 1.0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إضافة البيانات الافتراضية
            default_currencies = [
                ('SYP', 1000000, 1.0),
                ('USD', 500.0, 15000.0),
                ('EUR', 300.0, 16000.0)
            ]
            
            for currency_type, balance, exchange_rate in default_currencies:
                db.execute_query("""
                    INSERT INTO treasury (currency_type, balance, exchange_rate)
                    VALUES (?, ?, ?)
                """, (currency_type, balance, exchange_rate))
            
            print(f"  ✅ تم إنشاء جدول treasury مع البيانات الافتراضية")
        
        # فحص جدول treasury_transfers
        transfers_exists = any(table[0] == 'treasury_transfers' for table in tables)
        
        if transfers_exists:
            print(f"\n📋 جدول treasury_transfers:")
            transfers_count = db.fetch_one("SELECT COUNT(*) as count FROM treasury_transfers")
            print(f"  • عدد النقلات: {transfers_count['count'] if transfers_count else 0}")
        else:
            print(f"\n📋 إنشاء جدول treasury_transfers...")
            db.execute_query("""
                CREATE TABLE IF NOT EXISTS treasury_transfers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transfer_date DATE NOT NULL,
                    currency_type TEXT DEFAULT 'SYP',
                    amount REAL NOT NULL,
                    exchange_rate_used REAL DEFAULT 1.0,
                    amount_in_syp REAL,
                    receiver TEXT,
                    notes TEXT,
                    transferred_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print(f"  ✅ تم إنشاء جدول treasury_transfers")
        
        # عرض الحالة النهائية
        print(f"\n🎉 تم تنظيف جداول الخزينة!")
        
        final_tables = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%treasury%' ORDER BY name")
        print(f"\n📋 الجداول النهائية:")
        for table in final_tables:
            count = db.fetch_one(f"SELECT COUNT(*) as count FROM {table[0]}")
            print(f"  • {table[0]}: {count['count'] if count else 0} سجل")
        
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    cleanup_treasury_tables()
